/**
 * Restriction Handler
 *
 * This script handles API responses for restricted users and shows appropriate messages.
 */

class RestrictionHandler {
    /**
     * Initialize the restriction handler
     */
    constructor() {
        this.setupFetchInterceptor();
    }

    /**
     * Set up a fetch interceptor to handle restricted API responses
     */
    setupFetchInterceptor() {
        // Store the original fetch function
        const originalFetch = window.fetch;

        // Override the fetch function
        window.fetch = async (...args) => {
            // Log the request URL for debugging
            console.log('Fetch request:', args[0]);

            // Call the original fetch function
            const response = await originalFetch(...args);

            // Log the response status
            console.log('Fetch response status:', response.status, 'for URL:', args[0]);

            // Clone the response to avoid consuming it
            const clone = response.clone();

            // Check if the response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                try {
                    // Parse the JSON response
                    const data = await clone.json();

                    // Check if the response indicates a restriction
                    if (response.status === 403 && data.restricted === true) {
                        this.handleRestriction(data.service);
                    }
                } catch (error) {
                    // Ignore JSON parsing errors
                    console.error('Error parsing JSON response:', error);
                }
            }

            // Return the original response
            return response;
        };
    }

    /**
     * Handle a restriction response
     * @param {string} service - The restricted service name
     */
    handleRestriction(service) {
        // Show a modal or notification about the restriction
        const serviceName = this.getServiceDisplayName(service);

        // Create a modal if it doesn't exist
        if (!document.getElementById('apiRestrictionModal')) {
            const modal = document.createElement('div');
            modal.id = 'apiRestrictionModal';
            modal.className = 'fixed inset-0 bg-black/70 flex items-center justify-center z-[9999]';
            modal.innerHTML = `
                <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-slate-100">Access Restricted</h3>
                        <a href="/dashboard" class="text-slate-400 hover:text-slate-100">
                            <i data-lucide="x" class="h-5 w-5"></i>
                        </a>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-red-500/10 p-4 rounded-lg flex items-start">
                            <i data-lucide="alert-circle" class="h-5 w-5 text-red-500 mr-3 mt-0.5"></i>
                            <div>
                                <h4 class="text-red-500 font-medium">Access Denied</h4>
                                <p class="text-slate-300 text-sm mt-1">
                                    Your account has been restricted from accessing the ${serviceName} service.
                                </p>
                            </div>
                        </div>
                        <p class="text-slate-400 text-sm">
                            If you believe this is an error, please contact an administrator for assistance.
                        </p>
                        <div class="pt-2">
                            <a href="/dashboard" class="w-full bg-slate-700 hover:bg-slate-600 text-white rounded-md px-4 py-2 flex items-center justify-center">
                                <i data-lucide="arrow-left" class="h-4 w-4 mr-2"></i>
                                Return to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons();
            }
        }
    }

    /**
     * Get a display name for a service
     * @param {string} service - The service identifier
     * @returns {string} The display name
     */
    getServiceDisplayName(service) {
        const serviceNames = {
            'chat': 'Chat',
            'live': 'Live Chat',
            'spotify': 'Spotify'
        };

        return serviceNames[service] || service;
    }
}

// Initialize the restriction handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.restrictionHandler = new RestrictionHandler();
});
