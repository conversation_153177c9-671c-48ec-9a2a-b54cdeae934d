"""
API Method Checker Utility

This module provides functions to apply method restrictions to API endpoints.
It ensures that endpoints only respond to the HTTP methods they're designed for.
"""
import logging
from flask import Blueprint
from utils.method_decorators import method_not_allowed

def apply_method_restrictions(blueprint):
    """
    Apply method restrictions to all routes in a blueprint.
    
    Args:
        blueprint (Blueprint): The Flask blueprint to process
    """
    if not isinstance(blueprint, Blueprint):
        logging.error(f"Expected Blueprint, got {type(blueprint)}")
        return
    
    # Get all routes in the blueprint
    for route in blueprint.deferred_functions:
        # Extract the route function and its allowed methods
        if hasattr(route, '__name__') and route.__name__ == 'deferred':
            # This is a route registration function
            # We need to extract the view function and its methods
            view_func = None
            methods = None
            
            # Inspect the closure to find the view function and methods
            if hasattr(route, '__closure__') and route.__closure__:
                for cell in route.__closure__:
                    if cell.cell_contents and isinstance(cell.cell_contents, dict):
                        if 'view_func' in cell.cell_contents:
                            view_func = cell.cell_contents.get('view_func')
                        if 'options' in cell.cell_contents:
                            options = cell.cell_contents.get('options', {})
                            methods = options.get('methods', ['GET'])
            
            if view_func and methods:
                # Apply the method_not_allowed decorator
                original_view_func = view_func
                decorated_view_func = method_not_allowed(methods)(original_view_func)
                
                # Replace the original view function with the decorated one
                if hasattr(blueprint, 'view_functions'):
                    for endpoint, func in blueprint.view_functions.items():
                        if func == original_view_func:
                            blueprint.view_functions[endpoint] = decorated_view_func
                            logging.info(f"Applied method restriction to {endpoint} - Methods: {methods}")

def apply_method_restrictions_to_all(app):
    """
    Apply method restrictions to all blueprints in the Flask app.
    
    Args:
        app (Flask): The Flask application
    """
    # Process all registered blueprints
    for blueprint_name, blueprint in app.blueprints.items():
        if blueprint_name.startswith('api_'):
            logging.info(f"Applying method restrictions to blueprint: {blueprint_name}")
            apply_method_restrictions(blueprint)