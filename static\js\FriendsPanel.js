/**
 * FriendsPanel.js
 * Handles friends panel functionality in the dashboard
 */
class FriendsPanel {
    constructor() {
        this.currentChatId = null;
        this.currentFriendId = null;
        this.currentGroupChatId = null;
        this.isGroupChat = false;
        this.socket = null;
        this.friends = [];
        this.groupChats = [];
        this.selectedFriends = new Set(); // For group chat creation
        this.isLoading = false;
        this.isOnline = false; // Track if user is online (only when friends panel is open)

        // Typing indicator related properties
        this.typingTimeout = null; // Timeout for current user typing
        this.isTyping = false; // Whether the current user is typing
        this.friendTypingTimeout = null; // Timeout for friend typing indicator
        this.groupTypingTimeout = null; // Timeout for group chat typing indicator

        this.friendToRemove = null;
        this.friendToInvite = null;
        this.tempMessageCounter = 0; // Counter for temporary message IDs
        this.processingSocketMessage = false; // Flag to prevent processing socket messages while handling REST API

        // Friend requests cache
        this.pendingRequests = []; // Requests received from others
        this.sentRequests = []; // Requests sent to others
        this.requestsLoaded = false; // Flag to track if requests have been loaded

        // Performance optimization properties
        this._scrolling = false; // Flag to prevent multiple scroll operations
        this._lastScrollTime = 0; // Track last scroll time for throttling

        // Theme settings
        this.userTheme = localStorage.getItem('userChatTheme') || 'blue'; // Default theme
        this.friendThemes = {}; // Store friend themes: { friendId: themeName }

        // Get current username from meta tag
        this.currentUsername = document.querySelector('meta[name="username"]')?.getAttribute('content') || 'You';
        
        // Current user profile picture (will be loaded during initialization)
        this.currentUserProfilePicture = null;
        
        // Promise for profile picture loading (to prevent multiple concurrent requests)
        this._profilePicturePromise = null;
    }

    /**
     * Initialize the friends panel
     */
    init() {
        console.log('Initializing Friends Panel...');

        // Initialize image handling
        this.selectedImages = [];
        this.maxImages = 3;
        
        // Check if the friends panel is currently visible
        const currentView = document.body.getAttribute('data-current-view') || '';
        this.isOnline = currentView === 'friends';
        console.log(`Initial online status: ${this.isOnline ? 'online' : 'offline'}`);

        // Set up event listeners
        this.setupEventListeners();

        // Check if we have a last opened chat
        const lastOpenedChatId = localStorage.getItem('lastOpenedChatId');
        
        // If we have a last opened chat, hide the welcome message right away
        if (lastOpenedChatId) {
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage) {
                welcomeMessage.classList.add('hidden');
            }
            
            const chatContainer = document.getElementById('chatContainer');
            if (chatContainer) {
                chatContainer.classList.remove('hidden');
            }
        }

        // Trigger loading state
        if (typeof window.showFriendsLoading === 'function') {
            // Reset loading state
            if (typeof window.hideFriendsLoading === 'function') {
                window.hideFriendsLoading(); // Force reset
            }

            // Show loading placeholders
            window.showFriendsLoading();

            // Dispatch a custom event to indicate loading has started
            document.dispatchEvent(new CustomEvent('friends:loading'));
        }

        // Load friends list
        this.loadFriends();

        // Load group chats
        this.loadGroupChats();

        // Update friend requests badge
        this.updateFriendRequestsBadge();

        // Initialize Socket.IO
        this.initSocketIO();

        // Load friend themes
        this.loadFriendThemes();
        
        // Load current user's profile picture
        this.getCurrentUserProfilePicture()
            .then(profilePicture => {
                console.log('Loaded current user profile picture:', profilePicture);
                
                // Start periodic refresh of the profile picture (every 5 minutes)
                this.startProfilePictureRefresh();
            })
            .catch(error => {
                console.error('Error loading current user profile picture:', error);
            });
        
        // Start activity status updates
        this.startActivityStatusUpdates();
    }
    
    /**
     * Start periodic activity status updates
     */
    startActivityStatusUpdates() {
        // Update activity status immediately
        this.updateUserActivity();
        
        // Check friends' online status immediately
        this.checkFriendsOnlineStatus();
        
        // Set up periodic activity updates (every 15 seconds)
        this.activityUpdateInterval = setInterval(() => {
            this.updateUserActivity();
        }, 15000);
        
        // Set up periodic status checks for friends (every 30 seconds)
        this.friendsStatusInterval = setInterval(() => {
            this.checkFriendsOnlineStatus();
        }, 30000);
    }
    
    /**
     * Start periodic refresh of the user's profile picture
     * This ensures the profile picture is always up to date
     */
    startProfilePictureRefresh() {
        // Refresh the profile picture every 5 minutes
        this.profilePictureInterval = setInterval(() => {
            // Fetch the profile picture again with force refresh
            this.getCurrentUserProfilePicture(true) // Pass true to force refresh
                .then(profilePicture => {
                    console.log('Refreshed user profile picture:', profilePicture);
                    
                    // If the profile picture has changed, update all self messages
                    if (profilePicture) {
                        this.updateSelfMessageAvatars(profilePicture);
                    }
                })
                .catch(error => {
                    console.error('Error refreshing user profile picture:', error);
                });
        }, 5 * 60 * 1000); // 5 minutes
    }
    
    /**
     * Update all self message avatars with the new profile picture
     * @param {string} profilePictureUrl - The new profile picture URL
     */
    updateSelfMessageAvatars(profilePictureUrl) {
        // Format the profile picture URL with cache busting for refresh
        const formattedUrl = this.formatProfilePictureUrl(profilePictureUrl, true);
        
        // Find all self messages
        const selfMessages = document.querySelectorAll('.message.self .message-avatar');
        console.log(`Updating ${selfMessages.length} self message avatars with new profile picture`);
        
        // Update each message avatar
        selfMessages.forEach(avatarContainer => {
            // Check if it has an image
            const img = avatarContainer.querySelector('img');
            if (img) {
                // Update the image source
                img.src = formattedUrl;
            } else {
                // Replace the initials with an image
                avatarContainer.innerHTML = `
                    <img src="${formattedUrl}" alt="You" class="w-8 h-8 rounded-full object-cover shadow-md" onerror="this.onerror=null; this.src='/static/images/default-avatar.png';">
                `;
            }
        });
    }

    /**
     * Load friend themes from server
     * This is called once during initialization to prepare for theme loading
     */
    async loadFriendThemes() {
        try {
            console.log('Initializing friend themes system...');

            // Initialize the themes cache
            this.friendThemes = {};

            // First try to get the user's theme from the server
            try {
                const response = await fetch('/api/friends/theme/me');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.theme) {
                        console.log(`Loaded user theme from server: ${data.theme}`);
                        this.userTheme = data.theme;
                        // Update localStorage with the server value
                        localStorage.setItem('userChatTheme', data.theme);
                    }
                }
            } catch (error) {
                console.error('Error fetching user theme from server:', error);
                // Fall back to localStorage if server fetch fails
                this.userTheme = localStorage.getItem('userChatTheme') || 'blue';
            }

            // If we're in a friend chat, prioritize loading that friend's theme
            if (this.currentFriendId) {
                console.log(`Prioritizing theme for current friend: ${this.currentFriendId}`);
                await this.fetchFriendTheme(this.currentFriendId);

                // Update existing friend messages with the theme
                this.updateExistingFriendMessagesThemes();
            }

            // Initialize the themes to fetch set
            this._themesToFetch = new Set();
            this._themesFetchScheduled = false;

            // Find all friend messages that need themes
            const messagesContainer = document.getElementById('messagesContainer');
            if (messagesContainer) {
                const friendMessages = messagesContainer.querySelectorAll('.message.friend[data-user-id]');
                console.log(`Found ${friendMessages.length} friend messages that may need themes`);

                // Add each unique friend ID to the themes to fetch set
                friendMessages.forEach(message => {
                    const userId = message.getAttribute('data-user-id');
                    if (userId && !this.friendThemes[userId]) {
                        this._themesToFetch.add(userId);
                    }
                });

                // If we have themes to fetch, schedule a batch fetch
                if (this._themesToFetch.size > 0) {
                    console.log(`Scheduling batch fetch for ${this._themesToFetch.size} friend themes`);
                    this._themesFetchScheduled = true;
                    setTimeout(() => this.batchFetchThemes(), 100);
                }
            }

            // Update existing messages with the user's theme
            this.updateExistingMessagesTheme();
        } catch (error) {
            console.error('Error initializing friend themes:', error);
        }
    }

    /**
     * Update existing friend messages with their themes
     */
    updateExistingFriendMessagesThemes() {
        console.log('Updating existing message themes...');
        console.log('Current friend ID:', this.currentFriendId);
        console.log('Friend themes cache:', Object.keys(this.friendThemes).length);

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) {
            console.log('Messages container not found');
            return;
        }

        // Get all friend messages
        const friendMessages = messagesContainer.querySelectorAll('.message.friend');
        console.log(`Found ${friendMessages.length} friend messages to update`);

        // If we're in a friend chat, apply the current friend's theme to all their messages
        if (this.currentFriendId && this.friendThemes[this.currentFriendId]) {
            const currentFriendTheme = this.friendThemes[this.currentFriendId];
            console.log(`Applying theme ${currentFriendTheme} to all messages from friend ${this.currentFriendId}`);

            // First, update all messages from the current friend
            const currentFriendMessages = messagesContainer.querySelectorAll(`.message.friend[data-user-id="${this.currentFriendId}"]`);
            currentFriendMessages.forEach(message => {
                // Remove all theme classes
                message.className = message.className.replace(/theme-\w+/g, '');

                // Add the friend's theme class
                message.classList.add('message', 'friend', `theme-${currentFriendTheme}`);
            });

            // Then update any other friend messages that don't have a user ID
            // (these are likely from the current friend but missing the attribute)
            const unidentifiedFriendMessages = messagesContainer.querySelectorAll('.message.friend:not([data-user-id])');
            unidentifiedFriendMessages.forEach(message => {
                // Remove all theme classes
                message.className = message.className.replace(/theme-\w+/g, '');

                // Add the current friend's theme class
                message.classList.add('message', 'friend', `theme-${currentFriendTheme}`);

                // Add the user ID attribute for future updates
                message.setAttribute('data-user-id', this.currentFriendId);
            });

            // Update the message input field with the friend's theme
            this.updateMessageInputTheme(currentFriendTheme);
        }

        // Update all other friend messages with their specific themes
        friendMessages.forEach(message => {
            const userId = message.getAttribute('data-user-id');
            // Skip messages from the current friend (already handled above)
            if (userId && userId !== this.currentFriendId && this.friendThemes[userId]) {
                // Remove all theme classes
                message.className = message.className.replace(/theme-\w+/g, '');

                // Add the friend's theme class
                message.classList.add('message', 'friend', `theme-${this.friendThemes[userId]}`);
                console.log(`Updated friend message from user ${userId} with theme ${this.friendThemes[userId]}`);
            }
        });

        // Also update self messages to use the user's own theme
        const selfMessages = messagesContainer.querySelectorAll('.message.self');
        console.log(`Found ${selfMessages.length} self messages to update`);

        selfMessages.forEach(message => {
            // Remove all theme classes
            message.className = message.className.replace(/theme-\w+/g, '');

            // For messages sent by the current user, always apply the user's own theme
            message.classList.add('message', 'self', `theme-${this.userTheme}`);
        });

        // Check if we need to fetch any more themes
        if (this._themesToFetch && this._themesToFetch.size > 0 && !this._themesFetchScheduled) {
            console.log(`Still need to fetch ${this._themesToFetch.size} themes, scheduling batch fetch`);
            this._themesFetchScheduled = true;
            setTimeout(() => this.batchFetchThemes(), 100);
        }
    }

    /**
     * Fetch and apply a friend's theme to a message element
     * @param {string} friendId - The friend's user ID
     * @param {HTMLElement} messageElement - The message element to apply the theme to
     */
    async fetchAndApplyFriendTheme(friendId, messageElement) {
        // If we already have this friend's theme, use it without fetching
        if (this.friendThemes[friendId]) {
            const theme = this.friendThemes[friendId];

            // Apply the theme to the message element
            messageElement.className = messageElement.className.replace(/theme-\w+/g, '');
            messageElement.classList.add(`theme-${theme}`);

            // Apply the theme to all other messages from this friend
            document.querySelectorAll(`.message.friend[data-user-id="${friendId}"]`).forEach(el => {
                if (el !== messageElement) {
                    el.className = el.className.replace(/theme-\w+/g, '');
                    el.classList.add(`theme-${theme}`);
                }
            });

            return;
        }

        // If we're already fetching this friend's theme, don't fetch again
        if (this._fetchingThemes && this._fetchingThemes[friendId]) {
            return;
        }

        // Initialize fetching themes tracking object if it doesn't exist
        if (!this._fetchingThemes) {
            this._fetchingThemes = {};
        }

        // Mark that we're fetching this friend's theme
        this._fetchingThemes[friendId] = true;

        // Add to batch fetch queue instead of fetching immediately
        if (!this._themesToFetch) {
            this._themesToFetch = new Set();
        }
        this._themesToFetch.add(friendId);

        // Schedule a batch fetch if not already scheduled
        if (!this._themesFetchScheduled) {
            this._themesFetchScheduled = true;
            setTimeout(() => this.batchFetchThemes(), 100);
        }

        // Apply default theme for now
        messageElement.className = messageElement.className.replace(/theme-\w+/g, '');
        messageElement.classList.add('theme-blue');

        // Mark that we're done fetching this friend's theme
        if (this._fetchingThemes) {
            this._fetchingThemes[friendId] = false;
        }
    }

    /**
     * Update a friend's online status in the UI
     * @param {string} friendId - The friend's user ID
     * @param {boolean} isOnline - Whether the friend is online
     */
    updateFriendOnlineStatus(friendId, isOnline) {
        console.log(`Updating friend ${friendId} online status: ${isOnline}`);
        
        // Find the friend in the list
        const friendElement = document.querySelector(`.friend-item[data-user-id="${friendId}"]`);
        if (!friendElement) {
            console.log(`Friend element for ${friendId} not found in the DOM`);
            return;
        }
        
        // Get the friend avatar container
        const friendAvatar = friendElement.querySelector('.friend-avatar');
        if (!friendAvatar) {
            console.log(`Friend avatar for ${friendId} not found in the DOM`);
            return;
        }
        
        // Remove existing indicators
        const existingOnlineIndicator = friendAvatar.querySelector('.online-indicator');
        const existingOfflineIndicator = friendAvatar.querySelector('.offline-indicator');
        
        if (existingOnlineIndicator) existingOnlineIndicator.remove();
        if (existingOfflineIndicator) existingOfflineIndicator.remove();
        
        // Create and append the appropriate indicator
        const indicator = document.createElement('span');
        indicator.className = isOnline ? 'online-indicator' : 'offline-indicator';
        friendAvatar.appendChild(indicator);
        
        // Update the status text
        const statusText = friendElement.querySelector('.text-xs.text-slate-400');
        if (statusText) {
            statusText.textContent = isOnline ? 'Online' : 'Offline';
        }
        
        // Update the friend object in our array
        const friendIndex = this.friends.findIndex(f => f.id === friendId);
        if (friendIndex !== -1) {
            this.friends[friendIndex].isOnline = isOnline;
        }
        
        // If this is the currently active friend, also update the chat header
        if (this.currentFriendId === friendId) {
            // Update the active friend avatar status indicator
            const activeFriendAvatar = document.getElementById('activeFriendAvatar');
            if (activeFriendAvatar) {
                // Remove existing indicators
                const existingHeaderOnlineIndicator = activeFriendAvatar.querySelector('.online-indicator');
                const existingHeaderOfflineIndicator = activeFriendAvatar.querySelector('.offline-indicator');
                
                if (existingHeaderOnlineIndicator) existingHeaderOnlineIndicator.remove();
                if (existingHeaderOfflineIndicator) existingHeaderOfflineIndicator.remove();
                
                // Create and append the appropriate indicator
                const headerIndicator = document.createElement('span');
                headerIndicator.className = isOnline ? 'online-indicator' : 'offline-indicator';
                activeFriendAvatar.appendChild(headerIndicator);
            }
            
            // Update the status text in the header
            const activeFriendStatus = document.getElementById('activeFriendStatus');
            if (activeFriendStatus) {
                activeFriendStatus.textContent = isOnline ? 'Online' : 'Offline';
            }
        }
    }
    
    /**
     * Check online status for all friends using the new API endpoint
     */
    checkFriendsOnlineStatus() {
        if (!this.friends || this.friends.length === 0) {
            return;
        }
        
        console.log('Checking online status for all friends...');
        
        // Create a batch request for all friends to improve performance
        const usernames = this.friends.map(friend => friend.username);
        
        // First try the batch endpoint if available
        fetch('/api/profile/online-status-batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ usernames }),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Batch endpoint not available');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.results) {
                // Process batch results
                Object.entries(data.results).forEach(([username, status]) => {
                    // Find the friend with this username
                    const friend = this.friends.find(f => f.username === username);
                    if (friend && status.success) {
                        // Update the friend's online status
                        this.updateFriendOnlineStatus(friend.id, status.is_online);
                    }
                });
            }
        })
        .catch(error => {
            console.log('Falling back to individual status checks:', error);
            
            // Fall back to individual requests, but limit to 5 at a time to prevent overloading
            const processBatch = (friendsBatch) => {
                const promises = friendsBatch.map(friend => {
                    // Add cache buster to prevent browser caching
                    const cacheBuster = `?_=${Date.now()}`;
                    return fetch(`/api/profile/online-status/${friend.username}${cacheBuster}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update the friend's online status
                                this.updateFriendOnlineStatus(friend.id, data.is_online);
                            }
                            return true;
                        })
                        .catch(error => {
                            console.error(`Error checking online status for ${friend.username}:`, error);
                            return false;
                        });
                });
                
                return Promise.all(promises);
            };
            
            // Process friends in batches of 5
            const batchSize = 5;
            const processFriendsInBatches = async () => {
                for (let i = 0; i < this.friends.length; i += batchSize) {
                    const batch = this.friends.slice(i, i + batchSize);
                    await processBatch(batch);
                    
                    // Add a small delay between batches to prevent overloading
                    if (i + batchSize < this.friends.length) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
            };
            
            processFriendsInBatches();
        });
    }
    
    /**
     * Update the current user's activity status
     */
    updateUserActivity() {
        fetch('/api/profile/update-activity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                section: 'friends'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Activity status updated successfully');
            }
        })
        .catch(error => {
            console.error('Error updating activity status:', error);
        });
    }
    
    /**
     * Update existing messages with new theme
     */
    updateExistingMessagesTheme() {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        // Update self messages
        const selfMessages = messagesContainer.querySelectorAll('.message.self');
        selfMessages.forEach(message => {
            // Remove all theme classes
            message.className = message.className.replace(/theme-\w+/g, '');

            // For messages sent by the current user, always apply the user's own theme
            message.classList.add(`theme-${this.userTheme}`);
            console.log(`Updated self message with user's theme: ${this.userTheme}`);
        });

        // Update the message input field with the user's theme when in a group chat
        // or when not in any chat (default state)
        if (!this.currentFriendId || this.isGroupChat) {
            this.updateMessageInputTheme(this.userTheme);
        } else if (this.currentFriendId && this.friendThemes[this.currentFriendId]) {
            // If we're in a friend chat, use the friend's theme for the input field
            this.updateMessageInputTheme(this.friendThemes[this.currentFriendId]);
        }
    }

    /**
     * Update the message input field with a specific theme
     * @param {string} theme - The theme to apply
     */
    updateMessageInputTheme(theme) {
        const messageInput = document.getElementById('messageInput');
        const messageInputContainer = document.querySelector('.message-input-container');
        const friendHeader = document.querySelector('.friend-header');

        if (messageInput) {
            // Remove all theme classes from the input field
            messageInput.className = messageInput.className.replace(/theme-input-\w+/g, '');

            // Add the new theme class
            messageInput.classList.add(`theme-input-${theme}`);
            console.log(`Updated message input with theme: ${theme}`);

            // Apply theme-specific styles
            this.applyInputThemeStyles(messageInput, theme);
        }

        if (messageInputContainer) {
            // Remove all theme classes from the container
            messageInputContainer.className = messageInputContainer.className.replace(/theme-container-\w+/g, '');

            // Add the new theme class
            messageInputContainer.classList.add(`theme-container-${theme}`);
        }

        // Update the friend header with the theme
        if (friendHeader) {
            // Remove all theme classes from the header
            friendHeader.className = friendHeader.className.replace(/theme-header-\w+/g, '');

            // Add the new theme class
            friendHeader.classList.add(`theme-header-${theme}`);
            console.log(`Updated friend header with theme: ${theme}`);
        }
    }

    /**
     * Apply theme-specific styles to the input field
     * @param {HTMLElement} inputElement - The input element
     * @param {string} theme - The theme name
     */
    applyInputThemeStyles(inputElement, theme) {
        // Define theme-specific styles
        const themeStyles = {
            'blue': {
                borderColor: 'rgba(37, 99, 235, 0.5)',
                focusRing: 'rgba(37, 99, 235, 0.5)'
            },
            'green': {
                borderColor: 'rgba(16, 185, 129, 0.5)',
                focusRing: 'rgba(16, 185, 129, 0.5)'
            },
            'purple': {
                borderColor: 'rgba(139, 92, 246, 0.5)',
                focusRing: 'rgba(139, 92, 246, 0.5)'
            },
            'pink': {
                borderColor: 'rgba(236, 72, 153, 0.5)',
                focusRing: 'rgba(236, 72, 153, 0.5)'
            },
            'orange': {
                borderColor: 'rgba(249, 115, 22, 0.5)',
                focusRing: 'rgba(249, 115, 22, 0.5)'
            },
            'red': {
                borderColor: 'rgba(239, 68, 68, 0.5)',
                focusRing: 'rgba(239, 68, 68, 0.5)'
            },
            'yellow': {
                borderColor: 'rgba(234, 179, 8, 0.5)',
                focusRing: 'rgba(234, 179, 8, 0.5)'
            },
            'amber': {
                borderColor: 'rgba(245, 158, 11, 0.5)',
                focusRing: 'rgba(245, 158, 11, 0.5)'
            },
            'teal': {
                borderColor: 'rgba(20, 184, 166, 0.5)',
                focusRing: 'rgba(20, 184, 166, 0.5)'
            },
            'cyan': {
                borderColor: 'rgba(6, 182, 212, 0.5)',
                focusRing: 'rgba(6, 182, 212, 0.5)'
            },
            'indigo': {
                borderColor: 'rgba(79, 70, 229, 0.5)',
                focusRing: 'rgba(79, 70, 229, 0.5)'
            }
        };

        // Get the styles for the specified theme, or use blue as default
        const styles = themeStyles[theme] || themeStyles['blue'];

        // Apply the styles
        inputElement.style.borderColor = styles.borderColor;

        // Update the focus ring color using CSS variables
        document.documentElement.style.setProperty('--focus-ring-color-' + theme, styles.focusRing);
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for view changes to disconnect socket when leaving friends panel
        document.addEventListener('view-changed', (event) => {
            const { view, previousView } = event.detail;
            
            // If we're navigating away from the friends panel
            if (previousView === 'friends' && view !== 'friends') {
                console.log('Leaving friends panel, disconnecting socket');
                // Set user as offline
                this.isOnline = false;
                // Send offline status before disconnecting
                if (this.socket && this.socket.connected) {
                    this.socket.emit('user_status_update', { status: 'offline' });
                    this.socket.disconnect();
                }
            }
            
            // If we're navigating back to the friends panel
            if (view === 'friends' && previousView !== 'friends') {
                console.log('Returning to friends panel, reconnecting socket if needed');
                // Set user as online
                this.isOnline = true;
                // Reconnect the socket if it exists but is disconnected
                if (this.socket && !this.socket.connected) {
                    this.socket.connect();
                } else if (this.socket && this.socket.connected) {
                    // If already connected, just send the online status
                    this.socket.emit('user_status_update', { status: 'online' });
                }
            }
        });
        
        // Friends Menu button
        const friendsMenuBtn = document.getElementById('friendsMenuBtn');
        if (friendsMenuBtn) {
            friendsMenuBtn.addEventListener('click', () => this.showFriendsMenuModal());
        }

        // Modal Add Friend button
        const modalAddFriendBtn = document.getElementById('modalAddFriendBtn');
        if (modalAddFriendBtn) {
            modalAddFriendBtn.addEventListener('click', () => {
                this.closeFriendsMenuModal();
                this.showAddFriendModal();
            });
        }

        // Modal Create Group Chat button
        const modalCreateGroupChatBtn = document.getElementById('modalCreateGroupChatBtn');
        if (modalCreateGroupChatBtn) {
            modalCreateGroupChatBtn.addEventListener('click', () => {
                this.closeFriendsMenuModal();
                this.showCreateGroupChatModal();
            });
        }

        // Modal Friend Requests button
        const modalPendingRequestsBtn = document.getElementById('modalPendingRequestsBtn');
        if (modalPendingRequestsBtn) {
            modalPendingRequestsBtn.addEventListener('click', () => {
                this.closeFriendsMenuModal();
                this.showFriendRequestsModal();
            });
        }

        // Modal Chat Theme button
        const modalChatThemeBtn = document.getElementById('modalChatThemeBtn');
        if (modalChatThemeBtn) {
            modalChatThemeBtn.addEventListener('click', () => {
                this.closeFriendsMenuModal();
                this.showChatThemeModal();
            });
        }



        // Create Group Chat Submit button
        const createGroupChatSubmitBtn = document.getElementById('createGroupChatSubmitBtn');
        if (createGroupChatSubmitBtn) {
            createGroupChatSubmitBtn.addEventListener('click', () => this.createGroupChat());
        }

        // Friend search input
        const friendSearchInput = document.getElementById('friendSearchInput');
        if (friendSearchInput) {
            friendSearchInput.addEventListener('input', () => this.filterFriends());
        }

        // Toggle Search button (clear search)
        const toggleSearchBtn = document.getElementById('toggleSearchBtn');
        if (toggleSearchBtn) {
            toggleSearchBtn.addEventListener('click', () => {
                const friendSearchInput = document.getElementById('friendSearchInput');
                if (friendSearchInput) {
                    friendSearchInput.value = '';
                    this.filterFriends();
                }
            });
        }

        // Toggle Friend List button
        const toggleFriendListBtn = document.getElementById('toggleFriendListBtn');
        if (toggleFriendListBtn) {
            toggleFriendListBtn.addEventListener('click', () => {
                this.togglePanel('friendListPanel', toggleFriendListBtn);
            });
        }

        // Search users button
        const searchUsersBtn = document.getElementById('searchUsersBtn');
        if (searchUsersBtn) {
            searchUsersBtn.addEventListener('click', () => this.searchUsers());
        }

        // User search input (search on enter and as user types)
        const userSearchInput = document.getElementById('userSearchInput');
        if (userSearchInput) {
            // Search on Enter key
            userSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchUsers();
                }
            });

            // Search as user types (with debounce)
            let searchTimeout = null;
            userSearchInput.addEventListener('input', () => {
                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                const query = userSearchInput.value.trim();

                // Hide search results if query is too short
                if (query.length < 3) {
                    const searchResults = document.getElementById('searchResults');
                    if (searchResults) {
                        searchResults.innerHTML = `
                            <div class="empty-state py-8">
                                <div class="empty-state-icon">
                                    <i data-lucide="users" class="h-6 w-6 text-slate-500"></i>
                                </div>
                                <p class="empty-state-text text-sm text-slate-400 mt-3">Search for users by their username to add them as friends</p>
                            </div>
                        `;

                        // Initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: searchResults
                            });
                        }
                    }
                    return;
                }

                // Set a timeout to avoid too many requests
                searchTimeout = setTimeout(() => {
                    this.searchUsers();
                }, 300);
            });
        }

        // Message input and send button
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        if (messageInput && sendButton) {
            // Send message on button click
            sendButton.addEventListener('click', () => this.sendMessage());

            // Send message on Enter key (but allow Shift+Enter for new line)
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Auto-resize textarea based on content and handle typing events
            messageInput.addEventListener('input', () => {
                this.autoResizeMessageInput(messageInput);

                // Handle typing events for chat
                if (messageInput.value.length > 5) {
                    this.handleTypingEvent(true);
                }
            });

            // Initial resize
            this.autoResizeMessageInput(messageInput);
        }

        // Image upload button
        const imageBtn = document.getElementById('imageBtn');
        const imageFileInput = document.getElementById('imageFileInput');

        if (imageBtn && imageFileInput) {
            // Open file dialog when image button is clicked
            imageBtn.addEventListener('click', () => {
                imageFileInput.click();
            });

            // Handle file selection
            imageFileInput.addEventListener('change', (e) => {
                this.handleImageSelection(e.target.files);
            });

            // Add global remove button event listener
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            if (imagePreviewContainer) {
                // Add global remove button
                const globalRemoveBtn = document.createElement('button');
                globalRemoveBtn.className = 'global-remove-image-btn';
                globalRemoveBtn.innerHTML = '<i data-lucide="x" class="h-4 w-4"></i>';
                globalRemoveBtn.addEventListener('click', () => {
                    this.clearAllImages();
                });
                imagePreviewContainer.appendChild(globalRemoveBtn);

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: globalRemoveBtn
                    });
                }
            }
        }

        // Remove friend button
        const removeFriendBtn = document.getElementById('removeFriendBtn');
        if (removeFriendBtn) {
            removeFriendBtn.addEventListener('click', () => {
                if (this.currentFriendId) {
                    this.showRemoveFriendModal(this.currentFriendId);
                }
            });
        }

        // Confirm remove friend button
        const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');
        if (confirmRemoveBtn) {
            confirmRemoveBtn.addEventListener('click', () => {
                this.removeFriend();
                this.closeModal('removeFriendModal');
            });
        }

        // Cancel remove friend button
        const cancelRemoveBtn = document.getElementById('cancelRemoveBtn');
        if (cancelRemoveBtn) {
            cancelRemoveBtn.addEventListener('click', () => {
                this.closeModal('removeFriendModal');
            });
        }

        // Confirm delete group chat button
        const confirmDeleteGroupBtn = document.getElementById('confirmDeleteGroupBtn');
        if (confirmDeleteGroupBtn) {
            confirmDeleteGroupBtn.addEventListener('click', () => {
                this.deleteGroupChat();
                this.closeModal('deleteGroupChatModal');
            });
        }

        // Cancel delete group chat button
        const cancelDeleteGroupBtn = document.getElementById('cancelDeleteGroupBtn');
        if (cancelDeleteGroupBtn) {
            cancelDeleteGroupBtn.addEventListener('click', () => {
                this.closeModal('deleteGroupChatModal');
            });
        }

        // Invite to live button
        const inviteToLiveBtn = document.getElementById('inviteToLiveBtn');
        if (inviteToLiveBtn) {
            inviteToLiveBtn.addEventListener('click', () => {
                if (this.currentFriendId) {
                    this.showInviteToLiveModal(this.currentFriendId);
                }
            });
        }

        // Confirm invite to live button
        const confirmInviteBtn = document.getElementById('confirmInviteBtn');
        if (confirmInviteBtn) {
            confirmInviteBtn.addEventListener('click', () => {
                this.inviteToLive();
                this.closeModal('inviteToLiveModal');
            });
        }

        // Cancel invite to live button
        const cancelInviteBtn = document.getElementById('cancelInviteBtn');
        if (cancelInviteBtn) {
            cancelInviteBtn.addEventListener('click', () => {
                this.closeModal('inviteToLiveModal');
            });
        }

        // Close modal buttons
        document.querySelectorAll('.close-modal').forEach(button => {
            button.addEventListener('click', () => {
                const modal = button.closest('.modal');
                if (modal) {
                    this.closeModal(modal.id);
                }
            });
        });

        // Tab switching in friend requests modal
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.getAttribute('data-tab');
                if (!tabName) return;

                // Update active tab
                document.querySelectorAll('.tab').forEach(t => {
                    t.classList.remove('active');
                    const tabIndicator = t.querySelector('span:last-child');
                    if (tabIndicator) {
                        tabIndicator.classList.remove('scale-x-100');
                        tabIndicator.classList.add('scale-x-0');
                    }
                    t.classList.add('text-slate-400');
                    t.classList.remove('text-slate-200');
                });

                tab.classList.add('active', 'text-slate-200');
                tab.classList.remove('text-slate-400');
                const tabIndicator = tab.querySelector('span:last-child');
                if (tabIndicator) {
                    tabIndicator.classList.add('scale-x-100');
                    tabIndicator.classList.remove('scale-x-0');
                }

                // Show corresponding tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });

                const tabContent = document.getElementById(`${tabName}RequestsTab`);
                if (tabContent) {
                    tabContent.classList.remove('hidden');
                }
            });
        });
    }

    /**
     * Initialize Socket.IO connection
     */
    initSocketIO() {
        console.log('Initializing Socket.IO connection...');

        // Check if Socket.IO is available
        if (typeof io === 'undefined') {
            console.error('Socket.IO not available, loading it dynamically');

            // Try to load Socket.IO dynamically
            const script = document.createElement('script');
            script.src = 'https://cdn.socket.io/4.6.0/socket.io.min.js';
            script.async = true;
            script.onload = () => {
                console.log('Socket.IO loaded dynamically');
                this.initSocketConnection();
            };
            script.onerror = () => {
                console.error('Failed to load Socket.IO dynamically');
            };
            document.head.appendChild(script);
            return;
        }

        this.initSocketConnection();
    }

    /**
     * Initialize the actual Socket.IO connection
     */
    initSocketConnection() {
        try {
            // Initialize Socket.IO connection with optimized settings for reliability
            this.socket = io(window.location.origin, {
                transports: ['polling', 'websocket'], // Try polling first for better compatibility
                reconnection: true,
                reconnectionAttempts: 10, // More attempts for better reliability
                reconnectionDelay: 1000, // Slightly increased reconnection delay
                reconnectionDelayMax: 5000, // Increased max delay for better stability
                timeout: 20000, // Increased timeout for better reliability
                forceNew: true, // Create a new connection to avoid conflicts
                autoConnect: true, // Connect immediately
                upgrade: true, // Allow transport upgrades
                rememberUpgrade: true, // Remember successful upgrades
                pingInterval: 5000, // Match server ping interval
                pingTimeout: 10000, // Match server ping timeout
                maxHttpBufferSize: 50 * 1024 * 1024 // 50MB buffer for large messages with images
            });
            console.log('Socket.IO connection initialized with balanced settings for reliability and speed');

            // Set up Socket.IO event handlers
            this.socket.on('connect', () => {
                console.log('Socket.IO connected successfully');

                // Join the friends namespace immediately
                this.socket.emit('join_friends_namespace', {});

                // If we already have a chat open, join that room immediately
                if (this.currentChatId) {
                    this.joinChatRoom(this.currentChatId);
                }
                
                // Send online status only if the friends panel is open
                if (this.isOnline) {
                    console.log('Sending online status to server');
                    this.socket.emit('user_status_update', { status: 'online' });
                }
            });
        } catch (error) {
            console.error('Error initializing Socket.IO connection:', error);
            // Try to reconnect with fallback settings if optimized settings fail
            try {
                console.log('Attempting fallback Socket.IO connection with more compatible settings');
                this.socket = io(window.location.origin, {
                    transports: ['polling'], // Use only polling as a last resort
                    reconnection: true,
                    reconnectionAttempts: 10,
                    reconnectionDelay: 1000,
                    timeout: 30000, // Even longer timeout for extreme reliability
                    forceNew: true, // Create a new connection
                    autoConnect: true,
                    pingInterval: 5000, // Match server ping interval
                    pingTimeout: 10000, // Match server ping timeout
                    maxHttpBufferSize: 50 * 1024 * 1024 // 50MB buffer for large messages with images
                });
            } catch (fallbackError) {
                console.error('Fallback Socket.IO connection also failed:', fallbackError);
            }
            return;
        }

        // Set up all Socket.IO event handlers - BLAZING FAST IMPLEMENTATION
        this.socket.on('friend_new_message', (data) => {
            console.log('Received friend_new_message event:', data);

            // Fast path processing for message reception
            if (this.processingSocketMessage || !data || data.chat_id !== this.currentChatId) {
                return; // Skip processing if not relevant to current chat
            }

            try {
                // Extract the message object from the data
                const messageData = data.message || {};

                // Skip empty messages with fast check - but allow messages with only images
                if (!messageData.content && (!messageData.images || messageData.images.length === 0)) {
                    console.log('Skipping empty message with no content or images');
                    return;
                }

                // Get current user ID (cached if possible)
                const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');

                // Check if this is a message from the current user
                const isFromCurrentUser = currentUserId && messageData.user_id === currentUserId;

                // Mark messages from the current user
                if (isFromCurrentUser) {
                    // Mark as from self
                    messageData.is_self = true;
                }

                // Add timestamp if missing
                if (!messageData.timestamp) {
                    messageData.timestamp = new Date().toISOString();
                }

                console.log('Processing message with images:', messageData.images ? messageData.images.length : 0);

                // Append the message and scroll in one operation for better performance
                this.appendMessage(messageData);
                requestAnimationFrame(() => this.scrollToBottom());
            } catch (error) {
                console.error('Error processing message:', error);
            }
        });

        // Handle group chat message events - BLAZING FAST IMPLEMENTATION
        this.socket.on('group_new_message', (data) => {
            console.log('Received group_new_message event:', data);

            // Fast path processing for group message reception
            if (this.processingSocketMessage || !data || data.chat_id !== this.currentGroupChatId) {
                return; // Skip processing if not relevant to current chat
            }

            try {
                // Extract the message object from the data
                const messageData = data.message || {};

                // Skip empty messages with fast check - but allow messages with only images
                if (!messageData.content && (!messageData.images || messageData.images.length === 0)) {
                    console.log('Skipping empty group message with no content or images');
                    console.log('Message data:', messageData);
                    return;
                }

                // Get current user ID (cached if possible)
                const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');

                // Check if this is a message from the current user
                const isFromCurrentUser = currentUserId && messageData.user_id === currentUserId;

                // Skip messages from the current user to prevent duplication
                // The message is already displayed when sent via the temporary message
                if (isFromCurrentUser) {
                    console.log('Skipping message from current user to prevent duplication');
                    return;
                }

                console.log('Processing group message with content:', messageData.content, 'and images:', messageData.images);

                // Add timestamp if missing
                if (!messageData.timestamp) {
                    messageData.timestamp = new Date().toISOString();
                }

                console.log('Processing group message with images:', messageData.images ? messageData.images.length : 0);

                // Append the message and scroll in one operation for better performance
                this.appendMessage(messageData);
                requestAnimationFrame(() => this.scrollToBottom());
            } catch (error) {
                console.error('Error processing group message:', error);
            }
        });

        // Handle group chat user joined events
        this.socket.on('group_chat_user_joined', (data) => {
            console.log('User joined group chat:', data);

            // Only process if this is the current group chat
            if (data.chat_id === this.currentGroupChatId) {
                // Create a system message
                const systemMessage = {
                    is_system: true,
                    message_type: 'info',
                    content: `${data.user_display_name || data.username} joined the group chat`,
                    timestamp: new Date().toISOString()
                };

                // Add the message to the chat
                this.appendMessage(systemMessage);
                this.scrollToBottom();
            }
        });

        // Handle group chat user left events
        this.socket.on('group_chat_user_left', (data) => {
            console.log('User left group chat:', data);

            // Only process if this is the current group chat
            if (data.chat_id === this.currentGroupChatId) {
                // Create a system message
                const systemMessage = {
                    is_system: true,
                    message_type: 'info',
                    content: `${data.user_display_name || data.username} left the group chat`,
                    timestamp: new Date().toISOString()
                };

                // Add the message to the chat
                this.appendMessage(systemMessage);
                this.scrollToBottom();
            }
        });

        // Handle group chat invitation events
        this.socket.on('group_chat_invitation', (data) => {
            console.log('Group chat invitation received:', data);

            // Show a notification
            this.showNotification(`${data.creator_display_name || data.creator_name} added you to group chat "${data.name}"`, 'info');

            // Reload group chats
            this.loadGroupChats();
        });

        // Handle group chat created events
        this.socket.on('group_chat_created', (data) => {
            console.log('Group chat created:', data);

            if (data.success) {
                // Show a notification
                this.showNotification('Group chat created successfully!', 'success');

                // Reload group chats
                this.loadGroupChats();
            } else {
                // Show error notification
                this.showNotification(data.message || 'Failed to create group chat.', 'error');
            }
        });

        // Handle group message sent confirmation
        this.socket.on('group_message_sent', (data) => {
            console.log('Received group_message_sent confirmation:', data);

            // Update the temporary message with the real message data if available
            if (data && data.success && data.message) {
                const tempId = data.message.temp_id || data.temp_id;
                if (tempId) {
                    this.updateTempMessage(tempId, data.message);
                }
            }
        });

        this.socket.on('error', (data) => {
            console.error('Socket error:', data);
            this.showNotification(data.message || 'An error occurred', 'error');
        });

        this.socket.on('connect_error', (error) => {
            console.error('Socket.IO connection error:', error);
        });

        this.socket.on('disconnect', (reason) => {
            console.log(`Socket.IO disconnected: ${reason}`);
        });
        
        // Handle friend online status updates
        this.socket.on('friend_status_update', (data) => {
            console.log('Friend status update received:', data);
            if (data && data.userId && data.status) {
                this.updateFriendOnlineStatus(data.userId, data.status === 'online');
            }
        });

        this.socket.on('reconnect', (attemptNumber) => {
            console.log(`Socket.IO reconnected after ${attemptNumber} attempts`);

            // If we have a chat open, rejoin that room
            if (this.currentChatId) {
                console.log('Rejoining chat room after reconnect:', this.currentChatId);
                this.joinChatRoom(this.currentChatId);
            }
        });

        this.socket.on('friends_namespace_joined', (data) => {
            console.log('Joined friends namespace:', data);
        });

        this.socket.on('chat_joined', (data) => {
            console.log('Chat joined:', data);
        });

        // Listen for group chat created event
        this.socket.on('group_chat_created', (data) => {
            console.log('Group chat created:', data);
            this.handleGroupChatCreated(data);
        });

        // Listen for group chat deleted event
        this.socket.on('group_chat_deleted', (data) => {
            console.log('Group chat deleted:', data);
            this.handleGroupChatDeleted(data);
        });

        // Listen for group chat profile picture updated event
        this.socket.on('group_chat_profile_picture_updated', (data) => {
            console.log('Group chat profile picture updated:', data);

            // Update the UI with the new profile picture
            if (data.chat_id && data.profile_picture) {
                // Force a cache-busting parameter to ensure the image is reloaded
                const cachedProfilePictureUrl = `${data.profile_picture}${data.profile_picture.includes('?') ? '&' : '?'}cb=${Date.now()}`;

                // Update the UI with the new profile picture
                this.updateGroupChatUIWithNewProfilePicture(data.chat_id, cachedProfilePictureUrl);

                // Update the cached group chat data
                if (this.groupChats) {
                    const chatIndex = this.groupChats.findIndex(c => c.chat_id === data.chat_id);
                    if (chatIndex !== -1) {
                        this.groupChats[chatIndex].profile_picture = data.profile_picture;
                    }
                }

                // Show a notification if the update was done by someone else
                const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
                if (data.updated_by && data.updated_by.id !== currentUserId) {
                    this.showNotification(`${data.updated_by.display_name || data.updated_by.username} updated the group profile picture`, 'info');
                }
            }
        });

        // Handle live chat invitations
        this.socket.on('live_invitation', (data) => {
            console.log('Live invitation received:', data);

            // Extract data from the invitation
            const fromUser = data.from_user || {};
            const roomId = data.room_id;

            if (!roomId) {
                console.error('Invalid live invitation: missing room ID');
                return;
            }

            // Create the full room URL with origin
            const roomUrl = `${window.location.origin}/live/room/${roomId}`;
            console.log('Created room URL for invitation:', roomUrl);

            // Show a notification about the invitation
            this.showNotification(`${fromUser.display_name || fromUser.username} invited you to a live chat!`, 'info', 10000);

            // Find the friend who sent the invitation
            const friend = this.friends.find(f => f.id === fromUser.id);
            if (friend && friend.chat_id) {
                // Create a system message with the invitation
                const systemMessage = {
                    is_system: true,
                    message_type: 'invite',
                    content: `${fromUser.display_name || fromUser.username} has invited you to a live chat! Click the button below to join the conversation.`,
                    timestamp: new Date().toISOString(),
                    room_id: roomId,
                    room_url: roomUrl
                };

                console.log('Created system message for invitation:', systemMessage);

                // If this chat is currently open, immediately add the invitation message
                if (this.currentChatId === friend.chat_id) {
                    console.log('Chat is open, adding invitation message immediately');

                    // Add the message to the chat
                    this.appendMessage(systemMessage);
                    this.scrollToBottom();
                } else {
                    console.log('Chat is not open, sending message to server to be stored');

                    // Send a system message to the chat via API
                    fetch(`/api/friends/chat/${friend.chat_id}/messages`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: systemMessage.content,
                            is_system: true,
                            message_type: 'invite',
                            room_id: roomId,
                            room_url: roomUrl
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('System message sent and stored:', data);
                    })
                    .catch(error => {
                        console.error('Error sending system message:', error);
                    });

                    // Mark this chat for refresh when opened
                    friend.has_new_messages = true;

                    // Update the UI to show there are new messages
                    const friendItem = document.querySelector(`.friend-item[data-chat-id="${friend.chat_id}"]`);
                    if (friendItem) {
                        friendItem.classList.add('has-new-messages');
                    }
                }
            }
        });

        // Handle typing events from friends
        this.socket.on('friend_typing', (data) => {
            console.log('Friend typing event received:', data);

            // Only show typing indicator if we're in this chat
            if (data.chat_id === this.currentChatId) {
                if (data.is_typing) {
                    // Show typing indicator
                    this.showTypingIndicator({
                        display_name: data.display_name,
                        username: data.username
                    });

                    // Set a timeout to hide the indicator after 2 seconds
                    // This is a fallback in case we don't receive the "stopped typing" event
                    if (this.friendTypingTimeout) {
                        clearTimeout(this.friendTypingTimeout);
                    }

                    this.friendTypingTimeout = setTimeout(() => {
                        const typingIndicator = document.getElementById('typingIndicator');
                        if (typingIndicator) {
                            typingIndicator.remove();
                        }
                    }, 2000);
                } else {
                    // Hide typing indicator
                    const typingIndicator = document.getElementById('typingIndicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }

                    // Clear the timeout
                    if (this.friendTypingTimeout) {
                        clearTimeout(this.friendTypingTimeout);
                    }
                }
            }
        });

        // Handle typing events from group chat members
        this.socket.on('group_user_typing', (data) => {
            console.log('Group chat typing event received:', data);

            // Only show typing indicator if we're in this group chat
            if (data.chat_id === this.currentGroupChatId) {
                if (data.typing) {
                    // Show typing indicator
                    this.showTypingIndicator({
                        display_name: data.display_name,
                        username: data.username
                    });

                    // Set a timeout to hide the indicator after 2 seconds
                    // This is a fallback in case we don't receive the "stopped typing" event
                    if (this.groupTypingTimeout) {
                        clearTimeout(this.groupTypingTimeout);
                    }

                    this.groupTypingTimeout = setTimeout(() => {
                        const typingIndicator = document.getElementById('typingIndicator');
                        if (typingIndicator) {
                            typingIndicator.remove();
                        }
                    }, 2000);
                } else {
                    // Hide typing indicator
                    const typingIndicator = document.getElementById('typingIndicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }

                    // Clear the timeout
                    if (this.groupTypingTimeout) {
                        clearTimeout(this.groupTypingTimeout);
                    }
                }
            }
        });

        // Handle new message notifications (when not in the chat)
        this.socket.on('friend_new_message_notification', (data) => {
            console.log('New message notification received:', data);

            const chatId = data.chat_id;
            const fromUser = data.from_user || {};

            // If we're not in this chat, mark it for refresh
            if (chatId && chatId !== this.currentChatId) {
                // Find the friend associated with this chat
                const friend = this.friends.find(f => f.chat_id === chatId);
                if (friend) {
                    // Mark this chat for refresh when opened
                    friend.has_new_messages = true;

                    // Show a notification
                    this.showNotification(`New message from ${fromUser.display_name || fromUser.username}`, 'info');

                    // Update the UI to show there are new messages
                    const friendItem = document.querySelector(`.friend-item[data-chat-id="${chatId}"]`);
                    if (friendItem) {
                        friendItem.classList.add('has-new-messages');
                    }

                    // Flash the favicon if the tab is not active
                    if (window.notificationManager && document.visibilityState !== 'visible') {
                        window.notificationManager.startFlashing('message');
                    }
                }
            }
        });

        // Handle friend request received events
        this.socket.on('friend_request_received', (data) => {
            console.log('Friend request received:', data);

            // Show a notification
            const displayName = data.display_name || data.username;
            this.showNotification(`Friend request from ${displayName}`, 'info');

            // Mark the requests as needing a refresh
            this.requestsLoaded = false;

            // Update the badge on the friend requests button
            this.updateFriendRequestsBadge();

            // Flash the favicon if the tab is not active
            if (window.notificationManager && document.visibilityState !== 'visible') {
                window.notificationManager.startFlashing('request');
            }
        });

        // Handle friend request accepted events
        this.socket.on('friend_request_accepted', (data) => {
            console.log('Friend request accepted:', data);

            // Show a notification
            const displayName = data.display_name || data.username;
            this.showNotification(`${displayName} accepted your friend request`, 'success');

            // Mark the requests as needing a refresh
            this.requestsLoaded = false;

            // Reload friends list
            this.loadFriends();
        });

        // Handle group chat renamed events
        this.socket.on('group_chat_renamed', (data) => {
            console.log('Group chat renamed event received:', data);

            // Update the UI with the new name
            if (data.chat_id && data.name) {
                // Update the group chat name in the UI
                this.updateGroupChatName(data.chat_id, data.name);

                // Get current user ID
                const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');

                // Show a notification if the update was done by someone else
                if (data.updated_by && data.updated_by.id !== currentUserId) {
                    this.showNotification(`${data.updated_by.display_name || data.updated_by.username} renamed the group chat to "${data.name}"`, 'info');
                }

                // Add a system message if this is the current group chat
                if (data.chat_id === this.currentGroupChatId) {
                    const updaterName = data.updated_by ?
                        (data.updated_by.display_name || data.updated_by.username) :
                        'Someone';

                    // Create a system message
                    const systemMessage = {
                        is_system: true,
                        message_type: 'info',
                        content: `${updaterName} renamed the group chat to "${data.name}"`,
                        timestamp: new Date().toISOString()
                    };

                    // Add the message to the chat
                    this.appendMessage(systemMessage);
                    this.scrollToBottom();
                }
            }
        });
    }

    /**
     * Load friends list
     */
    loadFriends() {
        console.log('Loading friends...');
        this.isLoading = true;

        // Show loading placeholders
        if (typeof window.showFriendsLoading === 'function') {
            window.showFriendsLoading();
        }

        // Clear the loading indicator immediately
        const friendList = document.getElementById('friendList');
        if (friendList) {
            const loadingIndicator = friendList.querySelector('.flex.justify-center.items-center.py-4');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            // Remove any duplicate FRIENDS headers
            const existingFriendsHeaders = friendList.querySelectorAll('.text-xs.font-medium.text-slate-400:not(#groupChatHeader)');
            existingFriendsHeaders.forEach(header => {
                if (header.textContent === 'FRIENDS' && header.id !== 'friendsHeader') {
                    header.remove();
                }
            });
        }

        fetch('/api/friends/friends')
            .then(response => response.json())
            .then(friends => {
                console.log('Loaded friends:', friends);
                this.friends = friends;

                // Mark friends as loaded
                if (typeof window.hideFriendsLoading === 'function') {
                    window.hideFriendsLoading('friends');
                }

                this.displayFriends(friends);
                this.isLoading = false;

                // Dispatch a custom event to indicate friends are loaded
                document.dispatchEvent(new CustomEvent('friends:loaded'));
                
                // Check if there's a last opened chat and automatically open it
                const lastOpenedChatId = localStorage.getItem('lastOpenedChatId');
                const lastOpenedFriendId = localStorage.getItem('lastOpenedFriendId');
                
                if (lastOpenedChatId && lastOpenedFriendId) {
                    // Find the friend with the matching ID
                    const lastFriend = friends.find(f => f.id === lastOpenedFriendId);
                    
                    if (lastFriend) {
                        console.log('Auto-opening last chat:', lastFriend);
                        this.openChat(lastFriend);
                    }
                }
            })
            .catch(error => {
                console.error('Error loading friends:', error);
                this.isLoading = false;
                this.showNotification('Error loading friends. Please try again.', 'error');

                // Mark friends as loaded even on error
                if (typeof window.hideFriendsLoading === 'function') {
                    window.hideFriendsLoading('friends');
                }

                // Show error message in friend list if it's empty
                if (friendList && !friendList.hasChildNodes()) {
                    friendList.innerHTML = `
                        <div class="flex justify-center items-center py-4">
                            <div class="flex items-center text-red-400">
                                <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                                <span class="text-sm">Error loading friends. Please try again.</span>
                            </div>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: friendList
                        });
                    }
                }
            });
    }

    /**
     * Load group chats
     */
    loadGroupChats() {
        console.log('Loading group chats...');

        // Show loading placeholders if not already shown
        if (typeof window.showFriendsLoading === 'function' && !this.isLoading) {
            window.showFriendsLoading();
        }

        fetch('/api/friends/group-chats')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(groupChats => {
                console.log('Loaded group chats:', groupChats);

                // Log profile pictures for debugging
                groupChats.forEach(chat => {
                    console.log(`Group chat ${chat.chat_id} profile picture:`, chat.profile_picture);
                });

                // For each group chat that doesn't have a profile picture loaded,
                // fetch the full details to get the profile picture
                const fetchPromises = groupChats.map(chat => {
                    if (!chat.profile_picture) {
                        // Fetch the full group chat details to get the profile picture
                        return fetch(`/api/friends/group-chat/${chat.chat_id}`)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(fullChat => {
                                if (fullChat.profile_picture) {
                                    console.log(`Updated profile picture for group chat ${chat.chat_id}:`, fullChat.profile_picture);
                                    chat.profile_picture = fullChat.profile_picture;
                                }
                                return chat;
                            })
                            .catch(error => {
                                console.error(`Error fetching details for group chat ${chat.chat_id}:`, error);
                                return chat;
                            });
                    }
                    return Promise.resolve(chat);
                });

                // Wait for all fetch operations to complete
                return Promise.all(fetchPromises).then(updatedChats => {
                    // Store the updated group chats in the instance
                    this.groupChats = updatedChats;

                    // Mark group chats as loaded
                    if (typeof window.hideFriendsLoading === 'function') {
                        window.hideFriendsLoading('group_chats');
                    }

                    // Display the group chats in the UI
                    this.displayGroupChats(updatedChats);

                    // Dispatch a custom event to indicate group chats are loaded
                    document.dispatchEvent(new CustomEvent('group_chats:loaded'));
                    
                    // Check if there's a last opened group chat and automatically open it
                    // Only do this if we don't already have a friend chat open
                    const lastOpenedChatId = localStorage.getItem('lastOpenedChatId');
                    const lastOpenedGroupChatId = localStorage.getItem('lastOpenedGroupChatId');
                    const lastOpenedFriendId = localStorage.getItem('lastOpenedFriendId');
                    
                    // If we have a group chat ID and no friend chat is already open
                    if (lastOpenedGroupChatId && !lastOpenedFriendId && !this.currentFriendId) {
                        // Find the group chat with the matching ID
                        const lastGroupChat = updatedChats.find(c => c.chat_id === lastOpenedGroupChatId);
                        
                        if (lastGroupChat) {
                            console.log('Auto-opening last group chat:', lastGroupChat);
                            this.openGroupChat(lastGroupChat);
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading group chats:', error);
                this.showNotification('Error loading group chats. Please try again.', 'error');

                // Mark group chats as loaded even on error
                if (typeof window.hideFriendsLoading === 'function') {
                    window.hideFriendsLoading('group_chats');
                }
            });
    }

    /**
     * Display group chats in the sidebar
     */
    displayGroupChats(groupChats) {
        const friendList = document.getElementById('friendList');
        if (!friendList) return;

        // Remove existing group chat section if it exists
        const existingGroupChatSection = document.getElementById('groupChatSection');
        if (existingGroupChatSection) {
            existingGroupChatSection.remove();
        }

        // Remove existing group chat header if it exists
        const existingGroupChatHeader = document.getElementById('groupChatHeader');
        if (existingGroupChatHeader) {
            existingGroupChatHeader.remove();
        }

        // Remove existing separator after group chats if it exists
        const existingGroupChatSeparator = document.getElementById('groupChatSeparator');
        if (existingGroupChatSeparator) {
            existingGroupChatSeparator.remove();
        }

        // Remove existing friends header if it exists
        const existingFriendsHeader = document.getElementById('friendsHeader');
        if (existingFriendsHeader) {
            existingFriendsHeader.remove();
        }

        // Remove any duplicate FRIENDS headers
        const existingFriendsHeaders = friendList.querySelectorAll('.text-xs.font-medium.text-slate-400');
        existingFriendsHeaders.forEach(header => {
            if (header.textContent === 'FRIENDS') {
                header.remove();
            }
        });

        // If there are no group chats, don't modify the friend list
        if (!groupChats || groupChats.length === 0) {
            return;
        }

        // Create a section for group chats
        const groupChatSection = document.createElement('div');
        groupChatSection.className = 'group-chats-section';
        groupChatSection.id = 'groupChatSection';

        // Add a header for the group chats section
        const groupChatHeader = document.createElement('div');
        groupChatHeader.className = 'group-chats-header text-xs font-medium text-slate-400 mb-1 px-1';
        groupChatHeader.textContent = 'GROUP CHATS';
        groupChatHeader.id = 'groupChatHeader';
        groupChatSection.appendChild(groupChatHeader);

        // Sort group chats by last activity (most recent first)
        const sortedGroupChats = [...groupChats].sort((a, b) => {
            const aTime = a.updated_at ? new Date(a.updated_at) : new Date(0);
            const bTime = b.updated_at ? new Date(b.updated_at) : new Date(0);
            return bTime - aTime;
        });

        // Add each group chat to the section
        sortedGroupChats.forEach(chat => {
            const groupChatItem = document.createElement('div');
            groupChatItem.className = 'group-chat-item rounded-lg p-3 cursor-pointer transition-all';
            groupChatItem.setAttribute('data-chat-id', chat.chat_id);
            groupChatItem.setAttribute('data-group-chat', 'true');

            // Mark as active if this is the current group chat
            if (chat.chat_id === this.currentGroupChatId) {
                groupChatItem.classList.add('active');
            }

            // Format the chat name to show only the first part if it's too long
            let displayName = chat.name;
            if (displayName.includes(':')) {
                const parts = displayName.split(':');
                displayName = parts[0].trim();
            }

            // Truncate if still too long
            if (displayName.length > 20) {
                displayName = displayName.substring(0, 17) + '...';
            }

            // Get last message preview if available
            let lastMessagePreview = 'No messages yet';
            if (chat.last_message) {
                if (chat.last_message.system) {
                    lastMessagePreview = chat.last_message.content;
                } else {
                    // Get the username or display name of the sender
                    const senderName = chat.last_message.display_name || chat.last_message.username || 'Unknown';

                    // Create a preview of the message content
                    const contentPreview = chat.last_message.content.length > 20
                        ? chat.last_message.content.substring(0, 20) + '...'
                        : chat.last_message.content;

                    lastMessagePreview = `${senderName}: ${contentPreview}`;
                }
            }

            // Create avatar HTML based on whether there's a profile picture
            // Calculate member count from members array if available, otherwise use member_count
            const memberCount = chat.members ? chat.members.length : (chat.member_count || 0);

            // Debug log to check if profile picture exists
            console.log(`Group chat ${chat.chat_id} profile picture:`, chat.profile_picture);

            // Force image to reload by adding a cache-busting parameter
            const profilePictureUrl = chat.profile_picture ?
                `${chat.profile_picture}${chat.profile_picture.includes('?') ? '&' : '?'}t=${Date.now()}` :
                null;

            const avatarHtml = profilePictureUrl
                ? `<div class="group-chat-avatar">
                      <img src="${profilePictureUrl}" alt="${displayName}" class="w-full h-full object-cover">
                   </div>`
                : `<div class="group-chat-avatar flex items-center justify-center">
                      <i data-lucide="users-round" class="h-5 w-5"></i>
                   </div>`;

            groupChatItem.innerHTML = `
                <div class="flex items-center">
                    ${avatarHtml}
                    <div class="flex-grow overflow-hidden">
                        <div class="text-sm font-medium truncate">${displayName}</div>
                        <div class="text-xs truncate">${lastMessagePreview}</div>
                    </div>
                </div>
            `;

            // Add click event to open chat
            groupChatItem.addEventListener('click', () => {
                this.openGroupChat(chat);
            });

            groupChatSection.appendChild(groupChatItem);
        });

        // Add the group chat section to the friend list
        // We want to add it at the top, before the friends
        const firstChild = friendList.firstChild;
        if (firstChild) {
            friendList.insertBefore(groupChatSection, firstChild);
        } else {
            friendList.appendChild(groupChatSection);
        }

        // Add a separator
        const separator = document.createElement('div');
        separator.className = 'border-t border-slate-700/50 my-3';
        separator.id = 'groupChatSeparator';

        // Add a header for the friends section
        const friendsHeader = document.createElement('div');
        friendsHeader.className = 'text-xs font-medium text-slate-400 mb-1 px-1';
        friendsHeader.textContent = 'FRIENDS';
        friendsHeader.id = 'friendsHeader';

        // Add the separator and friends header after the group chat section
        if (firstChild) {
            friendList.insertBefore(separator, firstChild.nextSibling);
            friendList.insertBefore(friendsHeader, separator.nextSibling);
        }

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: groupChatSection
            });
        }
    }

    /**
     * Display friends in the sidebar
     */
    displayFriends(friends) {
        const friendList = document.getElementById('friendList');
        if (!friendList) return;

        // Remove existing friends section but keep group chats section
        // We'll find the separator and friends header later when we need them
        const separator = document.getElementById('groupChatSeparator');
        const friendsHeader = document.getElementById('friendsHeader');

        // Remove all existing friends headers to prevent duplicates
        const existingFriendsHeaders = friendList.querySelectorAll('.text-xs.font-medium.text-slate-400:not(#groupChatHeader)');
        existingFriendsHeaders.forEach(header => {
            if (header.textContent === 'FRIENDS' && header.id !== 'friendsHeader') {
                header.remove();
            }
        });

        // Remove all friend items
        const friendItems = friendList.querySelectorAll('.friend-item');
        friendItems.forEach(item => item.remove());

        // If there's an empty state, remove it
        const emptyState = friendList.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        if (friends.length === 0) {
            // Show empty state
            const emptyStateDiv = document.createElement('div');
            emptyStateDiv.className = 'empty-state';
            emptyStateDiv.innerHTML = `
                <div class="empty-state-icon">
                    <i data-lucide="users" class="h-6 w-6 text-slate-400"></i>
                </div>
                <p class="empty-state-text text-sm text-slate-400">You don't have any friends yet. Add friends to start chatting!</p>
                <button id="emptyStateAddFriendBtn" class="friend-action-btn mt-4 bg-cyan-600 hover:bg-cyan-500 text-white px-3 py-2 rounded-md flex items-center mx-auto shadow-md shadow-cyan-500/10 transition-all">
                    <i data-lucide="user-plus" class="h-4 w-4 mr-2"></i>
                    Add Friend
                </button>
            `;

            // Add the empty state after the group chat section and separator
            if (friendsHeader) {
                // Insert after the friends header
                friendList.insertBefore(emptyStateDiv, friendsHeader.nextSibling);
            } else if (separator) {
                // Insert after the separator
                friendList.insertBefore(emptyStateDiv, separator.nextSibling);
            } else {
                // Just append to the end
                friendList.appendChild(emptyStateDiv);
            }

            // Add event listener to the empty state add friend button
            setTimeout(() => {
                const emptyStateAddFriendBtn = document.getElementById('emptyStateAddFriendBtn');
                if (emptyStateAddFriendBtn) {
                    emptyStateAddFriendBtn.addEventListener('click', () => this.showAddFriendModal());
                }

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: emptyStateDiv
                    });
                }
            }, 0);
        } else {
            // Create a map to deduplicate friends by ID
            const uniqueFriends = new Map();

            // Add each friend to the map, using their ID as the key
            friends.forEach(friend => {
                uniqueFriends.set(friend.id, friend);
            });

            // Convert to array and sort alphabetically
            const sortedFriends = Array.from(uniqueFriends.values()).sort((a, b) => {
                const nameA = (a.display_name || a.username).toLowerCase();
                const nameB = (b.display_name || b.username).toLowerCase();
                return nameA.localeCompare(nameB);
            });

            // Create a container for friends
            const friendsContainer = document.createElement('div');
            friendsContainer.className = 'friends-container';

            // Add each friend to the container
            sortedFriends.forEach(friend => {
                const friendItem = document.createElement('div');
                friendItem.className = 'friend-item bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 hover:bg-slate-700/50 cursor-pointer transition-all mb-1';
                friendItem.setAttribute('data-friend-id', friend.id);
                friendItem.setAttribute('data-user-id', friend.id); // Add user-id attribute for online status updates
                friendItem.setAttribute('data-chat-id', friend.chat_id);

                // Set online status to false by default - will be updated via socket events
                const isOnline = friend.isOnline || false;
                // Create a proper DOM element for the status indicator
                const statusIndicator = document.createElement('span');
                statusIndicator.className = isOnline ? 'online-indicator' : 'offline-indicator';

                // Create friend avatar
                const avatarContainer = document.createElement('div');
                avatarContainer.className = 'friend-avatar relative';
                
                if (friend.profile_picture) {
                    // Format the profile picture URL using our helper method
                    const profilePicUrl = this.formatProfilePictureUrl(friend.profile_picture);
                    
                    const img = document.createElement('img');
                    img.src = profilePicUrl;
                    img.alt = friend.username;
                    img.className = 'w-10 h-10 rounded-full mr-3 object-cover shadow-md';
                    img.onerror = function() { 
                        this.onerror = null; 
                        this.src = '/static/images/default-avatar.png'; 
                    };
                    
                    avatarContainer.appendChild(img);
                } else {
                    const initials = this.getInitials(friend.display_name || friend.username);
                    
                    const initialsDiv = document.createElement('div');
                    initialsDiv.className = 'w-10 h-10 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white text-sm font-medium mr-3 shadow-md';
                    initialsDiv.textContent = initials;
                    
                    avatarContainer.appendChild(initialsDiv);
                }
                
                // Append the status indicator to the avatar container
                avatarContainer.appendChild(statusIndicator);

                // Create the content container
                const contentContainer = document.createElement('div');
                contentContainer.className = 'flex items-center';
                
                // Create the text container
                const textContainer = document.createElement('div');
                textContainer.className = 'flex-grow overflow-hidden';
                
                // Create the name element
                const nameElement = document.createElement('div');
                nameElement.className = 'text-sm font-medium text-slate-200 truncate';
                nameElement.textContent = friend.display_name || friend.username;
                
                // Create the status text element
                const statusTextElement = document.createElement('div');
                statusTextElement.className = 'text-xs text-slate-400 truncate';
                statusTextElement.textContent = isOnline ? 'Online' : 'Offline';
                
                // Assemble the DOM structure
                textContainer.appendChild(nameElement);
                textContainer.appendChild(statusTextElement);
                
                contentContainer.appendChild(avatarContainer);
                contentContainer.appendChild(textContainer);
                
                friendItem.appendChild(contentContainer);

                // Add click event to open chat
                friendItem.addEventListener('click', () => {
                    this.openChat(friend);
                });

                friendsContainer.appendChild(friendItem);
            });

            // Add the friends container to the friend list
            // Simply append to the end - safer approach to avoid DOM errors
            friendList.appendChild(friendsContainer);
        }

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: friendList
            });
        }
    }

    /**
     * Get initials from a name
     */
    getInitials(name) {
        if (!name) return '?';

        // Split the name by spaces and get the first letter of each part
        const parts = name.split(' ');
        if (parts.length === 1) {
            // If only one part, return the first two letters or just the first if it's a single character
            return name.length > 1 ? name.substring(0, 2).toUpperCase() : name.toUpperCase();
        }

        // Return the first letter of the first and last parts
        return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
    }
    
    /**
     * Format a profile picture URL to ensure it's properly loaded
     * @param {string} url - The original profile picture URL
     * @param {boolean} addCacheBusting - Whether to add a cache-busting parameter
     * @returns {string} - The formatted URL with proper path and optional cache busting
     */
    formatProfilePictureUrl(url, addCacheBusting = false) {
        if (!url) return null;
        
        // Ensure URL is absolute
        let formattedUrl = url;
        
        // If the URL doesn't start with http/https or /, make it relative to the root
        if (!formattedUrl.startsWith('http') && !formattedUrl.startsWith('/')) {
            formattedUrl = '/' + formattedUrl;
        }
        
        // Add cache-busting parameter only if requested
        // This prevents unnecessary reloads for the same image
        if (addCacheBusting) {
            // Use a session-based cache buster instead of timestamp
            // This ensures the same URL is used for all messages in a session
            if (!this._cacheBuster) {
                this._cacheBuster = Date.now();
            }
            formattedUrl = `${formattedUrl}${formattedUrl.includes('?') ? '&' : '?'}cb=${this._cacheBuster}`;
        }
        
        return formattedUrl;
    }
    
    /**
     * Get the current user's profile picture
     * This method fetches the current user's profile picture from the server
     * @param {boolean} forceRefresh - Whether to force a refresh from the server
     * @returns {Promise<string>} - A promise that resolves to the profile picture URL
     */
    async getCurrentUserProfilePicture(forceRefresh = false) {
        // Check if we already have the profile picture cached and a request isn't in progress
        if (this.currentUserProfilePicture && !forceRefresh) {
            return this.currentUserProfilePicture;
        }
        
        // Check if we already have a pending request
        if (this._profilePicturePromise) {
            return this._profilePicturePromise;
        }
        
        // Create a new promise for the request
        this._profilePicturePromise = new Promise(async (resolve) => {
            try {
                console.log('Fetching user avatar info from server...');
                // Fetch the current user's avatar info from the server
                const response = await fetch('/api/profile/avatar/info');
                if (response.ok) {
                    const data = await response.json();
                    if (data.avatar_url) {
                        console.log('Got user avatar URL:', data.avatar_url);
                        // Cache the profile picture URL
                        this.currentUserProfilePicture = data.avatar_url;
                        resolve(this.currentUserProfilePicture);
                    } else {
                        console.log('No avatar URL found in response:', data);
                        resolve(null);
                    }
                } else {
                    console.error('Error fetching avatar info, status:', response.status);
                    resolve(null);
                }
            } catch (error) {
                console.error('Error fetching current user profile picture:', error);
                resolve(null);
            } finally {
                // Clear the promise after a short delay to allow multiple concurrent requests to use the same result
                setTimeout(() => {
                    this._profilePicturePromise = null;
                }, 100);
            }
        });
        
        return this._profilePicturePromise;
    }

    /**
     * Filter friends in the sidebar
     */
    filterFriends() {
        const searchInput = document.getElementById('friendSearchInput');
        const friendItems = document.querySelectorAll('#friendList > div');

        if (!searchInput || !friendItems.length) return;

        const query = searchInput.value.toLowerCase();

        friendItems.forEach(item => {
            const friendName = item.querySelector('.text-sm').textContent.toLowerCase();
            if (friendName.includes(query)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * Fetch themes for multiple friends in a single batch
     * This reduces API calls by fetching all needed themes at once
     */
    async batchFetchThemes() {
        try {
            // Reset the scheduled flag
            this._themesFetchScheduled = false;

            // If no themes to fetch, return
            if (!this._themesToFetch || this._themesToFetch.size === 0) {
                return;
            }

            console.log(`Batch fetching themes for ${this._themesToFetch.size} friends`);

            // Get the current friend ID if we're in a friend chat
            const currentFriendId = this.currentFriendId;

            // Prioritize fetching the current friend's theme first
            if (currentFriendId && this._themesToFetch.has(currentFriendId) && !this.friendThemes[currentFriendId]) {
                console.log(`Prioritizing theme fetch for current friend: ${currentFriendId}`);
                await this.fetchFriendTheme(currentFriendId);
                this._themesToFetch.delete(currentFriendId);

                // Update message elements with the new theme
                this.updateExistingFriendMessagesThemes();

                // If there are no more themes to fetch, we're done
                if (this._themesToFetch.size === 0) {
                    return;
                }
            }

            // For the remaining friends, fetch one theme at a time
            // This prevents making too many API calls at once
            const friendId = [...this._themesToFetch][0];
            if (friendId && !this.friendThemes[friendId]) {
                console.log(`Fetching theme for friend: ${friendId}`);
                await this.fetchFriendTheme(friendId);
                this._themesToFetch.delete(friendId);

                // Update message elements with the new theme
                this.updateExistingFriendMessagesThemes();
            }

            // If there are still more themes to fetch, schedule another batch
            if (this._themesToFetch.size > 0) {
                this._themesFetchScheduled = true;
                setTimeout(() => this.batchFetchThemes(), 100);
            }
        } catch (error) {
            console.error('Error batch fetching themes:', error);

            // If there was an error, clear the themes to fetch to prevent infinite retries
            this._themesToFetch.clear();
        }
    }

    /**
     * Fetch a specific friend's theme
     * @param {string} friendId - The friend's user ID
     * @returns {Promise<string>} - The friend's theme
     */
    async fetchFriendTheme(friendId) {
        try {
            // If we already have this friend's theme cached, return it
            if (this.friendThemes[friendId]) {
                console.log(`Using cached theme for friend ${friendId}: ${this.friendThemes[friendId]}`);
                return this.friendThemes[friendId];
            }

            console.log(`Fetching theme for friend ${friendId}`);
            const response = await fetch(`/api/friends/themes/${friendId}`);

            if (response.ok) {
                const data = await response.json();
                // The API returns either { success: true, theme: "color" } or just { theme: "color" }
                const theme = data.theme || (data.success && data.theme) || 'blue';

                // Store the theme in the friendThemes object
                this.friendThemes[friendId] = theme;
                console.log(`Loaded theme for friend ${friendId}: ${theme}`);
                return theme;
            }

            // Return default theme if not found
            console.log(`No theme found for friend ${friendId}, using default blue`);
            this.friendThemes[friendId] = 'blue'; // Cache the default theme
            return 'blue';
        } catch (error) {
            console.error(`Error fetching theme for friend ${friendId}:`, error);
            this.friendThemes[friendId] = 'blue'; // Cache the default theme on error
            return 'blue';
        }
    }

    /**
     * Open a chat with a friend
     */
    async openChat(friend) {
        console.log('Opening chat with friend:', friend);

        // Set current chat and friend
        this.currentChatId = friend.chat_id;
        this.currentFriendId = friend.id;
        this.currentGroupChatId = null;
        this.isGroupChat = false;
        
        // Store the current chat ID in localStorage for auto-loading next time
        localStorage.setItem('lastOpenedChatId', friend.chat_id);
        localStorage.setItem('lastOpenedFriendId', friend.id);

        // Clear the new messages flag
        if (friend.has_new_messages) {
            friend.has_new_messages = false;

            // Remove the visual indicator
            const friendItem = document.querySelector(`.friend-item[data-chat-id="${friend.chat_id}"]`);
            if (friendItem) {
                friendItem.classList.remove('has-new-messages');
            }

            // Reset the notification manager's unread message count
            if (window.notificationManager) {
                window.notificationManager.resetUnreadMessages();
            }
        }

        // Use the friend's theme if we already have it
        // Otherwise it will be fetched when needed via batchFetchThemes

        // If we already have this friend's theme, apply it immediately to the header and input
        if (this.friendThemes[friend.id]) {
            this.updateMessageInputTheme(this.friendThemes[friend.id]);
        }

        // Update message themes based on the current friend
        this.updateExistingFriendMessagesThemes();

        // Hide exit group chat button for regular friend chats
        const exitGroupChatBtn = document.getElementById('exitGroupChatBtn');
        if (exitGroupChatBtn) {
            exitGroupChatBtn.classList.add('hidden');
        }

        // Group members container is now hidden via CSS

        // Group members popup is now hidden via CSS

        // Remove any document click event listeners for popups
        if (this.closePopupHandler) {
            document.removeEventListener('click', this.closePopupHandler);
            this.closePopupHandler = null;
        }

        // Update UI to show active friend
        const friendItems = document.querySelectorAll('#friendList > div.friend-item');
        friendItems.forEach(item => {
            item.classList.remove('active');

            if (item.getAttribute('data-friend-id') === friend.id) {
                item.classList.add('active');
            }
        });

        // Update friend info in header
        const activeFriendName = document.getElementById('activeFriendName');
        const activeFriendAvatar = document.getElementById('activeFriendAvatar');
        const encryptionStatus = document.getElementById('encryptionStatus');
        const inviteToLiveBtn = document.getElementById('inviteToLiveBtn');
        const removeFriendBtn = document.getElementById('removeFriendBtn');

        if (activeFriendName) {
            activeFriendName.textContent = friend.display_name || friend.username;
        }

        // Use the friend's actual online status from our friends array
        const friendData = this.friends.find(f => f.id === friend.id);
        const isOnline = friendData ? friendData.isOnline : false;
        
        // Create a proper DOM element for the status indicator
        const statusIndicator = document.createElement('span');
        statusIndicator.className = isOnline ? 'online-indicator' : 'offline-indicator';

        if (activeFriendAvatar) {
            // Clear previous content
            activeFriendAvatar.innerHTML = '';
            
            // Add the relative positioning class if not already present
            if (!activeFriendAvatar.classList.contains('relative')) {
                activeFriendAvatar.classList.add('relative');
            }
            
            if (friend.profile_picture) {
                // Format the profile picture URL using our helper method
                const profilePicUrl = this.formatProfilePictureUrl(friend.profile_picture);
                
                // Create image element
                const img = document.createElement('img');
                img.src = profilePicUrl;
                img.alt = friend.display_name || friend.username;
                img.className = 'w-10 h-10 rounded-full object-cover shadow-md';
                img.onerror = function() { 
                    this.onerror = null; 
                    this.src = '/static/images/default-avatar.png'; 
                };
                
                // Append image to avatar container
                activeFriendAvatar.appendChild(img);
            } else {
                // Create initials container
                const initials = this.getInitials(friend.display_name || friend.username);
                const initialsDiv = document.createElement('div');
                initialsDiv.className = 'w-10 h-10 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white text-sm font-medium shadow-md';
                initialsDiv.textContent = initials;
                
                // Append initials div to avatar container
                activeFriendAvatar.appendChild(initialsDiv);
            }
            
            // Append the status indicator to the avatar container
            activeFriendAvatar.appendChild(statusIndicator);
            
            // Also update the status text next to the friend name
            const activeFriendStatus = document.getElementById('activeFriendStatus');
            if (activeFriendStatus) {
                activeFriendStatus.textContent = isOnline ? 'Online' : 'Offline';
            }
        }

        // Show encryption status (randomly for demo, replace with actual status)
        if (encryptionStatus) {
            // Randomly show encryption status for demo
            if (Math.random() > 0.5) {
                encryptionStatus.classList.remove('hidden');
            } else {
                encryptionStatus.classList.add('hidden');
            }
        }

        // Show action buttons
        if (inviteToLiveBtn) {
            inviteToLiveBtn.classList.remove('hidden');
            inviteToLiveBtn.setAttribute('data-friend-id', friend.id);
        }

        if (removeFriendBtn) {
            removeFriendBtn.classList.remove('hidden');
            removeFriendBtn.setAttribute('data-friend-id', friend.id);
        }

        // Hide welcome message and show messages container
        const welcomeMessage = document.getElementById('welcomeMessage');
        const chatContainer = document.getElementById('chatContainer');
        const messagesContainer = document.getElementById('messagesContainer');

        if (welcomeMessage) {
            welcomeMessage.classList.add('hidden');
        }

        if (chatContainer) {
            chatContainer.classList.remove('hidden');
        }

        if (messagesContainer) {
            messagesContainer.classList.remove('hidden');
        }

        // Enable message input
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        if (messageInput) {
            messageInput.disabled = false;
            messageInput.focus();

            // Auto-resize textarea on input
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
                // Limit max height
                if (parseInt(this.style.height) > 120) {
                    this.style.height = '120px';
                }
            });
        }

        if (sendButton) {
            sendButton.disabled = false;
        }

        // Load chat messages
        this.loadChatMessages(friend.chat_id);

        // Join the chat room via socket
        this.joinChatRoom(friend.chat_id);
    }

    /**
     * Join a chat room via Socket.IO
     */
    joinChatRoom(chatId) {
        if (!this.socket) {
            console.log('Socket not initialized, initializing now...');
            this.initSocketIO();

            // Wait for socket to connect before joining
            setTimeout(() => {
                if (this.socket && this.socket.connected) {
                    console.log('Socket connected, now joining chat room:', chatId);
                    this.joinChatRoomWithConnectedSocket(chatId);
                } else {
                    console.log('Socket still not connected, trying to connect...');
                    if (this.socket) {
                        this.socket.connect();
                        setTimeout(() => {
                            if (this.socket.connected) {
                                console.log('Socket connected after retry, joining chat room:', chatId);
                                this.joinChatRoomWithConnectedSocket(chatId);
                            } else {
                                console.error('Socket still not connected after retry, using REST API fallback');
                            }
                        }, 1000);
                    }
                }
            }, 500);
            return;
        }

        if (!this.socket.connected) {
            console.log('Socket not connected, connecting now...');
            this.socket.connect();

            // Wait for socket to connect before joining
            setTimeout(() => {
                if (this.socket.connected) {
                    console.log('Socket connected, now joining chat room:', chatId);
                    this.joinChatRoomWithConnectedSocket(chatId);
                } else {
                    console.error('Socket still not connected, using REST API fallback');
                }
            }, 1000);
            return;
        }

        // Socket is connected, join the room
        this.joinChatRoomWithConnectedSocket(chatId);
    }

    /**
     * Join a chat room with a connected socket
     */
    joinChatRoomWithConnectedSocket(chatId) {
        console.log('Socket is connected, joining chat room:', chatId);

        // Check if this is a group chat or a friend chat
        if (this.isGroupChat && this.currentGroupChatId) {
            console.log('Joining group chat room:', this.currentGroupChatId);
            this.socket.emit('join_group_chat', { chat_id: this.currentGroupChatId });
        } else {
            console.log('Joining friend chat room:', chatId);
            this.socket.emit('join_chat', { chat_id: chatId });
        }
    }

    /**
     * Load chat messages with pagination support
     */
    loadChatMessages(chatId) {
        console.log('Loading chat messages for chat ID:', chatId);

        // Reset temporary message counter and processing flag
        this.tempMessageCounter = 0;
        this.processingSocketMessage = false;

        // Make sure we have the latest themes
        this.loadFriendThemes();

        // Initialize pagination data
        this.currentChatId = chatId;
        this.messageSkip = 0;
        this.hasMoreMessages = true;
        this.isLoadingMoreMessages = false;
        this.totalMessages = 0;

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        // Show loading indicator
        messagesContainer.innerHTML = `
            <div class="flex justify-center items-center h-full">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400">Loading messages...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: messagesContainer
            });
        }

        // Fetch initial batch of chat messages (20 most recent)
        fetch(`/api/friends/chat/${chatId}?limit=20&skip=0`)
            .then(response => response.json())
            .then(chat => {
                console.log('Loaded chat:', chat);

                // Clear messages container
                messagesContainer.innerHTML = '';

                // Store pagination info
                this.hasMoreMessages = chat.has_more;
                this.totalMessages = chat.total_messages;
                this.messageSkip = chat.messages.length;

                // Display messages
                if (chat.messages && chat.messages.length > 0) {
                    // Get current user ID from meta tag
                    const userIdMeta = document.querySelector('meta[name="user-id"]');
                    const currentUserId = userIdMeta ? userIdMeta.getAttribute('content') : null;

                    // Add load more button if there are more messages
                    if (chat.has_more) {
                        const loadMoreContainer = document.createElement('div');
                        loadMoreContainer.className = 'load-more-container';
                        loadMoreContainer.innerHTML = `
                            <button id="loadMoreMessagesBtn" class="load-more-btn">
                                <i data-lucide="chevrons-up" class="h-4 w-4 mr-1"></i>
                                Load older messages
                            </button>
                        `;
                        messagesContainer.appendChild(loadMoreContainer);

                        // Initialize Lucide icons for the button
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: loadMoreContainer
                            });
                        }

                        // Add click event to load more button
                        const loadMoreBtn = document.getElementById('loadMoreMessagesBtn');
                        if (loadMoreBtn) {
                            loadMoreBtn.addEventListener('click', () => {
                                this.loadMoreMessages();
                            });
                        }
                    }

                    // Add messages in reverse order (oldest first)
                    const reversedMessages = [...chat.messages].reverse();
                    reversedMessages.forEach(message => {
                        // Mark messages from current user
                        if (currentUserId && message.user_id === currentUserId) {
                            message.is_self = true;
                        }
                        this.appendMessage(message);
                    });

                    // Scroll to bottom
                    this.scrollToBottom();

                    // Add scroll event listener to detect when user scrolls to top
                    messagesContainer.addEventListener('scroll', () => {
                        // If we're near the top and have more messages, load more
                        if (messagesContainer.scrollTop < 50 && this.hasMoreMessages && !this.isLoadingMoreMessages) {
                            this.loadMoreMessages();
                        }
                    });
                } else {
                    // Show empty state
                    messagesContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full p-6 text-center">
                            <div class="w-12 h-12 rounded-full bg-slate-700/50 flex items-center justify-center mb-3">
                                <i data-lucide="message-circle" class="h-6 w-6 text-slate-400"></i>
                            </div>
                            <p class="text-slate-400 text-sm">No messages yet. Start the conversation!</p>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: messagesContainer
                        });
                    }
                }
            })
            .catch(error => {
                console.error('Error loading chat messages:', error);
                messagesContainer.innerHTML = `
                    <div class="flex flex-col items-center justify-center h-full p-6 text-center">
                        <div class="w-12 h-12 rounded-full bg-red-900/30 flex items-center justify-center mb-3">
                            <i data-lucide="alert-circle" class="h-6 w-6 text-red-400"></i>
                        </div>
                        <p class="text-red-400 text-sm">Error loading messages. Please try again.</p>
                    </div>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: messagesContainer
                    });
                }
            });
    }

    /**
     * Load more messages (older messages)
     */
    loadMoreMessages() {
        // Prevent multiple simultaneous loads
        if (this.isLoadingMoreMessages || !this.hasMoreMessages) return;

        this.isLoadingMoreMessages = true;

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        // Remember scroll position
        const scrollHeight = messagesContainer.scrollHeight;

        // Show loading indicator at the top
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-more-indicator';
        loadingIndicator.innerHTML = `
            <div class="flex items-center justify-center py-2">
                <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                <span class="text-slate-400 text-sm">Loading older messages...</span>
            </div>
        `;

        // Add loading indicator at the top
        messagesContainer.insertBefore(loadingIndicator, messagesContainer.firstChild);

        // Initialize Lucide icons for the loading indicator
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: loadingIndicator
            });
        }

        // Determine the API endpoint based on chat type
        let apiEndpoint;
        if (this.isGroupChat) {
            apiEndpoint = `/api/friends/group-chat/${this.currentGroupChatId}/messages?limit=20&skip=${this.messageSkip}`;
        } else {
            apiEndpoint = `/api/friends/chat/${this.currentChatId}/messages?limit=20&skip=${this.messageSkip}`;
        }

        // Fetch older messages
        fetch(apiEndpoint)
            .then(response => response.json())
            .then(data => {
                console.log('Loaded more messages:', data);

                // Remove loading indicator
                loadingIndicator.remove();

                // Update pagination info
                this.hasMoreMessages = data.has_more;
                this.messageSkip += data.messages.length;

                // Get current user ID from meta tag
                const userIdMeta = document.querySelector('meta[name="user-id"]');
                const currentUserId = userIdMeta ? userIdMeta.getAttribute('content') : null;

                // Add messages in reverse order (oldest first)
                const reversedMessages = [...data.messages].reverse();

                // Insert messages at the top
                reversedMessages.forEach(message => {
                    // Mark messages from current user
                    if (currentUserId && message.user_id === currentUserId) {
                        message.is_self = true;
                    }

                    // Create message element
                    const messageElement = document.createElement('div');
                    this.renderMessageIntoElement(message, messageElement);

                    // Insert at the beginning, after the load more button if it exists
                    const loadMoreContainer = messagesContainer.querySelector('.load-more-container');
                    if (loadMoreContainer) {
                        messagesContainer.insertBefore(messageElement, loadMoreContainer.nextSibling);
                    } else {
                        messagesContainer.insertBefore(messageElement, messagesContainer.firstChild);
                    }
                });

                // Maintain scroll position
                messagesContainer.scrollTop = messagesContainer.scrollHeight - scrollHeight;

                // Update or remove the load more button based on whether there are more messages
                const loadMoreContainer = messagesContainer.querySelector('.load-more-container');
                if (loadMoreContainer) {
                    if (!this.hasMoreMessages) {
                        loadMoreContainer.remove();
                    }
                }
            })
            .catch(error => {
                console.error('Error loading more messages:', error);

                // Remove loading indicator
                loadingIndicator.remove();

                // Show error notification
                this.showNotification('Error loading older messages. Please try again.', 'error');
            })
            .finally(() => {
                this.isLoadingMoreMessages = false;
            });
    }

    /**
     * Render a message into an existing element
     * This is a helper method to avoid code duplication between appendMessage and loadMoreMessages
     * @param {Object} message - The message object to render
     * @param {HTMLElement} element - The element to render the message into (unused for now)
     */
    renderMessageIntoElement(message, _element) {
        // Implementation will be added in a separate edit
        // For now, we'll just use appendMessage
        this.appendMessage(message);
    }

    /**
     * Append a message to the messages container
     */
    appendMessage(message) {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        console.log('Appending message:', message);

        // Ensure we have a valid message object
        if (!message || typeof message !== 'object') {
            console.error('Invalid message object:', message);
            return;
        }

        // Simply display the message without any duplicate detection
        // This ensures we show exactly what's fetched from the API

        // Get current user ID using multiple fallback methods
        let currentUserId;

        // Try to get from meta tag first
        const userIdMeta = document.querySelector('meta[name="user-id"]');
        if (userIdMeta) {
            currentUserId = userIdMeta.getAttribute('content');
        }

        // If not found, try to determine from the current chat context
        if (!currentUserId && this.currentFriendId && message.user_id !== this.currentFriendId) {
            // If the message is not from the friend we're chatting with, it must be from the current user
            currentUserId = message.user_id;
        }

        // If still not found, check if this is a message we just sent (it will have our user_id)
        if (!currentUserId && message.is_self) {
            currentUserId = message.user_id;
        }

        // If we still don't have a user ID, make a best guess based on the message data
        // For messages we send via REST API, the response includes our own user data
        // Also check if the message's user_id matches the current user's ID from the meta tag
        const isSentByCurrentUser = message.is_self ||
                                   (currentUserId && message.user_id === currentUserId) ||
                                   (this.currentFriendId && message.user_id !== this.currentFriendId);

        // Check if we need to add a date separator
        const shouldAddDateSeparator = this.shouldAddDateSeparator(message.timestamp);
        if (shouldAddDateSeparator) {
            const dateSeparator = document.createElement('div');
            dateSeparator.className = 'date-separator';
            dateSeparator.innerHTML = `<span>${this.formatMessageDate(message.timestamp)}</span>`;
            messagesContainer.appendChild(dateSeparator);
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isSentByCurrentUser ? 'self' : 'friend'}`;

        // Apply theme to message
        if (isSentByCurrentUser) {
            // For messages sent by the current user
            // Always apply the user's own theme to their own messages
            // This ensures consistency regardless of which friend chat they're viewing
            messageElement.classList.add(`theme-${this.userTheme}`);
            console.log(`Applied user's theme (${this.userTheme}) to self message`);
        } else if (this.currentFriendId && this.friendThemes[this.currentFriendId]) {
            // If we're in a friend chat, apply the current friend's theme to all their messages
            messageElement.classList.add(`theme-${this.friendThemes[this.currentFriendId]}`);
            console.log(`Applied current friend's theme (${this.friendThemes[this.currentFriendId]}) to friend message`);
        } else if (message.user_id && this.friendThemes[message.user_id]) {
            // For messages from other friends, apply their chosen theme
            messageElement.classList.add(`theme-${this.friendThemes[message.user_id]}`);
            console.log(`Applied friend's theme (${this.friendThemes[message.user_id]}) to friend message`);
        } else if (message.user_id) {
            // If we don't have the theme for this friend yet, use default theme
            // We'll update it later when themes are loaded
            messageElement.classList.add('theme-blue');
            console.log(`Applied default theme (blue) to message from ${message.user_id}`);

            // Store the user ID as a data attribute for later theme updates
            messageElement.setAttribute('data-user-id', message.user_id);

            // Add this user to the list of users whose themes need to be fetched
            // But don't fetch it immediately to avoid multiple API calls
            if (!this._themesToFetch) {
                this._themesToFetch = new Set();
            }

            // Only add to fetch queue if we don't already have this theme
            if (!this.friendThemes[message.user_id]) {
                this._themesToFetch.add(message.user_id);

                // Schedule a batch fetch of themes if not already scheduled
                if (!this._themesFetchScheduled) {
                    this._themesFetchScheduled = true;
                    setTimeout(() => this.batchFetchThemes(), 100);
                }
            } else {
                // If we already have the theme, apply it immediately
                messageElement.className = messageElement.className.replace(/theme-\w+/g, '');
                messageElement.classList.add(`theme-${this.friendThemes[message.user_id]}`);
                console.log(`Applied cached theme (${this.friendThemes[message.user_id]}) to message from ${message.user_id}`);
            }
        } else {
            // Default theme for messages without a user ID
            messageElement.classList.add('theme-blue');
            console.log(`Applied default theme (blue) to message without user ID`);
        }

        // Add timestamp as data attribute for time gap calculations
        if (message.timestamp) {
            messageElement.setAttribute('data-timestamp', message.timestamp);
        } else {
            messageElement.setAttribute('data-timestamp', new Date().toISOString());
        }

        // Add message ID as data attribute for updating later
        if (message.id || message._id) {
            messageElement.setAttribute('data-message-id', message.id || message._id);
        }

        // If this is a temporary message, add the temp ID as a data attribute
        if (message.temp && message.id) {
            messageElement.setAttribute('data-temp-id', message.id);
        }

        // Add user ID as data attribute for theme updates
        if (message.user_id) {
            messageElement.setAttribute('data-user-id', message.user_id);
        }

        // Format timestamp - handle invalid dates
        let formattedTime = '';
        try {
            if (message.timestamp) {
                const timestamp = new Date(message.timestamp);
                if (!isNaN(timestamp.getTime())) {
                    // Use the user's local timezone for displaying the time
                    formattedTime = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                } else {
                    formattedTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                }
            } else {
                formattedTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
        } catch (e) {
            console.error('Error formatting timestamp:', e);
            formattedTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        // Create avatar HTML
        let avatarHtml = '';
        if (isSentByCurrentUser) {
            // Current user avatar
            // First check if the message has a profile picture (for temporary messages)
            if (message.profile_picture || this.currentUserProfilePicture) {
                // Use message profile picture if available, otherwise use the cached one
                // Don't add cache busting to avoid multiple image loads
                const profilePicUrl = this.formatProfilePictureUrl(message.profile_picture || this.currentUserProfilePicture);
                
                avatarHtml = `
                    <div class="message-avatar">
                        <img src="${profilePicUrl}" alt="You" class="w-8 h-8 rounded-full object-cover shadow-md" onerror="this.onerror=null; this.src='/static/images/default-avatar.png';">
                    </div>
                `;
            } else {
                // Fallback to initials if no profile picture is available
                const userInitials = this.getCurrentUserInitials(message);
                avatarHtml = `
                    <div class="message-avatar">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-cyan-600 to-blue-600 flex items-center justify-center text-white text-sm font-medium shadow-md">
                            ${userInitials}
                        </div>
                    </div>
                `;
                
                // Try to load the profile picture asynchronously if not already loading
                if (!this._profilePicturePromise) {
                    this.getCurrentUserProfilePicture()
                        .then(profilePicture => {
                            // We'll update the avatar in the next message
                            console.log('Loaded profile picture for future messages');
                        })
                        .catch(error => {
                            console.error('Error loading profile picture for message:', error);
                        });
                }
            }
        } else {
            // Friend avatar
            if (message.profile_picture) {
                // Format the profile picture URL using our helper method
                // Don't add cache busting to avoid multiple image loads
                const profilePicUrl = this.formatProfilePictureUrl(message.profile_picture);
                
                avatarHtml = `
                    <div class="message-avatar">
                        <img src="${profilePicUrl}" alt="${message.display_name || message.username}" class="w-8 h-8 rounded-full object-cover shadow-md" onerror="this.onerror=null; this.src='/static/images/default-avatar.png';">
                    </div>
                `;
            } else {
                const initials = this.getInitials(message.display_name || message.username);
                avatarHtml = `
                    <div class="message-avatar">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white text-sm font-medium shadow-md">
                            ${initials}
                        </div>
                    </div>
                `;
            }
        }

        // Handle message content based on type
        let messageContent = '';

        // Check if it's a system message
        if (message.is_system) {
            // For system messages, we'll handle them differently
            if (message.message_type === 'invite') {
                // This is a live chat invitation

                // Create a special UI for invitations
                // Make sure we have a valid room URL with full origin
                let roomUrl;

                // First try to get the room URL directly from the message
                if (message.room_url && message.room_url !== '#') {
                    roomUrl = message.room_url;
                }
                // If not available, try to construct it from the room ID
                else if (message.room_id) {
                    roomUrl = `${window.location.origin}/live/room/${message.room_id}`;
                }
                // If neither is available, log an error but provide a fallback
                else {
                    console.error('No room URL or room ID found in invitation message:', message);
                    roomUrl = '#';
                }

                console.log('Using room URL in message:', roomUrl);
                console.log('Room URL components:', {
                    fromMessage: message.room_url,
                    fromRoomId: message.room_id ? `${window.location.origin}/live/room/${message.room_id}` : null,
                    final: roomUrl
                });

                // Create a click handler for the invitation button
                const inviteButtonId = `invite-btn-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

                messageContent = `
                    <div class="system-message invite-message">
                        <div class="invite-icon">
                            <i data-lucide="video" class="h-5 w-5"></i>
                        </div>
                        <div class="invite-content">
                            <div class="invite-text">${this.escapeHtml(message.content)}</div>
                            <button id="${inviteButtonId}" class="invite-button" data-room-url="${roomUrl}">
                                <i data-lucide="external-link" class="h-4 w-4 mr-1"></i>
                                Join Live Chat
                            </button>
                        </div>
                    </div>
                `;

                // Add event listener after the message is added to the DOM
                setTimeout(() => {
                    const inviteButton = document.getElementById(inviteButtonId);
                    if (inviteButton) {
                        inviteButton.addEventListener('click', (e) => {
                            e.preventDefault();
                            const url = inviteButton.getAttribute('data-room-url');
                            console.log('Opening live room URL:', url);

                            // Ensure the URL is valid
                            if (url && url !== '#') {
                                // Open in a new tab
                                window.open(url, '_blank');

                                // Show a confirmation notification
                                this.showNotification('Opening live chat room...', 'success', 3000);
                            } else {
                                console.error('Invalid room URL:', url);
                                this.showNotification('Invalid room URL. Please try again.', 'error');
                            }
                        });
                    } else {
                        console.error('Invite button not found with ID:', inviteButtonId);
                    }
                }, 0);
            } else {
                // Other system messages
                messageContent = `
                    <div class="system-message ${message.message_type || 'info'}-message">
                        <i data-lucide="info" class="h-4 w-4 mr-2"></i>
                        <span>${this.escapeHtml(message.content)}</span>
                    </div>
                `;
            }
        } else {
            // Regular user message
            messageContent = message.content ? this.escapeHtml(message.content) : '';
        }

        // Create message HTML based on type
        if (message.is_system) {
            // System messages are centered and don't have avatars
            messageElement.innerHTML = `
                <div class="message-content system-content">
                    ${messageContent}
                    <div class="message-time system-time">${formattedTime}</div>
                </div>
            `;

            // Add special class for system messages
            messageElement.classList.add('system');

            // Add specific class for message type
            if (message.message_type) {
                messageElement.classList.add(message.message_type);
            }
        } else {
            // Check if the message has images - BLAZING FAST IMPLEMENTATION
            let imagesHtml = '';
            if (message.images && message.images.length > 0) {
                console.log('Processing images in message:', message.images.length);

                // Create a container for images with better styling
                imagesHtml = '<div class="message-images">';

                // Process each image with optimized loading
                message.images.forEach((imageData, index) => {
                    // Generate a unique ID for this image
                    const imageId = `img_${Date.now()}_${index}_${Math.floor(Math.random() * 1000)}`;

                    // Add image with loading optimization
                    imagesHtml += `
                        <div class="message-image-container">
                            <div class="message-image-loading" id="loading_${imageId}">
                                <div class="spinner"></div>
                            </div>
                            <img
                                src="${imageData}"
                                alt="Image"
                                class="message-image-preview"
                                data-image-id="${imageId}"
                                onload="this.classList.add('loaded'); document.getElementById('loading_${imageId}').style.display='none';"
                                onerror="this.classList.add('error'); this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZjU1NTUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1hbGVydC1jaXJjbGUiPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIi8+PGxpbmUgeDE9IjEyIiB5MT0iOCIgeDI9IjEyIiB5Mj0iMTIiLz48bGluZSB4MT0iMTIiIHkxPSIxNiIgeDI9IjEyLjAxIiB5Mj0iMTYiLz48L3N2Zz4='; document.getElementById('loading_${imageId}').style.display='none';"
                            >
                            <div class="message-image-overlay">
                                <button class="message-image-fullscreen" data-image-id="${imageId}">
                                    <i data-lucide="maximize" class="h-4 w-4"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });

                imagesHtml += '</div>';
            }

            // Regular user messages with grid layout structure
            if (isSentByCurrentUser) {
                // Self message (right-aligned)
                messageElement.innerHTML = `
                    <div class="message-content">
                        ${messageContent ? `<div class="message-text">${messageContent}</div>` : ''}
                        ${imagesHtml}
                        <div class="message-time">${formattedTime}</div>
                    </div>
                    ${avatarHtml}
                `;
            } else {
                // Friend message (left-aligned)
                messageElement.innerHTML = `
                    ${avatarHtml}
                    <div class="message-content">
                        ${messageContent ? `<div class="message-text">${messageContent}</div>` : ''}
                        ${imagesHtml}
                        <div class="message-time">${formattedTime}</div>
                    </div>
                `;
            }

            // Add click event listeners to images for full-size view with improved UX
            if (message.images && message.images.length > 0) {
                // Use requestAnimationFrame for better performance
                requestAnimationFrame(() => {
                    // Add click handlers to all fullscreen buttons
                    const fullscreenButtons = messageElement.querySelectorAll('.message-image-fullscreen');
                    fullscreenButtons.forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            e.stopPropagation(); // Prevent event bubbling
                            const imageId = btn.getAttribute('data-image-id');
                            const img = messageElement.querySelector(`img[data-image-id="${imageId}"]`);
                            if (img) {
                                // Show image in the modal
                                this.showImageViewer(img.src);
                            }
                        });
                    });

                    // Also add click handlers to the images themselves
                    const imageElements = messageElement.querySelectorAll('.message-image-preview');
                    imageElements.forEach(img => {
                        img.addEventListener('click', () => {
                            // Show image in the modal
                            this.showImageViewer(img.src);
                        });
                    });
                });
            }
        }

        // Append message to container
        messagesContainer.appendChild(messageElement);

        // Initialize Lucide icons for system messages
        if (message.is_system && typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: messageElement
            });
        }
    }

    /**
     * Escape HTML to prevent XSS while preserving line breaks
     */
    escapeHtml(text) {
        // First escape the HTML
        const div = document.createElement('div');
        div.textContent = text;
        const escaped = div.innerHTML;

        // Then convert line breaks to <br> tags
        return escaped.replace(/\n/g, '<br>');
    }

    /**
     * Get current user's initials
     */
    getCurrentUserInitials(message) {
        // Try to get the user's name from the message
        if (message.display_name || message.username) {
            return this.getInitials(message.display_name || message.username);
        } else {
            // Fallback to getting it from the page
            const currentUserName = document.querySelector('meta[name="username"]')?.getAttribute('content');
            if (currentUserName) {
                return this.getInitials(currentUserName);
            } else {
                return 'Me';
            }
        }
    }

    /**
     * Format message date for separator
     */
    formatMessageDate(timestamp) {
        if (!timestamp) return 'Today';

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return 'Today';

        // Use the user's local timezone for date comparisons
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            // Use the user's locale and timezone for date formatting
            return date.toLocaleDateString(undefined, { weekday: 'long', month: 'short', day: 'numeric' });
        }
    }

    /**
     * Check if we should add a date separator
     * Only add a separator if there's a significant time gap (30+ mins) between messages
     */
    shouldAddDateSeparator(timestamp) {
        if (!timestamp) return false;

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return false;

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return false;

        // Get the last message
        const lastMessage = messagesContainer.querySelector('.message:last-child');
        if (!lastMessage) return false; // Don't add separator for the first message

        // Get the timestamp of the last message
        const lastMessageTime = lastMessage.getAttribute('data-timestamp');
        if (!lastMessageTime) return false;

        const lastDate = new Date(lastMessageTime);
        if (isNaN(lastDate.getTime())) return false;

        // Calculate time difference in minutes
        const timeDiff = (date.getTime() - lastDate.getTime()) / (1000 * 60);

        // Add separator if time difference is 30+ minutes
        return timeDiff >= 30;
    }

    /**
     * Handle typing event
     * @param {boolean} isTyping - Whether the user is typing or not
     */
    handleTypingEvent(isTyping) {
        // Don't send typing events if we're not in a chat
        if (!this.currentChatId) return;

        // Clear any existing typing timeout
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        // If user is typing, set a timeout to stop typing after 2 seconds of inactivity
        if (isTyping) {
            // Only emit typing event if we weren't already typing
            if (!this.isTyping) {
                this.isTyping = true;

                // Send typing event via socket
                if (this.socket && this.socket.connected) {
                    if (this.isGroupChat) {
                        this.socket.emit('group_typing', {
                            chat_id: this.currentGroupChatId,
                            typing: true
                        });
                    } else {
                        this.socket.emit('friend_typing', {
                            chat_id: this.currentChatId,
                            is_typing: true
                        });
                    }
                }
            }

            // Set timeout to stop typing after 2 seconds of inactivity
            this.typingTimeout = setTimeout(() => {
                this.isTyping = false;

                // Send stop typing event
                if (this.socket && this.socket.connected) {
                    if (this.isGroupChat) {
                        this.socket.emit('group_typing', {
                            chat_id: this.currentGroupChatId,
                            typing: false
                        });
                    } else {
                        this.socket.emit('friend_typing', {
                            chat_id: this.currentChatId,
                            is_typing: false
                        });
                    }
                }
            }, 2000);
        }
    }

    /**
     * Show typing indicator
     * @param {Object} user - The user who is typing (optional)
     */
    showTypingIndicator(user) {
        // If no user is provided or we don't have user info, don't show typing indicator
        if (!user || !user.display_name && !user.username) return;

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        // Remove any existing typing indicator
        const existingIndicator = messagesContainer.querySelector('.typing-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Get user info - only show for specific users, not for "Group Chat"
        const userName = user.display_name || user.username;

        // Skip if this is a group chat name rather than a specific user
        if (!userName || userName === 'Group chat') return;

        const initials = this.getInitials(userName);

        // Create typing indicator
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.id = 'typingIndicator';
        typingIndicator.innerHTML = `
            <div class="message-avatar">
                <div class="w-8 h-8 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white text-sm font-medium shadow-md">
                    ${initials}
                </div>
            </div>
            <span>${userName} is typing</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        messagesContainer.appendChild(typingIndicator);
        this.scrollToBottom();
    }

    /**
     * Scroll messages container to bottom
     */
    scrollToBottom() {
        // Simple, reliable implementation that always works
        const messagesContainer = document.getElementById('messagesContainer');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    /**
     * Send a message with rate limiting
     */
    sendMessage() {
        const messageInput = document.getElementById('messageInput');
        if (!messageInput || !this.currentChatId) return;

        // Get message content, preserving line breaks but trimming leading/trailing whitespace
        const message = messageInput.value.replace(/^\s+|\s+$/g, '');

        // Check if we have any message content or images
        if (!message && this.selectedImages.length === 0) return;

        // Rate limiting to prevent sending messages too quickly
        const now = Date.now();
        if (!this.lastMessageSentTime) {
            this.lastMessageSentTime = 0;
        }

        // Minimum time between messages (250ms)
        const minTimeBetweenMessages = 250;

        if (now - this.lastMessageSentTime < minTimeBetweenMessages) {
            console.log('Sending messages too quickly, adding delay');
            setTimeout(() => {
                this._sendMessageImpl(message);
            }, minTimeBetweenMessages);
            return;
        }

        // Update last message sent time
        this.lastMessageSentTime = now;

        // Proceed with sending the message
        this._sendMessageImpl(message);
    }

    /**
     * Internal implementation of message sending
     */
    _sendMessageImpl(message) {
        console.log('Sending message:', message);

        // Clear input and reset height IMMEDIATELY for better UX
        messageInput.value = '';
        this.autoResizeMessageInput(messageInput);

        // Clear typing status
        this.isTyping = false;
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }

        // Send stop typing event
        if (this.socket && this.socket.connected) {
            if (this.isGroupChat) {
                this.socket.emit('group_typing', {
                    chat_id: this.currentGroupChatId,
                    typing: false
                });
            } else {
                this.socket.emit('friend_typing', {
                    chat_id: this.currentChatId,
                    is_typing: false
                });
            }
        }

        // Try to ensure Socket.IO is connected
        if (!this.socket) {
            console.log('Socket not initialized, initializing now...');
            this.initSocketIO();
        } else if (!this.socket.connected) {
            console.log('Socket not connected, attempting to reconnect...');
            this.socket.connect();
        }

        // Generate a unique ID for this message
        const timestamp = Date.now();
        const tempId = `temp_${timestamp}`;

        // Get current user info
        const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content') || 'current-user';
        const currentUsername = document.querySelector('meta[name="username"]')?.getAttribute('content') || 'You';
        const currentDisplayName = document.querySelector('meta[name="display-name"]')?.getAttribute('content') || currentUsername;

        // Check if all images are uploaded
        const allImagesUploaded = this.selectedImages.every(img => !img.uploading && img.url);

        // If not all images are uploaded, show a notification and wait
        if (this.selectedImages.length > 0 && !allImagesUploaded) {
            this.showNotification('Please wait for all images to upload before sending the message.', 'warning');

            // Add a loading indicator to the message input area
            const messageInputContainer = document.querySelector('.message-input-container');
            if (messageInputContainer) {
                const loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'message-sending-loading';
                loadingIndicator.innerHTML = '<div class="spinner"></div><span>Waiting for images to upload...</span>';
                messageInputContainer.appendChild(loadingIndicator);

                // Set a timeout to check again in 1 second
                setTimeout(() => {
                    // Remove the loading indicator
                    if (loadingIndicator.parentNode) {
                        loadingIndicator.remove();
                    }

                    // Try sending again
                    this.sendMessage();
                }, 1000);

                return;
            }

            return;
        }

        // Prepare images if any - use URLs instead of data
        const images = this.selectedImages.map(img => img.url);

        // For display in the UI, we'll still use the data URLs
        const imageDataForUI = this.selectedImages.map(img => img.data);

        // Create a temporary message to show immediately
        const tempMessage = {
            content: message,
            user_id: currentUserId,
            username: currentUsername,
            display_name: currentDisplayName,
            timestamp: new Date().toISOString(),
            is_self: true,
            id: tempId, // Add a unique ID to prevent duplication
            temp: true,  // Mark as temporary
            images: imageDataForUI, // Add images for UI display
            profile_picture: this.currentUserProfilePicture // Add the current user's profile picture
        };
        
        // We'll only try to get the profile picture once for all messages
        // This prevents multiple API calls for the same image
        if (!this.currentUserProfilePicture && !this._profilePicturePromise) {
            this.getCurrentUserProfilePicture()
                .then(profilePicture => {
                    if (profilePicture) {
                        // Update all temporary messages with the profile picture
                        this.updateSelfMessageAvatars(profilePicture);
                    }
                })
                .catch(error => {
                    console.error('Error fetching profile picture for messages:', error);
                });
        }

        // We're no longer tracking messages for duplicate detection
        // Just log the temporary message ID for debugging
        console.log('Created temporary message with ID:', tempId);

        // Display the message IMMEDIATELY for instant feedback
        console.log('Displaying temporary message:', tempMessage);
        this.appendMessage(tempMessage);
        this.scrollToBottom();

        // Clear image previews after sending
        this.clearAllImages();

        // Send message via Socket.IO
        if (this.socket && this.socket.connected) {
            console.log('Sending message via Socket.IO');

            try {
                // Check if this is a group chat or a friend chat
                if (this.isGroupChat) {
                    console.log('Sending group chat message');

                    // Use regular emit instead of volatile for more reliable delivery
                    this.socket.emit('group_send_message', {
                        chat_id: this.currentGroupChatId,
                        message: message,
                        images: images,
                        temp_id: tempId  // Include the temporary ID for tracking
                    }, (response) => {
                        // Handle acknowledgment from server
                        if (response && response.success) {
                            console.log('Group message sent successfully:', response);
                            // Update the temporary message with the real message data
                            this.updateTempMessage(tempId, response.message);
                        } else if (response && response.error) {
                            console.error('Error sending group message:', response.error);
                            this.showNotification('Error sending message: ' + response.error, 'error');
                        }
                    });
                } else {
                    console.log('Sending friend chat message');

                    // Use regular emit instead of volatile for more reliable delivery
                    this.socket.emit('friend_send_message', {
                        chat_id: this.currentChatId,
                        message: message,
                        images: images
                    }, (response) => {
                        // Handle acknowledgment from server
                        if (response && response.success) {
                            console.log('Friend message sent successfully:', response);
                            // Update the temporary message with the real message data
                            this.updateTempMessage(tempId, response.message);
                        } else if (response && response.error) {
                            console.error('Error sending friend message:', response.error);
                            this.showNotification('Error sending message: ' + response.error, 'error');
                        }
                    });
                }
            } catch (error) {
                console.error('Error sending message via Socket.IO:', error);
                // Fall back to REST API if socket fails
                this.sendMessageViaREST(message, images, tempId);
            }
        } else {
            // Fallback to REST API
            console.log('Socket not available, using REST API fallback');
            this.sendMessageViaREST(message, images, tempId);
        }
    }

    /**
     * Send a message via REST API (fallback method)
     * @param {string} message - The message text
     * @param {Array} images - Array of image URLs
     * @param {string} tempId - ID of the temporary message to update
     */
    sendMessageViaREST(message, images = [], tempId = null) {
        // Set processing flag to prevent duplicate processing from socket
        this.processingSocketMessage = true;

        // Check if all images are uploaded
        if (this.selectedImages.length > 0) {
            const allImagesUploaded = this.selectedImages.every(img => !img.uploading && img.url);

            // If not all images are uploaded, show a notification and wait
            if (!allImagesUploaded) {
                this.showNotification('Please wait for all images to upload before sending the message.', 'warning');

                // Clear the processing flag after a short delay
                setTimeout(() => {
                    this.processingSocketMessage = false;
                }, 200);

                return;
            }

            // Use the uploaded image URLs instead of data URLs
            images = this.selectedImages.map(img => img.url);
        }

        // Create a temporary message to display immediately
        if (!tempId) {
            // Generate a temporary ID for the message
            tempId = `temp_${Date.now()}_${this.tempMessageCounter++}`;

            // Get current user ID and info
            const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
            const currentUsername = document.querySelector('meta[name="username"]')?.getAttribute('content') || 'You';
            const currentDisplayName = document.querySelector('meta[name="display-name"]')?.getAttribute('content') || currentUsername;

            // For display in the UI, we'll still use the data URLs if available
            const imageDataForUI = this.selectedImages.length > 0 ?
                this.selectedImages.map(img => img.data || img.url) :
                images;

            // Create a temporary message object
            const tempMessage = {
                id: tempId,
                user_id: currentUserId,
                username: currentUsername,
                display_name: currentDisplayName,
                content: message,
                images: imageDataForUI,
                timestamp: new Date().toISOString(),
                is_self: true,
                temp: true,
                profile_picture: this.currentUserProfilePicture // Add the current user's profile picture
            };
            
            // We'll only try to get the profile picture once for all messages
            // This prevents multiple API calls for the same image
            if (!this.currentUserProfilePicture && !this._profilePicturePromise) {
                this.getCurrentUserProfilePicture()
                    .then(profilePicture => {
                        if (profilePicture) {
                            // Update all temporary messages with the profile picture
                            this.updateSelfMessageAvatars(profilePicture);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching profile picture for messages:', error);
                    });
            }

            // Display the message IMMEDIATELY for instant feedback
            console.log('Displaying temporary message via REST API:', tempMessage);
            this.appendMessage(tempMessage);
            this.scrollToBottom();
        }

        // Determine the API endpoint based on chat type
        let apiEndpoint;
        if (this.isGroupChat) {
            apiEndpoint = `/api/friends/group-chat/${this.currentGroupChatId}/messages`;
        } else {
            apiEndpoint = `/api/friends/chat/${this.currentChatId}/messages`;
        }

        fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                images: images
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Message sent via REST API:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else if (data.success && data.message && tempId) {
                // Update the temporary message with the real message data
                this.updateTempMessage(tempId, data.message);
            }
        })
        .finally(() => {
            // Clear the processing flag after a short delay
            setTimeout(() => {
                this.processingSocketMessage = false;
            }, 200); // Reduced from 500ms for faster processing

            // Clear image previews after sending
            this.clearAllImages();
        })
        .catch(error => {
            console.error('Error sending message via REST API:', error);
            this.showNotification('Error sending message. Please try again.', 'error');
        });
    }

    /**
     * Update a temporary message with the real message data
     * @param {string} tempId - ID of the temporary message
     * @param {Object} realMessage - The real message data from the server
     */
    updateTempMessage(tempId, realMessage) {
        if (!tempId || !realMessage) return;

        console.log('Updating temporary message:', tempId, 'with real message:', realMessage);

        // Find the temporary message element
        const tempMessageElement = document.querySelector(`.message[data-temp-id="${tempId}"]`);
        if (!tempMessageElement) {
            console.log('Temporary message element not found, no update needed');
            return;
        }

        // Update the message element with the real message data
        tempMessageElement.setAttribute('data-message-id', realMessage.id || realMessage._id || '');
        tempMessageElement.removeAttribute('data-temp-id');

        // If the message has a status indicator, update it
        const statusIndicator = tempMessageElement.querySelector('.message-status');
        if (statusIndicator) {
            statusIndicator.innerHTML = '<i data-lucide="check" class="h-3 w-3"></i>';
            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: statusIndicator
                });
            }
        }

        console.log('Temporary message updated successfully');
    }

    /**
     * Show Add Friend modal
     */
    showAddFriendModal() {
        this.openModal('addFriendModal');

        // Clear search input and results
        const searchInput = document.getElementById('userSearchInput');
        const searchResults = document.getElementById('searchResults');

        if (searchInput) {
            searchInput.value = '';
            searchInput.focus();
        }

        if (searchResults) {
            searchResults.innerHTML = '';
        }
    }

    /**
     * Show Chat Theme modal
     */
    showChatThemeModal() {
        const modal = document.getElementById('chatThemeModal');
        if (!modal) {
            console.error('Chat theme modal not found in the DOM');
            return;
        }

        console.log('Opening chat theme modal');

        // Remove any existing event listeners by cloning and replacing the modal
        const newModal = modal.cloneNode(true);
        modal.parentNode.replaceChild(newModal, modal);

        // Get the fresh modal element
        const freshModal = document.getElementById('chatThemeModal');

        // Show modal with animation
        freshModal.classList.remove('hidden');

        // Add animation classes after a small delay to trigger transitions
        setTimeout(() => {
            const modalContent = freshModal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }
        }, 10);

        // Set up close modal functionality
        const closeButtons = freshModal.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => this.closeChatThemeModal());
        });

        // Close modal when clicking outside content
        freshModal.addEventListener('click', (e) => {
            if (e.target === freshModal) {
                this.closeChatThemeModal();
            }
        });

        // Set up theme selection
        this.setupThemeSelection();
    }

    /**
     * Close Chat Theme modal
     */
    closeChatThemeModal() {
        const modal = document.getElementById('chatThemeModal');
        if (!modal) return;

        // Add animation for closing
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';
        }

        // Hide modal after animation completes
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
    }

    /**
     * Setup theme selection in the theme modal
     */
    setupThemeSelection() {
        const themeCards = document.querySelectorAll('#chatThemeModal .theme-card');
        const selectedThemeNameEl = document.getElementById('selectedThemeName');
        const selectedThemeColorEl = document.getElementById('selectedThemeColor');
        const applyThemeBtn = document.getElementById('applyThemeBtn');
        const resetThemeBtn = document.getElementById('resetThemeBtn');
        const prevBtn = document.getElementById('prevThemeBtn');
        const nextBtn = document.getElementById('nextThemeBtn');
        const themeCarousel = document.getElementById('themeCarousel');
        const themeIndicatorsContainer = document.getElementById('themeIndicators');

        // Use the theme from the class property, which should be synced with the server
        // Fall back to localStorage if needed
        const currentTheme = this.userTheme || localStorage.getItem('userChatTheme') || 'blue';
        let selectedTheme = currentTheme;
        let currentIndex = 0;

        console.log(`Setting up theme selection with current theme: ${currentTheme}`);

        // Theme color mapping
        const themeColors = {
            'blue': {
                color: '#4f46e5',
                gradient: 'from-indigo-500 to-blue-600'
            },
            'purple': {
                color: '#8b5cf6',
                gradient: 'from-purple-500 to-violet-600'
            },
            'green': {
                color: '#10b981',
                gradient: 'from-emerald-500 to-green-600'
            },
            'red': {
                color: '#ef4444',
                gradient: 'from-red-500 to-rose-600'
            },
            'orange': {
                color: '#f97316',
                gradient: 'from-orange-500 to-amber-600'
            },
            'teal': {
                color: '#14b8a6',
                gradient: 'from-teal-500 to-cyan-600'
            },
            'pink': {
                color: '#ec4899',
                gradient: 'from-pink-500 to-rose-500'
            },
            'indigo': {
                color: '#6366f1',
                gradient: 'from-indigo-500 to-violet-500'
            },
            'amber': {
                color: '#f59e0b',
                gradient: 'from-amber-500 to-yellow-500'
            }
        };

        // Create theme indicators
        themeIndicatorsContainer.innerHTML = '';
        themeCards.forEach((_, index) => {
            const indicator = document.createElement('div');
            indicator.className = 'theme-indicator';
            indicator.dataset.index = index;
            indicator.addEventListener('click', () => {
                goToSlide(index);
            });
            themeIndicatorsContainer.appendChild(indicator);
        });

        // Find the index of the current theme
        themeCards.forEach((card, index) => {
            if (card.getAttribute('data-theme') === currentTheme) {
                currentIndex = index;
            }
        });

        // Initialize carousel
        const updateCarousel = () => {
            themeCards.forEach((card, index) => {
                // Reset all classes
                card.className = 'theme-card';

                // Set position classes
                if (index === currentIndex) {
                    card.classList.add('active');
                    // Show the selected indicator
                    const indicator = card.querySelector('.theme-selected-indicator');
                    if (indicator) indicator.style.opacity = '1';
                } else {
                    // Hide the selected indicator
                    const indicator = card.querySelector('.theme-selected-indicator');
                    if (indicator) indicator.style.opacity = '0';
                }

                if (index === currentIndex - 1) {
                    card.classList.add('prev');
                } else if (index === currentIndex + 1) {
                    card.classList.add('next');
                } else if (index === currentIndex - 2) {
                    card.classList.add('prev-out');
                } else if (index === currentIndex + 2) {
                    card.classList.add('next-out');
                }
            });

            // Update indicators
            const indicators = themeIndicatorsContainer.querySelectorAll('.theme-indicator');
            indicators.forEach((indicator, index) => {
                if (index === currentIndex) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            });

            // Update selected theme
            const activeCard = themeCards[currentIndex];
            selectedTheme = activeCard.getAttribute('data-theme');

            // Update theme name display
            if (selectedThemeNameEl) {
                selectedThemeNameEl.textContent = this.getThemeDisplayName(selectedTheme);
            }

            // Update theme color indicator
            if (selectedThemeColorEl && themeColors[selectedTheme]) {
                selectedThemeColorEl.style.backgroundColor = themeColors[selectedTheme].color;
            }
        };

        // Go to specific slide
        const goToSlide = (index) => {
            if (index < 0) {
                index = themeCards.length - 1;
            } else if (index >= themeCards.length) {
                index = 0;
            }
            currentIndex = index;
            updateCarousel();
        };

        // Previous slide
        const prevSlide = () => {
            goToSlide(currentIndex - 1);
        };

        // Next slide
        const nextSlide = () => {
            goToSlide(currentIndex + 1);
        };

        // Add click events to theme cards
        themeCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                goToSlide(index);
            });
        });

        // Add click events to navigation buttons
        if (prevBtn) {
            prevBtn.addEventListener('click', prevSlide);
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', nextSlide);
        }

        // Apply button click handler
        if (applyThemeBtn) {
            applyThemeBtn.addEventListener('click', () => {
                if (selectedTheme) {
                    // Update localStorage
                    localStorage.setItem('userChatTheme', selectedTheme);
                    this.userTheme = selectedTheme;

                    // Update existing messages with new theme
                    this.updateExistingMessagesTheme();

                    // Send theme update to server
                    this.updateThemeOnServer(selectedTheme);

                    // Show notification
                    this.showNotification(`Theme changed to ${this.getThemeDisplayName(selectedTheme)}`, 'success');

                    // Close the modal
                    this.closeChatThemeModal();
                }
            });
        }

        // Reset button click handler
        if (resetThemeBtn) {
            resetThemeBtn.addEventListener('click', () => {
                const defaultTheme = 'blue';

                // Find the index of the default theme
                themeCards.forEach((card, index) => {
                    if (card.getAttribute('data-theme') === defaultTheme) {
                        goToSlide(index);
                    }
                });
            });
        }

        // Initialize the carousel with the current theme
        updateCarousel();

        // Initialize Lucide icons in the modal
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': '2'
                }
            });
        }

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!document.getElementById('chatThemeModal') || document.getElementById('chatThemeModal').classList.contains('hidden')) {
                return;
            }

            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            } else if (e.key === 'Enter') {
                applyThemeBtn.click();
            } else if (e.key === 'Escape') {
                this.closeChatThemeModal();
            }
        });
    }

    /**
     * Update theme preview in the modal
     */
    updateThemePreview(theme, themeColors, selectedThemeNameEl, selectedThemeColorEl, previewSelfMessageEl) {
        if (!themeColors[theme]) return;

        // Update theme name display
        if (selectedThemeNameEl) {
            selectedThemeNameEl.textContent = this.getThemeDisplayName(theme);
        }

        // Update theme color indicator
        if (selectedThemeColorEl) {
            selectedThemeColorEl.style.backgroundColor = themeColors[theme].color;
        }

        // Update message preview
        if (previewSelfMessageEl) {
            previewSelfMessageEl.style.background = themeColors[theme].color;
        }
    }

    /**
     * Get display name for a theme
     * @param {string} theme - The theme identifier
     * @returns {string} The display name for the theme
     */
    getThemeDisplayName(theme) {
        const displayNames = {
            'blue': 'Blue (Default)',
            'purple': 'Purple',
            'green': 'Green',
            'red': 'Red',
            'orange': 'Orange',
            'teal': 'Teal',
            'pink': 'Pink',
            'indigo': 'Indigo',
            'amber': 'Amber'
        };

        return displayNames[theme] || theme.charAt(0).toUpperCase() + theme.slice(1);
    }

    /**
     * Update theme on server
     * @param {string} theme - The selected theme
     */
    async updateThemeOnServer(theme) {
        try {
            console.log(`Updating theme on server to: ${theme}`);

            // Send the theme update to the server using the correct endpoint
            const response = await fetch('/api/friends/theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify({ theme: theme })
            });

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Theme update response:', data);

            if (data.success) {
                console.log('Theme successfully updated on server');
            } else {
                console.error('Failed to update theme on server:', data.error);
                // Still keep the local theme change even if server update fails
            }
        } catch (error) {
            console.error('Error updating theme on server:', error);
            // Show error notification but keep the local theme change
            this.showNotification('Theme changed locally, but failed to update on server', 'error');
        }
    }

    /**
     * Get CSRF token from cookies
     * @returns {string} CSRF token
     */
    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    /**
     * Show a notification
     * @param {string} message - The notification message
     * @param {string} type - The notification type (success, error, info)
     */
    showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type} flex items-center p-3 mb-2 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300`;

        // Set background color based on type
        if (type === 'success') {
            notification.classList.add('bg-green-600', 'text-white');
        } else if (type === 'error') {
            notification.classList.add('bg-red-600', 'text-white');
        } else {
            notification.classList.add('bg-indigo-600', 'text-white');
        }

        // Add icon based on type
        let icon = 'info';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'alert-circle';

        notification.innerHTML = `
            <i data-lucide="${icon}" class="h-5 w-5 mr-2"></i>
            <span>${message}</span>
        `;

        container.appendChild(notification);

        // Initialize icon
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                }
            });
        }

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Show Friend Requests modal
     */
    showFriendRequestsModal() {
        this.openModal('friendRequestsModal');

        // Load requests if they haven't been loaded yet or if we need a refresh
        if (!this.requestsLoaded) {
            // Load pending requests
            this.loadPendingRequests();

            // Load sent requests
            this.loadSentRequests();

            // Mark as loaded
            this.requestsLoaded = true;
        } else {
            // Just display the cached requests
            this.displayPendingRequests();
            this.displaySentRequests();
        }

        // Reset the notification manager's unread request count
        if (window.notificationManager) {
            window.notificationManager.resetUnreadRequests();
        }
    }

    /**
     * Show friends menu modal
     */
    showFriendsMenuModal() {
        const modal = document.getElementById('friendsMenuModal');
        if (!modal) return;

        // Show modal with animation
        modal.classList.remove('hidden');
        const modalContent = modal.querySelector('.modal-content');

        if (modalContent) {
            // Force a reflow to ensure the initial state is applied
            void modalContent.offsetWidth;

            // Apply the animated state
            modalContent.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        }

        // Add event listener to close when clicking outside
        const closeOnOutsideClick = (e) => {
            if (e.target === modal) {
                this.closeFriendsMenuModal();
                modal.removeEventListener('click', closeOnOutsideClick);
            }
        };

        modal.addEventListener('click', closeOnOutsideClick);

        // Add event listener for close buttons
        const closeButtons = modal.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.closeFriendsMenuModal();
            });
        });
    }

    /**
     * Close friends menu modal
     */
    closeFriendsMenuModal() {
        const modal = document.getElementById('friendsMenuModal');
        if (!modal) return;

        const modalContent = modal.querySelector('.modal-content');

        if (modalContent) {
            // Animate out
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';

            // Hide after animation completes
            setTimeout(() => {
                modal.classList.add('hidden');
                // Reset styles for next opening
                modalContent.style.opacity = '';
                modalContent.style.transform = '';
            }, 300);
        } else {
            modal.classList.add('hidden');
        }
    }

    /**
     * Update the badge on the friend requests button
     */
    updateFriendRequestsBadge() {
        // Fetch the count of pending requests
        fetch('/api/friends/pending')
            .then(response => response.json())
            .then(requests => {
                // Store in cache
                this.pendingRequests = requests;

                // Update the menu badge
                const menuRequestBadge = document.getElementById('menuRequestBadge');
                const modalRequestBadge = document.getElementById('modalRequestBadge');

                // Update badges
                if (requests.length > 0) {
                    const count = requests.length > 9 ? '9+' : requests.length;

                    // Update menu badge
                    if (menuRequestBadge) {
                        menuRequestBadge.textContent = count;
                        menuRequestBadge.classList.remove('hidden');
                    }

                    // Update modal badge
                    if (modalRequestBadge) {
                        modalRequestBadge.textContent = count;
                        modalRequestBadge.classList.remove('hidden');
                    }
                } else {
                    // Hide badges if there are no requests
                    if (menuRequestBadge) {
                        menuRequestBadge.classList.add('hidden');
                    }

                    if (modalRequestBadge) {
                        modalRequestBadge.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating friend requests badge:', error);
            });
    }

    /**
     * Load pending friend requests
     */
    loadPendingRequests() {
        console.log('Loading pending friend requests...');

        const pendingRequests = document.getElementById('pendingRequests');
        if (!pendingRequests) return;

        // Show loading indicator
        pendingRequests.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading requests...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: pendingRequests
            });
        }

        // Fetch pending requests
        fetch('/api/friends/pending')
            .then(response => response.json())
            .then(requests => {
                console.log('Loaded pending requests:', requests);

                // Store in cache
                this.pendingRequests = requests;

                // Display the requests
                this.displayPendingRequests();
            })
            .catch(error => {
                console.error('Error loading pending requests:', error);
                pendingRequests.innerHTML = `
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center text-red-400">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <span class="text-sm">Error loading requests. Please try again.</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: pendingRequests
                    });
                }
            });
    }

    /**
     * Display pending friend requests from cache
     */
    displayPendingRequests() {
        console.log('Displaying pending friend requests from cache:', this.pendingRequests);

        const pendingRequests = document.getElementById('pendingRequests');
        if (!pendingRequests) return;

        if (!this.pendingRequests || this.pendingRequests.length === 0) {
            pendingRequests.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center text-slate-500">
                        <i data-lucide="inbox" class="h-4 w-4 mr-2"></i>
                        <span class="text-sm">No pending requests</span>
                    </div>
                </div>
            `;
        } else {
            pendingRequests.innerHTML = '';

            // Add each request to the list
            this.pendingRequests.forEach(request => {
                const requestItem = document.createElement('div');
                requestItem.className = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2';
                requestItem.setAttribute('data-user-id', request.id);

                // Create user avatar
                let avatarHtml = '';
                if (request.profile_picture) {
                    avatarHtml = `<img src="${request.profile_picture}" alt="${request.username}" class="w-8 h-8 rounded-full mr-3 object-cover">`;
                } else {
                    const initials = this.getInitials(request.display_name || request.username);
                    avatarHtml = `
                        <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3">
                            ${initials}
                        </div>
                    `;
                }

                requestItem.innerHTML = `
                    <div class="flex items-center">
                        ${avatarHtml}
                        <div class="flex-grow">
                            <div class="text-sm font-medium text-slate-200">${request.display_name || request.username}</div>
                            <div class="text-xs text-slate-400">@${request.username}</div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md text-xs accept-btn" data-user-id="${request.id}">
                                <i data-lucide="check" class="h-3 w-3 inline-block mr-1"></i>
                                Accept
                            </button>
                            <button class="px-3 py-1 bg-slate-600 hover:bg-slate-500 text-slate-300 rounded-md text-xs reject-btn" data-user-id="${request.id}">
                                <i data-lucide="x" class="h-3 w-3 inline-block mr-1"></i>
                                Reject
                            </button>
                        </div>
                    </div>
                `;

                pendingRequests.appendChild(requestItem);

                // Add event listeners for accept and reject buttons
                const acceptBtn = requestItem.querySelector('.accept-btn');
                if (acceptBtn) {
                    acceptBtn.addEventListener('click', () => {
                        this.acceptFriendRequest(request.id);
                    });
                }

                const rejectBtn = requestItem.querySelector('.reject-btn');
                if (rejectBtn) {
                    rejectBtn.addEventListener('click', () => {
                        this.rejectFriendRequest(request.id);
                    });
                }
            });
        }

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: pendingRequests
            });
        }
    }

    /**
     * Load sent friend requests
     */
    loadSentRequests() {
        console.log('Loading sent friend requests...');

        const sentRequests = document.getElementById('sentRequests');
        if (!sentRequests) return;

        // Show loading indicator
        sentRequests.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading requests...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: sentRequests
            });
        }

        // Fetch sent requests
        fetch('/api/friends/requests/sent')
            .then(response => response.json())
            .then(requests => {
                console.log('Loaded sent requests:', requests);

                // Store in cache
                this.sentRequests = requests;

                // Display the requests
                this.displaySentRequests();
            })
            .catch(error => {
                console.error('Error loading sent requests:', error);
                sentRequests.innerHTML = `
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center text-red-400">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <span class="text-sm">Error loading requests. Please try again.</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: sentRequests
                    });
                }
            });
    }

    /**
     * Display sent friend requests from cache
     */
    displaySentRequests() {
        console.log('Displaying sent friend requests from cache:', this.sentRequests);

        const sentRequests = document.getElementById('sentRequests');
        if (!sentRequests) return;

        if (!this.sentRequests || this.sentRequests.length === 0) {
            sentRequests.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center text-slate-500">
                        <i data-lucide="send" class="h-4 w-4 mr-2"></i>
                        <span class="text-sm">No sent requests</span>
                    </div>
                </div>
            `;
        } else {
            sentRequests.innerHTML = '';

            // Add each request to the list
            this.sentRequests.forEach(request => {
                const requestItem = document.createElement('div');
                requestItem.className = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2';
                requestItem.setAttribute('data-user-id', request.id);

                // Create user avatar
                let avatarHtml = '';
                if (request.profile_picture) {
                    avatarHtml = `<img src="${request.profile_picture}" alt="${request.username}" class="w-8 h-8 rounded-full mr-3 object-cover">`;
                } else {
                    const initials = this.getInitials(request.display_name || request.username);
                    avatarHtml = `
                        <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3">
                            ${initials}
                        </div>
                    `;
                }

                requestItem.innerHTML = `
                    <div class="flex items-center">
                        ${avatarHtml}
                        <div class="flex-grow">
                            <div class="text-sm font-medium text-slate-200">${request.display_name || request.username}</div>
                            <div class="text-xs text-slate-400">@${request.username}</div>
                        </div>
                        <div>
                            <button class="px-3 py-1 bg-slate-600 hover:bg-slate-500 text-slate-300 rounded-md text-xs cancel-btn" data-user-id="${request.id}">
                                <i data-lucide="x" class="h-3 w-3 inline-block mr-1"></i>
                                Cancel
                            </button>
                        </div>
                    </div>
                `;

                sentRequests.appendChild(requestItem);

                // Add event listener for cancel button
                const cancelBtn = requestItem.querySelector('.cancel-btn');
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', () => {
                        this.cancelFriendRequest(request.id);
                    });
                }
            });
        }

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: sentRequests
            });
        }
    }

    /**
     * Show Remove Friend modal
     */
    showRemoveFriendModal(friendId) {
        const friend = this.friends.find(f => f.id === friendId);
        if (!friend) return;

        const removeFriendName = document.getElementById('removeFriendName');
        if (removeFriendName) {
            removeFriendName.textContent = friend.display_name || friend.username;
        }

        // Store the friend ID for the remove action
        this.friendToRemove = friendId;

        this.openModal('removeFriendModal');
    }

    /**
     * Show Change Group Profile Picture modal
     */
    showChangeGroupProfilePictureModal(chat) {
        // Create the modal if it doesn't exist
        let modal = document.getElementById('changeGroupProfilePictureModal');

        if (!modal) {
            // Create the modal element
            modal = document.createElement('div');
            modal.id = 'changeGroupProfilePictureModal';
            modal.className = 'modal';
            modal.setAttribute('aria-hidden', 'true');

            // Create the modal content
            modal.innerHTML = `
                <div class="modal-overlay"></div>
                <div class="modal-container">
                    <div class="modal-header">
                        <h3>Change Group Profile Picture</h3>
                        <button class="modal-close-btn">
                            <i data-lucide="x" class="h-4 w-4"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="text-sm text-slate-400 mb-4">Enter a URL for the new profile picture or upload an image.</p>

                        <div class="mb-4">
                            <label for="profilePictureUrl" class="block text-sm font-medium text-slate-300 mb-1">Image URL</label>
                            <input type="text" id="profilePictureUrl" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-200 focus:outline-none focus:ring-1 focus:ring-cyan-500" placeholder="https://example.com/image.jpg">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-slate-300 mb-1">Or upload an image</label>
                            <div class="flex items-center">
                                <label for="profilePictureFile" class="flex items-center justify-center px-4 py-2 bg-slate-700 border border-slate-600 rounded-md cursor-pointer hover:bg-slate-600 transition-colors">
                                    <i data-lucide="upload" class="h-4 w-4 mr-2"></i>
                                    <span>Choose file</span>
                                </label>
                                <input type="file" id="profilePictureFile" class="hidden" accept="image/*">
                                <span id="selectedFileName" class="ml-2 text-sm text-slate-400">No file selected</span>
                            </div>
                        </div>

                        <div id="profilePicturePreview" class="hidden mb-4">
                            <label class="block text-sm font-medium text-slate-300 mb-1">Preview</label>
                            <div class="w-24 h-24 rounded-full overflow-hidden bg-slate-700 flex items-center justify-center">
                                <img id="previewImage" src="" alt="Preview" class="w-full h-full object-cover">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary mr-2" id="cancelChangeProfilePictureBtn">Cancel</button>
                        <button class="btn btn-primary" id="confirmChangeProfilePictureBtn">Save Changes</button>
                    </div>
                </div>
            `;

            // Add the modal to the document
            document.body.appendChild(modal);

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: modal
                });
            }

            // Add event listeners for the file input
            const fileInput = document.getElementById('profilePictureFile');
            const fileNameDisplay = document.getElementById('selectedFileName');
            const previewContainer = document.getElementById('profilePicturePreview');
            const previewImage = document.getElementById('previewImage');
            const urlInput = document.getElementById('profilePictureUrl');

            if (fileInput && fileNameDisplay && previewContainer && previewImage) {
                fileInput.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        fileNameDisplay.textContent = file.name;

                        // Create a preview
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            previewImage.src = e.target.result;
                            previewContainer.classList.remove('hidden');

                            // Clear the URL input since we're using a file
                            if (urlInput) {
                                urlInput.value = '';
                            }
                        };
                        reader.readAsDataURL(file);
                    } else {
                        fileNameDisplay.textContent = 'No file selected';
                        previewContainer.classList.add('hidden');
                    }
                });
            }

            // Add event listener for URL input
            if (urlInput && previewContainer && previewImage) {
                urlInput.addEventListener('input', (e) => {
                    const url = e.target.value.trim();
                    if (url) {
                        // Show preview for the URL
                        previewImage.src = url;
                        previewContainer.classList.remove('hidden');

                        // Clear the file input since we're using a URL
                        if (fileInput && fileNameDisplay) {
                            fileInput.value = '';
                            fileNameDisplay.textContent = 'No file selected';
                        }
                    } else {
                        previewContainer.classList.add('hidden');
                    }
                });
            }
        }

        // Set up the modal for this specific chat
        const confirmBtn = document.getElementById('confirmChangeProfilePictureBtn');
        const cancelBtn = document.getElementById('cancelChangeProfilePictureBtn');
        const closeBtn = modal.querySelector('.modal-close-btn');

        // Remove any existing event listeners
        if (confirmBtn) {
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

            // Add click event to change profile picture
            newConfirmBtn.addEventListener('click', () => {
                this.changeGroupProfilePicture(chat);
            });
        }

        // Add click events to close the modal
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeModal('changeGroupProfilePictureModal');
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal('changeGroupProfilePictureModal');
            });
        }

        // Open the modal
        this.openModal('changeGroupProfilePictureModal');
    }

    /**
     * Change the group profile picture
     */
    changeGroupProfilePicture(chat) {
        // Get the profile picture URL or file
        const urlInput = document.getElementById('profilePictureUrl');
        const fileInput = document.getElementById('profilePictureFile');

        let profilePictureUrl = '';

        if (urlInput && urlInput.value.trim()) {
            // Use the URL directly
            profilePictureUrl = urlInput.value.trim();

            // Update the cached group chat data first
            if (this.groupChats) {
                const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                if (chatIndex !== -1) {
                    this.groupChats[chatIndex].profile_picture = profilePictureUrl;
                    // Update the chat reference to use the updated object
                    chat = this.groupChats[chatIndex];
                }
            }

            // Update the UI immediately to show the new profile picture
            this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, profilePictureUrl);

            // Close the modal immediately for better UX
            this.closeModal('changeGroupProfilePictureModal');

            // Then send the update to the server
            this.updateGroupProfilePicture(chat, profilePictureUrl);
        } else if (fileInput && fileInput.files && fileInput.files[0]) {
            // Upload the file first
            const file = fileInput.files[0];

            // Show loading notification
            this.showNotification('Uploading image...', 'info');

            // Create a FormData object to send the file
            const formData = new FormData();
            formData.append('file', file);

            // Create a temporary URL for the file to show immediately
            const tempUrl = URL.createObjectURL(file);

            // Store the original profile picture for potential rollback
            const originalProfilePicture = chat.profile_picture;

            // Update the cached group chat data first with the temporary URL
            if (this.groupChats) {
                const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                if (chatIndex !== -1) {
                    this.groupChats[chatIndex].profile_picture = tempUrl;
                    // Update the chat reference to use the updated object
                    chat = this.groupChats[chatIndex];
                }
            }

            // Update the UI immediately with the temporary URL
            this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, tempUrl);

            // Close the modal immediately for better UX
            this.closeModal('changeGroupProfilePictureModal');

            // Upload the file
            fetch('/api/upload', {
                method: 'POST',
                body: formData,
                // Don't set Content-Type header - the browser will set it with the boundary parameter
                headers: {
                    // No Content-Type header for multipart/form-data
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                // Use the uploaded file URL
                profilePictureUrl = data.url;

                // Update the cached group chat data with the permanent URL
                if (this.groupChats) {
                    const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                    if (chatIndex !== -1) {
                        this.groupChats[chatIndex].profile_picture = profilePictureUrl;
                        // Update the chat reference to use the updated object
                        chat = this.groupChats[chatIndex];
                    }
                }

                // Update the UI again with the permanent URL
                this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, profilePictureUrl);

                // Then send the update to the server
                this.updateGroupProfilePicture(chat, profilePictureUrl);

                // Revoke the temporary URL to free up memory
                URL.revokeObjectURL(tempUrl);
            })
            .catch(error => {
                console.error('Error uploading image:', error);
                this.showNotification('Error uploading image. Please try again.', 'error');

                // Revert to the original profile picture if there was an error
                if (originalProfilePicture) {
                    // Update the cached group chat data back to the original
                    if (this.groupChats) {
                        const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                        if (chatIndex !== -1) {
                            this.groupChats[chatIndex].profile_picture = originalProfilePicture;
                        }
                    }

                    this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, originalProfilePicture);
                } else {
                    // If there was no original profile picture, remove the temporary one
                    // Update the cached group chat data to remove the profile picture
                    if (this.groupChats) {
                        const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                        if (chatIndex !== -1) {
                            this.groupChats[chatIndex].profile_picture = null;
                        }
                    }

                    const activeFriendAvatar = document.getElementById('activeFriendAvatar');
                    if (activeFriendAvatar && this.currentGroupChatId === chat.chat_id) {
                        const avatarContent = activeFriendAvatar.querySelector('.group-chat-avatar');
                        if (avatarContent) {
                            avatarContent.innerHTML = `
                                <i data-lucide="users-round" class="h-5 w-5"></i>
                                <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer" id="changeGroupPicBtn">
                                    <i data-lucide="camera" class="h-4 w-4 text-white"></i>
                                </div>
                            `;

                            // Initialize Lucide icons
                            if (typeof lucide !== 'undefined') {
                                lucide.createIcons({
                                    attrs: {
                                        'stroke-width': '2',
                                        'class': 'icon'
                                    },
                                    root: avatarContent
                                });
                            }
                        }
                    }
                }

                // Revoke the temporary URL to free up memory
                URL.revokeObjectURL(tempUrl);
            });
        } else {
            this.showNotification('Please enter a URL or select an image file', 'warning');
            return;
        }
    }

    /**
     * Update the group profile picture with the provided URL
     */
    updateGroupProfilePicture(chat, profilePictureUrl) {
        // Show loading notification
        this.showNotification('Updating group profile picture...', 'info');

        // Close the modal if it's still open
        this.closeModal('changeGroupProfilePictureModal');

        // Store the old profile picture URL to send to the server for deletion
        // Ensure we always have a value for the old profile picture if it exists
        const oldProfilePictureUrl = chat.profile_picture || null;

        // Log detailed information about the profile picture update
        console.log('%c[GROUP CHAT IMAGE] Updating group profile picture:', 'color: red; font-weight: bold;');
        console.log('%c[GROUP CHAT IMAGE] Chat ID:', 'color: red;', chat.chat_id);
        console.log('%c[GROUP CHAT IMAGE] Old Picture URL:', 'color: red;', oldProfilePictureUrl);
        console.log('%c[GROUP CHAT IMAGE] New Picture URL:', 'color: red;', profilePictureUrl);

        // Immediately update the UI with the new profile picture for the current user
        // This provides immediate feedback while the server request is processing
        const cachedProfilePictureUrl = `${profilePictureUrl}${profilePictureUrl.includes('?') ? '&' : '?'}cb=${Date.now()}`;
        this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, cachedProfilePictureUrl);

        // Prepare the request data
        const requestData = {
            profile_picture: profilePictureUrl,
            old_profile_picture: oldProfilePictureUrl
        };

        // Log the exact data being sent to the server
        console.log('%c[GROUP CHAT IMAGE] Sending data to server:', 'color: red;', JSON.stringify(requestData, null, 2));

        // Send the update to the server
        fetch(`/api/friends/group-chat/${chat.chat_id}/profile-picture`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                console.log('%c[GROUP CHAT IMAGE] Error response from server:', 'color: red;', response.status, response.statusText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            console.log('%c[GROUP CHAT IMAGE] Server response OK:', 'color: green;', response.status);
            return response.json();
        })
        .then(data => {
            console.log('%c[GROUP CHAT IMAGE] Server response data:', 'color: green;', data);
            if (data.error) {
                throw new Error(data.error);
            }

            // Show success notification
            this.showNotification('Group profile picture updated successfully!', 'success');

            // The server will emit the websocket event to all members including the current user
            // We don't need to emit it here as the backend already does this
            console.log('Group profile picture updated successfully on the server');
        })
        .catch(error => {
            console.error('Error updating group profile picture:', error);
            this.showNotification('Error updating group profile picture. Please try again.', 'error');

            // If there was an error, revert to the original profile picture
            if (chat.profile_picture && chat.profile_picture !== profilePictureUrl) {
                this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, chat.profile_picture);
            }
        });
    }

    /**
     * Update the UI with the new group profile picture
     */
    updateGroupChatUIWithNewProfilePicture(chatId, profilePictureUrl) {
        console.log(`Updating UI for group chat ${chatId} with profile picture:`, profilePictureUrl);

        // Skip if no profile picture URL is provided
        if (!profilePictureUrl) {
            console.log(`No profile picture URL provided for group chat ${chatId}`);
            return;
        }

        // Update the avatar in the chat header
        const activeFriendAvatar = document.getElementById('activeFriendAvatar');
        if (activeFriendAvatar && this.currentGroupChatId === chatId) {
            const avatarContent = activeFriendAvatar.querySelector('.group-chat-avatar');
            if (avatarContent) {
                // Replace the icon with the image
                avatarContent.innerHTML = `
                    <img src="${profilePictureUrl}" alt="Group Chat" class="rounded-full w-full h-full object-cover shadow-none">
                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer" id="changeGroupPicBtn">
                        <i data-lucide="camera" class="h-4 w-4 text-white"></i>
                    </div>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: avatarContent
                    });
                }

                // Re-add the event listener for the change button
                const changeGroupPicBtn = document.getElementById('changeGroupPicBtn');
                if (changeGroupPicBtn) {
                    // Remove any existing event listeners by cloning the node
                    const newChangeGroupPicBtn = changeGroupPicBtn.cloneNode(true);
                    changeGroupPicBtn.parentNode.replaceChild(newChangeGroupPicBtn, changeGroupPicBtn);

                    // Add new event listener
                    newChangeGroupPicBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const chat = this.groupChats.find(c => c.chat_id === chatId);
                        if (chat) {
                            this.showChangeGroupProfilePictureModal(chat);
                        }
                    });

                    // Initialize Lucide icons for the button
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: newChangeGroupPicBtn
                        });
                    }
                }
            }
        }

        // Update the avatar in the sidebar
        const groupChatItem = document.querySelector(`.group-chat-item[data-chat-id="${chatId}"]`);
        if (groupChatItem) {
            const avatarContainer = groupChatItem.querySelector('.group-chat-avatar');
            if (avatarContainer) {
                // Use the provided URL which may already include a cache-busting parameter
                avatarContainer.innerHTML = `
                    <img src="${profilePictureUrl}" alt="Group Chat" class="w-full h-full object-cover">
                `;
            }
        }

        // Extract the base URL without cache-busting parameters for storage
        let baseProfilePictureUrl = profilePictureUrl;
        if (baseProfilePictureUrl.includes('?')) {
            // Remove cache-busting parameters for storage
            const urlParts = baseProfilePictureUrl.split('?');
            if (urlParts.length > 1) {
                const params = new URLSearchParams(urlParts[1]);
                // Remove cache-busting parameters (t, cb, timestamp, etc.)
                params.delete('t');
                params.delete('cb');
                params.delete('timestamp');

                // Reconstruct the URL with remaining parameters
                const remainingParams = params.toString();
                baseProfilePictureUrl = urlParts[0] + (remainingParams ? `?${remainingParams}` : '');
            }
        }

        // Update the cached group chat data with the base URL (without cache-busting)
        if (this.groupChats) {
            const chatIndex = this.groupChats.findIndex(c => c.chat_id === chatId);
            if (chatIndex !== -1) {
                this.groupChats[chatIndex].profile_picture = baseProfilePictureUrl;
            }
        }
    }

    /**
     * Show Delete Group Chat modal
     */
    showDeleteGroupChatModal(chat) {
        const deleteGroupChatName = document.getElementById('deleteGroupChatName');
        if (deleteGroupChatName) {
            deleteGroupChatName.textContent = chat.name;
        }

        // Store the chat ID for deletion
        this.groupChatToDelete = chat.chat_id;

        this.openModal('deleteGroupChatModal');
    }

    /**
     * Show Group Info modal
     * @param {Object} chat - The group chat object
     */
    showGroupInfoModal(chat) {
        console.log('Showing group info modal for chat:', chat);

        // Get modal elements
        const groupCreationDate = document.getElementById('groupCreationDate');
        const groupMembersList = document.getElementById('groupMembersList');
        const exitGroupBtn = document.getElementById('exitGroupBtn');
        const deleteGroupBtn = document.getElementById('deleteGroupBtn');

        if (!groupCreationDate || !groupMembersList || !exitGroupBtn || !deleteGroupBtn) {
            console.error('Could not find all required elements for group info modal');
            return;
        }

        // Format and display creation date
        if (chat.created_at) {
            const creationDate = new Date(chat.created_at);
            groupCreationDate.textContent = creationDate.toLocaleDateString() + ' ' + creationDate.toLocaleTimeString();
        } else {
            groupCreationDate.textContent = 'Unknown';
        }

        // Show loading indicator for members list
        groupMembersList.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading members...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: groupMembersList
            });
        }

        // Load group chat details including members
        fetch(`/api/friends/group-chat/${chat.chat_id}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(groupChatDetails => {
                console.log('Loaded group chat details:', groupChatDetails);

                const members = groupChatDetails.members || [];

                if (members.length === 0) {
                    groupMembersList.innerHTML = `
                        <div class="empty-state py-4">
                            <p class="text-sm text-slate-400 text-center">No members found in this group chat.</p>
                        </div>
                    `;
                    return;
                }

                // Display members
                groupMembersList.innerHTML = members.map(member => `
                    <div class="group-member-item flex items-center p-2 rounded-md">
                        <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3 overflow-hidden">
                            ${member.profile_picture ?
                                `<img src="${member.profile_picture}" alt="${member.display_name || member.username}" class="w-full h-full object-cover">` :
                                `<span>${(member.display_name || member.username).charAt(0).toUpperCase()}</span>`
                            }
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium text-slate-200">
                                ${member.display_name || member.username}
                                ${member.id === groupChatDetails.creator_id ?
                                    '<span class="ml-2 text-xs bg-purple-900/30 text-purple-400 px-1.5 py-0.5 rounded-full border border-purple-800/50">Creator</span>' :
                                    ''
                                }
                            </div>
                            <div class="text-xs text-slate-400">@${member.username}</div>
                        </div>
                    </div>
                `).join('');

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: groupMembersList
                    });
                }
            })
            .catch(error => {
                console.error('Error loading group chat members:', error);
                groupMembersList.innerHTML = `
                    <div class="empty-state py-4">
                        <div class="flex items-center justify-center text-red-400">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <p class="text-sm">Error loading members. Please try again.</p>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: groupMembersList
                    });
                }
            });

        // Set up exit button
        exitGroupBtn.onclick = () => {
            this.closeModal('groupInfoModal');
            this.leaveGroupChat(chat);
        };

        // Set up delete button (only visible for creator)
        const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
        if (currentUserId && chat.creator_id === currentUserId) {
            deleteGroupBtn.classList.remove('hidden');
            deleteGroupBtn.onclick = () => {
                this.closeModal('groupInfoModal');
                this.showDeleteGroupChatModal(chat);
            };
        } else {
            deleteGroupBtn.classList.add('hidden');
        }

        // Open the modal
        this.openModal('groupInfoModal');
    }

    /**
     * Handle group chat created event from WebSocket
     */
    handleGroupChatCreated(groupChat) {
        // Check if we already have this group chat
        if (this.groupChats.some(chat => chat.chat_id === groupChat.chat_id)) {
            console.log('Group chat already exists in the list, skipping');
            return;
        }

        // Add the new group chat to our list
        this.groupChats.push(groupChat);

        // Update the UI
        this.displayGroupChats(this.groupChats);

        // Show notification
        this.showNotification(`You've been added to a new group chat: ${groupChat.name}`, 'info');
    }

    /**
     * Handle group chat deleted event from WebSocket
     */
    handleGroupChatDeleted(data) {
        // Check if we have this group chat
        const chatIndex = this.groupChats.findIndex(chat => chat.chat_id === data.chat_id);
        if (chatIndex === -1) {
            console.log('Group chat not found in the list, skipping');
            return;
        }

        // Get the chat name for notification
        const chatName = data.name;

        // Remove the group chat from our list
        this.groupChats.splice(chatIndex, 1);

        // Update the UI
        this.displayGroupChats(this.groupChats);

        // If we're currently viewing this chat, reset the view
        if (this.currentGroupChatId === data.chat_id) {
            // Reset current chat state
            this.currentChatId = null;
            this.currentFriendId = null;
            this.currentGroupChatId = null;
            this.isGroupChat = false;

            // Show welcome message and hide chat container
            const welcomeMessage = document.getElementById('welcomeMessage');
            const messagesContainer = document.getElementById('messagesContainer');
            const activeFriendName = document.getElementById('activeFriendName');
            const activeFriendAvatar = document.getElementById('activeFriendAvatar');

            if (welcomeMessage) {
                welcomeMessage.style.display = 'flex';
            }

            if (messagesContainer) {
                messagesContainer.style.display = 'none';
            }

            if (activeFriendName) {
                activeFriendName.textContent = '';
            }

            if (activeFriendAvatar) {
                activeFriendAvatar.innerHTML = '';
            }

            // Hide buttons
            const exitGroupChatBtn = document.getElementById('exitGroupChatBtn');
            if (exitGroupChatBtn) {
                exitGroupChatBtn.classList.add('hidden');
            }

            const deleteGroupChatBtn = document.getElementById('deleteGroupChatBtn');
            if (deleteGroupChatBtn) {
                deleteGroupChatBtn.classList.add('hidden');
            }

            // Disable message input
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');

            if (messageInput) {
                messageInput.disabled = true;
            }

            if (sendButton) {
                sendButton.disabled = true;
            }
        }

        // Show notification
        if (data.deleted_by) {
            const deletedBy = data.deleted_by.display_name || data.deleted_by.username;
            this.showNotification(`Group chat "${chatName}" was deleted by ${deletedBy}`, 'info');
        } else {
            this.showNotification(`Group chat "${chatName}" was deleted`, 'info');
        }
    }

    /**
     * Delete a group chat (creator only)
     */
    deleteGroupChat() {
        if (!this.groupChatToDelete) {
            this.showNotification('No group chat selected to delete', 'error');
            return;
        }

        console.log('Deleting group chat with ID:', this.groupChatToDelete);

        fetch(`/api/friends/group-chat/${this.groupChatToDelete}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Delete group chat response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else {
                this.showNotification('Group chat deleted successfully', 'success');

                // Reset current chat state
                this.currentChatId = null;
                this.currentFriendId = null;
                this.currentGroupChatId = null;
                this.isGroupChat = false;

                // Hide the delete button
                const deleteGroupChatBtn = document.getElementById('deleteGroupChatBtn');
                if (deleteGroupChatBtn) {
                    deleteGroupChatBtn.classList.add('hidden');
                }

                // Hide the exit button
                const exitGroupChatBtn = document.getElementById('exitGroupChatBtn');
                if (exitGroupChatBtn) {
                    exitGroupChatBtn.classList.add('hidden');
                }

                // Group members container is now hidden via CSS

                // Show welcome message and hide chat container
                const welcomeMessage = document.getElementById('welcomeMessage');
                const messagesContainer = document.getElementById('messagesContainer');
                const activeFriendName = document.getElementById('activeFriendName');
                const activeFriendAvatar = document.getElementById('activeFriendAvatar');

                if (welcomeMessage) {
                    welcomeMessage.style.display = 'flex';
                }

                if (messagesContainer) {
                    messagesContainer.style.display = 'none';
                }

                if (activeFriendName) {
                    activeFriendName.textContent = '';
                }

                if (activeFriendAvatar) {
                    activeFriendAvatar.innerHTML = '';
                }

                // Disable message input
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');

                if (messageInput) {
                    messageInput.disabled = true;
                }

                if (sendButton) {
                    sendButton.disabled = true;
                }

                // Remove the group chat from the UI
                const groupChatItem = document.querySelector(`.group-chat-item[data-chat-id="${this.groupChatToDelete}"]`);
                if (groupChatItem) {
                    groupChatItem.remove();
                }

                // Reload the group chats to update the UI
                this.loadGroupChats();

                // Clear the group chat to delete
                this.groupChatToDelete = null;
            }
        })
        .catch(error => {
            console.error('Error deleting group chat:', error);
            this.showNotification('Error deleting group chat. Please try again.', 'error');
        });
    }

    /**
     * Remove a friend
     */
    removeFriend() {
        if (!this.friendToRemove) {
            this.showNotification('No friend selected to remove', 'error');
            return;
        }

        console.log('Removing friend with ID:', this.friendToRemove);

        fetch('/api/friends/remove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                friend_id: this.friendToRemove
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Remove friend response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else {
                this.showNotification('Friend removed successfully', 'success');

                // Remove friend from the list
                this.friends = this.friends.filter(f => f.id !== this.friendToRemove);

                // Update the UI
                this.displayFriends(this.friends);

                // Clear current chat if it was with this friend
                if (this.currentFriendId === this.friendToRemove) {
                    this.currentFriendId = null;
                    this.currentChatId = null;

                    // Hide chat container and show welcome message
                    const welcomeMessage = document.getElementById('welcomeMessage');
                    const messagesContainer = document.getElementById('messagesContainer');
                    const activeFriendName = document.getElementById('activeFriendName');
                    const activeFriendAvatar = document.getElementById('activeFriendAvatar');
                    const inviteToLiveBtn = document.getElementById('inviteToLiveBtn');
                    const removeFriendBtn = document.getElementById('removeFriendBtn');

                    if (welcomeMessage) {
                        welcomeMessage.style.display = 'flex';
                    }

                    if (messagesContainer) {
                        messagesContainer.style.display = 'none';
                    }

                    if (activeFriendName) {
                        activeFriendName.textContent = '';
                    }

                    if (activeFriendAvatar) {
                        activeFriendAvatar.innerHTML = '';
                    }

                    if (inviteToLiveBtn) {
                        inviteToLiveBtn.classList.add('hidden');
                    }

                    if (removeFriendBtn) {
                        removeFriendBtn.classList.add('hidden');
                    }

                    // Disable message input
                    const messageInput = document.getElementById('messageInput');
                    const sendButton = document.getElementById('sendButton');

                    if (messageInput) {
                        messageInput.disabled = true;
                    }

                    if (sendButton) {
                        sendButton.disabled = true;
                    }
                }

                // Clear the friend to remove
                this.friendToRemove = null;
            }
        })
        .catch(error => {
            console.error('Error removing friend:', error);
            this.showNotification('Error removing friend. Please try again.', 'error');
        });
    }

    /**
     * Show Invite to Live modal
     */
    showInviteToLiveModal(friendId) {
        const friend = this.friends.find(f => f.id === friendId);
        if (!friend) return;

        const inviteFriendName = document.getElementById('inviteFriendName');
        if (inviteFriendName) {
            inviteFriendName.textContent = friend.display_name || friend.username;
        }

        // Store the friend ID for the invite action
        this.friendToInvite = friendId;

        this.openModal('inviteToLiveModal');
    }

    /**
     * Invite a friend to a live chat
     */
    inviteToLive() {
        if (!this.friendToInvite) {
            this.showNotification('No friend selected to invite', 'error');
            return;
        }

        console.log('Inviting friend with ID:', this.friendToInvite);

        // Close the invite modal
        this.closeModal('inviteToLiveModal');

        // Find the friend object
        const friend = this.friends.find(f => f.id === this.friendToInvite);
        if (!friend || !friend.chat_id) {
            this.showNotification('Friend information not found', 'error');
            return;
        }

        // Show loading notification
        this.showNotification('Creating a private room for you and your friend...', 'info');

        // Create a new room for the live chat
        fetch('/api/live/room', {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Create room response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
                return;
            }

            const roomId = data.room_id;
            const currentUsername = document.querySelector('meta[name="username"]')?.getAttribute('content') || 'Your friend';

            // Create the full room URL with the room ID and origin
            const fullRoomUrl = `${window.location.origin}/live/room/${roomId}`;

            console.log('Room URL for invitation:', fullRoomUrl);

            // Debug the URL to make sure it's correct
            console.log('URL components:', {
                origin: window.location.origin,
                roomId: roomId,
                fullUrl: fullRoomUrl
            });

            // Create a system message with the invitation link
            const systemMessage = `${currentUsername} has invited you to a live chat! Click the button below to join the conversation.`;

            // Also emit the socket event directly for immediate notification
            if (this.socket && this.socket.connected) {
                console.log('Emitting friend_invite_to_live event via socket');
                this.socket.emit('friend_invite_to_live', {
                    friend_id: this.friendToInvite,
                    room_id: roomId,
                    room_url: fullRoomUrl // Include the full room URL in the socket event
                });
            } else {
                console.log('Socket not connected, skipping direct socket notification');
            }

            // Send a system message to the chat
            fetch(`/api/friends/chat/${friend.chat_id}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: systemMessage,
                    is_system: true,
                    message_type: 'invite',
                    room_id: roomId,
                    room_url: fullRoomUrl
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(messageData => {
                console.log('System message sent:', messageData);
                this.showNotification('Invitation sent successfully', 'success');

                // Open the live chat in a new tab
                window.open(fullRoomUrl, '_blank');

                // Clear the friend to invite
                this.friendToInvite = null;
            })
            .catch(error => {
                console.error('Error sending system message:', error);
                this.showNotification('Error sending invitation message, but room was created', 'warning');

                // Still open the live chat even if the message fails
                window.open(fullRoomUrl, '_blank');

                // Clear the friend to invite
                this.friendToInvite = null;
            });
        })
        .catch(error => {
            console.error('Error creating room:', error);
            this.showNotification('Error creating room. Please try again.', 'error');
        });
    }

    /**
     * Open a modal
     */
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');

            // Add event listener to close modal when clicking outside
            setTimeout(() => {
                const closeOnClickOutside = (e) => {
                    if (e.target === modal) {
                        this.closeModal(modalId);
                        document.removeEventListener('click', closeOnClickOutside);
                    }
                };
                document.addEventListener('click', closeOnClickOutside);
            }, 100);
        }
    }

    /**
     * Show a modal with animation
     * @param {HTMLElement} modal - The modal element to show
     */
    showModal(modal) {
        if (!modal) return;

        // Show modal with animation
        modal.classList.remove('hidden');
        const modalContent = modal.querySelector('.modal-content');

        if (modalContent) {
            // Force a reflow to ensure the initial state is applied
            void modalContent.offsetWidth;

            // Apply the animated state
            modalContent.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        }

        // Add backdrop blur
        document.body.classList.add('modal-open');

        // Add event listener to close when clicking outside
        const closeOnOutsideClick = (e) => {
            if (e.target === modal) {
                this.closeModal(modal.id);
                modal.removeEventListener('click', closeOnOutsideClick);
            }
        };

        modal.addEventListener('click', closeOnOutsideClick);

        // Add event listener to close buttons
        const closeButtons = modal.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.closeModal(modal.id);
            });
        });
    }

    /**
     * Close a modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            // Reset animation
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.95)';
            }

            // Hide modal after animation
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 200);

            // Remove backdrop blur
            document.body.classList.remove('modal-open');

            // Reset any form fields in the modal
            const inputs = modal.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.value = '';
            });
        }
    }

    /**
     * Search for users
     */
    searchUsers() {
        const searchInput = document.getElementById('userSearchInput');
        const searchResults = document.getElementById('searchResults');

        console.log('Search input element:', searchInput);
        console.log('Search results element:', searchResults);

        if (!searchInput || !searchResults) {
            console.error('Search input or results element not found');
            return;
        }

        const query = searchInput.value.trim();
        console.log('Search query:', query);

        // Debug: Log all input fields to see if there are multiple with the same ID
        const allInputs = document.querySelectorAll('input');
        console.log('All input fields:', allInputs.length);
        const userSearchInputs = document.querySelectorAll('#userSearchInput');
        console.log('All userSearchInput fields:', userSearchInputs.length);

        if (userSearchInputs.length > 1) {
            console.warn('Multiple userSearchInput fields found! This could cause issues.');
            userSearchInputs.forEach((input, index) => {
                console.log(`Input #${index}:`, input.value);
            });
        }

        if (query.length < 3) {
            this.showNotification('Please enter at least 3 characters to search', 'info');
            return;
        }

        // Show loading state
        searchResults.innerHTML = `
            <div class="p-3 text-center text-slate-400 text-sm flex items-center justify-center">
                <i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>
                Searching...
            </div>
        `;
        searchResults.classList.remove('hidden');

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: searchResults
            });
        }

        // First try the admin search API as it's more reliable
        const adminSearchUrl = `/api/admin/search-users?query=${encodeURIComponent(query)}`;
        console.log('Admin search URL:', adminSearchUrl);

        // Debug: Check if the request is actually being sent
        console.log('Network requests before fetch:', performance.getEntriesByType('resource').length);

        fetch(adminSearchUrl)
            .then(response => {
                console.log('Admin search response status:', response.status);
                console.log('Network requests after fetch:', performance.getEntriesByType('resource').length);

                // Debug: Check all network requests
                const resources = performance.getEntriesByType('resource');
                const searchRequests = resources.filter(r => r.name.includes('search-users'));
                console.log('Search API requests:', searchRequests.length);
                searchRequests.forEach((req, i) => {
                    console.log(`Request #${i}:`, req.name);
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(users => {
                console.log('Admin search results data:', users);

                // Clear previous results
                searchResults.innerHTML = '';

                if (users.length === 0) {
                    searchResults.innerHTML = `
                        <div class="p-3 text-center text-slate-400 text-sm">
                            No users found matching "${query}"
                        </div>
                    `;
                } else {
                    // Add each user to the results
                    users.forEach(user => {
                        const userItem = document.createElement('div');
                        userItem.className = 'p-2 hover:bg-slate-600 cursor-pointer flex items-center justify-between group';
                        userItem.innerHTML = `
                            <div class="flex items-center space-x-2 flex-grow overflow-hidden">
                                <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center overflow-hidden flex-shrink-0">
                                    ${user.profile_picture ?
                                        `<img src="${user.profile_picture}" alt="${user.username}" class="w-full h-full object-cover">` :
                                        `<i data-lucide="user" class="h-4 w-4 text-slate-300"></i>`
                                    }
                                </div>
                                <div class="min-w-0 flex-grow">
                                    <div class="text-sm font-medium text-slate-200 truncate">${user.username || 'Unknown User'}</div>
                                    <div class="text-xs text-slate-400 truncate">${user.email}</div>
                                </div>
                            </div>
                            <button class="add-friend-hover-btn ml-2 px-2 py-1 bg-cyan-600 hover:bg-cyan-500 text-white rounded text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity flex items-center flex-shrink-0">
                                <i data-lucide="user-plus" class="h-3 w-3 mr-1"></i>
                                Add
                            </button>
                        `;

                        // Add click event to select the user (but not when clicking the Add button)
                        userItem.addEventListener('click', (e) => {
                            // Skip if clicking the Add button
                            if (e.target.closest('.add-friend-hover-btn')) {
                                return;
                            }

                            // Convert to the format expected by displaySelectedUser
                            const formattedUser = {
                                id: user.id,
                                username: user.username,
                                display_name: user.username,
                                email: user.email,
                                profile_picture: user.profile_picture,
                                is_friend: false,
                                request_sent: false,
                                request_received: false
                            };

                            this.displaySelectedUser(formattedUser);
                        });

                        // Add event listener for the Add Friend button
                        const addBtn = userItem.querySelector('.add-friend-hover-btn');
                        if (addBtn) {
                            addBtn.addEventListener('click', (e) => {
                                e.stopPropagation(); // Prevent triggering the parent click event
                                this.sendFriendRequest(user.id);

                                // Update button to show "Request Sent"
                                addBtn.innerHTML = `
                                    <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                                    Sent
                                `;
                                addBtn.classList.remove('bg-cyan-600', 'hover:bg-cyan-500');
                                addBtn.classList.add('bg-slate-600', 'cursor-default');
                                addBtn.disabled = true;

                                // Initialize Lucide icons
                                if (typeof lucide !== 'undefined') {
                                    lucide.createIcons({
                                        attrs: {
                                            'stroke-width': '2',
                                            'class': 'icon'
                                        },
                                        root: addBtn
                                    });
                                }
                            });
                        }

                        searchResults.appendChild(userItem);
                    });
                }

                // Show search results
                searchResults.classList.remove('hidden');

                // Refresh icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            })
            .catch(error => {
                console.error('Error searching users with admin API:', error);

                // If the admin search API fails, try the friends search API as a fallback
                console.log('Trying friends search API as fallback');
                const friendsSearchUrl = `/api/friends/search?q=${encodeURIComponent(query)}`;

                fetch(friendsSearchUrl)
                    .then(response => {
                        console.log('Friends search response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(users => {
                        console.log('Friends search results data:', users);
                        this.displaySearchResults(users);
                    })
                    .catch(error => {
                        console.error('Error searching users with friends API:', error);
                        searchResults.innerHTML = `
                            <div class="p-3 text-center text-red-400 text-sm">
                                Failed to search users. Please try again.
                            </div>
                        `;
                        searchResults.classList.remove('hidden');

                        // Initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons();
                        }
                    });
            });
    }

    /**
     * Display a selected user with an Add Friend button
     */
    displaySelectedUser(user) {
        const searchResults = document.getElementById('searchResults');
        if (!searchResults) return;

        console.log('Displaying selected user:', user);

        // Clear previous results
        searchResults.innerHTML = '';

        // Create user item
        const userItem = document.createElement('div');
        userItem.className = 'bg-slate-700/50 rounded-lg p-4 border border-slate-600/50 mb-2';

        // Create user avatar
        let avatarHtml = '';
        if (user.profile_picture) {
            avatarHtml = `<img src="${user.profile_picture}" alt="${user.username}" class="w-10 h-10 rounded-full mr-3 object-cover">`;
        } else {
            const initials = this.getInitials(user.display_name || user.username);
            avatarHtml = `
                <div class="w-10 h-10 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3">
                    ${initials}
                </div>
            `;
        }

        // Create action button based on friendship status
        let actionButton = '';

        if (user.is_friend) {
            actionButton = `
                <button class="px-4 py-2 bg-slate-600 text-slate-300 rounded-md text-sm font-medium" disabled>
                    <i data-lucide="check" class="h-4 w-4 inline-block mr-1"></i>
                    Friends
                </button>
            `;
        } else if (user.request_sent) {
            actionButton = `
                <button class="px-4 py-2 bg-slate-600 text-slate-300 rounded-md text-sm font-medium" disabled>
                    <i data-lucide="clock" class="h-4 w-4 inline-block mr-1"></i>
                    Request Sent
                </button>
            `;
        } else if (user.request_received) {
            actionButton = `
                <button class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md text-sm font-medium accept-request-btn" data-user-id="${user.id}">
                    <i data-lucide="user-check" class="h-4 w-4 inline-block mr-1"></i>
                    Accept Request
                </button>
            `;
        } else {
            actionButton = `
                <button class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md text-sm font-medium add-friend-btn" data-user-id="${user.id}">
                    <i data-lucide="user-plus" class="h-4 w-4 inline-block mr-1"></i>
                    Add Friend
                </button>
            `;
        }

        userItem.innerHTML = `
            <div class="flex flex-col">
                <div class="flex items-center mb-3">
                    ${avatarHtml}
                    <div class="flex-grow">
                        <div class="text-sm font-medium text-slate-200">${user.display_name || user.username}</div>
                        <div class="text-xs text-slate-400">@${user.username}</div>
                        <div class="text-xs text-slate-400 mt-1">${user.email}</div>
                    </div>
                </div>
                <div class="flex justify-end">
                    ${actionButton}
                </div>
            </div>
        `;

        searchResults.appendChild(userItem);

        // Add event listener to add friend button
        const addFriendBtn = userItem.querySelector('.add-friend-btn');
        if (addFriendBtn) {
            addFriendBtn.addEventListener('click', () => {
                this.sendFriendRequest(user.id);

                // Update button to show request sent
                const parentDiv = addFriendBtn.parentNode;
                parentDiv.innerHTML = `
                    <button class="px-4 py-2 bg-slate-600 text-slate-300 rounded-md text-sm font-medium" disabled>
                        <i data-lucide="clock" class="h-4 w-4 inline-block mr-1"></i>
                        Request Sent
                    </button>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: parentDiv
                    });
                }
            });
        }

        // Add event listener to accept request button
        const acceptRequestBtn = userItem.querySelector('.accept-request-btn');
        if (acceptRequestBtn) {
            acceptRequestBtn.addEventListener('click', () => {
                this.acceptFriendRequest(user.id);

                // Update button to show friends
                const parentDiv = acceptRequestBtn.parentNode;
                parentDiv.innerHTML = `
                    <button class="px-4 py-2 bg-slate-600 text-slate-300 rounded-md text-sm font-medium" disabled>
                        <i data-lucide="check" class="h-4 w-4 inline-block mr-1"></i>
                        Friends
                    </button>
                `;

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: parentDiv
                    });
                }
            });
        }

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: searchResults
            });
        }

        // Show search results
        searchResults.classList.remove('hidden');
    }

    /**
     * Display search results
     */
    displaySearchResults(users) {
        const searchResults = document.getElementById('searchResults');
        if (!searchResults) return;

        console.log('Displaying search results:', users);

        if (users.error) {
            searchResults.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center text-red-400">
                        <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                        <span class="text-sm">${users.error}</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: searchResults
                });
            }

            return;
        }

        if (users.length === 0) {
            searchResults.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center text-slate-500">
                        <i data-lucide="search" class="h-4 w-4 mr-2"></i>
                        <span class="text-sm">No users found</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: searchResults
                });
            }

            return;
        }

        searchResults.innerHTML = '';
        users.forEach(user => {
            console.log('Processing user:', user);

            const userItem = document.createElement('div');
            // We'll set the class later based on friendship status

            // Create user avatar
            let avatarHtml = '';
            if (user.profile_picture) {
                avatarHtml = `<img src="${user.profile_picture}" alt="${user.username}" class="w-8 h-8 rounded-full mr-3 object-cover">`;
            } else {
                const initials = this.getInitials(user.display_name || user.username);
                avatarHtml = `
                    <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3">
                        ${initials}
                    </div>
                `;
            }

            // Create action button based on friendship status
            let actionButton = '';
            let userItemClass = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2';

            if (user.is_friend) {
                actionButton = `
                    <button class="px-2 py-1 bg-slate-600 text-slate-300 rounded text-xs font-medium flex items-center" disabled>
                        <i data-lucide="check" class="h-3 w-3 mr-1"></i>
                        Friends
                    </button>
                `;
                // No cursor-pointer for already friends
                userItemClass = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2';
            } else if (user.request_sent) {
                actionButton = `
                    <button class="px-2 py-1 bg-slate-600 text-slate-300 rounded text-xs font-medium flex items-center" disabled>
                        <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                        Sent
                    </button>
                `;
                // No cursor-pointer for request already sent
                userItemClass = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2';
            } else if (user.request_received) {
                actionButton = `
                    <button class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 text-white rounded text-xs font-medium accept-request-btn flex items-center" data-user-id="${user.id}">
                        <i data-lucide="user-check" class="h-3 w-3 mr-1"></i>
                        Accept
                    </button>
                `;
                // Add cursor-pointer for clickable items
                userItemClass = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2 cursor-pointer hover:bg-slate-700/80 transition-colors';
            } else {
                actionButton = `
                    <button class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 text-white rounded text-xs font-medium add-friend-btn flex items-center" data-user-id="${user.id}">
                        <i data-lucide="user-plus" class="h-3 w-3 mr-1"></i>
                        Add
                    </button>
                `;
                // Add cursor-pointer for clickable items
                userItemClass = 'bg-slate-700/50 rounded-lg p-3 border border-slate-600/50 mb-2 cursor-pointer hover:bg-slate-700/80 transition-colors';
            }

            // Update the user item class and add group for hover effects
            userItem.className = `${userItemClass} group`;

            // For users that can be added as friends, we'll show the button on hover
            // For other states (already friends, request sent/received), we'll show the static button
            const buttonDisplay = (!user.is_friend && !user.request_sent && !user.request_received)
                ? 'opacity-0 group-hover:opacity-100 transition-opacity'
                : '';

            userItem.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center flex-grow overflow-hidden">
                        ${avatarHtml}
                        <div class="flex-grow min-w-0">
                            <div class="text-sm font-medium text-slate-200 truncate">${user.display_name || user.username}</div>
                            <div class="text-xs text-slate-400 truncate">@${user.username}</div>
                        </div>
                    </div>
                    <div class="${buttonDisplay}">
                        ${actionButton}
                    </div>
                </div>
            `;

            searchResults.appendChild(userItem);

            // Add event listener to the entire user item if they're not already friends and no request is sent/received
            if (!user.is_friend && !user.request_sent && !user.request_received) {
                userItem.addEventListener('click', (e) => {
                    // Only trigger if the click wasn't on the button itself (to avoid double triggering)
                    if (!e.target.closest('.add-friend-btn')) {
                        this.sendFriendRequest(user.id);
                    }
                });
            } else if (user.request_received) {
                // If there's a pending request to accept, make the whole item clickable to accept
                userItem.addEventListener('click', (e) => {
                    // Only trigger if the click wasn't on the button itself (to avoid double triggering)
                    if (!e.target.closest('.accept-request-btn')) {
                        this.acceptFriendRequest(user.id);
                    }
                });
            }

            // Add event listener to add friend button
            const addFriendBtn = userItem.querySelector('.add-friend-btn');
            if (addFriendBtn) {
                addFriendBtn.addEventListener('click', () => {
                    this.sendFriendRequest(user.id);
                });
            }

            // Add event listener to accept request button
            const acceptRequestBtn = userItem.querySelector('.accept-request-btn');
            if (acceptRequestBtn) {
                acceptRequestBtn.addEventListener('click', () => {
                    this.acceptFriendRequest(user.id);
                });
            }
        });

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: searchResults
            });
        }
    }

    /**
     * Send a friend request
     */
    sendFriendRequest(userId) {
        console.log('Sending friend request to user ID:', userId);

        fetch('/api/friends/request', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                friend_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Friend request response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else {
                this.showNotification('Friend request sent!', 'success');

                // Update UI to show request sent
                const addFriendBtn = document.querySelector(`.add-friend-btn[data-user-id="${userId}"]`);
                if (addFriendBtn) {
                    const parentDiv = addFriendBtn.parentNode;
                    parentDiv.innerHTML = `
                        <button class="px-3 py-1 bg-slate-600 text-slate-300 rounded-md text-xs" disabled>
                            <i data-lucide="clock" class="h-3 w-3 inline-block mr-1"></i>
                            Request Sent
                        </button>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: parentDiv
                        });
                    }

                    // Find the user details from the search results
                    const userItem = addFriendBtn.closest('.p-2, .bg-slate-700');
                    if (userItem) {
                        const username = userItem.querySelector('.text-sm')?.textContent || 'Unknown User';
                        const email = userItem.querySelector('.text-xs')?.textContent || '';

                        // Create a new request object
                        const newRequest = {
                            id: userId,
                            username: username,
                            display_name: username,
                            email: email,
                            profile_picture: null // We don't have this info from the search
                        };

                        // Add to sent requests cache if it doesn't already exist
                        if (!this.sentRequests.some(req => req.id === userId)) {
                            this.sentRequests.push(newRequest);
                        }
                    }
                }

                // Mark the request as loaded so we don't reload everything
                this.requestsLoaded = true;
            }
        })
        .catch(error => {
            console.error('Error sending friend request:', error);
            this.showNotification('Error sending friend request. Please try again.', 'error');
        });
    }

    /**
     * Accept a friend request
     */
    acceptFriendRequest(userId) {
        console.log('Accepting friend request from user ID:', userId);

        fetch('/api/friends/accept', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                friend_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Accept friend request response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else {
                this.showNotification('Friend request accepted!', 'success');

                // Update the cache - remove the request from pending requests
                this.pendingRequests = this.pendingRequests.filter(request => request.id !== userId);

                // Update the UI
                const requestItem = document.querySelector(`#pendingRequests [data-user-id="${userId}"]`);
                if (requestItem) {
                    requestItem.remove();

                    // Check if there are no more requests
                    const pendingRequests = document.getElementById('pendingRequests');
                    if (pendingRequests && pendingRequests.children.length === 0) {
                        pendingRequests.innerHTML = `
                            <div class="flex justify-center items-center py-4">
                                <div class="flex items-center text-slate-500">
                                    <i data-lucide="inbox" class="h-4 w-4 mr-2"></i>
                                    <span class="text-sm">No pending requests</span>
                                </div>
                            </div>
                        `;

                        // Initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: pendingRequests
                            });
                        }
                    }
                }

                // Reload friends list
                this.loadFriends();
            }
        })
        .catch(error => {
            console.error('Error accepting friend request:', error);
            this.showNotification('Error accepting friend request. Please try again.', 'error');
        });
    }

    /**
     * Reject a friend request
     */
    rejectFriendRequest(userId) {
        console.log('Rejecting friend request from user ID:', userId);

        fetch('/api/friends/reject', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                friend_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Reject friend request response:', data);
            if (data.error) {
                this.showNotification(data.error, 'error');
            } else {
                this.showNotification('Friend request rejected', 'info');

                // Update the cache - remove the request from pending requests
                this.pendingRequests = this.pendingRequests.filter(request => request.id !== userId);

                // Update the UI
                const requestItem = document.querySelector(`#pendingRequests [data-user-id="${userId}"]`);
                if (requestItem) {
                    requestItem.remove();

                    // Check if there are no more requests
                    const pendingRequests = document.getElementById('pendingRequests');
                    if (pendingRequests && pendingRequests.children.length === 0) {
                        pendingRequests.innerHTML = `
                            <div class="flex justify-center items-center py-4">
                                <div class="flex items-center text-slate-500">
                                    <i data-lucide="inbox" class="h-4 w-4 mr-2"></i>
                                    <span class="text-sm">No pending requests</span>
                                </div>
                            </div>
                        `;

                        // Initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: pendingRequests
                            });
                        }
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error rejecting friend request:', error);
            this.showNotification('Error rejecting friend request. Please try again.', 'error');
        });
    }

    /**
     * Cancel a sent friend request
     */
    cancelFriendRequest(userId) {
        console.log('Canceling friend request to user ID:', userId);

        // Create a custom endpoint call that will work for canceling sent requests
        fetch('/api/friends/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                friend_id: userId
            })
        })
        .then(response => {
            // If the cancel endpoint doesn't exist (404) or fails, use the reject endpoint
            if (!response.ok) {
                console.log('Cancel endpoint failed, trying reject endpoint');
                return fetch('/api/friends/reject', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        friend_id: userId
                    })
                });
            }
            return response;
        })
        .then(response => response.json())
        .then(data => {
            console.log('Cancel friend request response:', data);

            // Show success message regardless of the response
            this.showNotification('Friend request canceled', 'info');

            // Update the cache - remove the request from sent requests
            this.sentRequests = this.sentRequests.filter(request => request.id !== userId);

            // Update the UI
            const requestItem = document.querySelector(`#sentRequests [data-user-id="${userId}"]`);
            if (requestItem) {
                requestItem.remove();

                // Check if there are no more requests
                const sentRequests = document.getElementById('sentRequests');
                if (sentRequests && sentRequests.children.length === 0) {
                    sentRequests.innerHTML = `
                        <div class="flex justify-center items-center py-4">
                            <div class="flex items-center text-slate-500">
                                <i data-lucide="send" class="h-4 w-4 mr-2"></i>
                                <span class="text-sm">No sent requests</span>
                            </div>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: sentRequests
                        });
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error canceling friend request:', error);
            this.showNotification('Friend request canceled', 'info');
        });
    }

    /**
     * Show Add Friend modal
     */
    showAddFriendModal() {
        this.openModal('addFriendModal');
    }

    /**
     * Show Create Group Chat modal
     */
    showCreateGroupChatModal() {
        // Reset selected friends
        this.selectedFriends = new Set();

        // Reset the group name input
        const groupNameInput = document.getElementById('groupChatName');
        if (groupNameInput) {
            groupNameInput.value = '';
        }

        // Load friends for selection
        this.loadFriendsForGroupChat();

        // Open the modal
        this.openModal('createGroupChatModal');
    }

    /**
     * Load friends for group chat selection
     */
    loadFriendsForGroupChat() {
        const friendSelectList = document.getElementById('friendSelectList');
        if (!friendSelectList) return;

        // Show loading state
        friendSelectList.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading friends...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: friendSelectList
            });
        }

        // If we already have friends loaded, use them
        if (this.friends && this.friends.length > 0) {
            this.displayFriendsForGroupChat(this.friends);
            return;
        }

        // Otherwise, load friends from the API
        fetch('/api/friends/friends')
            .then(response => response.json())
            .then(friends => {
                this.friends = friends;
                this.displayFriendsForGroupChat(friends);
            })
            .catch(error => {
                console.error('Error loading friends for group chat:', error);
                friendSelectList.innerHTML = `
                    <div class="empty-state py-4">
                        <p class="text-sm text-slate-400 text-center">Error loading friends. Please try again.</p>
                    </div>
                `;
            });
    }

    /**
     * Create a group chat with selected friends
     */
    createGroupChat() {
        console.log('Creating group chat with selected friends:', this.selectedFriends);

        // Get the group name
        const groupNameInput = document.getElementById('groupChatName');
        if (!groupNameInput) {
            this.showNotification('Group name input not found', 'error');
            return;
        }

        const groupName = groupNameInput.value.trim() || 'Group Chat';

        // Check if any friends are selected
        if (!this.selectedFriends || this.selectedFriends.size === 0) {
            this.showNotification('Please select at least one friend to create a group chat', 'warning');
            return;
        }

        // Convert Set to Array
        const memberIds = Array.from(this.selectedFriends);

        // Show loading notification
        this.showNotification('Creating group chat...', 'info');

        // Create the group chat via API
        fetch('/api/friends/group-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: groupName,
                member_ids: memberIds
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Create group chat response:', data);

            if (data.error) {
                this.showNotification(data.error, 'error');
                return;
            }

            // Show success notification
            this.showNotification('Group chat created successfully!', 'success');

            // Close the modal
            this.closeModal('createGroupChatModal');

            // Reload group chats
            this.loadGroupChats();

            // Reset selected friends
            this.selectedFriends = new Set();
        })
        .catch(error => {
            console.error('Error creating group chat:', error);
            this.showNotification('Error creating group chat. Please try again.', 'error');
        });
    }

    /**
     * Display friends for group chat selection
     */
    displayFriendsForGroupChat(friends) {
        const friendSelectList = document.getElementById('friendSelectList');
        if (!friendSelectList) return;

        if (friends.length === 0) {
            friendSelectList.innerHTML = `
                <div class="empty-state py-4">
                    <p class="text-sm text-slate-400 text-center">You don't have any friends yet. Add friends first to create a group chat.</p>
                </div>
            `;
            return;
        }

        // Clear the list
        friendSelectList.innerHTML = '';

        // Create a map to deduplicate friends by ID
        const uniqueFriends = new Map();

        // Add each friend to the map, using their ID as the key
        friends.forEach(friend => {
            uniqueFriends.set(friend.id, friend);
        });

        // Convert map values to array and sort alphabetically
        const sortedFriends = Array.from(uniqueFriends.values()).sort((a, b) => {
            const nameA = (a.display_name || a.username).toLowerCase();
            const nameB = (b.display_name || b.username).toLowerCase();
            return nameA.localeCompare(nameB);
        });

        // Add each friend to the list
        sortedFriends.forEach(friend => {
            const friendItem = document.createElement('div');
            friendItem.className = 'friend-select-item bg-slate-700/30 rounded-lg p-2 border border-slate-600/50 hover:bg-slate-700/50 cursor-pointer transition-all mb-1';
            friendItem.setAttribute('data-friend-id', friend.id);

            // Create friend avatar
            let avatarHtml = '';
            if (friend.profile_picture) {
                avatarHtml = `
                    <div class="friend-avatar relative">
                        <img src="${friend.profile_picture}" alt="${friend.username}" class="w-8 h-8 rounded-full mr-2 object-cover shadow-md">
                    </div>
                `;
            } else {
                const initials = this.getInitials(friend.display_name || friend.username);
                avatarHtml = `
                    <div class="friend-avatar relative">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center text-white text-sm font-medium mr-2 shadow-md">
                            ${initials}
                        </div>
                    </div>
                `;
            }

            friendItem.innerHTML = `
                <div class="flex items-center">
                    ${avatarHtml}
                    <div class="flex-grow">
                        <div class="text-sm font-medium text-slate-200">${friend.display_name || friend.username}</div>
                    </div>
                    <div class="checkbox-container">
                        <i data-lucide="check-circle" class="h-5 w-5 text-purple-400 hidden check-icon"></i>
                        <i data-lucide="circle" class="h-5 w-5 text-slate-500 unchecked-icon"></i>
                    </div>
                </div>
            `;

            // Add click event to select/deselect friend
            friendItem.addEventListener('click', () => {
                this.toggleFriendSelection(friendItem, friend.id);
            });

            friendSelectList.appendChild(friendItem);
        });

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: friendSelectList
            });
        }

        // Update the create button state
        this.updateCreateGroupChatButtonState();
    }

    /**
     * Toggle friend selection for group chat
     */
    toggleFriendSelection(friendItem, friendId) {
        const isSelected = friendItem.classList.contains('selected');

        if (isSelected) {
            // Deselect the friend
            friendItem.classList.remove('selected');
            this.selectedFriends.delete(friendId);

            // Update the checkbox icons
            const checkIcon = friendItem.querySelector('.check-icon');
            const uncheckedIcon = friendItem.querySelector('.unchecked-icon');

            if (checkIcon && uncheckedIcon) {
                checkIcon.classList.add('hidden');
                uncheckedIcon.classList.remove('hidden');
            }
        } else {
            // Check if we've reached the maximum number of friends (6)
            if (this.selectedFriends.size >= 6) {
                this.showNotification('You can only select up to 6 friends for a group chat.', 'warning');
                return;
            }

            // Select the friend
            friendItem.classList.add('selected');
            this.selectedFriends.add(friendId);

            // Update the checkbox icons
            const checkIcon = friendItem.querySelector('.check-icon');
            const uncheckedIcon = friendItem.querySelector('.unchecked-icon');

            if (checkIcon && uncheckedIcon) {
                checkIcon.classList.remove('hidden');
                uncheckedIcon.classList.add('hidden');
            }
        }

        // Update the create button state
        this.updateCreateGroupChatButtonState();
    }

    /**
     * Update the create group chat button state
     */
    updateCreateGroupChatButtonState() {
        const createGroupChatSubmitBtn = document.getElementById('createGroupChatSubmitBtn');
        if (!createGroupChatSubmitBtn) return;

        // Enable the button if at least one friend is selected
        if (this.selectedFriends.size > 0) {
            createGroupChatSubmitBtn.disabled = false;
        } else {
            createGroupChatSubmitBtn.disabled = true;
        }
    }

    /**
     * Load group chat messages with pagination support
     */
    loadGroupChatMessages(chatId) {
        console.log('Loading group chat messages for chat ID:', chatId);

        // Reset temporary message counter and processing flag
        this.tempMessageCounter = 0;
        this.processingSocketMessage = false;

        // Initialize pagination data
        this.currentGroupChatId = chatId;
        this.messageSkip = 0;
        this.hasMoreMessages = true;
        this.isLoadingMoreMessages = false;
        this.totalMessages = 0;

        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) return;

        // Show loading indicator
        messagesContainer.innerHTML = `
            <div class="flex justify-center items-center h-full">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400">Loading messages...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: messagesContainer
            });
        }

        // Fetch initial batch of chat messages (20 most recent)
        fetch(`/api/friends/group-chat/${chatId}?limit=20&skip=0`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(chat => {
                console.log('Loaded group chat:', chat);

                // Clear messages container
                messagesContainer.innerHTML = '';

                // Store pagination info
                this.hasMoreMessages = chat.has_more;
                this.totalMessages = chat.total_messages;
                this.messageSkip = chat.messages.length;

                // Display messages
                if (chat.messages && chat.messages.length > 0) {
                    // Get current user ID from meta tag
                    const userIdMeta = document.querySelector('meta[name="user-id"]');
                    const currentUserId = userIdMeta ? userIdMeta.getAttribute('content') : null;

                    // Add load more button if there are more messages
                    if (chat.has_more) {
                        const loadMoreContainer = document.createElement('div');
                        loadMoreContainer.className = 'load-more-container';
                        loadMoreContainer.innerHTML = `
                            <button id="loadMoreMessagesBtn" class="load-more-btn">
                                <i data-lucide="chevrons-up" class="h-4 w-4 mr-1"></i>
                                Load older messages
                            </button>
                        `;
                        messagesContainer.appendChild(loadMoreContainer);

                        // Initialize Lucide icons for the button
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: loadMoreContainer
                            });
                        }

                        // Add click event to load more button
                        const loadMoreBtn = document.getElementById('loadMoreMessagesBtn');
                        if (loadMoreBtn) {
                            loadMoreBtn.addEventListener('click', () => {
                                this.loadMoreMessages();
                            });
                        }
                    }

                    // Add messages in reverse order (oldest first)
                    const reversedMessages = [...chat.messages].reverse();
                    reversedMessages.forEach(message => {
                        // Mark messages from current user
                        if (currentUserId && message.user_id === currentUserId) {
                            message.is_self = true;
                        }
                        this.appendMessage(message);
                    });

                    // Scroll to bottom
                    this.scrollToBottom();

                    // Add scroll event listener to detect when user scrolls to top
                    messagesContainer.addEventListener('scroll', () => {
                        // If we're near the top and have more messages, load more
                        if (messagesContainer.scrollTop < 50 && this.hasMoreMessages && !this.isLoadingMoreMessages) {
                            this.loadMoreMessages();
                        }
                    });
                } else {
                    // Show empty state
                    messagesContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full p-6 text-center">
                            <div class="w-12 h-12 rounded-full bg-slate-700/50 flex items-center justify-center mb-3">
                                <i data-lucide="users-round" class="h-6 w-6 text-slate-400"></i>
                            </div>
                            <p class="text-slate-400 text-sm">No messages yet. Start the conversation!</p>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: messagesContainer
                        });
                    }
                }

                // Enable message input
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');

                if (messageInput) {
                    messageInput.disabled = false;
                    messageInput.placeholder = 'Type a message...';
                    messageInput.focus();
                }

                if (sendButton) {
                    sendButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error loading group chat messages:', error);

                // Show error message
                const messagesContainer = document.getElementById('messagesContainer');
                if (messagesContainer) {
                    messagesContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full p-6 text-center">
                            <div class="w-12 h-12 rounded-full bg-red-900/30 flex items-center justify-center mb-3">
                                <i data-lucide="alert-circle" class="h-6 w-6 text-red-400"></i>
                            </div>
                            <p class="text-red-400 text-sm">Error loading messages. Please try again.</p>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: messagesContainer
                        });
                    }
                }
            });
    }

    /**
     * Open a group chat
     */
    openGroupChat(chat) {
        console.log('Opening group chat:', chat);

        // Reset current chat state
        this.currentChatId = chat.chat_id;
        this.currentFriendId = null;
        this.currentGroupChatId = chat.chat_id;
        this.isGroupChat = true;
        
        // Store the current group chat ID in localStorage for auto-loading next time
        localStorage.setItem('lastOpenedChatId', chat.chat_id);
        localStorage.setItem('lastOpenedGroupChatId', chat.chat_id);
        localStorage.removeItem('lastOpenedFriendId'); // Clear friend ID since we're in a group chat

        // Update UI to show active group chat
        const chatItems = document.querySelectorAll('#friendList > div > div.friend-item, #friendList > div > div.group-chat-item');
        chatItems.forEach(item => {
            item.classList.remove('active');

            if (item.getAttribute('data-chat-id') === chat.chat_id && item.getAttribute('data-group-chat') === 'true') {
                item.classList.add('active');
            }
        });

        // Update group chat info in header
        const activeFriendName = document.getElementById('activeFriendName');
        const activeFriendAvatar = document.getElementById('activeFriendAvatar');
        const encryptionStatus = document.getElementById('encryptionStatus');
        const inviteToLiveBtn = document.getElementById('inviteToLiveBtn');
        const removeFriendBtn = document.getElementById('removeFriendBtn');

        if (activeFriendName) {
            activeFriendName.textContent = chat.name;

            // Make the group chat name clickable for renaming
            activeFriendName.classList.add('cursor-pointer', 'hover:text-cyan-300', 'transition-colors');

            // Add a small edit icon next to the name
            const editIcon = document.createElement('i');
            editIcon.setAttribute('data-lucide', 'edit-3');
            editIcon.className = 'h-3 w-3 ml-1 text-slate-400 group-hover:text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity';

            // Wrap the name in a group div for hover effects
            const nameWrapper = document.createElement('div');
            nameWrapper.className = 'flex items-center group';

            // Move the text content to the wrapper
            nameWrapper.textContent = chat.name;
            nameWrapper.appendChild(editIcon);
            activeFriendName.textContent = '';
            activeFriendName.appendChild(nameWrapper);

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: activeFriendName
                });
            }

            // Add click event to rename the group chat
            activeFriendName.addEventListener('click', () => {
                this.showRenameGroupChatModal(chat);
            });
        }

        if (activeFriendAvatar) {
            // Group members container is now hidden via CSS

            // Debug log to check if profile picture exists
            console.log(`Opening group chat ${chat.chat_id} with profile picture:`, chat.profile_picture);

            // Use the profile picture from the cached group chat data if available
            // This ensures we reuse the already loaded profile picture
            const cachedChat = this.groupChats ? this.groupChats.find(c => c.chat_id === chat.chat_id) : null;
            const profilePicture = cachedChat && cachedChat.profile_picture ? cachedChat.profile_picture : chat.profile_picture;

            console.log(`Using cached profile picture for group chat ${chat.chat_id}:`, profilePicture);

            // Immediately show the avatar with the profile picture if available
            // This ensures the profile picture is shown right away without waiting for the API call
            activeFriendAvatar.innerHTML = `
                <div class="group-chat-avatar">
                    ${profilePicture
                        ? `<img src="${profilePicture}" alt="${chat.name}" class="rounded-full w-full h-full object-cover shadow-none">`
                        : `<i data-lucide="users-round" class="h-5 w-5"></i>`
                    }
                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer" id="changeGroupPicBtn">
                        <i data-lucide="camera" class="h-4 w-4 text-white"></i>
                    </div>
                </div>
            `;

            // Initialize Lucide icons for the initial avatar
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: activeFriendAvatar
                });
            }

            // Add event listener for changing group profile picture
            const initialChangeGroupPicBtn = document.getElementById('changeGroupPicBtn');
            if (initialChangeGroupPicBtn) {
                initialChangeGroupPicBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showChangeGroupProfilePictureModal(chat);
                });
            }

            // Fetch the full group chat details to get member information
            fetch(`/api/friends/group-chat/${chat.chat_id}`)
                .then(response => response.json())
                .then(groupChatDetails => {
                    // Debug log to check if profile picture exists in the detailed response
                    console.log(`Group chat ${chat.chat_id} detailed profile picture:`, groupChatDetails.profile_picture);

                    // Get the current profile picture we're using
                    const cachedChat = this.groupChats ? this.groupChats.find(c => c.chat_id === chat.chat_id) : null;
                    const currentProfilePicture = cachedChat && cachedChat.profile_picture ? cachedChat.profile_picture : chat.profile_picture;

                    // Only update the profile picture if it's different from what we already have
                    if (groupChatDetails.profile_picture && groupChatDetails.profile_picture !== currentProfilePicture) {
                        console.log(`Updating profile picture for group chat ${chat.chat_id} from detailed response`);

                        // Update the avatar with the new profile picture
                        activeFriendAvatar.innerHTML = `
                            <div class="group-chat-avatar">
                                <img src="${groupChatDetails.profile_picture}" alt="${chat.name}" class="rounded-full w-full h-full object-cover shadow-none">
                                <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer" id="changeGroupPicBtn">
                                    <i data-lucide="camera" class="h-4 w-4 text-white"></i>
                                </div>
                            </div>
                        `;

                        // Update the cached group chat data
                        if (this.groupChats) {
                            const chatIndex = this.groupChats.findIndex(c => c.chat_id === chat.chat_id);
                            if (chatIndex !== -1) {
                                this.groupChats[chatIndex].profile_picture = groupChatDetails.profile_picture;
                            }
                        }

                        // Also update the group chat item in the sidebar
                        this.updateGroupChatUIWithNewProfilePicture(chat.chat_id, groupChatDetails.profile_picture);

                        // Initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons({
                                attrs: {
                                    'stroke-width': '2',
                                    'class': 'icon'
                                },
                                root: activeFriendAvatar
                            });
                        }

                        // Re-add the event listener for the change button
                        const newChangeGroupPicBtn = document.getElementById('changeGroupPicBtn');
                        if (newChangeGroupPicBtn) {
                            // Remove any existing event listeners by cloning the node
                            const clonedBtn = newChangeGroupPicBtn.cloneNode(true);
                            newChangeGroupPicBtn.parentNode.replaceChild(clonedBtn, newChangeGroupPicBtn);

                            // Add new event listener
                            clonedBtn.addEventListener('click', (e) => {
                                e.stopPropagation();
                                this.showChangeGroupProfilePictureModal(chat);
                            });

                            // Initialize Lucide icons for the button
                            if (typeof lucide !== 'undefined') {
                                lucide.createIcons({
                                    attrs: {
                                        'stroke-width': '2',
                                        'class': 'icon'
                                    },
                                    root: clonedBtn
                                });
                            }
                        }
                    }

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: activeFriendAvatar
                        });
                    }

                    // Get the members from the group chat details (for potential future use)
                    const members = groupChatDetails.members || [];

                    // Add event listener for changing group profile picture
                    const changeGroupPicBtn = document.getElementById('changeGroupPicBtn');
                    if (changeGroupPicBtn) {
                        changeGroupPicBtn.addEventListener('click', (e) => {
                            e.stopPropagation(); // Prevent opening the members popup
                            this.showChangeGroupProfilePictureModal(chat);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading group chat members:', error);
                    // Fallback to basic avatar if we can't load members
                    activeFriendAvatar.innerHTML = `
                        <div class="group-chat-avatar">
                            <i data-lucide="users-round" class="h-5 w-5 text-white"></i>
                        </div>
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: activeFriendAvatar
                        });
                    }
                });
        }

        // Hide encryption status for group chats
        if (encryptionStatus) {
            encryptionStatus.classList.add('hidden');
        }

        // Hide invite to live button for group chats
        if (inviteToLiveBtn) {
            inviteToLiveBtn.classList.add('hidden');
        }

        // Hide remove friend button for group chats
        if (removeFriendBtn) {
            removeFriendBtn.classList.add('hidden');
        }

        // Show group info button for group chats
        const groupInfoBtn = document.getElementById('groupInfoBtn');
        if (groupInfoBtn) {
            groupInfoBtn.classList.remove('hidden');

            // Remove any existing event listeners
            const newInfoBtn = groupInfoBtn.cloneNode(true);
            groupInfoBtn.parentNode.replaceChild(newInfoBtn, groupInfoBtn);

            // Add click event to show group info modal
            newInfoBtn.addEventListener('click', () => {
                this.showGroupInfoModal(chat);
            });

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: newInfoBtn
                });
            }
        }

        // Hide the exit and delete buttons (they will be shown in the modal)
        const exitGroupChatBtn = document.getElementById('exitGroupChatBtn');
        if (exitGroupChatBtn) {
            exitGroupChatBtn.classList.add('hidden');
        }

        // Hide delete group chat button (it will be shown in the modal)
        const deleteGroupChatBtn = document.getElementById('deleteGroupChatBtn');
        if (deleteGroupChatBtn) {
            deleteGroupChatBtn.classList.add('hidden');
        }

        // Store the current chat for use in the modal
        this.currentOpenGroupChat = chat;

        // Show the chat container
        const welcomeMessage = document.getElementById('welcomeMessage');
        const chatContainer = document.getElementById('chatContainer');
        const messagesContainer = document.getElementById('messagesContainer');

        if (welcomeMessage) {
            welcomeMessage.classList.add('hidden');
        }

        if (chatContainer) {
            chatContainer.classList.remove('hidden');
        }

        if (messagesContainer) {
            messagesContainer.classList.remove('hidden');
        }

        // Clear the messages container and show loading indicator
        if (messagesContainer) {
            messagesContainer.innerHTML = '';

            // Add loading indicator
            messagesContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-500 animate-spin mr-2"></i>
                        <span class="text-slate-500 text-sm">Loading messages...</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: messagesContainer
                });
            }
        }

        // Join the group chat room via Socket.IO
        if (this.socket) {
            this.socket.emit('join_group_chat', {
                chat_id: chat.chat_id
            });
        }

        // Load the group chat messages
        this.loadGroupChatMessages(chat.chat_id);
    }

    /**
     * Show the rename group chat modal
     * @param {Object} chat - The group chat object
     */
    showRenameGroupChatModal(chat) {
        // Get the modal elements
        const modal = document.getElementById('renameGroupChatModal');
        const nameInput = document.getElementById('newGroupChatName');
        const cancelBtn = document.getElementById('cancelRenameGroupBtn');
        const confirmBtn = document.getElementById('confirmRenameGroupBtn');

        if (!modal || !nameInput || !cancelBtn || !confirmBtn) {
            console.error('Could not find all required elements for rename group chat modal');
            return;
        }

        // Set the current name as the default value
        nameInput.value = chat.name;

        // Store the chat ID for later use
        this.currentRenameGroupChatId = chat.chat_id;

        // Show the modal
        modal.classList.remove('hidden');

        // Focus the input field
        setTimeout(() => {
            nameInput.focus();
            nameInput.select();
        }, 100);

        // Add event listeners
        const closeModal = () => {
            modal.classList.add('hidden');
            // Remove event listeners
            cancelBtn.removeEventListener('click', closeModal);
            confirmBtn.removeEventListener('click', handleRename);
            document.removeEventListener('keydown', handleKeydown);

            // Clear the stored chat ID
            this.currentRenameGroupChatId = null;
        };

        const handleRename = () => {
            const newName = nameInput.value.trim();

            if (!newName) {
                this.showNotification('Group name cannot be empty', 'error');
                return;
            }

            // Disable the button to prevent multiple clicks
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = `
                <i data-lucide="loader" class="h-4 w-4 mr-2 inline animate-spin"></i>
                Saving...
            `;

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    root: confirmBtn
                });
            }

            // Send the rename request
            this.renameGroupChat(this.currentRenameGroupChatId, newName)
                .then(() => {
                    closeModal();
                })
                .catch(error => {
                    console.error('Error renaming group chat:', error);
                    this.showNotification('Failed to rename group chat', 'error');

                    // Re-enable the button
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = `
                        <i data-lucide="save" class="h-4 w-4 mr-2 inline"></i>
                        Save
                    `;

                    // Initialize Lucide icons
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            root: confirmBtn
                        });
                    }
                });
        };

        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'Enter') {
                handleRename();
            }
        };

        // Add event listeners
        cancelBtn.addEventListener('click', closeModal);
        confirmBtn.addEventListener('click', handleRename);
        document.addEventListener('keydown', handleKeydown);

        // Add event listener to close button
        const closeBtn = modal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }
    }

    /**
     * Rename a group chat
     * @param {string} chatId - The ID of the group chat
     * @param {string} newName - The new name for the group chat
     * @returns {Promise} - A promise that resolves when the rename is complete
     */
    renameGroupChat(chatId, newName) {
        return new Promise((resolve, reject) => {
            // Check if socket is available
            if (this.socket) {
                // Try to rename via WebSocket first
                this.socket.emit('rename_group_chat', {
                    chat_id: chatId,
                    name: newName
                });

                // Listen for the response
                const handleSuccess = (data) => {
                    if (data.chat_id === chatId) {
                        // Update the UI
                        this.updateGroupChatName(chatId, newName);

                        // Show success notification
                        this.showNotification('Group chat renamed successfully', 'success');

                        // Remove the event listener
                        this.socket.off('group_chat_name_updated', handleSuccess);

                        resolve(data);
                    }
                };

                // Listen for errors
                const handleError = (data) => {
                    // Check if this error is related to our request
                    if (data.message && data.message.includes('group chat')) {
                        this.showNotification(data.message, 'error');

                        // Remove the event listener
                        this.socket.off('error', handleError);

                        reject(new Error(data.message));
                    }
                };

                // Add event listeners
                this.socket.on('group_chat_name_updated', handleSuccess);
                this.socket.on('error', handleError);

                // Set a timeout to fall back to REST API if socket doesn't respond
                setTimeout(() => {
                    // Remove the event listeners
                    this.socket.off('group_chat_name_updated', handleSuccess);
                    this.socket.off('error', handleError);

                    // Fall back to REST API
                    this.renameGroupChatViaREST(chatId, newName)
                        .then(resolve)
                        .catch(reject);
                }, 3000);
            } else {
                // Fall back to REST API if socket is not available
                this.renameGroupChatViaREST(chatId, newName)
                    .then(resolve)
                    .catch(reject);
            }
        });
    }

    /**
     * Rename a group chat via REST API
     * @param {string} chatId - The ID of the group chat
     * @param {string} newName - The new name for the group chat
     * @returns {Promise} - A promise that resolves when the rename is complete
     */
    renameGroupChatViaREST(chatId, newName) {
        return fetch(`/api/friends/group-chat/${chatId}/rename`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: newName
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Failed to rename group chat');
                });
            }
            return response.json();
        })
        .then(data => {
            // Update the UI
            this.updateGroupChatName(chatId, newName);

            // Show success notification
            this.showNotification('Group chat renamed successfully', 'success');

            return data;
        });
    }

    /**
     * Update the group chat name in the UI
     * @param {string} chatId - The ID of the group chat
     * @param {string} newName - The new name for the group chat
     */
    updateGroupChatName(chatId, newName) {
        // Update the active friend name if this is the current chat
        const activeFriendName = document.getElementById('activeFriendName');
        if (activeFriendName && this.currentGroupChatId === chatId) {
            // If we have a wrapper div (which we should)
            const nameWrapper = activeFriendName.querySelector('.group');
            if (nameWrapper) {
                // Update just the text content, preserving the edit icon
                // We need to update the first text node of the wrapper
                const textNodes = Array.from(nameWrapper.childNodes).filter(node => node.nodeType === Node.TEXT_NODE);
                if (textNodes.length > 0) {
                    textNodes[0].nodeValue = newName;
                } else {
                    // If no text node exists, create one
                    nameWrapper.prepend(document.createTextNode(newName));
                }
            } else {
                // Fallback if the wrapper doesn't exist - recreate the structure
                activeFriendName.textContent = '';

                // Create the wrapper and edit icon
                const newWrapper = document.createElement('div');
                newWrapper.className = 'flex items-center group';

                // Add the text node with the new name
                newWrapper.textContent = newName;

                // Add the edit icon
                const editIcon = document.createElement('i');
                editIcon.setAttribute('data-lucide', 'edit-3');
                editIcon.className = 'h-3 w-3 ml-1 text-slate-400 group-hover:text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity';
                newWrapper.appendChild(editIcon);

                // Add the wrapper to the header
                activeFriendName.appendChild(newWrapper);

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: activeFriendName
                    });
                }
            }
        }

        // Update the chat in the sidebar
        const chatItem = document.querySelector(`.group-chat-item[data-chat-id="${chatId}"]`);
        if (chatItem) {
            // Update the name in the text-sm element (group chat name)
            const nameElement = chatItem.querySelector('.text-sm');
            if (nameElement) {
                nameElement.textContent = newName;
            }

            // Also update the friend-name element if it exists (for backward compatibility)
            const friendNameElement = chatItem.querySelector('.friend-name');
            if (friendNameElement) {
                friendNameElement.textContent = newName;
            }
        }

        // Update the chat in our local cache
        if (this.groupChats) {
            const chatIndex = this.groupChats.findIndex(c => c.chat_id === chatId);
            if (chatIndex !== -1) {
                this.groupChats[chatIndex].name = newName;
            }
        }

        // Update the active chat if this is the current chat
        if (this.currentGroupChatId === chatId) {
            // If we have an activeChat object, update it too
            if (this.activeChat && this.activeChat.chat_id === chatId) {
                this.activeChat.name = newName;
            }
        }
    }

    /**
     * Show notification
     * @param {string} message - The message to display
     * @param {string} type - The type of notification (info, success, error, warning)
     * @param {number} duration - Duration in milliseconds to show the notification (default: 5000)
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 bg-slate-800 border ${type === 'error' ? 'border-red-500' : type === 'success' ? 'border-green-500' : type === 'warning' ? 'border-yellow-500' : 'border-cyan-500'} rounded-md shadow-lg p-4 max-w-md`;

        // Create notification content
        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="${type === 'error' ? 'alert-circle' : type === 'success' ? 'check-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="h-5 w-5 ${type === 'error' ? 'text-red-500' : type === 'success' ? 'text-green-500' : type === 'warning' ? 'text-yellow-500' : 'text-cyan-500'}"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-slate-200">${message}</p>
                </div>
                <div class="ml-auto pl-3">
                    <button class="text-slate-400 hover:text-slate-200">
                        <i data-lucide="x" class="h-4 w-4"></i>
                    </button>
                </div>
            </div>
        `;

        // Add to document
        document.body.appendChild(notification);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: notification
            });
        }

        // Add close button functionality
        const closeBtn = notification.querySelector('button');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notification.remove();
            });
        }

        // Auto-remove after specified duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * Toggle a collapsible panel
     */
    togglePanel(panelId, toggleBtn) {
        const panel = document.getElementById(panelId);
        if (!panel) return;

        if (panel.classList.contains('collapsed')) {
            // Expand panel
            panel.classList.remove('collapsed');
            toggleBtn.classList.remove('collapsed');
        } else {
            // Collapse panel
            panel.classList.add('collapsed');
            toggleBtn.classList.add('collapsed');
        }
    }

    /**
     * Leave a group chat
     */
    leaveGroupChat(chat) {
        if (!chat || !chat.chat_id) {
            this.showNotification('Invalid group chat', 'error');
            return;
        }

        // Show confirmation dialog
        if (!confirm('Are you sure you want to leave this group chat? You will no longer receive messages from this group.')) {
            return;
        }

        // Show loading notification
        this.showNotification('Leaving group chat...', 'info');

        // Leave the group chat (the server will add the system message)
        fetch(`/api/friends/group-chat/${chat.chat_id}/leave`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Left group chat:', data);

            // Show success notification
            this.showNotification('You have left the group chat', 'success');

            // Reset current chat state
            this.currentChatId = null;
            this.currentFriendId = null;
            this.currentGroupChatId = null;
            this.isGroupChat = false;

            // Hide the exit button
            const exitGroupChatBtn = document.getElementById('exitGroupChatBtn');
            if (exitGroupChatBtn) {
                exitGroupChatBtn.classList.add('hidden');
            }

            // Group members container is now hidden via CSS

            // Group members popup is now hidden via CSS

            // Remove any document click event listeners for popups
            if (this.closePopupHandler) {
                document.removeEventListener('click', this.closePopupHandler);
                this.closePopupHandler = null;
            }

            // Show welcome message and hide chat container
            const welcomeMessage = document.getElementById('welcomeMessage');
            const chatContainer = document.getElementById('chatContainer');

            if (welcomeMessage) {
                welcomeMessage.classList.remove('hidden');
            }

            if (chatContainer) {
                chatContainer.classList.add('hidden');
            }

            // Remove the group chat from the UI first to prevent duplicate containers
            const groupChatItem = document.querySelector(`.group-chat-item[data-chat-id="${chat.chat_id}"]`);
            if (groupChatItem) {
                groupChatItem.remove();
            }

            // Remove any duplicate FRIENDS headers
            const friendList = document.getElementById('friendList');
            if (friendList) {
                const existingFriendsHeaders = friendList.querySelectorAll('.text-xs.font-medium.text-slate-400:not(#groupChatHeader)');
                existingFriendsHeaders.forEach(header => {
                    if (header.textContent === 'FRIENDS' && header.id !== 'friendsHeader') {
                        header.remove();
                    }
                });
            }

            // Reload the group chats to update the UI
            this.loadGroupChats();

            // Also reload friends to ensure proper UI structure
            this.loadFriends();
        })
        .catch(error => {
            console.error('Error leaving group chat:', error);
            this.showNotification('Error leaving group chat. Please try again.', 'error');
        });
    }

    /**
     * Auto-resize the message input textarea based on content
     */
    autoResizeMessageInput(textarea) {
        if (!textarea) return;

        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';

        // Get the container width to adjust max height based on screen size
        const containerWidth = textarea.parentElement ? textarea.parentElement.offsetWidth : window.innerWidth;

        // Use a smaller max height on mobile devices
        const maxHeight = containerWidth < 640 ? 80 : 120;

        // Calculate the new height (with a min height of 42px)
        const newHeight = Math.max(42, Math.min(textarea.scrollHeight, maxHeight));

        // Set the new height
        textarea.style.height = newHeight + 'px';

        // No need to adjust button position as it's now centered with CSS
    }

    /**
     * Set up image handling
     */
    setupImageHandling() {
        // Set up image button click handler
        const imageBtn = document.getElementById('imageBtn');
        const imageFileInput = document.getElementById('imageFileInput');

        if (imageBtn && imageFileInput) {
            imageBtn.addEventListener('click', () => {
                // Trigger file input click
                imageFileInput.click();
            });

            // Set up file input change handler
            imageFileInput.addEventListener('change', (e) => {
                this.handleImageSelection(e.target.files);
            });

            // Set up drag and drop for the message input area
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    messageInput.addEventListener(eventName, (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                    });
                });

                // Highlight drop area when dragging over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    messageInput.addEventListener(eventName, () => {
                        messageInput.classList.add('drag-over');
                    });
                });

                // Remove highlight when dragging leaves
                ['dragleave', 'drop'].forEach(eventName => {
                    messageInput.addEventListener(eventName, () => {
                        messageInput.classList.remove('drag-over');
                    });
                });

                // Handle dropped files
                messageInput.addEventListener('drop', (e) => {
                    const files = e.dataTransfer.files;
                    this.handleImageSelection(files);
                });
            }
        }
    }

    /**
     * Handle image selection from file input
     * @param {FileList} files - The selected files
     */
    handleImageSelection(files) {
        if (!files || files.length === 0) return;

        // Get the image preview container
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (!imagePreviewContainer) return;

        // Show the container if it's hidden
        imagePreviewContainer.classList.remove('hidden');

        // Process each file (up to the maximum allowed)
        for (let i = 0; i < files.length && this.selectedImages.length < this.maxImages; i++) {
            const file = files[i];

            // Check if the file is an image
            if (!file.type.startsWith('image/')) {
                console.warn('Skipping non-image file:', file.name);
                continue;
            }

            // Create a unique ID for this image
            const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

            // Optimize and resize the image before sending
            this.optimizeImage(file, (optimizedImageData) => {
                // Create a temporary object to store the image data
                const tempImageData = {
                    id: imageId,
                    data: optimizedImageData,
                    file: file,
                    uploading: true,
                    url: null
                };

                // Store the image data
                this.selectedImages.push(tempImageData);

                // Create the image preview
                this.addImagePreview(imageId, optimizedImageData);

                // Upload the image to the server
                this.uploadImageToServer(file, imageId);
            });
        }
    }

    /**
     * Upload an image to the server
     * @param {File} file - The image file to upload
     * @param {string} imageId - The unique ID of the image
     */
    uploadImageToServer(file, imageId) {
        // Create a FormData object to send the file
        const formData = new FormData();
        formData.append('file', file);

        // Show loading indicator in the preview
        const previewContainer = document.querySelector(`.image-preview-container[data-image-id="${imageId}"]`);
        if (previewContainer) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'image-upload-loading';
            loadingIndicator.innerHTML = '<div class="spinner"></div>';
            previewContainer.appendChild(loadingIndicator);
        }

        // Upload the file
        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            console.log('Image uploaded successfully:', data);

            // Update the image data with the URL
            const imageIndex = this.selectedImages.findIndex(img => img.id === imageId);
            if (imageIndex !== -1) {
                this.selectedImages[imageIndex].uploading = false;
                this.selectedImages[imageIndex].url = data.url;
            }

            // Remove loading indicator
            if (previewContainer) {
                const loadingIndicator = previewContainer.querySelector('.image-upload-loading');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }

                // Add success indicator
                const successIndicator = document.createElement('div');
                successIndicator.className = 'image-upload-success';
                successIndicator.innerHTML = '<i data-lucide="check" class="h-4 w-4"></i>';
                previewContainer.appendChild(successIndicator);

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: successIndicator
                    });
                }

                // Remove success indicator after 2 seconds
                setTimeout(() => {
                    if (successIndicator.parentNode) {
                        successIndicator.remove();
                    }
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error uploading image:', error);

            // Update the image data with the error
            const imageIndex = this.selectedImages.findIndex(img => img.id === imageId);
            if (imageIndex !== -1) {
                this.selectedImages[imageIndex].uploading = false;
                this.selectedImages[imageIndex].error = true;
            }

            // Remove loading indicator and add error indicator
            if (previewContainer) {
                const loadingIndicator = previewContainer.querySelector('.image-upload-loading');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }

                // Add error indicator
                const errorIndicator = document.createElement('div');
                errorIndicator.className = 'image-upload-error';
                errorIndicator.innerHTML = '<i data-lucide="alert-circle" class="h-4 w-4"></i>';
                previewContainer.appendChild(errorIndicator);

                // Initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        root: errorIndicator
                    });
                }
            }

            // Show error notification
            this.showNotification('Error uploading image. Please try again.', 'error');
        });
    }

    /**
     * Optimize image size before sending
     * @param {File} file - The image file
     * @param {Function} callback - Callback function with optimized image data
     */
    optimizeImage(file, callback) {
        // Create a FileReader to read the file
        const reader = new FileReader();

        reader.onload = (e) => {
            // Create an image element to get dimensions
            const img = new Image();
            img.onload = () => {
                // Create a canvas to resize the image
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;

                // Maximum dimensions (1200px width or height)
                const maxDimension = 1200;

                // Resize if needed while maintaining aspect ratio
                if (width > maxDimension || height > maxDimension) {
                    if (width > height) {
                        height = Math.round(height * (maxDimension / width));
                        width = maxDimension;
                    } else {
                        width = Math.round(width * (maxDimension / height));
                        height = maxDimension;
                    }
                }

                // Set canvas dimensions
                canvas.width = width;
                canvas.height = height;

                // Draw the resized image on the canvas
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // Get the optimized image data (JPEG at 85% quality)
                let optimizedImageData;
                if (file.type === 'image/png' && file.size < 500000) {
                    // Keep small PNGs as PNG for better quality
                    optimizedImageData = canvas.toDataURL('image/png', 0.85);
                } else {
                    // Convert to JPEG for better compression
                    optimizedImageData = canvas.toDataURL('image/jpeg', 0.85);
                }

                // Call the callback with the optimized image data
                callback(optimizedImageData);
            };

            // Set the image source to the file data
            img.src = e.target.result;
        };

        // Read the file as a data URL
        reader.readAsDataURL(file);
    }

    /**
     * Add an image preview to the container
     * @param {string} imageId - The unique ID of the image
     * @param {string} imageData - The image data URL
     */
    addImagePreview(imageId, imageData) {
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (!imagePreviewContainer) return;

        // Create the preview container
        const previewContainer = document.createElement('div');
        previewContainer.className = 'image-preview-container';
        previewContainer.setAttribute('data-image-id', imageId);

        // Create the image element
        const imageElement = document.createElement('img');
        imageElement.className = 'image-preview';
        imageElement.src = imageData;
        imageElement.alt = 'Image Preview';

        // Create the remove button
        const removeButton = document.createElement('button');
        removeButton.className = 'remove-image-btn';
        removeButton.innerHTML = '<i data-lucide="x" class="h-3 w-3"></i>';
        removeButton.addEventListener('click', () => {
            this.removeImage(imageId);
        });

        // Add elements to the container
        previewContainer.appendChild(imageElement);
        previewContainer.appendChild(removeButton);
        imagePreviewContainer.appendChild(previewContainer);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: previewContainer
            });
        }
    }

    /**
     * Remove an image from the selected images
     * @param {string} imageId - The unique ID of the image to remove
     */
    removeImage(imageId) {
        // Remove from the array
        this.selectedImages = this.selectedImages.filter(img => img.id !== imageId);

        // Remove from the DOM
        const previewContainer = document.querySelector(`.image-preview-container[data-image-id="${imageId}"]`);
        if (previewContainer) {
            previewContainer.remove();
        }

        // Hide the container if there are no more images
        if (this.selectedImages.length === 0) {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            if (imagePreviewContainer) {
                imagePreviewContainer.classList.add('hidden');
            }
        }
    }

    /**
     * Clear all selected images
     */
    clearAllImages() {
        // Clear the array
        this.selectedImages = [];

        // Clear the DOM
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            // Remove all preview containers but keep the global remove button
            const previewContainers = imagePreviewContainer.querySelectorAll('.image-preview-container');
            previewContainers.forEach(container => {
                container.remove();
            });

            // Hide the container
            imagePreviewContainer.classList.add('hidden');
        }

        // Reset the file input so the same files can be selected again
        const imageFileInput = document.getElementById('imageFileInput');
        if (imageFileInput) {
            imageFileInput.value = '';
        }
    }

    /**
     * Show image viewer modal
     * @param {string} imageSrc - The source URL of the image to display
     */
    showImageViewer(imageSrc) {
        if (!imageSrc) return;

        // Get the modal and image elements
        const modal = document.getElementById('imageViewerModal');
        const fullSizeImage = document.getElementById('fullSizeImage');

        if (!modal || !fullSizeImage) return;

        // Set the image source
        fullSizeImage.src = imageSrc;

        // Show the modal
        modal.classList.remove('hidden');
        modal.style.display = 'flex';

        // Store reference to this for event handlers
        const self = this;

        // Add event listener to close on Escape key
        const keydownHandler = function(e) {
            if (e.key === 'Escape') {
                self.closeImageViewer();
            }
        };

        // Store the handler for later removal
        this._imageViewerKeydownHandler = keydownHandler;
        document.addEventListener('keydown', keydownHandler);

        // Add event listener to close on click outside the image
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                self.closeImageViewer();
            }
        });

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: modal
            });
        }
    }

    /**
     * Close image viewer modal
     */
    closeImageViewer() {
        const modal = document.getElementById('imageViewerModal');
        if (!modal) return;

        // Hide the modal
        modal.classList.add('hidden');
        modal.style.display = 'none';

        // Remove event listeners
        if (this._imageViewerKeydownHandler) {
            document.removeEventListener('keydown', this._imageViewerKeydownHandler);
            this._imageViewerKeydownHandler = null;
        }
    }
}

// Initialize Friends Panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create a global instance of FriendsPanel
    window.friendsPanel = new FriendsPanel();

    // Initialize when the friends view is shown
    const friendsNavItem = document.querySelector('.nav-item[data-view="friends"]');
    if (friendsNavItem) {
        friendsNavItem.addEventListener('click', () => {
            window.friendsPanel.init();
        });
    }

    // Also initialize if we're already on the friends view
    if (document.getElementById('friendsView') && !document.getElementById('friendsView').classList.contains('hidden')) {
        window.friendsPanel.init();
    }

    // Set up image viewer modal close button
    const closeImageViewerBtn = document.getElementById('closeImageViewerBtn');
    if (closeImageViewerBtn) {
        closeImageViewerBtn.addEventListener('click', () => {
            window.friendsPanel.closeImageViewer();
        });
    }
});
