/**
 * ProfileCustomization.js
 * Handles client-side functionality for profile customization
 */

class ProfileCustomization {
    constructor() {
        this.currentUsername = null;
        this.profileData = null;
        this.isEditing = false;
        this.formModified = false;
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => this.init());
    }
    
    /**
     * Initialize the profile customization
     */
    init() {
        // Check if we're on a profile page
        const profileContainer = document.querySelector('.profile-container');
        if (!profileContainer) return;
        
        // Get current username from URL
        const urlPath = window.location.pathname;
        const profileMatch = urlPath.match(/\/profile\/([^\/]+)/);
        
        if (profileMatch && profileMatch[1]) {
            this.currentUsername = profileMatch[1];
            this.loadProfileData();
        }
        
        // Check if we're on the edit page
        this.isEditing = urlPath.includes('/profile/edit');
        if (this.isEditing) {
            this.initializeEditPage();
        }
    }
    
    /**
     * Load profile customization data
     */
    loadProfileData() {
        fetch(`/api/profile/customization/${this.currentUsername}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.profileData = data.profile;
                    this.applyCustomization();
                }
            })
            .catch(error => {
                console.error('Error loading profile data:', error);
            });
    }
    
    /**
     * Apply customization to the profile page
     */
    applyCustomization() {
        if (!this.profileData) return;
        
        // Apply custom CSS if available
        if (this.profileData.custom_css) {
            const customStyle = document.createElement('style');
            customStyle.textContent = this.profileData.custom_css;
            document.head.appendChild(customStyle);
        }
        
        // Apply dynamic customizations not handled by CSS variables
        this.applyDynamicCustomizations();
    }
    
    /**
     * Apply dynamic customizations that can't be handled by CSS variables
     */
    applyDynamicCustomizations() {
        // Add animations to profile elements
        this.animateProfileElements();
        
        // Add interactive hover effects
        this.addInteractiveEffects();
    }
    
    /**
     * Add animations to profile elements
     */
    animateProfileElements() {
        // Animate profile sections with a staggered fade-in
        const sections = document.querySelectorAll('.profile-section');
        if (sections.length) {
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 100 + (index * 150));
            });
        }
    }
    
    /**
     * Add interactive effects to profile elements
     */
    addInteractiveEffects() {
        // Add hover effects to social links
        const socialLinks = document.querySelectorAll('.social-link');
        if (socialLinks.length) {
            socialLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.05)';
                });
                
                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }
    }
    
    /**
     * Initialize the edit page functionality
     */
    initializeEditPage() {
        // Add form change detection
        this.setupFormChangeDetection();
        
        // Add real-time preview updates
        this.setupRealtimePreview();
        
        // Add form validation
        this.setupFormValidation();
        
        // Add unsaved changes warning
        this.setupUnsavedChangesWarning();
    }
    
    /**
     * Setup detection for form changes
     */
    setupFormChangeDetection() {
        const form = document.getElementById('profile-edit-form');
        if (!form) return;
        
        // Mark form as modified when any input changes
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.formModified = true;
            });
            
            // For text inputs, also listen for keyup events
            if (input.type === 'text' || input.type === 'textarea') {
                input.addEventListener('keyup', () => {
                    this.formModified = true;
                });
            }
        });
    }
    
    /**
     * Setup real-time preview updates
     */
    setupRealtimePreview() {
        // This could be implemented to update the preview in real-time
        // as the user makes changes to the form
        const previewFrame = document.getElementById('preview-frame');
        if (!previewFrame) return;
        
        // Add debounced preview update function
        let previewUpdateTimeout;
        const updatePreview = () => {
            clearTimeout(previewUpdateTimeout);
            previewUpdateTimeout = setTimeout(() => {
                // Get current form values and update preview
                const generatePreviewBtn = document.getElementById('generate-preview');
                if (generatePreviewBtn) {
                    generatePreviewBtn.click();
                }
            }, 1000); // 1 second debounce
        };
        
        // Listen for changes on color inputs for immediate feedback
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(input => {
            input.addEventListener('input', updatePreview);
        });
    }
    
    /**
     * Setup form validation
     */
    setupFormValidation() {
        const form = document.getElementById('profile-edit-form');
        if (!form) return;
        
        form.addEventListener('submit', (e) => {
            // Validate display name (optional)
            const displayName = document.getElementById('display-name');
            if (displayName && displayName.value.length > 50) {
                e.preventDefault();
                alert('Display name must be less than 50 characters');
                displayName.focus();
                return false;
            }
            
            // Additional validation could be added here
            
            return true;
        });
    }
    
    /**
     * Setup warning for unsaved changes
     */
    setupUnsavedChangesWarning() {
        window.addEventListener('beforeunload', (e) => {
            if (this.formModified) {
                // Standard way of showing a confirmation dialog before leaving
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
        
        // Reset the modified flag when form is submitted successfully
        const form = document.getElementById('profile-edit-form');
        if (form) {
            form.addEventListener('submit', () => {
                // This will be set to false only after successful submission
                // The actual reset happens in the fetch response handler
                setTimeout(() => {
                    this.formModified = false;
                }, 500);
            });
        }
    }
}

// Create a global instance
window.profileCustomization = new ProfileCustomization();