from functools import wraps
from flask import request
from flask_login import current_user
from models.api_log import APILog
import logging

def log_api_request(action, service):
    """
    Decorator to log API requests.

    Args:
        action (str): The action being performed (e.g., 'thread_create', 'thread_interaction')
        service (str): The service being used (e.g., 'chat', 'live', 'spotify', 'friends')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Only log if user is authenticated
            if current_user.is_authenticated:
                # Extract relevant details from request
                details = {}

                # For POST requests, extract non-sensitive data
                if request.method == 'POST' and request.is_json:
                    json_data = request.get_json()
                    if json_data:
                        # Don't log message content for privacy
                        if 'message' in json_data:
                            details['has_message'] = True

                        # Log thread_id if available
                        if 'thread_id' in json_data:
                            details['thread_id'] = json_data['thread_id']

                        # Log room_id if available
                        if 'room_id' in json_data:
                            details['room_id'] = json_data['room_id']

                        # Log model if available
                        if 'model' in json_data:
                            details['model'] = json_data['model']

                # Add URL path
                details['path'] = request.path

                # Add any route parameters
                if kwargs:
                    for key, value in kwargs.items():
                        if key not in ['message', 'content']:  # Skip sensitive data
                            details[key] = value

                # Log the request
                try:
                    logging.info(f"Logging API request: {action} for service {service} by {current_user.username}")
                    APILog.log_action(
                        user=current_user,
                        action=action,
                        service=service,
                        details=details
                    )
                    logging.info(f"Successfully logged API request: {action} for service {service}")
                except Exception as e:
                    logging.error(f"Error logging API request: {str(e)}")

            # Call the original function
            return f(*args, **kwargs)

        return decorated_function

    return decorator
