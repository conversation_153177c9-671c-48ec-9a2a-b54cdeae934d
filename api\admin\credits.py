from flask import jsonify, request
from flask_login import current_user, login_required
from functools import wraps
import logging
from datetime import datetime, timezone

from models.user import User
from models.user_credit import UserCredit
from models.credit_transaction import CreditTransaction
from models.model_credit_cost import ModelCreditCost
from . import admin_api

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({'error': 'Admin privileges required'}), 403
        return f(*args, **kwargs)
    return decorated_function

@admin_api.route('/credits/users', methods=['GET'])
@login_required
@admin_required
def get_all_user_credits():
    """
    Get credit balances for all users
    """
    try:
        logger.info("Fetching all user credits")
        
        # Get all user credits
        user_credits = UserCredit.objects().all()
        logger.info(f"Found {len(user_credits)} user credit records")

        # Format user credits for response
        formatted_credits = []
        for credit in user_credits:
            try:
                # Get user information
                user = User.objects(id=credit.user.id).first()
                if not user:
                    logger.warning(f"User not found for credit record: {credit.user.id}")
                    continue

                formatted_credits.append({
                    'user_id': str(credit.user.id),
                    'username': user.username,
                    'email': user.email,
                    'balance': credit.balance,
                    'total_earned': credit.total_earned,
                    'last_updated': credit.last_updated.isoformat()
                })
            except Exception as inner_e:
                logger.error(f"Error processing credit record: {str(inner_e)}")
                # Continue with next record instead of failing completely
                continue

        logger.info(f"Returning {len(formatted_credits)} formatted credit records")
        return jsonify(formatted_credits)

    except Exception as e:
        import traceback
        logger.error(f"Error getting all user credits: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/credits/update', methods=['POST'])
@login_required
@admin_required
def update_user_credits():
    """
    Update a user's credit balance
    """
    try:
        data = request.json

        # Validate required fields
        if not data or 'user_id' not in data or 'amount' not in data:
            return jsonify({'error': 'Missing required fields: user_id, amount'}), 400

        user_id = data['user_id']
        amount = int(data['amount'])
        reason = data.get('reason', 'Admin adjustment')

        # Validate user exists
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Add credits to user's balance
        if not UserCredit.add_credits(user_id, amount):
            return jsonify({'error': 'Failed to update user credits'}), 500

        # Log credit transaction
        CreditTransaction.log_addition(user_id, amount, description=reason)

        # Get updated user credit record
        user_credit = UserCredit.get_or_create(user_id)

        return jsonify({
            'success': True,
            'user_id': str(user_id),
            'username': user.username,
            'email': user.email,
            'new_balance': user_credit.balance,
            'amount_added': amount
        })

    except Exception as e:
        logger.error(f"Error updating user credits: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/credits/model-costs', methods=['GET'])
@login_required
@admin_required
def get_model_costs():
    """
    Get credit costs for all models
    """
    try:
        # Get all model costs
        model_costs = ModelCreditCost.objects().all()

        # Format model costs for response
        formatted_costs = []
        for cost in model_costs:
            formatted_costs.append({
                'model_name': cost.model_name,
                'credit_cost': cost.credit_cost,
                'last_updated': cost.last_updated.isoformat()
            })

        return jsonify(formatted_costs)

    except Exception as e:
        logger.error(f"Error getting model costs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/credits/model-cost', methods=['POST'])
@login_required
@admin_required
def update_model_cost():
    """
    Update credit cost for a specific model
    """
    try:
        data = request.json
        logger.info(f"Received model cost update request: {data}")

        # Validate required fields
        if not data or 'model_name' not in data or 'credit_cost' not in data:
            logger.error("Missing required fields in model cost update request")
            return jsonify({'error': 'Missing required fields: model_name, credit_cost'}), 400

        model_name = data['model_name']
        credit_cost = int(data['credit_cost'])

        # Validate credit cost
        if credit_cost < 0:
            logger.error(f"Invalid credit cost: {credit_cost}")
            return jsonify({'error': 'Credit cost must be a non-negative integer'}), 400

        logger.info(f"Updating model cost for {model_name} to {credit_cost}")
        
        # Update model cost
        if not ModelCreditCost.set_cost(model_name, credit_cost):
            logger.error(f"Failed to update model cost for {model_name}")
            return jsonify({'error': 'Failed to update model cost'}), 500

        # Verify the update by retrieving the latest cost from the database
        # No need to reload, just query directly to get fresh data
        updated_cost = ModelCreditCost.objects(model_name=model_name.strip().lower()).first()
        
        if updated_cost:
            actual_cost = updated_cost.credit_cost
            logger.info(f"Verified model cost update for {model_name}: {actual_cost}")
            
            if actual_cost != credit_cost:
                logger.warning(f"Cost mismatch after update: requested={credit_cost}, actual={actual_cost}")
        else:
            logger.warning(f"Could not verify model cost update for {model_name}")
            actual_cost = credit_cost  # Assume it worked if we can't verify

        return jsonify({
            'success': True,
            'model_name': model_name,
            'credit_cost': actual_cost,
            'updated_at': datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        logger.error(f"Error updating model cost: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/credits/delete-model-cost', methods=['POST'])
@login_required
@admin_required
def delete_model_cost():
    """
    Delete credit cost for a specific model
    """
    try:
        data = request.json
        logger.info(f"Received model cost delete request: {data}")

        # Validate required fields
        if not data or 'model_name' not in data:
            logger.error("Missing required field in model cost delete request")
            return jsonify({'error': 'Missing required field: model_name'}), 400

        model_name = data['model_name']
        
        # Delete model cost
        if ModelCreditCost.delete_cost(model_name):
            return jsonify({
                'success': True,
                'message': f'Model cost for {model_name} deleted successfully'
            })
        else:
            return jsonify({'error': f'Model cost for {model_name} not found or could not be deleted'}), 404

    except Exception as e:
        logger.error(f"Error deleting model cost: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/credits/initialize-defaults', methods=['POST'])
@login_required
@admin_required
def initialize_default_costs():
    """
    Initialize default costs for common models
    """
    try:
        # Initialize default costs
        ModelCreditCost.initialize_default_costs()

        # Get all model costs after initialization
        model_costs = ModelCreditCost.objects().all()

        # Format model costs for response
        formatted_costs = []
        for cost in model_costs:
            formatted_costs.append({
                'model_name': cost.model_name,
                'credit_cost': cost.credit_cost,
                'last_updated': cost.last_updated.isoformat()
            })

        return jsonify({
            'success': True,
            'message': 'Default model costs initialized',
            'model_costs': formatted_costs
        })

    except Exception as e:
        logger.error(f"Error initializing default costs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
