:root {
    --min-width: 320px;
    --max-width: 1920px;
    --content-padding: 20px;
    --menu-width: 60px;
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden; /* Prevents scrolling */
}

body {
    font-family: Arial, sans-serif;
    background: url('https://c.pxhere.com/photos/c9/3e/weisshorn_valais_switzerland_mountains_alpine_snow_high_mountains_blue-981049.jpg!d') no-repeat center center fixed;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

/* Global scrollbar styling */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.2);
}

/* Webkit scrollbar styling (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid transparent;
    background-clip: padding-box;
}

/* Remove floating scrollbar */
::-webkit-scrollbar-track-piece {
    background-color: transparent;
}

/* Ensure content doesn't shift when scrollbar appears */
html {
    scrollbar-gutter: auto;
}

.container {
    width: 90%;
    height: 90vh; /* Use viewport height instead of percentage */
    max-height: 900px;
    min-height: 500px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    display: flex;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
    position: fixed; /* Fix the container in place */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Base menu styles for desktop */
.menu {
    width: 60px;
    background-color: rgba(30, 30, 30, 0.8);
    display: flex;
    flex-direction: column;
    padding: 10px;
    box-shadow: inset -1px 0 0 #333;
    border-radius: 20px 0 0 20px;
    position: relative;
    opacity: 1;
    transform: none;
    height: auto;
}

/* Mobile-specific menu styles */
@media (max-width: 768px) {
    .menu {
        position: fixed;
        top: 50%;
        left: 0;
        transform: translateY(-50%) translateX(-230px);
        height: 300px;
        width: 250px;
        background: rgba(0, 0, 0, 0.95);
        z-index: 996;
        transition: transform 0.3s ease, opacity 0.5s ease;
        border-radius: 0 15px 15px 0;
        box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
        opacity: 0.3;
    }

    .menu.collapsed {
        transform: translateY(-50%) translateX(-230px);
    }

    .menu:not(.collapsed) {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
}

/* Ensure desktop menu stays fixed */
@media (min-width: 769px) {
    .menu {
        position: relative;
        transform: none !important;
        opacity: 1 !important;
        width: 60px;
        height: auto;
        transition: none;
    }

    .menu.collapsed {
        transform: none !important;
    }

    /* Hide mobile-specific elements on desktop */
    .menu::after,
    .menu-overlay {
        display: none !important;
    }
}

/* Base playlist container styles */
.playlists-container {
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 20px;
    padding-top: 10px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Desktop playlist layout */
@media (min-width: 769px) {
    .playlists-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 5px;
    }

    .playlist-item {
        width: 40px;
        height: 40px;
        margin: 0 0 10px 0; /* Adjust vertical spacing between items */
    }
}

/* Mobile playlist grid layout */
@media (max-width: 768px) {
    .playlists-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, 40px);
        grid-gap: 10px;
        justify-content: center;
        width: 100%;
    }

    .playlist-item {
        margin: 0; /* Remove margin since we're using grid-gap */
    }
}

/* Common playlist item styles */
.playlist-item {
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;
    position: relative;
}

.playlist-item:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

.playlist-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.playlist-item.active {
    border: 2px solid #1db954;
}

/* Custom scrollbar for webkit browsers */
.playlists-container::-webkit-scrollbar {
    width: 4px;
}

.playlists-container::-webkit-scrollbar-track {
    background: transparent;
}

.playlists-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

/* Keep existing menu styles but adjust for the new layout */
.menu-item.search,
.menu-item.logout {
    margin-top: 10px;
    text-align: center;
}

.menu-item.search {
    margin-top: auto;
}

.menu-item.logout {
    color: #ff4444;
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.menu-item.logout:hover {
    transform: scale(1.1);
}

.content {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: row;
    gap: 15px;
    overflow-y: auto;
}

.left-section {
    width: 300px;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.link-bar {
    background-color: rgba(44, 44, 44, 0.8);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    position: relative;
    z-index: 997;
}

.info-box {
    background-color: rgba(44, 44, 44, 0.8);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    flex: 1;
    position: relative;
    z-index: 4;
    min-height: 200px;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .left-section {
        width: 100%;
    }

    .link-bar,
    .info-box {
        margin: 0;
        border-radius: 15px;
        padding: 15px;
    }

    .device-manager {
        margin-top: 15px;
    }
}

.main-section {
    flex: 1;
    min-width: 280px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.main-section .main-window {
    flex: 1;
    background-color: rgba(44, 44, 44, 0.8);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow content to shrink */
}
.main-window {
    flex: 1;
    background-color: rgba(44, 44, 44, 0.8);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow content to shrink */
    overflow: hidden; /* Disable scrolling */
}

.main-section .main-window {
    overflow: hidden; /* Disable scrolling */
}

.main-section .now-playing {
    position: relative;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    min-height: 80px;
    background-color: rgba(44, 44, 44, 0.9);
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    gap: 15px;
    box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    margin-top: auto; /* Push to bottom of main-window */
}

.track-info {
    flex: 1;
}

.track-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.track-artist {
    font-size: 0.9em;
    color: #b3b3b3;
}

.control-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.control-buttons button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 20px;
    padding: 5px;
    transition: color 0.3s;
}

.control-buttons button:hover {
    color: #1db954;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.volume-control input {
    width: 100px;
    height: 6px;
    background-color: #b3b3b3;
    border-radius: 10px;
    cursor: pointer;
}

.volume-control .volume-icon {
    font-size: 20px;
    cursor: pointer;
}

/* Add these new styles */
.player-content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    /* Add this to account for the now-playing bar on mobile */
    padding-bottom: 100px;
}

.track-artwork {
    width: 60%;
    max-width: 300px;
    margin: 0 auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    /* Ensure aspect ratio is maintained */
    aspect-ratio: 1 / 1;
}

.track-artwork img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.progress-section {
    width: 100%;
    max-width: 512px;
    margin: 30px auto 0;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .player-content-wrapper {
        /* Adjust padding to account for the now-playing bar */
        padding-bottom: 90px;
        /* Ensure full height minus the now-playing bar */
        height: calc(100vh - 80px);
    }

    .track-artwork {
        width: 70%;
        max-width: 280px;
        margin: 0 auto;
    }

    .progress-section {
        width: 90%;
        max-width: 400px;
        margin: 20px auto 0;
        padding: 0;
    }
}

/* Smaller mobile devices */
@media (max-width: 480px) {
    .player-content-wrapper {
        padding-bottom: 80px;
        /* Adjust height for smaller devices */
        height: calc(100vh - 70px);
    }

    .track-artwork {
        width: 80%;
        max-width: 250px;
    }
}

/* Landscape mode adjustments */
@media (max-height: 480px) and (orientation: landscape) {
    .player-content-wrapper {
        padding: 10px 20px 70px;
        height: calc(100vh - 60px);
    }

    .track-artwork {
        width: 40%;
        max-width: 180px;
    }
}

/* Mobile-specific progress bar styles */
@media (max-width: 768px) {
    .progress-section {
        width: 90%; /* Slightly narrower on mobile */
        max-width: 400px;
        margin: 0 auto 20px;
        padding: 0;
        position: relative;
        z-index: 2;
    }

    .progress-container {
        width: 100%;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center; /* Center horizontally */
        position: relative;
    }

    .time-info {
        width: 100%;
        display: flex;
        justify-content: space-between;
        color: #b3b3b3;
        font-size: 11px; /* Slightly smaller font on mobile */
        margin-top: 5px;
        padding: 0 5px; /* Add small padding for time labels */
    }

    .progress-bar {
        width: 100%;
        height: 3px; /* Slightly thinner on mobile */
    }

    .progress-bar:hover {
        height: 5px; /* Slightly thinner hover state on mobile */
    }
}
.mobile-volume-toggle {
    display: none; /* Hidden by default on desktop */
}

.progress-container {
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
    position: relative;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    z-index: 1;
    transform: translateZ(0); /* Hardware acceleration */
}

#progress {
    width: 0%;
    height: 100%;
    background-color: #1db954;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 0;
    transform: translateZ(0); /* Hardware acceleration */
    will-change: width; /* Optimize for animations */
    transition: width 0.016s linear;
}

/* Handle styles */
#progress::before {
    content: '';
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background-color: #ffffff;
    border-radius: 50%;
    display: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Show handle on hover */
.progress-bar:hover #progress::before {
    display: block;
}

/* Hover states */
.progress-bar:hover {
    height: 6px;
}

.progress-bar:hover #progress {
    background-color: #1ed760;
}

/* Add hover effect for progress bar */
.progress-bar:hover::after {
    content: '';
    position: absolute;
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.hover-time {
    position: absolute;
    display: none;
    background-color: #282828;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transform: translateX(-50%);
    top: -25px;
    pointer-events: none;
}

.track-progress-time {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    color: #b3b3b3;
    font-size: 12px;
}

.next-track-preview {
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.next-track-label {
    font-size: 12px;
    color: #b3b3b3;
    margin-bottom: 8px;
}

.next-track-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

#next-track-artwork {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    object-fit: cover;
}

.next-track-info {
    flex: 1;
    min-width: 0;
}

#next-track-name {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#next-track-artist {
    font-size: 12px;
    color: #b3b3b3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.queue-container {
    margin-top: 20px;
    overflow-y: auto;
    max-height: 300px;
}

.queue-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.queue-track-artwork {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 4px;
}

.queue-track-info {
    flex-grow: 1;
}

.queue-track-name {
    font-weight: bold;
    margin-bottom: 4px;
}

.queue-track-artist {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.7);
}

.error-message {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 5px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateX(-50%) translateY(10px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

.search-container {
    width: 100%;
    position: relative;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-input-wrapper input {
    width: 94%;
    padding: 10px 5px 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input-wrapper input:focus {
    background: rgba(255, 255, 255, 0.15);
    outline: none;
    box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.5);
}

.search-input-wrapper .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #b3b3b3;
    pointer-events: none;
}

.search-results {
    position: absolute;
    left: 0;
    right: 0;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    background: #282828;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    margin: 0 10px;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin: 4px;
    background: rgba(255, 255, 255, 0.05);
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.search-result-artwork {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 12px;
    object-fit: cover;
}

.search-result-info {
    flex: 1;
    min-width: 0;
}

.search-result-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #fff;
}

.search-result-artist {
    font-size: 12px;
    color: #b3b3b3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Custom scrollbar for search results */
.search-results::-webkit-scrollbar {
    width: 8px;
}

.search-results::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.search-results::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.search-results::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
}

.liked-songs {
    width: 40px;
    height: 40px;
    margin: 0 10px 50px;
    background: linear-gradient(135deg, #450af5, #c4efd9);
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.liked-songs:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

.liked-songs::before {
    content: '♥';
    color: white;
    font-size: 20px;
}

.liked-songs.active {
    border: 2px solid #1db954;
}

/* Add media queries for different screen sizes */
@media (max-width: 768px) {
    .content {
        flex-direction: column;
    }

    .left-section {
        width: 100%;
    }

    .main-section .now-playing {
        height: 60px;
        min-height: 60px;
        padding: 0 10px;
    }

    .control-buttons {
        gap: 10px;
    }

    .volume-control {
        position: fixed;
        bottom: 70px; /* Position above the now-playing bar */
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.9);
        padding: 10px 20px;
        z-index: 995;
        display: none; /* Hidden by default */
        justify-content: center;
        align-items: center;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    }

    .volume-control.show {
        display: flex;
    }

    .volume-control input {
        width: 80%;
        max-width: 300px;
    }

    .mobile-volume-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        color: #b3b3b3;
        padding: 8px;
        cursor: pointer;
    }

    .mobile-volume-toggle:hover {
        color: #fff;
    }

    .track-info {
        max-width: 150px;
    }

    .track-name {
        font-size: 14px;
    }

    .track-artist {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 5px;
    }

    .container {
        border-radius: 10px;
    }

    .menu {
        width: 50px;
        padding: 5px;
    }

    .playlist-item {
        width: 35px;
        height: 35px;
    }

    .content {
        padding: 10px;
        gap: 10px;
    }

    .track-artwork {
        max-width: 280px;
    }

    .main-section .now-playing {
        height: 50px;
        min-height: 50px;
    }

    .control-buttons button {
        font-size: 16px;
        padding: 3px;
    }
}

/* Add styles for landscape orientation on mobile */
@media (max-height: 480px) and (orientation: landscape) {
    .container {
        height: calc(100vh - 20px);
    }

    .track-artwork {
        max-width: 200px;
        margin: 0 auto 15px;
    }

    .content {
        padding-bottom: 60px; /* Make space for now-playing bar */
    }
}

/* Ensure minimum spacing */
@media (min-width: 1920px) {
    .container {
        max-width: 1800px;
    }
}

/* Menu toggle button */
.menu-toggle {
    display: none;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    color: #b3b3b3;
    margin-right: 15px;
}

.menu-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Menu states - only apply transform on mobile */
.menu {
    transition: transform 0.3s ease;
    position: relative;
    width: var(--menu-width);
    transform: translateX(0);
    touch-action: pan-y pinch-zoom;
}

/* Only collapse menu on mobile */
@media (max-width: 768px) {
    .menu.collapsed {
        transform: translateX(calc(-1 * var(--menu-width)));
    }

    .menu {
        position: fixed;
        top: 50%;
        left: 0;
        transform: translateY(-50%) translateX(-230px);
        height: 300px;
        width: 250px;
        background: rgba(0, 0, 0, 0.95);
        z-index: 996;
        transition: transform 0.3s ease, opacity 0.5s ease;
        border-radius: 0 15px 15px 0;
        box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
        opacity: 0.3;
    }
}

/* Desktop menu should always be visible */
@media (min-width: 769px) {
    .menu {
        transform: none !important;
        opacity: 1 !important;
        position: relative;
        width: 60px;
        height: auto;
    }

    .menu.collapsed {
        transform: none !important;
    }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .container {
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        position: fixed;
        top: 0;
        left: 0;
        transform: none;
    }

    .menu {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 250px; /* Wider menu for mobile */
        background: rgba(0, 0, 0, 0.95);
        z-index: 996; /* Lower than up next and search sections */
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .menu.collapsed {
        transform: translateX(-100%);
    }

    .menu:not(.collapsed) {
        transform: translateX(0);
    }

    /* Overlay for menu */
    .menu-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 995; /* Lower than menu */
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .menu:not(.collapsed) + .menu-overlay {
        display: block;
        opacity: 1;
    }

    /* Remove menu toggle button */
    .menu-toggle {
        display: none;
    }

    /* Adjust playlist items for wider mobile menu */
    .playlist-item {
        width: 60px;
        height: 60px;
        margin: 10px auto;
    }

    .content {
        width: 100%;
        height: 100%;
        padding: 10px;
        padding-bottom: 90px; /* Make space for now playing bar */
        margin-left: 0;
    }

    .main-section {
        position: relative;
        height: 100%;
    }

    .main-section .now-playing {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background: rgba(44, 44, 44, 0.95);
        border-radius: 0;
        z-index: 999; /* Higher than menu */
    }

    .menu-toggle {
        position: fixed;
        top: 10px;
        left: 10px;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.7);
        border: none;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1000; /* Higher than now-playing */
        transition: transform 0.3s ease;
    }

    /* Add overlay when menu is open */
    .menu-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 997; /* Lower than menu */
    }

    .menu:not(.collapsed) + .menu-overlay {
        display: block;
    }

    /* Adjust content layout */
    .left-section {
        width: 100%;
        margin-bottom: 10px;
    }

    .main-window {
        margin-bottom: 80px; /* Space for now playing bar */
    }
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
    .main-section .now-playing {
        height: 70px;
        padding: 0 10px;
    }

    .content {
        padding-bottom: 80px;
    }

    .track-info {
        max-width: 150px;
    }

    .track-name {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .track-artist {
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Mobile menu styles */
@media (max-width: 768px) {
    .menu {
        position: fixed;
        top: 50%;
        left: 0;
        height: auto;
        min-height: 300px;
        width: 250px;
        background: rgba(0, 0, 0, 0.95);
        z-index: 996;
        border-radius: 0 15px 15px 0;
        box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
        padding: 15px 10px;
    }

    .menu.collapsed {
        animation: slideOut 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .menu:not(.collapsed) {
        animation: slideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    /* Pull tab indicator */
    .menu::after {
        content: '⋮';
        position: absolute;
        top: 50%;
        right: -20px;
        width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.95);
        border-radius: 0 4px 4px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        transform: translateY(-50%);
        box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
    }
}

/* Modify playlists container for grid layout */
.playlists-container {
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, 40px);

    justify-content: center;

    width: 100%;
}

/* Adjust playlist items for grid */
.playlist-item {
    width: 40px;
    height: 40px;
    margin: 0; /* Remove margin since we're using grid-gap */
}

/* Keep hover effects */
.playlist-item:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

/* Ensure liked songs section aligns with grid */
.liked-songs {
    margin: 10px;
    text-align: center;
}

.menu:hover,
.menu.active {
    opacity: 1;
}

/* Adjust menu height to accommodate grid */
.menu {
    height: auto;
    min-height: 300px;
    padding: 15px 10px;
}

/* Pull tab indicator */
.menu::after {
    content: '⋮';
    position: absolute;
    top: 50%;
    right: -20px;
    width: 20px;
    height: 60px;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 0 8px 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    transform: translateY(-50%);
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.3);
    transition: opacity 0.5s ease;
    opacity: 0.3; /* Match menu opacity */
}

.menu:hover::after,
.menu.active::after {
    opacity: 1;
}

/* Remove the full-screen overlay */
.menu-overlay {
    display: none !important;
}

/* Hide the menu toggle button since we're using the pull tab */
.menu-toggle {
    display: none !important;
}

/* Adjust playlist items for the smaller menu */
.playlist-item {
    width: 40px;
    height: 40px;
    margin: 5px auto;
}

.liked-songs {
    margin: 0 10px 20px;
}

/* Device Manager Styles */
.device-manager {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 15px;
    margin-top: 15px;
}

.device-label {
    color: #b3b3b3;
    font-size: 12px;
    margin-bottom: 8px;
}

.device-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.device-button:hover {
    background: rgba(255, 255, 255, 0.15);
}

.device-button .device-name {
    flex-grow: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.device-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #282828;
    border-radius: 8px;
    margin-top: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: none;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1040;
}

.device-dropdown.show {
    display: block;
}

.device-error {
    color: #ff4444;
    padding: 12px;
    text-align: center;
    font-size: 14px;
}

.device-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #ffffff;
}

.device-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.device-option.active {
    background: rgba(29, 185, 84, 0.3);
}

.device-option i {
    width: 20px;
    text-align: center;
}

.device-option i.fa-check {
    margin-left: auto;
    color: #1DB954;
}

/* Mobile styles */
@media (max-width: 768px) {
    .device-manager {
        margin: 10px;
        padding: 12px;
    }

    .device-button {
        padding: 8px;
        font-size: 14px;
    }
    
    .device-dropdown {
        position: fixed;
        left: 10px;
        right: 10px;
        bottom: auto;
        max-height: 50vh;
    }
}

/* Desktop-specific main window size */
@media (min-width: 769px) {
    .main-window {
        width: fit-content;
        max-width: 500px;
        max-height: fit-content;
        align-self: center;
        margin: auto;
    }

    .player-content-wrapper {
        width: 100%;
        max-width: 420px;
        margin: 0 auto;
        padding: 40px 00px;
        padding-bottom: 40px;
    }

    .track-artwork {
        width: 300px;
        max-width: 300px;
    }

    .progress-section {
        width: 100%;
        max-width: 300px; /* Match artwork width */
        margin: 30px auto 0;
        padding: 0; /* Remove padding to prevent overflow */
    }

    /* Add padding inside main window */
    .main-section .main-window {
        padding: 30px;
    }
}

/* Keep existing mobile styles unchanged */
@media (max-width: 768px) {

    .player-content-wrapper {
        padding-bottom: 90px;
        height: calc(100vh - 80px);
    }

    .track-artwork {
        width: 70%;
        max-width: 280px;
    }

    .progress-section {
        width: 90%;
        max-width: 400px;
        margin: 20px auto 0;
        padding: 0;
    }
}

/* Screen reader only class */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@keyframes slideIn {
    from {
        transform: translateY(-50%) translateX(-270px);
        opacity: 0.3;
    }
    to {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
    to {
        transform: translateY(-50%) translateX(-270px);
        opacity: 0.3;
    }
}
