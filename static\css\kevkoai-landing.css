/* KevkoAI Landing Page Styles */

/* Variables */
:root {
    --primary-color: #0a0e17;
    --secondary-color: #1e293b;
    --accent-color-1: #6366f1; /* Indigo */
    --accent-color-2: #8b5cf6; /* Violet */
    --accent-color-3: #ec4899; /* Pink */
    --accent-color-4: #06b6d4; /* Cyan */
    --text-color: #f8fafc;
    --text-color-muted: rgba(248, 250, 252, 0.7);
    --background-color: #0a0e17;
    --card-bg: rgba(30, 41, 59, 0.5);
    --card-border: rgba(51, 65, 85, 0.5);
    --primary-gradient: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    --secondary-gradient: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-3));
    --cta-gradient: linear-gradient(135deg, var(--accent-color-3), var(--accent-color-4));
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --highlight-color: #e2e8f0;
    --glow-color: rgba(99, 102, 241, 0.5);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--primary-color);
    background-image:
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.05) 0%, transparent 60%);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    cursor: none; /* Hide default cursor */
    position: relative;
}

/* Custom Cursor */
.cursor-outer {
    position: fixed;
    width: 30px;
    height: 30px;
    border: 2px solid var(--accent-color-1);
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    transition: width 0.2s, height 0.2s, border-color 0.2s;
    z-index: 9999;
    mix-blend-mode: difference;
    left: -100px; /* Hide initially */
    top: -100px;
    box-shadow: 0 0 10px var(--glow-color);
    opacity: 0.8;
}

.cursor-inner {
    position: fixed;
    width: 8px;
    height: 8px;
    background: var(--accent-color-2);
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    z-index: 9999;
    transition: width 0.1s, height 0.1s, background-color 0.1s;
    mix-blend-mode: difference;
    left: -100px; /* Hide initially */
    top: -100px;
    box-shadow: 0 0 5px var(--glow-color);
}

/* Cursor hover effects */
a:hover ~ .cursor-outer,
button:hover ~ .cursor-outer {
    width: 40px;
    height: 40px;
    border-color: var(--accent-color-3);
    box-shadow: 0 0 15px var(--accent-color-3);
}

a:hover ~ .cursor-inner,
button:hover ~ .cursor-inner {
    background-color: var(--accent-color-3);
    box-shadow: 0 0 8px var(--accent-color-3);
}

/* Cursor click effects */
.cursor-outer.click {
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0.7;
    border-color: var(--accent-color-4);
    box-shadow: 0 0 20px var(--accent-color-4);
}

.cursor-inner.click {
    transform: translate(-50%, -50%) scale(0.7);
    background-color: var(--accent-color-4);
}

/* Cursor hover effects */
.cursor-outer.hover {
    width: 40px;
    height: 40px;
    border-color: var(--accent-color-3);
    box-shadow: 0 0 15px var(--accent-color-3);
}

.cursor-inner.hover {
    background-color: var(--accent-color-3);
    box-shadow: 0 0 8px var(--accent-color-3);
}

/* Typographic Elements */
.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.shape {
    position: absolute;
    font-family: var(--font-family);
    font-weight: 700;
    line-height: 1;
    letter-spacing: -0.05em;
    transition: all 0.5s ease-out;
}

.shape.letter-a {
    font-size: 40rem;
    top: -10%;
    left: -10%;
    transform: rotate(-10deg);
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    opacity: 0.15;
    animation: float-slow 15s ease-in-out infinite;
}

.shape.letter-i {
    font-size: 35rem;
    bottom: -15%;
    right: -5%;
    transform: rotate(5deg);
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-3));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    opacity: 0.15;
    animation: float-slow 18s ease-in-out infinite reverse;
}

.shape.letter-k {
    font-size: 25rem;
    top: 40%;
    right: 5%;
    background: linear-gradient(135deg, var(--accent-color-3), var(--accent-color-4));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    opacity: 0.1;
    animation: float-slow 20s ease-in-out infinite;
}

.shape.grid-lines {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image:
        linear-gradient(to right, rgba(99, 102, 241, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 40px 40px;
    opacity: 0.15;
    animation: pulse 8s ease-in-out infinite;
}

.shape.dot-grid {
    width: 60%;
    height: 60%;
    top: 20%;
    right: 0;
    background-image: radial-gradient(rgba(139, 92, 246, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.15;
    animation: pulse 10s ease-in-out infinite reverse;
}

.shape.line {
    width: 1px;
    height: 80vh;
    background: linear-gradient(to bottom, transparent, var(--accent-color-1), transparent);
    left: 15%;
    top: 10%;
    opacity: 0.2;
    filter: blur(1px);
    animation: glow 5s ease-in-out infinite;
}

.shape.line-2 {
    width: 1px;
    height: 60vh;
    background: linear-gradient(to bottom, transparent, var(--accent-color-2), transparent);
    right: 25%;
    top: 20%;
    opacity: 0.2;
    filter: blur(1px);
    animation: glow 7s ease-in-out infinite reverse;
}

.shape.circle {
    width: 400px;
    height: 400px;
    border: 1px solid var(--accent-color-3);
    border-radius: 50%;
    top: 60%;
    left: 60%;
    opacity: 0.1;
    transform: translateX(-50%) translateY(-50%);
    box-shadow: 0 0 30px rgba(236, 72, 153, 0.1);
    animation: pulse 12s ease-in-out infinite;
}

/* Animations for typographic elements */
@keyframes float-slow {
    0%, 100% { transform: translateY(0) rotate(-10deg); }
    50% { transform: translateY(-10px) rotate(-8deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.2; }
}

@keyframes glow {
    0%, 100% {
        opacity: 0.1;
        filter: blur(1px);
    }
    50% {
        opacity: 0.3;
        filter: blur(2px);
    }
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 5%; /* Slightly reduced padding */
    position: fixed; /* Changed to fixed for better positioning */
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100; /* Increased z-index to ensure it's above all content */
    backdrop-filter: blur(5px);
    background: rgba(10, 14, 23, 0.8); /* Increased opacity for better visibility */
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-3));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    letter-spacing: -0.02em;
    position: relative;
    padding-left: 15px;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
}

.logo::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1.4em;
    background: linear-gradient(to bottom, var(--accent-color-1), var(--accent-color-2));
    border-radius: 4px;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

nav a {
    color: var(--text-color-muted);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.95rem;
    letter-spacing: 0.02em;
    padding: 0.5rem 0;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--accent-color-1), var(--accent-color-2));
    transition: width 0.3s ease;
    border-radius: 2px;
}

nav a:hover {
    color: var(--text-color);
    text-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
}

nav a:hover::after {
    width: 100%;
}

.login-btn {
    color: var(--text-color);
    padding: 0.6rem 1.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    border-radius: 4px;
    box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-3));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
}

.login-btn:hover::before {
    opacity: 1;
}

.login-btn span {
    position: relative;
    z-index: 1;
}

/* Hero Section */
.hero {
    height: 100vh;
    min-height: 800px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6rem 5% 0; /* Added top padding to account for header height */
    position: relative;
    overflow: hidden;
    margin-bottom: 4rem;
}

.hero::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.1) 0%, transparent 60%);
    animation: pulse 8s ease-in-out infinite;
}

.hero-content {
    max-width: 600px;
    animation: fadeIn 1s ease-out;
    position: relative;
    z-index: 5; /* Ensure hero content is above bubbles */
    text-align: left;
    flex: 1;
    padding-top: 2rem; /* Added padding to ensure content is below header */
}

.hero-badge {
    display: inline-block;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
    color: var(--accent-color-1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    margin-top: 1rem; /* Added top margin for better spacing */
    border: 1px solid rgba(99, 102, 241, 0.3);
    backdrop-filter: blur(4px);
    letter-spacing: 0.05em;
    text-transform: uppercase;
    animation: fadeInUp 1s ease-out;
    position: relative; /* Ensure proper stacking */
    z-index: 5; /* Higher z-index to ensure visibility */
}

.hero h1 {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    color: var(--text-color);
    position: relative;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero h1 .highlight {
    background: linear-gradient(135deg, var(--accent-color-1) 0%, var(--accent-color-2) 50%, var(--accent-color-3) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    animation: gradient-shift 8s ease infinite;
    background-size: 300% 300%;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero h1::after {
    content: '';
    position: absolute;
    width: 100px;
    height: 5px;
    background: linear-gradient(to right, var(--accent-color-1), var(--accent-color-2));
    bottom: -15px;
    left: 0;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    color: var(--text-color-muted);
    max-width: 550px;
    font-weight: 300;
    letter-spacing: 0.01em;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.primary-cta {
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-3));
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.secondary-cta {
    background: transparent;
    border: 1px solid var(--accent-color-1);
    color: var(--text-color);
    box-shadow: none;
}

.secondary-cta:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.hero-stats {
    display: flex;
    gap: 2.5rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.stat-item {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-color-muted);
    margin-top: 0.5rem;
}

.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 5;
}

/* AI Dashboard */
.ai-dashboard {
    width: 500px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 16px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 102, 241, 0.2);
    position: relative;
    overflow: hidden;
    animation: float 6s ease-in-out infinite;
    z-index: 5;
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: white;
}

.dashboard-controls {
    display: flex;
    gap: 8px;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
}

.dashboard-content {
    padding: 20px;
}

.model-selector {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background: rgba(51, 65, 85, 0.5);
    padding: 10px 15px;
    border-radius: 8px;
}

.model-label {
    font-size: 0.9rem;
    color: var(--text-color-muted);
    margin-right: 10px;
}

.model-dropdown {
    display: flex;
    align-items: center;
    background: rgba(99, 102, 241, 0.2);
    padding: 5px 12px;
    border-radius: 6px;
    cursor: pointer;
}

.selected-model {
    font-weight: 500;
    margin-right: 8px;
}

.dropdown-arrow {
    font-size: 0.7rem;
    opacity: 0.7;
}

.ai-conversation {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.ai-message, .user-message {
    display: flex;
    gap: 12px;
    max-width: 100%;
}

.user-message {
    flex-direction: row-reverse;
}

.ai-avatar, .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    flex-shrink: 0;
}

.ai-avatar {
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
}

.user-avatar {
    background: linear-gradient(135deg, var(--accent-color-3), var(--accent-color-4));
}

.message-content {
    background: rgba(51, 65, 85, 0.5);
    padding: 12px 15px;
    border-radius: 12px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.ai-message .message-content {
    border-top-left-radius: 0;
    background: rgba(99, 102, 241, 0.2);
}

.user-message .message-content {
    border-top-right-radius: 0;
    background: rgba(236, 72, 153, 0.2);
    text-align: right;
}

.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 0;
    padding-left: 20px;
}

.message-content li {
    margin-bottom: 5px;
}

.chart-placeholder {
    display: flex;
    align-items: flex-end;
    height: 100px;
    gap: 8px;
    margin-top: 10px;
    padding: 10px;
    background: rgba(30, 41, 59, 0.5);
    border-radius: 8px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, var(--accent-color-1), var(--accent-color-2));
    border-radius: 4px 4px 0 0;
    min-height: 10%;
    transition: height 0.5s ease;
}

.ai-tools {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.tool-button {
    background: rgba(51, 65, 85, 0.5);
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-button:hover {
    background: rgba(99, 102, 241, 0.3);
    transform: translateY(-2px);
}

.floating-element {
    position: absolute;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(99, 102, 241, 0.2);
    backdrop-filter: blur(5px);
    z-index: 1;
}

.code-block {
    bottom: -30px;
    right: -40px;
    transform: rotate(5deg);
    font-family: monospace;
    font-size: 0.85rem;
    color: var(--accent-color-4);
    width: 220px;
}

.code-block pre {
    margin: 0;
    white-space: pre-wrap;
}
    animation: fadeIn 1.5s ease-out;
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 400px;
}

.floating-element {
    position: absolute;
    background: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite;
}

.floating-element.code-block {
    top: 20%;
    right: 10%;
    width: 300px;
    animation-delay: 0.5s;
}

.floating-element.data-visualization {
    bottom: 15%;
    left: 15%;
    width: 250px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation-delay: 1s;
}

.code-block pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--text-color);
    overflow-x: auto;
}

.code-block code {
    color: var(--text-color-muted);
}

.code-block code .keyword {
    color: var(--accent-color-2);
}

.code-block code .function {
    color: var(--accent-color-1);
}

.code-block code .string {
    color: var(--accent-color-3);
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-3));
    color: var(--text-color);
    padding: 1.2rem 3rem;
    border: none;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.05em;
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
    text-transform: uppercase;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.cta-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at var(--x-pos, center) var(--y-pos, center),
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(139, 92, 246, 0.4);
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover::after {
    opacity: 1;
}

.cta-button span {
    position: relative;
    z-index: 1;
}

/* AI Response Bubbles */
.ai-responses {
    position: absolute;
    width: 40%;
    height: 100vh;
    top: 0;
    right: 0;
    pointer-events: none;
    z-index: 2; /* Above background, below hero content */
    overflow: hidden; /* Ensure bubbles don't cause scrollbars */
}

.response-bubble {
    position: absolute;
    max-width: 280px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s, transform 0.5s;
    z-index: 1; /* Ensure bubbles are above background but below content */
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
}

.response-bubble:nth-child(1) {
    top: 20%;
    right: 15%;
}

.response-bubble:nth-child(2) {
    top: 40%;
    right: 25%;
}

.response-bubble:nth-child(3) {
    top: 60%;
    right: 10%;
}

.response-bubble:nth-child(4) {
    top: 30%;
    right: 5%;
}

.response-bubble:nth-child(5) {
    top: 75%;
    right: 20%;
}

.bubble-content {
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.3) 0%,
                rgba(139, 92, 246, 0.3) 50%,
                rgba(236, 72, 153, 0.3) 100%);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.bubble-content::before {
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.3) 0%,
                rgba(139, 92, 246, 0.3) 100%);
    transform: rotate(45deg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-right: none;
    border-top: none;
    bottom: -7px;
    left: 20px;
    backdrop-filter: blur(10px);
}

.response-bubble:nth-child(even) .bubble-content::before {
    left: auto;
    right: 20px;
}

.bubble-content p {
    margin: 0;
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.5;
    transition: opacity 0.3s ease;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.response-bubble.visible {
    opacity: 1;
    transform: translateY(0);
    animation: bubblePulse 4s ease-in-out infinite;
}

@keyframes bubblePulse {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-5px) scale(1.02); filter: drop-shadow(0 15px 20px rgba(0, 0, 0, 0.25)); }
}

/* Features Section */
.features {
    padding: 12rem 5%;
    text-align: center;
    margin-top: 4rem;
}

.features h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 5rem;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-3));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    display: inline-block;
    position: relative;
}

.features h2::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--accent-color-1), var(--accent-color-2));
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
}

.feature-card {
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.1) 0%,
                rgba(139, 92, 246, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 3.5rem;
    transition: transform 0.3s, box-shadow 0.3s;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s forwards;
    animation-delay: calc(var(--card-index, 0) * 0.2s);
}

.feature-card:nth-child(1) {
    --card-index: 1;
}

.feature-card:nth-child(2) {
    --card-index: 2;
}

.feature-card:nth-child(3) {
    --card-index: 3;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: 0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.15) 0%,
                rgba(139, 92, 246, 0.15) 100%);
}

.feature-card:hover::before {
    transform: translateX(100%);
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-2));
    border-radius: 50%;
    color: white;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.feature-icon svg {
    width: 30px;
    height: 30px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card h3 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 1.2rem;
    background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-color-1) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.feature-card p {
    color: var(--text-color-muted);
    font-size: 1.05rem;
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: 12rem 5%;
    margin-top: 4rem;
    background: linear-gradient(135deg,
                rgba(30, 41, 59, 0.7) 0%,
                rgba(15, 23, 42, 0.7) 100%);
    position: relative;
    overflow: hidden;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.about-section h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--accent-color-4), var(--accent-color-1));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    position: relative;
}

.about-section h2::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--accent-color-4), var(--accent-color-1));
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4px;
}

.about-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 4rem;
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-color-muted);
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
}

.about-item {
    padding: 3rem;
    border-radius: 1rem;
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.05) 0%,
                rgba(139, 92, 246, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.about-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.1) 0%,
                rgba(139, 92, 246, 0.1) 100%);
    border-color: rgba(99, 102, 241, 0.2);
}

.about-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-color-2) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.about-item p {
    color: var(--text-color-muted);
    line-height: 1.6;
    font-size: 1rem;
}

/* Testimonials Section */
.testimonials-section {
    padding: 8rem 5%;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
    border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.testimonials-section h2 {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 4rem;
    background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-color-1) 50%, var(--accent-color-2) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    position: relative;
}

.testimonials-section h2::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--accent-color-1), var(--accent-color-2));
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4px;
}

.testimonial-slider {
    display: flex;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: hidden;
    padding: 1rem;
    transition: transform 0.5s ease;
}

.testimonial-card {
    flex: 0 0 calc(33.333% - 2rem);
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    min-width: 280px;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.4);
}

.testimonial-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.quote-icon {
    color: var(--accent-color-1);
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.quote-icon svg {
    width: 32px;
    height: 32px;
}

.testimonial-content p {
    flex-grow: 1;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color-muted);
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-color);
    font-size: 1.1rem;
}

.author-info h4 {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
    color: var(--text-color);
}

.author-info p {
    font-size: 0.9rem;
    color: var(--text-color-muted);
    margin: 0;
    font-style: normal;
}

.testimonial-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 3rem;
}

.control-prev, .control-next {
    background: transparent;
    border: 1px solid rgba(99, 102, 241, 0.3);
    color: var(--text-color-muted);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-prev:hover, .control-next:hover {
    background: rgba(99, 102, 241, 0.1);
    color: var(--text-color);
    border-color: var(--accent-color-1);
}

.control-prev svg, .control-next svg {
    width: 20px;
    height: 20px;
}

.control-indicators {
    display: flex;
    gap: 0.5rem;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    width: 24px;
    border-radius: 4px;
    background: linear-gradient(to right, var(--accent-color-1), var(--accent-color-2));
}

/* CTA Section */
.cta-section {
    text-align: center;
    padding: 8rem 5%;
    position: relative;
    overflow: hidden;
    margin-top: 4rem;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15));
    z-index: -1;
    border-top: 1px solid rgba(99, 102, 241, 0.2);
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.cta-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: radial-gradient(circle at center, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
    animation: pulse 8s ease-in-out infinite;
    z-index: -1;
}

.cta-content {
    position: relative;
    z-index: 1;
    max-width: 900px;
    margin: 0 auto;
}

.cta-section h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-color-1) 50%, var(--accent-color-2) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.cta-section p {
    font-size: 1.3rem;
    color: var(--text-color-muted);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    margin-bottom: 2.5rem;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    margin-top: 2rem;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-color-muted);
    font-size: 0.95rem;
}

.cta-feature svg {
    width: 18px;
    height: 18px;
    color: var(--accent-color-2);
}

/* Footer */
footer {
    padding: 5rem 5% 2rem;
    background: linear-gradient(to bottom,
                rgba(10, 14, 23, 0.9) 0%,
                rgba(30, 41, 59, 0.8) 100%);
    color: rgba(255, 255, 255, 0.7);
    border-top: 1px solid rgba(99, 102, 241, 0.1);
    position: relative;
    backdrop-filter: blur(5px);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto 4rem;
    gap: 4rem;
}

.footer-brand {
    flex: 0 0 25%;
}

.footer-logo {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-color-1), var(--accent-color-3));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
    margin-bottom: 1rem;
}

.footer-tagline {
    color: var(--text-color-muted);
    margin-bottom: 2rem;
    line-height: 1.5;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(99, 102, 241, 0.1);
    color: var(--text-color-muted);
    transition: all 0.3s ease;
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.social-link:hover {
    background: rgba(99, 102, 241, 0.2);
    color: var(--text-color);
    transform: translateY(-3px);
    border-color: var(--accent-color-1);
}

.social-link svg {
    width: 18px;
    height: 18px;
}

.footer-links {
    flex: 1;
    display: flex;
    justify-content: space-between;
    gap: 2rem;
}

.footer-links-column {
    flex: 1;
}

.footer-links-column h4 {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.footer-links-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-column li {
    margin-bottom: 0.8rem;
}

.footer-links-column a {
    color: var(--text-color-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.footer-links-column a:hover {
    color: var(--accent-color-1);
    text-decoration: none;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(99, 102, 241, 0.1);
    max-width: 1200px;
    margin: 0 auto;
}

.footer-bottom p {
    font-size: 0.95rem;
}

.footer-language-selector select {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(99, 102, 241, 0.2);
    color: var(--text-color-muted);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    outline: none;
}

.footer-language-selector select:focus {
    border-color: var(--accent-color-1);
}

@media (max-width: 992px) {
    .footer-content {
        flex-direction: column;
        gap: 3rem;
    }
    
    .footer-brand {
        flex: 0 0 100%;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
    }
    
    .footer-links-column {
        flex: 0 0 calc(50% - 1rem);
        margin-bottom: 2rem;
    }
    
    .testimonial-card {
        flex: 0 0 calc(100% - 2rem);
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding-top: 6rem;
    }
    
    .hero-content {
        max-width: 100%;
        text-align: center;
    }
    
    .hero h1::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .hero-cta {
        justify-content: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .hero-visual {
        margin-top: 3rem;
    }
    
    .ai-dashboard {
        width: 90%;
        max-width: 500px;
    }
}

@media (max-width: 576px) {
    .ai-dashboard {
        width: 95%;
    }
    
    .code-block {
        display: none;
    }
    .footer-links-column {
        flex: 0 0 100%;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .hero h1 {
        font-size: 3rem;
    }
    
    .hero-badge {
        font-size: 0.8rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stat-item {
        flex: 0 0 calc(50% - 1rem);
        margin-bottom: 1.5rem;
        align-items: center;
    }
    
    .testimonials-section h2,
    .cta-section h2,
    .about-section h2 {
        font-size: 2.5rem;
    }
    
    .cta-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .floating-element {
        width: 80% !important;
        left: 10% !important;
        right: 10% !important;
    }
}
    letter-spacing: 0.02em;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero h1 {
        font-size: 4rem;
    }

    .about-grid, .feature-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .feature-card, .about-item {
        padding: 3rem;
    }

    .about-section, .features, .cta-section {
        padding: 10rem 5%;
    }
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 3rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .feature-grid, .about-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }

    .features h2, .about-section h2, .cta-section h2 {
        font-size: 2.5rem;
    }

    .response-bubble {
        display: none;
    }

    .response-bubble:nth-child(1),
    .response-bubble:nth-child(3) {
        display: block;
    }

    .about-description {
        font-size: 1.1rem;
    }

    .feature-card, .about-item {
        padding: 2.5rem;
    }

    .about-section, .features, .cta-section {
        padding: 10rem 5%;
        margin-top: 3rem;
    }
}

@media (max-width: 480px) {
    header {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem 5%;
    }

    nav ul {
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 2.5rem;
    }

    .features h2, .about-section h2, .cta-section h2 {
        font-size: 2rem;
    }

    .feature-card, .about-item {
        padding: 1.5rem;
    }

    .cta-button {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .about-section, .features, .cta-section {
        padding: 8rem 5%;
        margin-top: 2rem;
    }

    .feature-card, .about-item {
        padding: 2rem;
    }
}
