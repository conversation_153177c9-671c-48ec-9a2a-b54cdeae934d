import logging
import sys
from datetime import datetime

# Configure token usage logger
token_logger = logging.getLogger('token_usage')
token_logger.setLevel(logging.INFO)

# Create console handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)

# Create formatter
formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s', 
                            datefmt='%Y-%m-%d %H:%M:%S')
console_handler.setFormatter(formatter)

# Add handler to logger
token_logger.addHandler(console_handler)