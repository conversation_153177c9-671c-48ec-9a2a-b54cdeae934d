// Custom Lucide icons extension
(function() {
  // Only run if Lucide is loaded
  if (typeof window.lucide === 'undefined') {
    console.error('Lucide library not found. Custom icons will not be registered.');
    return;
  }

  // Add custom icons
  window.lucide.icons['zap'] = [
    ['path', { d: 'M11 12H3l4-8' }],
    ['path', { d: 'M15 12h8l-4 8' }],
    ['path', { d: 'M15 12 9 6' }],
    ['path', { d: 'M9 12 3 18' }]
  ];

  // Register any other missing icons that are used in the application
  window.lucide.icons['brain'] = [
    ['path', { d: 'M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-1.04Z' }],
    ['path', { d: 'M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-1.04Z' }]
  ];

  // Add flame icon
  window.lucide.icons['flame'] = [
    ['path', { d: 'M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z' }]
  ];

  // Make sure the script runs after Lucide is fully loaded
  setTimeout(() => {
    if (window.lucide && window.lucide.createIcons) {
      window.lucide.createIcons({
        attrs: {
          'stroke-width': '2',
          'class': 'icon'
        }
      });
      console.log('Custom Lucide icons registered and initialized successfully.');
    }
  }, 100);
})();