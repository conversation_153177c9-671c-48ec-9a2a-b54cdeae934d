<div id="clearLogsModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-900 rounded-lg p-6 w-full max-w-md max-h-[90vh] flex flex-col">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Clear Logs</h3>
            <button id="closeClearLogsModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>

        <p class="text-slate-300 mb-4">Select which logs you want to clear. This action cannot be undone.</p>

        <!-- Filters -->
        <div class="space-y-4 mb-6">
            <div>
                <label for="clearLogsServiceFilter" class="block text-sm text-slate-400 mb-1">Service:</label>
                <select id="clearLogsServiceFilter" class="w-full bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-3 py-2">
                    <option value="">All Services</option>
                    <option value="chat">Chat</option>
                    <option value="live">Live Chat</option>
                    <option value="spotify">Spotify</option>
                </select>
            </div>
            <div>
                <label for="clearLogsActionFilter" class="block text-sm text-slate-400 mb-1">Action:</label>
                <select id="clearLogsActionFilter" class="w-full bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-3 py-2">
                    <option value="">All Actions</option>
                    <option value="thread_create">Thread Create</option>
                    <option value="thread_interaction">Thread Interaction</option>
                    <option value="live_create">Live Create</option>
                    <option value="live_join">Live Join</option>
                    <option value="live_interaction">Live Interaction</option>
                    <option value="playback_control">Playback Control</option>
                </select>
            </div>
            <div>
                <label for="clearLogsTimeFilter" class="block text-sm text-slate-400 mb-1">Time Period:</label>
                <select id="clearLogsTimeFilter" class="w-full bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-3 py-2">
                    <option value="">All Time</option>
                    <option value="24">Last 24 hours</option>
                    <option value="48">Last 48 hours</option>
                    <option value="72">Last 72 hours</option>
                    <option value="168">Last week</option>
                </select>
            </div>
        </div>

        <!-- Warning -->
        <div class="bg-red-900/30 border border-red-800/50 rounded-md p-3 mb-6">
            <div class="flex items-start">
                <i data-lucide="alert-triangle" class="h-5 w-5 text-red-400 mr-2 flex-shrink-0 mt-0.5"></i>
                <div>
                    <p class="text-red-300 text-sm font-medium">Warning</p>
                    <p class="text-red-400/80 text-xs mt-1">
                        This action will permanently delete the selected logs. This cannot be undone.
                    </p>
                </div>
            </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end space-x-3">
            <button id="cancelClearLogs" class="bg-slate-700 hover:bg-slate-600 text-slate-300 rounded px-4 py-2 text-sm">
                Cancel
            </button>
            <button id="confirmClearLogs" class="bg-red-600 hover:bg-red-500 text-white rounded px-4 py-2 text-sm flex items-center">
                <i data-lucide="trash-2" class="h-4 w-4 mr-1"></i>
                Clear Logs
            </button>
        </div>

        <!-- Status -->
        <div id="clearLogsStatus" class="mt-4 hidden"></div>
    </div>
</div>
