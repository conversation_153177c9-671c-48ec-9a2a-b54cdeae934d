/* Critical styles for above-the-fold content */
:root {
  --bg-dark: rgb(21,21,21);
  --chat-bg: rgb(38,38,44);
  --border-color: rgb(73,73,73);
}

/* Initial state - hide content but show background */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-dark);
  font-family: system-ui, -apple-system, sans-serif;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Remove the FOUC prevention that was causing the flash */
html {
  visibility: visible !important;
  background-color: var(--bg-dark);
}

html.fonts-loaded {
  visibility: visible;
}

/* Core layout styles */
.chat-container {
  height: calc(100vh - 1.4cm);
  width: calc(100vw - 7cm);
  overflow: hidden;
  border-radius: 1.5rem;
  background-color: var(--chat-bg);
  color: #f3f4f6;
}

/* Critical message styles */
.message {
  max-width: 70%;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
}

/* Pre-define sizes for elements that might shift */
.function-button {
  height: 2rem;
  min-width: 2rem;
  contain: layout style;
}

#messageInput {
  height: 44px;
  min-height: 44px;
  contain: layout style;
}

/* Optimize modal rendering */
#settingsModal {
  position: fixed;
  inset: 0;
  z-index: 50;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  contain: layout size style;
}

/* Optimize button layout */
.function-buttons {
  display: flex;
  gap: 0.5rem;
  contain: layout style;
}

/* Font loading optimization */
@font-face {
  font-family: 'System Font';
  font-display: swap;
  src: local('Arial'), local('Helvetica'), local('sans-serif');
}

/* Prevent layout shifts */
html {
  font-family: 'System Font', Arial, sans-serif;
}

/* Ensure content stays within bounds during font load */
* {
  max-width: 100%;
  overflow-wrap: break-word;
}




