from flask import jsonify, request
from flask_login import login_required, current_user
from models.feature_restriction import FeatureRestriction
from models.user import User
from . import admin_api
from .routes import admin_required
import logging

@admin_api.route('/feature-restrictions', methods=['GET'])
@login_required
@admin_required
def get_feature_restrictions():
    """Get all feature restrictions"""
    try:
        restrictions = FeatureRestriction.get_all_restrictions()
        result = []

        for restriction in restrictions:
            user = restriction.user
            result.append({
                'user_id': str(user.id),
                'username': user.username,
                'email': user.email,
                'max_threads': restriction.max_threads,
                'max_conversation_sets': restriction.max_conversation_sets,
                'max_live_rooms': restriction.max_live_rooms,
                'max_live_conversation_sets': restriction.max_live_conversation_sets,
                'created_at': restriction.created_at.isoformat(),
                'created_by': restriction.created_by
            })

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in get_feature_restrictions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_api.route('/feature-restrictions', methods=['POST'])
@login_required
@admin_required
def add_feature_restriction():
    """Add a feature restriction for a user"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        max_threads = data.get('max_threads', 5)
        max_conversation_sets = data.get('max_conversation_sets', 5)
        max_live_rooms = data.get('max_live_rooms', 5)
        max_live_conversation_sets = data.get('max_live_conversation_sets', 5)

        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400

        # Check if user exists
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Set restrictions
        FeatureRestriction.set_user_restrictions(
            user_id=user_id,
            max_threads=max_threads,
            max_conversation_sets=max_conversation_sets,
            admin_email=current_user.email,
            max_live_rooms=max_live_rooms,
            max_live_conversation_sets=max_live_conversation_sets
        )

        return jsonify({
            'message': 'Feature restrictions set successfully',
            'user': {
                'id': str(user.id),
                'username': user.username,
                'email': user.email
            },
            'restrictions': {
                'max_threads': max_threads,
                'max_conversation_sets': max_conversation_sets,
                'max_live_rooms': max_live_rooms,
                'max_live_conversation_sets': max_live_conversation_sets
            }
        })
    except Exception as e:
        logging.error(f"Error in add_feature_restriction: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_api.route('/feature-restrictions/<user_id>', methods=['GET'])
@login_required
@admin_required
def get_user_feature_restrictions(user_id):
    """Get feature restrictions for a specific user"""
    try:
        # Check if user exists
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get restrictions
        restriction = FeatureRestriction.objects(user=user_id).first()
        if not restriction:
            return jsonify({
                'user_id': str(user.id),
                'username': user.username,
                'email': user.email,
                'has_restrictions': False
            })

        return jsonify({
            'user_id': str(user.id),
            'username': user.username,
            'email': user.email,
            'has_restrictions': True,
            'max_threads': restriction.max_threads,
            'max_conversation_sets': restriction.max_conversation_sets,
            'max_live_rooms': restriction.max_live_rooms,
            'max_live_conversation_sets': restriction.max_live_conversation_sets,
            'created_at': restriction.created_at.isoformat(),
            'created_by': restriction.created_by
        })
    except Exception as e:
        logging.error(f"Error in get_user_feature_restrictions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_api.route('/feature-restrictions/<user_id>', methods=['DELETE'])
@login_required
@admin_required
def remove_feature_restriction(user_id):
    """Remove feature restrictions for a user"""
    try:
        # Check if user exists
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Remove restrictions
        FeatureRestriction.objects(user=user_id).delete()

        return jsonify({
            'message': 'Feature restrictions removed successfully',
            'user': {
                'id': str(user.id),
                'username': user.username,
                'email': user.email
            }
        })
    except Exception as e:
        logging.error(f"Error in remove_feature_restriction: {str(e)}")
        return jsonify({'error': str(e)}), 500
