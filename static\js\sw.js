// Service Worker for KevkoAI Chat
const CACHE_NAME = 'kevko-chat-cache-v1';

// Assets to cache on install
const STATIC_ASSETS = [
  '/static/css/chat.css',
  '/static/js/chat.js',
  '/static/js/audio-player.js',
  '/static/js/vendor/lucide.min.js'
];

// Install event - cache static assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName !== CACHE_NAME;
        }).map(cacheName => {
          return caches.delete(cacheName);
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Helper function to determine if a request should be cached
function shouldCache(url) {
  const parsedUrl = new URL(url);
  
  // Cache thread list API responses
  if (parsedUrl.pathname === '/api/chat/threads') {
    return true;
  }
  
  // Cache individual thread API responses
  if (parsedUrl.pathname.match(/^\/api\/chat\/thread\/[a-zA-Z0-9]+$/)) {
    return true;
  }
  
  // Cache room list API responses
  if (parsedUrl.pathname === '/api/live/load/rooms') {
    return true;
  }
  
  // Cache individual room API responses
  if (parsedUrl.pathname.match(/^\/api\/live\/room\/[a-zA-Z0-9]+$/)) {
    return true;
  }
  
  // Cache static assets
  if (parsedUrl.pathname.startsWith('/static/')) {
    return true;
  }
  
  return false;
}

// Fetch event - network first with cache fallback for API, cache first for static assets
self.addEventListener('fetch', event => {
  const request = event.request;
  
  // Skip non-GET requests
  if (request.method !== 'GET') return;
  
  // Handle API requests - network first with cache fallback
  if (request.url.includes('/api/chat/')) {
    event.respondWith(
      fetch(request)
        .then(response => {
          // Clone the response to store in cache
          const responseToCache = response.clone();
          
          // Only cache successful responses
          if (response.status === 200 && shouldCache(request.url)) {
            caches.open(CACHE_NAME)
              .then(cache => {
                // Store with a short expiration time
                cache.put(request, responseToCache);
                
                // Delete this cache entry after 30 seconds
                setTimeout(() => {
                  cache.delete(request);
                }, 30000);
              });
          }
          
          return response;
        })
        .catch(() => {
          // If network fails, try the cache
          return caches.match(request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // If not in cache, return a basic error response
              return new Response(JSON.stringify({
                error: 'Network error, and no cached version available'
              }), {
                headers: { 'Content-Type': 'application/json' },
                status: 503
              });
            });
        })
    );
  } 
  // Handle static assets - cache first with network fallback
  else if (request.url.includes('/static/')) {
    event.respondWith(
      caches.match(request)
        .then(cachedResponse => {
          return cachedResponse || fetch(request)
            .then(response => {
              // Clone the response to store in cache
              const responseToCache = response.clone();
              
              // Only cache successful responses
              if (response.status === 200) {
                caches.open(CACHE_NAME)
                  .then(cache => {
                    cache.put(request, responseToCache);
                  });
              }
              
              return response;
            });
        })
    );
  }
});