"""
Simple in-memory cache implementation
"""
import time
import threading
import logging

class Cache:
    """Simple in-memory cache with expiration"""
    
    def __init__(self, default_ttl=60):
        """Initialize the cache
        
        Args:
            default_ttl (int): Default time-to-live in seconds
        """
        self._cache = {}
        self._lock = threading.RLock()
        self.default_ttl = default_ttl
        
        # Start the cleanup thread
        self._start_cleanup_thread()
    
    def get(self, key, default=None):
        """Get a value from the cache
        
        Args:
            key: The cache key
            default: Value to return if key not found
            
        Returns:
            The cached value or default
        """
        with self._lock:
            cache_item = self._cache.get(key)
            
            if cache_item is None:
                return default
            
            # Check if the item has expired
            if cache_item['expires'] < time.time():
                del self._cache[key]
                return default
            
            return cache_item['value']
    
    def set(self, key, value, ttl=None):
        """Set a value in the cache
        
        Args:
            key: The cache key
            value: The value to cache
            ttl (int, optional): Time-to-live in seconds
        """
        if ttl is None:
            ttl = self.default_ttl
        
        with self._lock:
            self._cache[key] = {
                'value': value,
                'expires': time.time() + ttl
            }
    
    def delete(self, key):
        """Delete a key from the cache
        
        Args:
            key: The cache key
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
    
    def clear(self):
        """Clear all items from the cache"""
        with self._lock:
            self._cache.clear()
    
    def _cleanup(self):
        """Remove expired items from the cache"""
        now = time.time()
        with self._lock:
            # Create a list of expired keys
            expired_keys = [
                key for key, item in self._cache.items()
                if item['expires'] < now
            ]
            
            # Delete expired keys
            for key in expired_keys:
                del self._cache[key]
    
    def _start_cleanup_thread(self):
        """Start a thread to periodically clean up expired items"""
        def cleanup_task():
            while True:
                try:
                    # Sleep for 60 seconds
                    time.sleep(60)
                    
                    # Clean up expired items
                    self._cleanup()
                except Exception as e:
                    logging.error(f"Error in cache cleanup: {str(e)}")
        
        # Create and start the thread
        thread = threading.Thread(target=cleanup_task, daemon=True)
        thread.start()

# Create a global cache instance
cache = Cache()