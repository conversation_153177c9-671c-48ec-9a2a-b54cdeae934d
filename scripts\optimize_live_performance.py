"""
<PERSON><PERSON><PERSON> to optimize MongoDB performance for the live chat feature.
This script creates indexes on the rooms collection to improve query performance.
"""

import sys
import os
import logging

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.room import Room
from models.user import User

def create_indexes():
    """Create indexes on the rooms collection"""
    try:
        print("Creating indexes for Room collection...")
        
        # Ensure indexes are created based on the meta definition
        Room.ensure_indexes()
        
        # Create additional indexes for common queries
        Room.objects._collection.create_index([("room_id", 1)], unique=True)
        Room.objects._collection.create_index([("creator", 1)])
        Room.objects._collection.create_index([("participant", 1)])
        Room.objects._collection.create_index([("updated_at", -1)])
        Room.objects._collection.create_index([("creator", 1), ("updated_at", -1)])
        Room.objects._collection.create_index([("participant", 1), ("updated_at", -1)])
        
        # Create index for message count queries
        Room.objects._collection.create_index([("messages", 1)])
        
        print("Indexes created successfully!")
        
        # Verify indexes
        indexes = Room.objects._collection.index_information()
        print(f"Current indexes: {indexes}")
        
    except Exception as e:
        print(f"Error creating indexes: {str(e)}")

if __name__ == "__main__":
    create_indexes()