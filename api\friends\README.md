# Friends API

This API provides endpoints for managing friend relationships and friend-to-friend chat functionality.

## Structure

The API is organized into the following modules:

```
api/friends/
├── __init__.py                # Blueprint initialization
├── routes/                    # Routes directory
│   ├── __init__.py            # Routes package initialization
│   ├── friend_management.py   # Friend request/management endpoints
│   ├── friend_chat.py         # Direct chat endpoints
│   ├── group_chat.py          # Group chat endpoints
│   ├── encryption.py          # Encryption-related endpoints
│   └── search.py              # Search functionality
└── README.md                  # This file
```

## Modules

### Friend Management (`routes/friend_management.py`)

Handles friend relationships, including:
- Getting a user's friends list
- Sending, accepting, rejecting, and canceling friend requests
- Removing friends
- Managing pending and sent friend requests

### Friend Chat (`routes/friend_chat.py`)

Manages direct chats between friends:
- Getting chat details
- Sending and retrieving messages
- Cleaning up stale chats (admin only)

### Group Chat (`routes/group_chat.py`)

Handles group chat functionality:
- Creating and deleting group chats
- Adding and removing members
- Sending and retrieving messages
- Updating group chat settings (name, profile picture)
- Leaving group chats

### Encryption (`routes/encryption.py`)

Manages end-to-end encryption for chats:
- Storing and retrieving public keys
- Managing shared secrets
- Resetting encryption
- Updating message encryption

### Search (`routes/search.py`)

Provides user search functionality:
- Searching for users by username
- Returning user details with friendship status

## Endpoints

### GET /api/friends/friends
- **Description**: Get all friends for the current user
- **Authentication**: Required
- **Response**: List of friend objects with chat information

### GET /api/friends/search
- **Description**: Search for users by username
- **Authentication**: Required
- **Query Parameters**:
  - `q`: Search query (minimum 3 characters)
- **Response**: List of user objects with friendship status

### POST /api/friends/request
- **Description**: Send a friend request
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### POST /api/friends/accept
- **Description**: Accept a friend request
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### POST /api/friends/reject
- **Description**: Reject a friend request
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### POST /api/friends/cancel
- **Description**: Cancel a sent friend request
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### POST /api/friends/remove
- **Description**: Remove a friend
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### GET /api/friends/chat/:chat_id
- **Description**: Get a chat by ID
- **Authentication**: Required
- **Response**: Chat object with messages

### POST /api/friends/chat/:chat_id/messages
- **Description**: Send a message to a chat
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "message": "Message content"
  }
  ```
- **Response**: New message object

### GET /api/friends/pending
- **Description**: Get pending friend requests
- **Authentication**: Required
- **Response**: List of pending friend request objects

### GET /api/friends/requests/sent
- **Description**: Get friend requests sent by the current user
- **Authentication**: Required
- **Response**: List of sent friend request objects

### GET /api/friends/request/sent (Alias)
- **Description**: Alias for GET /api/friends/requests/sent
- **Authentication**: Required
- **Response**: List of sent friend request objects

### POST /api/friends/request/accept (Alias)
- **Description**: Alias for POST /api/friends/accept
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: Success message

### POST /api/friends/invite-to-live
- **Description**: Invite a friend to a live chat
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "friend_id": "user_id"
  }
  ```
- **Response**: New room object

## Models

### FriendRelationship
- `user`: User who sent the request
- `friend`: User who received the request
- `is_accepted`: Boolean indicating if the request was accepted
- `created_at`: Creation timestamp

### FriendChat
- `chat_id`: Chat ID
- `user1`: First user in the chat
- `user2`: Second user in the chat
- `messages`: List of message objects
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Message
- `content`: Message content (encrypted in the database)
- `sender`: User who sent the message
- `timestamp`: Message timestamp
- `encrypted`: Boolean indicating if the message is encrypted

## Message Encryption

All messages in friend chats are encrypted using Fernet symmetric encryption from the cryptography library. This provides strong encryption with the following properties:

- **End-to-End Encryption**: Messages are encrypted before being stored in the database
- **Secure Key Derivation**: Each chat has a unique encryption key derived from the chat_id and a server-side secret
- **Database Security**: Even administrators with direct database access cannot read the message contents without the server's encryption secret
- **Backward Compatibility**: The system can still decrypt messages that were encrypted with the older Base64 encoding method

## Usage Example

```javascript
// Search for users
const searchResponse = await fetch('/api/friends/search?q=username', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const users = await searchResponse.json();

// Send a friend request
const requestResponse = await fetch('/api/friends/request', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    friend_id: users[0].id
  })
});

// Cancel a sent friend request
const cancelResponse = await fetch('/api/friends/cancel', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    friend_id: users[0].id
  })
});

// Get all friends
const friendsResponse = await fetch('/api/friends/friends', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const friends = await friendsResponse.json();

// Send a message to a friend
const messageResponse = await fetch(`/api/friends/chat/${friends[0].chat_id}/messages`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: 'Hello, friend!'
  })
});
```
