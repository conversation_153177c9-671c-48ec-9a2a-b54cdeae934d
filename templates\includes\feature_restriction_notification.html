<div id="featureRestrictionNotification" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Feature Restricted</h3>
            <button id="closeFeatureRestrictionNotification" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="space-y-4">
            <div class="bg-amber-500/10 p-4 rounded-lg flex items-start">
                <i data-lucide="alert-triangle" class="h-5 w-5 text-amber-500 mr-3 mt-0.5"></i>
                <div>
                    <h4 class="text-amber-500 font-medium">Limit Reached</h4>
                    <p class="text-slate-300 text-sm mt-1" id="featureRestrictionMessage">
                        <!-- Message will be populated by JS -->
                    </p>
                </div>
            </div>
            <p class="text-slate-400 text-sm">
                Your account has feature restrictions that limit the number of threads or messages you can create.
                Please contact an administrator if you need these limits increased.
            </p>
            <div class="pt-2">
                <button id="featureRestrictionOkButton" class="w-full bg-slate-700 hover:bg-slate-600 text-white rounded-md px-4 py-2 flex items-center justify-center">
                    <i data-lucide="check" class="h-4 w-4 mr-2"></i>
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const notification = document.getElementById('featureRestrictionNotification');
        const closeButton = document.getElementById('closeFeatureRestrictionNotification');
        const okButton = document.getElementById('featureRestrictionOkButton');
        const messageElement = document.getElementById('featureRestrictionMessage');
        
        // Function to show notification
        window.showFeatureRestrictionNotification = function(message) {
            messageElement.textContent = message;
            notification.classList.remove('hidden');
            
            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons();
            }
        };
        
        // Close notification when close button is clicked
        closeButton.addEventListener('click', function() {
            notification.classList.add('hidden');
        });
        
        // Close notification when OK button is clicked
        okButton.addEventListener('click', function() {
            notification.classList.add('hidden');
        });
    });
</script>
