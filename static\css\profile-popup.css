/* Profile Popup CSS - Compact profile tooltip for friend avatars */

.profile-popup {
    position: fixed;
    z-index: 9999;
    background: var(--slate-800, #1e293b);
    border: 1px solid var(--slate-600, #475569);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    padding: 16px;
    width: 280px;
    max-width: 90vw;
    max-height: 400px;
    overflow: hidden;
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(8px);
}

.profile-popup.show {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: auto;
}

.profile-popup.hide {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    pointer-events: none;
}

/* Profile popup header */
.profile-popup-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.profile-popup-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--slate-600, #475569);
    flex-shrink: 0;
}

.profile-popup-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-popup-avatar .avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--slate-600, #475569), var(--slate-700, #334155));
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.profile-popup-info {
    flex: 1;
    min-width: 0;
}

.profile-popup-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--slate-200, #e2e8f0);
    margin: 0 0 2px 0;
    line-height: 1.2;
    word-break: break-word;
}

.profile-popup-username {
    font-size: 14px;
    color: var(--slate-400, #94a3b8);
    margin: 0;
    line-height: 1.2;
}

/* About me section */
.profile-popup-about {
    border-top: 1px solid var(--slate-700, #334155);
    padding-top: 12px;
}

.profile-popup-about-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--slate-400, #94a3b8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 0 8px 0;
}

.profile-popup-about-content {
    font-size: 14px;
    color: var(--slate-300, #cbd5e1);
    line-height: 1.4;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    word-break: break-word;
    scrollbar-width: thin;
    scrollbar-color: var(--slate-600, #475569) transparent;
}

.profile-popup-about-content::-webkit-scrollbar {
    width: 4px;
}

.profile-popup-about-content::-webkit-scrollbar-track {
    background: transparent;
}

.profile-popup-about-content::-webkit-scrollbar-thumb {
    background-color: var(--slate-600, #475569);
    border-radius: 2px;
}

.profile-popup-about-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--slate-500, #64748b);
}

/* Empty state */
.profile-popup-about-empty {
    font-style: italic;
    color: var(--slate-500, #64748b);
}

/* Loading state */
.profile-popup-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--slate-400, #94a3b8);
}

.profile-popup-loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--slate-600, #475569);
    border-top: 2px solid var(--slate-400, #94a3b8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.profile-popup-error {
    color: var(--red-400, #f87171);
    font-size: 14px;
    text-align: center;
    padding: 12px;
}

/* Theme-specific colors - Enhanced for all themes */

/* Purple Theme */
.theme-purple .profile-popup {
    background: linear-gradient(135deg, #581c87 0%, #4c1d95 100%);
    border-color: #7c3aed;
    box-shadow: 0 20px 25px -5px rgba(124, 58, 237, 0.3), 0 10px 10px -5px rgba(124, 58, 237, 0.1);
}

.theme-purple .profile-popup::before {
    border-bottom-color: #581c87;
    border-top-color: #581c87;
    border-left-color: #581c87;
    border-right-color: #581c87;
}

.theme-purple .profile-popup-about {
    border-top-color: #6d28d9;
}

/* Blue Theme */
.theme-blue .profile-popup {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-color: #3b82f6;
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.3), 0 10px 10px -5px rgba(59, 130, 246, 0.1);
}

.theme-blue .profile-popup::before {
    border-bottom-color: #1e3a8a;
    border-top-color: #1e3a8a;
    border-left-color: #1e3a8a;
    border-right-color: #1e3a8a;
}

.theme-blue .profile-popup-about {
    border-top-color: #2563eb;
}

/* Green Theme */
.theme-green .profile-popup {
    background: linear-gradient(135deg, #14532d 0%, #166534 100%);
    border-color: #22c55e;
    box-shadow: 0 20px 25px -5px rgba(34, 197, 94, 0.3), 0 10px 10px -5px rgba(34, 197, 94, 0.1);
}

.theme-green .profile-popup::before {
    border-bottom-color: #14532d;
    border-top-color: #14532d;
    border-left-color: #14532d;
    border-right-color: #14532d;
}

.theme-green .profile-popup-about {
    border-top-color: #16a34a;
}

/* Orange Theme */
.theme-orange .profile-popup {
    background: linear-gradient(135deg, #9a3412 0%, #c2410c 100%);
    border-color: #f97316;
    box-shadow: 0 20px 25px -5px rgba(249, 115, 22, 0.3), 0 10px 10px -5px rgba(249, 115, 22, 0.1);
}

.theme-orange .profile-popup::before {
    border-bottom-color: #9a3412;
    border-top-color: #9a3412;
    border-left-color: #9a3412;
    border-right-color: #9a3412;
}

.theme-orange .profile-popup-about {
    border-top-color: #ea580c;
}

/* Pink Theme */
.theme-pink .profile-popup {
    background: linear-gradient(135deg, #831843 0%, #be185d 100%);
    border-color: #ec4899;
    box-shadow: 0 20px 25px -5px rgba(236, 72, 153, 0.3), 0 10px 10px -5px rgba(236, 72, 153, 0.1);
}

.theme-pink .profile-popup::before {
    border-bottom-color: #831843;
    border-top-color: #831843;
    border-left-color: #831843;
    border-right-color: #831843;
}

.theme-pink .profile-popup-about {
    border-top-color: #db2777;
}

/* Cyan Theme */
.theme-cyan .profile-popup {
    background: linear-gradient(135deg, #164e63 0%, #0891b2 100%);
    border-color: #06b6d4;
    box-shadow: 0 20px 25px -5px rgba(6, 182, 212, 0.3), 0 10px 10px -5px rgba(6, 182, 212, 0.1);
}

.theme-cyan .profile-popup::before {
    border-bottom-color: #164e63;
    border-top-color: #164e63;
    border-left-color: #164e63;
    border-right-color: #164e63;
}

.theme-cyan .profile-popup-about {
    border-top-color: #0891b2;
}

/* Red Theme */
.theme-red .profile-popup {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
    border-color: #ef4444;
    box-shadow: 0 20px 25px -5px rgba(239, 68, 68, 0.3), 0 10px 10px -5px rgba(239, 68, 68, 0.1);
}

.theme-red .profile-popup::before {
    border-bottom-color: #7f1d1d;
    border-top-color: #7f1d1d;
    border-left-color: #7f1d1d;
    border-right-color: #7f1d1d;
}

.theme-red .profile-popup-about {
    border-top-color: #dc2626;
}

/* Yellow Theme */
.theme-yellow .profile-popup {
    background: linear-gradient(135deg, #713f12 0%, #92400e 100%);
    border-color: #eab308;
    box-shadow: 0 20px 25px -5px rgba(234, 179, 8, 0.3), 0 10px 10px -5px rgba(234, 179, 8, 0.1);
}

.theme-yellow .profile-popup::before {
    border-bottom-color: #713f12;
    border-top-color: #713f12;
    border-left-color: #713f12;
    border-right-color: #713f12;
}

.theme-yellow .profile-popup-about {
    border-top-color: #ca8a04;
}

/* Indigo Theme */
.theme-indigo .profile-popup {
    background: linear-gradient(135deg, #312e81 0%, #3730a3 100%);
    border-color: #6366f1;
    box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.3), 0 10px 10px -5px rgba(99, 102, 241, 0.1);
}

.theme-indigo .profile-popup::before {
    border-bottom-color: #312e81;
    border-top-color: #312e81;
    border-left-color: #312e81;
    border-right-color: #312e81;
}

.theme-indigo .profile-popup-about {
    border-top-color: #4f46e5;
}

/* Teal Theme */
.theme-teal .profile-popup {
    background: linear-gradient(135deg, #134e4a 0%, #115e59 100%);
    border-color: #14b8a6;
    box-shadow: 0 20px 25px -5px rgba(20, 184, 166, 0.3), 0 10px 10px -5px rgba(20, 184, 166, 0.1);
}

.theme-teal .profile-popup::before {
    border-bottom-color: #134e4a;
    border-top-color: #134e4a;
    border-left-color: #134e4a;
    border-right-color: #134e4a;
}

.theme-teal .profile-popup-about {
    border-top-color: #0d9488;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-popup {
        width: 260px;
        padding: 14px;
        max-height: 350px;
    }
    
    .profile-popup-avatar {
        width: 40px;
        height: 40px;
    }
    
    .profile-popup-name {
        font-size: 15px;
    }
    
    .profile-popup-username {
        font-size: 13px;
    }
    
    .profile-popup-about-content {
        font-size: 13px;
        max-height: 150px;
    }
}

/* Animation for popup arrow (optional enhancement) */
.profile-popup::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    pointer-events: none;
}

.profile-popup.arrow-top::before {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-bottom::before {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-left::before {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-right::before {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid var(--slate-800, #1e293b);
}
