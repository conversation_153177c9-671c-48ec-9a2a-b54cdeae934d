/* Profile Popup CSS - Compact profile tooltip for friend avatars */

.profile-popup {
    position: fixed;
    z-index: 9999;
    background: var(--slate-800, #1e293b);
    border: 1px solid var(--slate-600, #475569);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    padding: 16px;
    width: 280px;
    max-width: 90vw;
    max-height: 400px;
    overflow: hidden;
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(8px);
}

.profile-popup.show {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: auto;
}

.profile-popup.hide {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    pointer-events: none;
}

/* Profile popup header */
.profile-popup-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.profile-popup-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--slate-600, #475569);
    flex-shrink: 0;
}

.profile-popup-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-popup-avatar .avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--slate-600, #475569), var(--slate-700, #334155));
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.profile-popup-info {
    flex: 1;
    min-width: 0;
}

.profile-popup-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--slate-200, #e2e8f0);
    margin: 0 0 2px 0;
    line-height: 1.2;
    word-break: break-word;
}

.profile-popup-username {
    font-size: 14px;
    color: var(--slate-400, #94a3b8);
    margin: 0;
    line-height: 1.2;
}

/* About me section */
.profile-popup-about {
    border-top: 1px solid var(--slate-700, #334155);
    padding-top: 12px;
}

.profile-popup-about-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--slate-400, #94a3b8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 0 8px 0;
}

.profile-popup-about-content {
    font-size: 14px;
    color: var(--slate-300, #cbd5e1);
    line-height: 1.4;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    word-break: break-word;
    scrollbar-width: thin;
    scrollbar-color: var(--slate-600, #475569) transparent;
}

.profile-popup-about-content::-webkit-scrollbar {
    width: 4px;
}

.profile-popup-about-content::-webkit-scrollbar-track {
    background: transparent;
}

.profile-popup-about-content::-webkit-scrollbar-thumb {
    background-color: var(--slate-600, #475569);
    border-radius: 2px;
}

.profile-popup-about-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--slate-500, #64748b);
}

/* Empty state */
.profile-popup-about-empty {
    font-style: italic;
    color: var(--slate-500, #64748b);
}

/* Loading state */
.profile-popup-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--slate-400, #94a3b8);
}

.profile-popup-loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--slate-600, #475569);
    border-top: 2px solid var(--slate-400, #94a3b8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.profile-popup-error {
    color: var(--red-400, #f87171);
    font-size: 14px;
    text-align: center;
    padding: 12px;
}

/* Theme-specific colors */
.theme-purple .profile-popup {
    background: var(--purple-900, #581c87);
    border-color: var(--purple-700, #7c3aed);
}

.theme-blue .profile-popup {
    background: var(--blue-900, #1e3a8a);
    border-color: var(--blue-700, #1d4ed8);
}

.theme-green .profile-popup {
    background: var(--green-900, #14532d);
    border-color: var(--green-700, #15803d);
}

.theme-orange .profile-popup {
    background: var(--orange-900, #9a3412);
    border-color: var(--orange-700, #c2410c);
}

.theme-pink .profile-popup {
    background: var(--pink-900, #831843);
    border-color: var(--pink-700, #be185d);
}

.theme-cyan .profile-popup {
    background: var(--cyan-900, #164e63);
    border-color: var(--cyan-700, #0891b2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-popup {
        width: 260px;
        padding: 14px;
        max-height: 350px;
    }
    
    .profile-popup-avatar {
        width: 40px;
        height: 40px;
    }
    
    .profile-popup-name {
        font-size: 15px;
    }
    
    .profile-popup-username {
        font-size: 13px;
    }
    
    .profile-popup-about-content {
        font-size: 13px;
        max-height: 150px;
    }
}

/* Animation for popup arrow (optional enhancement) */
.profile-popup::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    pointer-events: none;
}

.profile-popup.arrow-top::before {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-bottom::before {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-left::before {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-right::before {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid var(--slate-800, #1e293b);
}
