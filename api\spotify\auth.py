from flask import session, jsonify, request
from . import spotify_api
from .services.spotify_client import create_spotify_oauth, get_spotify_client, save_spotify_credentials
import spotipy
from flask_login import current_user
from models.spotify_credentials import SpotifyCredentials
import logging

@spotify_api.route('/connect')
def connect():
    """Get Spotify authorization URL"""
    sp_oauth = create_spotify_oauth()
    auth_url = sp_oauth.get_authorize_url(state='account_selection')
    return jsonify({'auth_url': auth_url})

@spotify_api.route('/callback')
def callback():
    """Handle Spotify OAuth callback"""
    try:
        sp_oauth = create_spotify_oauth()
        code = request.args.get('code')
        state = request.args.get('state')

        if state != 'account_selection':
            return jsonify({'error': 'Invalid state parameter'}), 400

        token_info = sp_oauth.get_access_token(code)
        session['token_info'] = token_info

        # Get user info
        sp = spotipy.Spotify(auth_manager=sp_oauth)
        user_info = sp.current_user()
        user_data = {
            'name': user_info['display_name'],
            'email': user_info['email'],
            'image': user_info['images'][0]['url'] if user_info.get('images') else None
        }

        # Save credentials to database if user is logged in
        credentials_saved = False
        if current_user.is_authenticated:
            credentials_saved = save_spotify_credentials(token_info)

        return jsonify({
            'success': True,
            'user': user_data,
            'credentials_saved': credentials_saved
        })
    except Exception as e:
        logging.error(f"Error in Spotify callback: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/logout')
def logout():
    """Clear Spotify tokens from session"""
    if 'token_info' in session:
        session.pop('token_info', None)
    return jsonify({'success': True})

@spotify_api.route('/token/refresh')
def refresh_token():
    """Refresh Spotify access token"""
    try:
        sp_oauth = create_spotify_oauth()
        token_info = session.get('token_info')

        # If user is logged in and no token in session, try to get from database
        if current_user.is_authenticated and not token_info:
            try:
                # This will try to get credentials from database
                sp = get_spotify_client()
                token_info = session.get('token_info')  # Should be populated by get_spotify_client
            except Exception:
                return jsonify({'error': 'Not authenticated'}), 401
        elif not token_info:
            return jsonify({'error': 'Not authenticated'}), 401

        # Check if token is expired and refresh if needed
        if sp_oauth.is_token_expired(token_info):
            token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
            session['token_info'] = token_info

            # Save refreshed token to database if user is logged in
            if current_user.is_authenticated:
                save_spotify_credentials(token_info)

        return jsonify({
            'access_token': token_info['access_token'],
            'expires_at': token_info['expires_at']
        })
    except Exception as e:
        logging.error(f"Error refreshing token: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/status')
def status():
    """Check if the user has Spotify credentials"""
    has_session_token = 'token_info' in session

    # Check if user is logged in and has stored credentials
    has_stored_credentials = False
    if current_user.is_authenticated:
        credentials = SpotifyCredentials.objects(user=current_user.id).first()
        has_stored_credentials = credentials is not None

    return jsonify({
        'has_session_token': has_session_token,
        'has_stored_credentials': has_stored_credentials,
        'is_authenticated': current_user.is_authenticated
    })
