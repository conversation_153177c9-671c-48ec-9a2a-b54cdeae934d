/**
 * KevkoFy Service
 * Custom Spotify interface
 */

// Import dependencies
// None for now

// Service class definition
window.KevkoFyService = class KevkoFyService extends window.Service {
    constructor(config) {
        super(config);
        this.playlists = [];
        this.currentTrack = null;
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        try {
            const response = await fetch('/api/spotify/playlists');
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to fetch playlists');
            }

            const data = await response.json();
            if (!Array.isArray(data)) {
                throw new Error('Invalid playlist data received');
            }

            this.playlists = data;
        } catch (error) {
            console.error('Error fetching playlists:', error);
            this.playlists = [];
        }
    }

    /**
     * Render the service card HTML with additional features
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering KevkoFy service:`, {
                id: this.id,
                name: this.name,
                status: this.status,
                url: this.url,
                type: this.type
            });
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            // Define the action button based on status
            let actionButton;

            // Always use the Go to Spotify button regardless of status
            actionButton = `
                <a href="/spotify"
                   class="w-full bg-${this.color}-600 hover:bg-${this.color}-500 border border-${this.color}-500/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                    <span class="text-sm text-white">Go to ${this.name}</span>
                    <i data-lucide="arrow-right" class="h-4 w-4 ml-1 text-white"></i>
                </a>
            `;

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                        <div id="playlists-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                            <h4 class="text-xs font-medium text-slate-300 mb-2">Your Playlists</h4>
                            <div class="space-y-1 max-h-32 overflow-y-auto">
                                <!-- Playlists will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize playlists button
            const showPlaylistsBtn = element.querySelector('#showPlaylists');
            if (showPlaylistsBtn) {
                console.log(`Found playlists button for service: ${this.id}`);
                showPlaylistsBtn.addEventListener('click', () => {
                    this.togglePlaylists(element);
                });
            } else {
                console.log(`No playlists button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoFyService ${this.id}:`, error);
        }
    }

    /**
     * Toggle playlists visibility
     * @param {HTMLElement} element The service element
     */
    async togglePlaylists(element) {
        const playlistsContainer = element.querySelector(`#playlists-${this.id}`);
        if (!playlistsContainer) return;

        const isHidden = playlistsContainer.classList.contains('hidden');

        if (isHidden) {
            // Show loading state
            const playlistsContent = playlistsContainer.querySelector('.space-y-1');
            if (playlistsContent) {
                playlistsContent.innerHTML = '<div class="text-xs text-slate-400 text-center py-2">Loading playlists...</div>';
            }
            playlistsContainer.classList.remove('hidden');

            // Initialize playlists if needed
            if (this.playlists.length === 0) {
                await this.init();
            }

            // Populate playlists
            if (playlistsContent) {
                if (this.playlists.length > 0) {
                    playlistsContent.innerHTML = this.playlists.map(playlist => `
                        <div class="flex items-center justify-between bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors cursor-pointer"
                             onclick="window.location.href='/spotify?playlist=${playlist.id}'">
                            <div class="flex items-center space-x-2">
                                ${playlist.image ?
                                    `<img src="${playlist.image}" alt="${playlist.name}" class="w-6 h-6 rounded">` :
                                    '<div class="w-6 h-6 rounded bg-slate-600"></div>'
                                }
                                <span class="text-xs text-slate-300">${playlist.name}</span>
                            </div>
                            <span class="text-xs text-slate-500">${playlist.tracks_total} tracks</span>
                        </div>
                    `).join('');
                } else {
                    playlistsContent.innerHTML = '<div class="text-xs text-slate-400 text-center py-2">No playlists found</div>';
                }
            }
        } else {
            // Hide playlists
            playlistsContainer.classList.add('hidden');
        }
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoFy',
            name: 'KevkoFy',
            description: 'Custom Spotify interface',
            icon: 'music',
            color: 'cyan',
            category: 'Entertainment',
            version: '1.0.0',
            author: 'Kevko',
            dependencies: [],
            features: [
                'Playlist management',
                'Music discovery',
                'Custom recommendations',
                'Mood-based playlists'
            ],
            screenshots: [
                '/img/services/kevkoFy/screenshot1.jpg'
            ]
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }

    /**
     * Get service downloads
     * @returns {Array} Service downloads
     */
    static getDownloads() {
        return [
            {
                name: 'KevkoFy Desktop App',
                description: 'Enhanced music experience for desktop',
                icon: 'monitor',
                size: '45 MB',
                version: '1.3.2',
                date: '2023-03-15',
                url: '#'
            },
            {
                name: 'KevkoFy Mobile App',
                description: 'Take your music anywhere',
                icon: 'smartphone',
                size: '22 MB',
                version: '1.3.0',
                date: '2023-03-10',
                url: '#'
            }
        ];
    }
};
