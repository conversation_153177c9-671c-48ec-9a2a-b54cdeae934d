from flask import jsonify, request
from flask_login import login_required
from models.user import User
from . import admin_api
from .routes import admin_required
import logging

@admin_api.route('/search-users', methods=['GET'])
@admin_required
def search_users():
    """Search for users by username or email"""
    try:
        # Get the search query
        query = request.args.get('query', '')
        if not query or len(query) < 3:
            return jsonify({'error': 'Search query must be at least 3 characters'}), 400

        # Search for users by username or email
        users = User.objects.filter(
            __raw__={
                '$or': [
                    {'username': {'$regex': query, '$options': 'i'}},
                    {'email': {'$regex': query, '$options': 'i'}}
                ]
            }
        ).limit(10)  # Limit to 10 results

        # Format the response
        result = [{
            'id': str(user.id),
            'username': user.username,
            'email': user.email,
            'profile_picture': user.profile_picture if hasattr(user, 'profile_picture') else None,
            'created_at': user.created_at.isoformat() if hasattr(user, 'created_at') else None,
            'is_admin': user.is_admin if hasattr(user, 'is_admin') else False
        } for user in users]

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in search_users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
