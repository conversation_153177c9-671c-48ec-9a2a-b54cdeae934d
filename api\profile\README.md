# Profile API

This API provides endpoints for managing user profile information and account settings.

## File Structure

The Profile API has been segregated into logical modules for better organization, readability, and performance:

```
api/profile/
├── __init__.py                # Blueprint registration and imports
├── profile_basic.py           # Basic profile operations
├── profile_security.py        # Security-related operations
├── profile_data.py            # Data management operations
├── profile_stats.py           # User statistics
├── profile_cleanup.py         # Cleanup operations
├── profile_utils.py           # Utility functions
└── README.md                  # This documentation file
```

## Module Descriptions

- **profile_basic.py**: Basic profile operations (username, display name, profile picture, info)
- **profile_security.py**: Security-related operations (two-factor authentication, login history)
- **profile_data.py**: Data management operations (export, deletion)
- **profile_stats.py**: User statistics
- **profile_cleanup.py**: Cleanup operations (clear threads, rooms)
- **profile_utils.py**: Utility functions (data collection, email sending, verification codes)

## Endpoints

### GET /api/profile/info
- **Description**: Get the current user's profile information
- **Authentication**: Required
- **Response**: User profile object

### POST /api/profile/update-username
- **Description**: Update the current user's username
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "username": "new_username"
  }
  ```
- **Response**: Success message with updated username

### POST /api/profile/update-display-name
- **Description**: Update the current user's display name
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "display_name": "New Display Name"
  }
  ```
- **Response**: Success message with updated display name

### POST /api/profile/update-profile-picture
- **Description**: Update the current user's profile picture URL
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "profile_picture": "https://example.com/image.jpg"
  }
  ```
- **Response**: Success message with updated profile picture URL

### POST /api/profile/clear-threads
- **Description**: Delete all threads for the current user
- **Authentication**: Required
- **Response**: Success message with count of deleted threads

### POST /api/profile/clear-rooms
- **Description**: Delete all rooms created by the current user
- **Authentication**: Required
- **Response**: Success message with count of deleted rooms

### POST /api/profile/enable-two-factor
- **Description**: Enable two-factor authentication for the current user
- **Authentication**: Required
- **Response**: Success message

### POST /api/profile/disable-two-factor
- **Description**: Disable two-factor authentication for the current user
- **Authentication**: Required
- **Response**: Success message

### POST /api/profile/request-data-export
- **Description**: Request a data export for the current user
- **Authentication**: Required
- **Response**: Success message
- **Details**: This endpoint collects all user data and sends it to the user's email in CSV, JSON, and XML formats

### POST /api/profile/request-data-deletion
- **Description**: Request data deletion for the current user
- **Authentication**: Required
- **Request Body**:
  - `verification_code` (optional): Required for email login users with 2FA enabled
- **Response**:
  - Success message, or
  - `requires_verification: true` if verification is needed
- **Details**: This endpoint exports user data to their email and then deletes all user data after a 5-minute delay

### POST /api/profile/toggle-two-factor
- **Description**: Toggle two-factor authentication for the current user
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "enabled": true
  }
  ```
- **Response**: Success message with updated two-factor status

### GET /api/profile/stats
- **Description**: Get usage statistics for the current user
- **Authentication**: Required
- **Response**: User statistics object

## Models

### User
- `id`: User ID
- `username`: Username
- `display_name`: Display name (optional)
- `email`: Email address
- `profile_picture`: Profile picture URL (optional)
- `is_admin`: Boolean indicating if the user is an admin
- `two_factor_enabled`: Boolean indicating if two-factor authentication is enabled
- `created_at`: Account creation timestamp

## Usage Example

```javascript
// Get profile information
const profileResponse = await fetch('/api/profile/info', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const profile = await profileResponse.json();

// Update username
const usernameResponse = await fetch('/api/profile/update-username', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'new_username'
  })
});

// Toggle two-factor authentication
const twoFactorResponse = await fetch('/api/profile/toggle-two-factor', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    enabled: true
  })
});

// Get user statistics
const statsResponse = await fetch('/api/profile/stats', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const stats = await statsResponse.json();
```
