<div id="featureRestrictionModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Add Feature Restriction</h3>
            <button id="closeFeatureRestrictionModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="space-y-4">
            <!-- User Search -->
            <div>
                <label for="featureRestrictionUserSearch" class="block text-sm font-medium text-slate-300 mb-1">Search User by Username/Email</label>
                <div class="relative">
                    <input type="text" id="featureRestrictionUserSearch" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-1 focus:ring-cyan-500" placeholder="Enter username or email">
                    <div id="featureRestrictionSearchResults" class="absolute left-0 right-0 mt-1 bg-slate-700 border border-slate-600 rounded-md max-h-48 overflow-y-auto z-10 hidden"></div>
                </div>
            </div>

            <!-- Selected User Info -->
            <div id="selectedUserInfo" class="bg-slate-700/50 p-3 rounded-md hidden">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-full bg-slate-600 flex items-center justify-center overflow-hidden" id="selectedUserAvatar">
                        <!-- User avatar will be populated by JS -->
                    </div>
                    <div>
                        <div class="text-sm font-medium text-slate-200" id="selectedUserName"><!-- Username will be populated by JS --></div>
                        <div class="text-xs text-slate-400" id="selectedUserEmail"><!-- Email will be populated by JS --></div>
                    </div>
                </div>
            </div>

            <!-- Restriction Settings -->
            <div id="restrictionSettings" class="space-y-3 hidden">
                <div>
                    <label for="maxThreads" class="block text-sm font-medium text-slate-300 mb-1">Max Threads</label>
                    <input type="number" id="maxThreads" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-1 focus:ring-cyan-500" min="1" value="5">
                    <p class="text-xs text-slate-400 mt-1">Maximum number of chat threads the user can create</p>
                </div>
                <div>
                    <label for="maxConversationSets" class="block text-sm font-medium text-slate-300 mb-1">Max Conversation Sets</label>
                    <input type="number" id="maxConversationSets" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-1 focus:ring-cyan-500" min="1" value="5">
                    <p class="text-xs text-slate-400 mt-1">Maximum number of conversation sets (user + assistant messages) per thread</p>
                </div>
                <div>
                    <label for="maxLiveRooms" class="block text-sm font-medium text-slate-300 mb-1">Max Live Rooms</label>
                    <input type="number" id="maxLiveRooms" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-1 focus:ring-cyan-500" min="1" value="7">
                    <p class="text-xs text-slate-400 mt-1">Maximum number of live rooms the user can create</p>
                </div>
                <div>
                    <label for="maxLiveConversationSets" class="block text-sm font-medium text-slate-300 mb-1">Max Live Conversation Sets</label>
                    <input type="number" id="maxLiveConversationSets" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-1 focus:ring-cyan-500" min="1" value="15">
                    <p class="text-xs text-slate-400 mt-1">Maximum number of conversation sets (user + assistant messages) per live room</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3 pt-2">
                <button id="cancelFeatureRestriction" class="flex-1 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md px-4 py-2">Cancel</button>
                <button id="saveFeatureRestriction" class="flex-1 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed" disabled>Save</button>
            </div>
        </div>
    </div>
</div>
