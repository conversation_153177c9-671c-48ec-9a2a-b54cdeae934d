# Live API

This API provides endpoints for managing live chat rooms with real-time AI interactions.

## Endpoints

### GET /api/live/rooms
- **Description**: Get all rooms where the current user is a member
- **Authentication**: Required
- **Response**: List of room objects

### GET /api/live/room/:room_id
- **Description**: Get a specific room by ID
- **Authentication**: Required
- **Response**: Room object

### POST /api/live/room
- **Description**: Create a new room
- **Authentication**: Required
- **Response**: New room object

### POST /api/live/room/:room_id/invite
- **Description**: Invite a user to a room
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "username": "username"
  }
  ```
- **Response**: Updated room object

### DELETE /api/live/room/:room_id
- **Description**: Delete a room
- **Authentication**: Required
- **Response**: Success message

### POST /api/live/room/:room_id/leave
- **Description**: Leave a room (as participant)
- **Authentication**: Required
- **Response**: Success message

### POST /api/live/send
- **Description**: Send a message in a room and get AI response
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "room_id": "room_id",
    "message": "User message",
    "model": "gpt-4o-mini"
  }
  ```
- **Response**: Streaming response with AI message chunks

## Models

### Room
- `room_id`: Room ID
- `creator`: User who created the room
- `participant`: User who was invited to the room
- `messages`: List of message objects
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Message
- `content`: Message content
- `user_id`: ID of the user who sent the message
- `username`: Username of the user who sent the message
- `is_user`: Boolean indicating if the message is from a user (vs. AI)
- `timestamp`: Message timestamp

## Usage Example

```javascript
// Create a new room
const response = await fetch('/api/live/room', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
});
const room = await response.json();

// Invite a user to the room
const inviteResponse = await fetch(`/api/live/room/${room.room_id}/invite`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'friend_username'
  })
});

// Send a message
const messageResponse = await fetch('/api/live/send', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    room_id: room.room_id,
    message: 'Hello, everyone!',
    model: 'gpt-4o-mini'
  })
});
// Handle streaming response
```
