"""
Utility script to update country codes for existing users
This script can be run periodically to ensure all users have country data
"""
import logging
import requests
import time
from models.user import User
from models.api_log import APILog

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# IP Geolocation batch size
BATCH_SIZE = 50
# Delay between batches to avoid rate limiting
BATCH_DELAY = 2  # seconds

def get_country_from_ip_batch(ip_addresses):
    """
    Get country codes for a batch of IP addresses
    Uses ipinfo.io batch API

    Args:
        ip_addresses (list): List of IP addresses to lookup

    Returns:
        dict: Dictionary mapping IP addresses to country codes
    """
    results = {}

    # Skip for empty list
    if not ip_addresses:
        return results

    # Skip localhost/internal IPs
    filtered_ips = [ip for ip in ip_addresses if ip and
                   ip not in ['127.0.0.1', 'localhost', '::1'] and
                   not ip.startswith('192.168.') and
                   not ip.startswith('10.')]

    if not filtered_ips:
        return results

    try:
        # Make batch request to ipinfo.io
        response = requests.post(
            'https://ipinfo.io/batch',
            json=filtered_ips,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            for ip, info in data.items():
                country_code = info.get('country')
                if country_code:
                    results[ip] = country_code
        else:
            logger.warning(f"Failed to get countries for batch: {response.status_code}")

    except Exception as e:
        logger.error(f"Error in batch country lookup: {str(e)}")

    return results

def update_user_countries():
    """
    Update country codes for users who don't have one set
    Uses API logs to find IP addresses for users
    """
    # Get users without country codes
    users_without_country = User.objects(country_code__in=[None, ""])

    if not users_without_country:
        logger.info("All users have country codes set")
        return 0

    logger.info(f"Found {len(users_without_country)} users without country codes")

    # Create a mapping of user IDs to users
    user_map = {str(user.id): user for user in users_without_country}

    # Get API logs for these users to find their IP addresses
    user_ids = list(user_map.keys())
    logs = APILog.objects(user__in=user_ids).order_by('-timestamp')

    # Create a mapping of user IDs to their most recent IP address
    user_ip_map = {}
    for log in logs:
        user_id = str(log.user.id)
        if user_id not in user_ip_map and hasattr(log, 'details') and 'ip_address' in log.details:
            user_ip_map[user_id] = log.details['ip_address']

    logger.info(f"Found IP addresses for {len(user_ip_map)} users")

    # Process IP addresses in batches
    ip_list = list(set(user_ip_map.values()))  # Deduplicate IP addresses
    updated_count = 0

    # Process in batches
    for i in range(0, len(ip_list), BATCH_SIZE):
        batch = ip_list[i:i+BATCH_SIZE]
        logger.info(f"Processing batch {i//BATCH_SIZE + 1} of {(len(ip_list) + BATCH_SIZE - 1) // BATCH_SIZE}")

        # Get country codes for this batch
        ip_country_map = get_country_from_ip_batch(batch)

        # Update users with the country codes
        for user_id, ip in user_ip_map.items():
            if ip in ip_country_map and user_id in user_map:
                user = user_map[user_id]
                country_code = ip_country_map[ip]

                if user.update_country_code(country_code):
                    logger.info(f"Updated country for user {user.username}: {country_code}")
                    updated_count += 1

        # Add delay between batches
        if i + BATCH_SIZE < len(ip_list):
            time.sleep(BATCH_DELAY)

    logger.info(f"Updated country codes for {updated_count} users")
    return updated_count

if __name__ == "__main__":
    update_user_countries()
