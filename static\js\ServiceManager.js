/**
 * ServiceManager.js
 * Manages the lifecycle and state of all services in the dashboard.
 * Provides methods for loading, registering, updating, and removing services dynamically.
 */
class ServiceManager {
    constructor() {
        this.services = new Map(); // Store services by ID
        this.serviceContainer = document.getElementById('servicesContainer');
        this.eventListeners = {
            'serviceAdded': [],
            'serviceRemoved': [],
            'serviceUpdated': [],
            'serviceStatusChanged': []
        };

        // Initialize with stored service visibility preferences
        this.visibilityPreferences = this.loadVisibilityPreferences();
    }

    /**
     * Initialize the ServiceManager
     */
    async init() {
        console.log('Initializing ServiceManager...');
        // Create services container if it doesn't exist
        if (!this.serviceContainer) {
            console.log('Looking for services container...');
            this.serviceContainer = document.getElementById('servicesContainer');

            if (!this.serviceContainer) {
                console.warn('Services container not found, creating one...');
                this.serviceContainer = document.createElement('div');
                this.serviceContainer.id = 'servicesContainer';
                this.serviceContainer.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8';

                // Find the right place to insert it
                const dashboardView = document.getElementById('dashboardView');
                if (dashboardView) {
                    // Find the existing services grid and replace it
                    const existingGrid = dashboardView.querySelector('.grid');
                    if (existingGrid) {
                        existingGrid.parentNode.replaceChild(this.serviceContainer, existingGrid);
                    } else {
                        // If no existing grid, insert after the welcome card
                        const welcomeCard = dashboardView.querySelector('.card');
                        if (welcomeCard) {
                            welcomeCard.insertAdjacentElement('afterend', this.serviceContainer);
                        } else {
                            dashboardView.appendChild(this.serviceContainer);
                        }
                    }
                }
            } else {
                console.log('Found existing services container');
            }
        }

        // Load services from configuration or storage
        console.log('Loading services...');
        await this.loadServices();
        console.log('Services loaded');

        // Set up event listeners for service visibility toggles
        this.setupVisibilityToggles();
    }

    /**
     * Load all services from configuration or storage
     */
    async loadServices() {
        try {
            // Clear existing services
            this.services.clear();
            if (this.serviceContainer) {
                this.serviceContainer.innerHTML = '';
            }

            // Attempt to load service configuration from server or local storage
            const serviceConfigs = await this.fetchServiceConfigurations();
            console.log('Service configurations to load:', serviceConfigs);

            if (!serviceConfigs || serviceConfigs.length === 0) {
                console.log('No service configurations found, dashboard will be empty');
                return;
            }

            // Register each service
            for (const config of serviceConfigs) {
                console.log('Registering service from config:', config);
                await this.registerService(config);
            }

            console.log(`Loaded ${serviceConfigs.length} services`);
            this.trigger('servicesLoaded', { count: serviceConfigs.length });
        } catch (error) {
            console.error('Error loading services:', error);
        }
    }

    /**
     * Fetch service configurations from server or local storage
     * @returns {Promise<Array>} Array of service configurations
     */
    async fetchServiceConfigurations() {
        // In a real implementation, this might fetch from an API
        // Always prioritize localStorage configurations

        // Check if we have configurations in localStorage
        const storedConfigs = localStorage.getItem('serviceConfigurations');
        console.log('Stored service configurations in localStorage:', storedConfigs);
        if (storedConfigs) {
            try {
                const configs = JSON.parse(storedConfigs);
                console.log('Parsed service configurations:', configs);
                if (configs && Array.isArray(configs) && configs.length > 0) {
                    console.log('Using stored service configurations from localStorage');
                    return configs;
                } else {
                    console.log('Stored configurations are empty or invalid');
                }
            } catch (e) {
                console.warn('Failed to parse stored service configurations:', e);
                // If there's an error parsing, clear the invalid data
                localStorage.removeItem('serviceConfigurations');
            }
        } else {
            console.log('No service configurations found in localStorage');
        }

        // Return an empty array by default - services must be added from the Store
        // We no longer fall back to ServiceConfig to ensure user customizations are preserved
        return [];
    }

    /**
     * Save current service configurations to localStorage
     */
    saveServiceConfigurations() {
        const configs = Array.from(this.services.values()).map(service => service.getConfig());
        console.log('Saving service configurations to localStorage:', configs);

        if (configs && configs.length > 0) {
            localStorage.setItem('serviceConfigurations', JSON.stringify(configs));
            console.log('Service configurations saved successfully');
        } else {
            // If there are no services, clear the configurations
            localStorage.removeItem('serviceConfigurations');
            console.log('No services to save, cleared service configurations');
        }
    }

    /**
     * Register a new service with the manager
     * @param {Object} config Service configuration object
     * @returns {Promise<Service>} The registered service instance
     */
    async registerService(config) {
        try {
            // Check for suspicious service names or descriptions
            const suspiciousTerms = ['asd', 'sda', 'test', 'asdf'];
            const suspiciousPatterns = [/^test/i, /^asd/i, /^sda/i, /^asdf/i];

            // Check for exact matches
            if (suspiciousTerms.includes(config.id) ||
                suspiciousTerms.includes(config.name) ||
                suspiciousTerms.includes(config.description)) {
                console.error(`Suspicious service detected: ${config.id}. Registration blocked.`);
                return null;
            }

            // Check for pattern matches
            for (const pattern of suspiciousPatterns) {
                if (pattern.test(config.id) || pattern.test(config.name) || pattern.test(config.description)) {
                    console.error(`Suspicious service pattern detected: ${config.id}. Registration blocked.`);
                    return null;
                }
            }

            // Check if service module exists
            let ServiceClass = window.Service; // Default to base Service class

            // Check if a custom class is specified in the config
            if (config.className) {
                // Try to get the class from the ServiceConfig class mapping
                if (window.ServiceConfig && window.ServiceConfig.classMapping &&
                    window.ServiceConfig.classMapping[config.className]) {
                    ServiceClass = window.ServiceConfig.classMapping[config.className];
                    console.log(`Using custom service class ${config.className} for ${config.id}`);
                } else {
                    // Try to get the class from the global scope
                    try {
                        ServiceClass = window[config.className];
                        if (!ServiceClass) {
                            console.warn(`Custom service class ${config.className} not found, using base Service class`);
                            ServiceClass = window.Service;
                        }
                    } catch (e) {
                        console.warn(`Error getting custom service class ${config.className}, using base Service class`);
                        ServiceClass = window.Service;
                    }
                }
            }

            // Create service instance
            const service = new ServiceClass(config);

            // Services are visible by default when explicitly added, unless specified otherwise
            if (config.visible === undefined) {
                service.visible = true;
            }

            // Update visibility preferences
            this.visibilityPreferences[config.id] = service.visible;

            // Add to services map
            this.services.set(config.id, service);

            // Render the service
            this.renderService(service);

            // Save service configurations
            this.saveServiceConfigurations();

            // Trigger event
            this.trigger('serviceAdded', { service });

            return service;
        } catch (error) {
            console.error(`Failed to register service ${config.id}:`, error);
            throw error;
        }
    }

    /**
     * Set service visibility
     * @param {string} serviceId Service ID
     * @param {boolean} visible Whether the service should be visible
     */
    setServiceVisibility(serviceId, visible) {
        console.log(`Setting visibility of service ${serviceId} to ${visible}`);

        // Update visibility preference
        this.visibilityPreferences[serviceId] = visible;

        // Save preferences
        this.saveVisibilityPreferences();

        // Update UI
        const serviceElement = document.getElementById(`service-${serviceId}`);
        if (serviceElement) {
            if (visible) {
                serviceElement.classList.remove('hidden');
            } else {
                serviceElement.classList.add('hidden');
            }
        }

        // Update service object
        const service = this.services.get(serviceId);
        if (service) {
            service.visible = visible;
        }
    }

    /**
     * Render a service in the UI
     * @param {Service} service The service to render
     */
    renderService(service) {
        if (!this.serviceContainer) {
            console.error('Service container not found, trying to find it again...');
            this.serviceContainer = document.getElementById('servicesContainer');

            if (!this.serviceContainer) {
                console.error('Service container still not found, cannot render service');
                return;
            }
        }

        console.log(`Rendering service: ${service.id}`);

        // Check if service element already exists
        let serviceElement = document.getElementById(`service-${service.id}`);

        if (serviceElement) {
            // Update existing element
            console.log(`Updating existing service element for ${service.id}`);
            serviceElement.innerHTML = service.render();
        } else {
            // Create new element
            console.log(`Creating new service element for ${service.id}`);
            serviceElement = document.createElement('div');
            serviceElement.id = `service-${service.id}`;
            serviceElement.innerHTML = service.render();

            // Instead of relying on firstChild, we'll use a more direct approach
            // Add a data attribute to the service element itself
            serviceElement.setAttribute('data-service', service.id);

            // Check if the HTML is empty
            if (!serviceElement.innerHTML.trim()) {
                console.error(`Service ${service.id} rendered empty HTML`);
                console.log('Rendered HTML:', service.render());
                return;
            }

            // Set visibility
            if (!service.visible) {
                serviceElement.classList.add('hidden');
            }

            // Add to container
            this.serviceContainer.appendChild(serviceElement);
            console.log(`Service ${service.id} added to container`);
        }

        // Initialize any event handlers the service needs
        // Pass the entire service element instead of just the first child
        service.initEventHandlers(serviceElement);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * Update an existing service
     * @param {string} id Service ID
     * @param {Object} updates Updates to apply to the service
     * @returns {Service} The updated service
     */
    updateService(id, updates) {
        const service = this.services.get(id);
        if (!service) {
            throw new Error(`Service ${id} not found`);
        }

        // Apply updates
        Object.assign(service, updates);

        // Re-render the service
        this.renderService(service);

        // Save updated configurations
        this.saveServiceConfigurations();

        // Trigger event
        this.trigger('serviceUpdated', { service });

        return service;
    }

    /**
     * Remove a service
     * @param {string} id Service ID
     * @returns {boolean} True if service was removed
     */
    removeService(id) {
        const service = this.services.get(id);
        if (!service) {
            return false;
        }

        // Remove from DOM
        const serviceElement = document.getElementById(`service-${id}`);
        if (serviceElement) {
            serviceElement.remove();
        }

        // Remove from services map
        this.services.delete(id);

        // Save updated configurations
        this.saveServiceConfigurations();

        // Trigger event
        this.trigger('serviceRemoved', { serviceId: id });

        return true;
    }

    /**
     * Set the status of a service
     * @param {string} id Service ID
     * @param {string} status New status ('online', 'offline', 'coming-soon', etc.)
     */
    setServiceStatus(id, status) {
        const service = this.services.get(id);
        if (!service) {
            throw new Error(`Service ${id} not found`);
        }

        const oldStatus = service.status;
        service.status = status;

        // Re-render the service
        this.renderService(service);

        // Save updated configurations
        this.saveServiceConfigurations();

        // Trigger event
        this.trigger('serviceStatusChanged', {
            service,
            oldStatus,
            newStatus: status
        });
    }

    /**
     * Set the visibility of a service
     * @param {string} id Service ID
     * @param {boolean} visible Whether the service should be visible
     */
    setServiceVisibility(id, visible) {
        const service = this.services.get(id);
        if (!service) {
            throw new Error(`Service ${id} not found`);
        }

        service.visible = visible;

        // Update DOM
        const serviceElement = document.getElementById(`service-${id}`);
        if (serviceElement) {
            if (visible) {
                serviceElement.classList.remove('hidden');
            } else {
                serviceElement.classList.add('hidden');
            }
        }

        // Update visibility preferences
        this.visibilityPreferences[id] = visible;
        this.saveVisibilityPreferences();
    }

    /**
     * Load service visibility preferences from localStorage
     * @returns {Object} Map of service IDs to visibility preferences
     */
    loadVisibilityPreferences() {
        const stored = localStorage.getItem('serviceVisibility');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (e) {
                console.warn('Failed to parse stored visibility preferences');
            }
        }
        return {};
    }

    /**
     * Save service visibility preferences to localStorage
     */
    saveVisibilityPreferences() {
        localStorage.setItem('serviceVisibility', JSON.stringify(this.visibilityPreferences));
    }

    /**
     * Set up event listeners for service visibility toggles in settings
     */
    setupVisibilityToggles() {
        // Find all service visibility toggles
        const toggles = document.querySelectorAll('[data-service-toggle]');
        toggles.forEach(toggle => {
            const serviceId = toggle.getAttribute('data-service-toggle');

            // Set initial state based on preferences
            if (this.visibilityPreferences[serviceId] !== undefined) {
                toggle.checked = this.visibilityPreferences[serviceId];
            }

            // Add change listener
            toggle.addEventListener('change', () => {
                this.setServiceVisibility(serviceId, toggle.checked);
            });
        });
    }

    /**
     * Add an event listener
     * @param {string} event Event name
     * @param {Function} callback Callback function
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    /**
     * Remove an event listener
     * @param {string} event Event name
     * @param {Function} callback Callback function
     */
    off(event, callback) {
        if (!this.eventListeners[event]) {
            return;
        }
        this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }

    /**
     * Trigger an event
     * @param {string} event Event name
     * @param {Object} data Event data
     */
    trigger(event, data) {
        if (!this.eventListeners[event]) {
            return;
        }
        this.eventListeners[event].forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in ${event} event handler:`, error);
            }
        });
    }
}

/**
 * Base Service class
 * All services should extend this class
 */
window.Service = class Service {
    constructor(config) {
        this.id = config.id;
        this.name = config.name;
        this.description = config.description;
        this.icon = config.icon;
        this.color = config.color;
        this.status = config.status || 'offline';
        this.url = config.url;
        this.type = config.type || 'internal';
        this.visible = config.visible !== undefined ? config.visible : true;
    }

    /**
     * Get the service configuration
     * @returns {Object} Service configuration
     */
    getConfig() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            icon: this.icon,
            color: this.color,
            status: this.status,
            url: this.url,
            type: this.type,
            visible: this.visible,
            className: this.id.charAt(0).toUpperCase() + this.id.slice(1) + 'Service'
        };
    }

    /**
     * Render the service card HTML
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering service: ${this.id}`, {
                id: this.id,
                name: this.name,
                status: this.status,
                url: this.url,
                type: this.type
            });
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            let actionButton = '';

            // Define routes for specific services
            const serviceRoutes = {
                'kevkoFy': '/spotify',
                'kevkoAI': '/chat',
                'kevkoLive': '/live',
                'kevkoFriends': '/friends'
            };

            // Check if this service has a predefined route
            const hasRoute = serviceRoutes.hasOwnProperty(this.id);

            if (this.url) {
                // Service has a URL - use it for the button
                const isExternalUrl = this.url.startsWith('http');
                const openInNewTab = this.openInNewTab || isExternalUrl;
                const target = openInNewTab ? 'target="_blank"' : '';
                const icon = openInNewTab ? 'chevron-right' : 'arrow-right';
                const buttonText = openInNewTab && !this.openInNewTab ? `Launch ${this.name}` : `Go to ${this.name}`;
                const buttonClass = isExternalUrl ?
                    `bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50` :
                    `service-btn-${this.color}`;
                const textClass = isExternalUrl ? 'text-slate-300 group-hover:text-slate-100' : 'text-white';

                actionButton = `
                    <a href="${this.url}" ${target}
                       class="w-full ${buttonClass} rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                        <span class="text-sm ${textClass}">${buttonText}</span>
                        <i data-lucide="${icon}" class="h-4 w-4 ml-1 ${isExternalUrl ? '' : 'text-white'}"></i>
                    </a>
                `;
            } else if (hasRoute) {
                // No URL but has a predefined route
                const openInNewTab = this.openInNewTab || false;
                const target = openInNewTab ? 'target="_blank"' : '';
                const icon = openInNewTab ? 'chevron-right' : 'arrow-right';

                actionButton = `
                    <a href="${serviceRoutes[this.id]}" ${target}
                       class="w-full service-btn-${this.color} rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                        <span class="text-sm text-white">Go to ${this.name}</span>
                        <i data-lucide="${icon}" class="h-4 w-4 ml-1 text-white"></i>
                    </a>
                `;
            } else if (this.status === 'offline') {
                // Disabled button for offline services
                actionButton = `
                    <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed">
                        <span class="text-sm text-slate-300">Offline</span>
                    </div>
                `;
            } else {
                // Coming soon for other services
                actionButton = `
                    <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed">
                        <span class="text-sm text-slate-300 animate-pulse">Coming Soon</span>
                    </div>
                `;
            }

            const html = `
                <div class="service-card service-${this.color} ${statusClass} bg-slate-800/50 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg service-icon-bg-${this.color}">
                                <i data-lucide="${this.icon}" class="h-6 w-6 service-icon-${this.color}"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        try {
            console.log(`Initializing event handlers for service: ${this.id}`);

            // Find all elements with data-lucide attribute
            const iconElements = element.querySelectorAll('[data-lucide]');

            // Initialize Lucide icons if they exist
            if (window.lucide && iconElements.length > 0) {
                window.lucide.createIcons({
                    attrs: {
                        class: ["h-6", "w-6"]
                    },
                    elements: iconElements
                });
                console.log(`Initialized ${iconElements.length} icons for service: ${this.id}`);
            }

            // Add any additional event handlers here
            // For example, buttons, links, etc.

        } catch (error) {
            console.error(`Error initializing event handlers for service ${this.id}:`, error);
        }
    }
}
