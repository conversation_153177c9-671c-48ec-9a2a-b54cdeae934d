"""
Utility script to add sample country data to users for testing the world map
"""
from models.user import User
import random

# List of country codes for sample data
COUNTRY_CODES = [
    'US', 'GB', 'CA', 'AU', 'DE', 'FR', 'JP', 'CN', 'IN', 'BR', 'RU', 'ZA', 
    'MX', 'ES', 'IT', 'NL', 'SE', 'NO', 'DK', 'FI', 'PL', 'TR', 'AE', 'SA',
    'SG', 'KR', 'TH', 'VN', 'MY', 'ID', 'PH', 'NZ', 'AR', 'CL', 'CO', 'PE'
]

# Distribution weights (some countries should have more users)
WEIGHTS = {
    'US': 20, 'GB': 15, 'CA': 10, 'AU': 8, 'DE': 12, 'FR': 10, 'JP': 8, 
    'CN': 15, 'IN': 18, 'BR': 10, 'RU': 8
}

def add_country_data():
    """Add random country data to users"""
    users = User.objects()
    updated_count = 0
    
    for user in users:
        # Skip users that already have country data
        if user.country_code and user.country_code.strip():
            continue
            
        # Assign a random country with weighted distribution
        if random.random() < 0.8:  # 80% chance to get a weighted country
            weighted_countries = []
            for country, weight in WEIGHTS.items():
                weighted_countries.extend([country] * weight)
            country_code = random.choice(weighted_countries)
        else:
            country_code = random.choice(COUNTRY_CODES)
            
        user.country_code = country_code
        user.save()
        updated_count += 1
    
    return updated_count

if __name__ == "__main__":
    count = add_country_data()
    print(f"Added country data to {count} users")
