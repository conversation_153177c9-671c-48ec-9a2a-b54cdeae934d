from flask import jsonify, request
from . import spotify_api
from .services.spotify_client import get_spotify_client
from .decorators import require_auth
from utils.api_logger import log_api_request
import logging

@spotify_api.route('/playlists')
@require_auth
def get_playlists():
    """Get user's playlists"""
    try:
        sp = get_spotify_client()
        playlists = sp.current_user_playlists()
        return jsonify(playlists['items'])
    except Exception as e:
        logging.error(f"Error getting playlists: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/playlist/<playlist_id>')
@require_auth
def get_playlist(playlist_id):
    """Get a specific playlist"""
    try:
        sp = get_spotify_client()
        playlist = sp.playlist(playlist_id)
        return jsonify(playlist)
    except Exception as e:
        logging.error(f"Error getting playlist: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/playlist/<playlist_id>/tracks')
@require_auth
def get_playlist_tracks(playlist_id):
    """Get tracks in a playlist"""
    try:
        sp = get_spotify_client()
        offset = request.args.get('offset', 0, type=int)
        limit = request.args.get('limit', 100, type=int)

        tracks = sp.playlist_tracks(playlist_id, offset=offset, limit=limit)
        return jsonify(tracks)
    except Exception as e:
        logging.error(f"Error getting playlist tracks: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/play-playlist', methods=['POST'])
@require_auth
@log_api_request('playlist_play', 'spotify')
def play_playlist():
    """Play a specific playlist"""
    try:
        sp = get_spotify_client()
        data = request.json
        playlist_id = data.get('playlist_id')

        if not playlist_id:
            return jsonify({'error': 'playlist_id is required'}), 400

        # Get current playback state first
        playback = sp.current_playback()
        if not playback or not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)

        # Start playback with the playlist
        sp.start_playback(context_uri=f'spotify:playlist:{playlist_id}')
        return jsonify({'success': True})
    except Exception as e:
        logging.error(f"Error playing playlist: {str(e)}")
        return jsonify({'error': str(e)}), 500
