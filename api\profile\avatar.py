from flask import request, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models.uploaded_file import UploadedFile
from models.user import User
from . import profile_api
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Allowed file extensions for avatars
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    """Check if the file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@profile_api.route('/avatar/upload', methods=['POST'])
@login_required
def upload_avatar():
    """Upload a profile avatar and store it in the database"""
    try:
        # Check if the post request has the file part
        if 'avatar' not in request.files:
            return jsonify({'error': 'No avatar file provided'}), 400

        avatar_file = request.files['avatar']

        # If user does not select file, browser also
        # submit an empty part without filename
        if avatar_file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        if avatar_file and allowed_file(avatar_file.filename):
            # Create a secure filename
            filename = secure_filename(avatar_file.filename)

            # Get the file extension
            ext = '.' + filename.rsplit('.', 1)[1].lower()

            # Read the file data
            file_data = avatar_file.read()

            # Determine content type based on file extension
            content_type = avatar_file.content_type
            if not content_type or content_type == 'application/octet-stream':
                if ext == '.jpg' or ext == '.jpeg':
                    content_type = 'image/jpeg'
                elif ext == '.png':
                    content_type = 'image/png'
                elif ext == '.gif':
                    content_type = 'image/gif'
                elif ext == '.webp':
                    content_type = 'image/webp'
                else:
                    content_type = 'image/jpeg'  # Default to JPEG

            # Delete old avatar if exists
            if current_user.profile_picture:
                try:
                    # Delete the old avatar file from the database
                    UploadedFile.delete_by_url(current_user.profile_picture)
                    logger.info(f"Deleted old avatar for user {current_user.username}")
                except Exception as e:
                    logger.error(f"Error deleting old avatar: {str(e)}")

            # Store the new avatar in the database
            uploaded_file = UploadedFile(
                filename=f"avatar_{current_user.username}_{filename}",
                content_type=content_type,
                data=file_data,
                uploaded_by=current_user
            )
            uploaded_file.save()

            # Generate the URL for the file
            file_url = f"/api/upload/{uploaded_file.file_id}"

            # Update user's profile picture URL
            current_user.profile_picture = file_url
            current_user.save()

            logger.info(f"Avatar uploaded successfully for user {current_user.username}: {file_url}")

            return jsonify({
                'success': True,
                'url': file_url,
                'message': 'Avatar uploaded successfully'
            })

        return jsonify({'error': 'File type not allowed'}), 400

    except Exception as e:
        logger.error(f"Error uploading avatar: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/avatar/info', methods=['GET'])
@login_required
def get_avatar_info():
    """Get the current user's avatar information"""
    try:
        avatar_url = current_user.profile_picture
        
        # For Google users, only return custom avatar if it exists
        is_google_user = current_user.is_google_user()
        
        return jsonify({
            'avatar_url': avatar_url,
            'is_google_user': is_google_user,
            'has_custom_avatar': bool(avatar_url)
        })
    except Exception as e:
        logger.error(f"Error getting avatar info: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500