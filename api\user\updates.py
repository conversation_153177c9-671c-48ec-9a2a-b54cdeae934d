from flask import jsonify, request
from flask_login import login_required, current_user
from . import user_api
from models.service_update import ServiceUpdate
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@user_api.route('/updates', methods=['GET'])
@login_required
def get_user_updates():
    """
    Get service updates for the current user
    If service_ids is provided as a comma-separated list, filter by those services
    """
    try:
        # Get optional service_ids filter
        service_ids_param = request.args.get('service_ids', '')

        # Parse service IDs
        if service_ids_param:
            service_ids = [s.strip() for s in service_ids_param.split(',')]
        else:
            service_ids = []

        # Query updates
        if service_ids:
            # Use case-insensitive regex for service_id
            import re
            # Create a regex that matches any of the service IDs, case-insensitive
            service_ids_pattern = '|'.join([f'^{re.escape(service_id)}$' for service_id in service_ids])
            logger.info(f"Service IDs regex pattern: {service_ids_pattern}")

            # Use the $regex operator for case-insensitive matching
            updates = ServiceUpdate.objects(service_id__iregex=service_ids_pattern).order_by('-published_at')
        else:
            # If no service IDs provided, get all updates
            updates = ServiceUpdate.objects().order_by('-published_at')

        # Log the query for debugging
        logger.info(f"Querying updates for service_ids: {service_ids}")

        # Log the results for debugging
        logger.info(f"Found {len(updates)} updates")
        for update in updates:
            logger.info(f"Update: service_id={update.service_id}, title={update.title}")

        # Convert to list of dictionaries
        updates_list = [update.to_dict() for update in updates]

        return jsonify({
            'success': True,
            'updates': updates_list
        })
    except Exception as e:
        logger.error(f"Error getting user updates: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
