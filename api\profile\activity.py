from flask import jsonify, request
from flask_login import current_user, login_required
from models.user import User
from models.user_activity import UserActivity
from models.constants import VALID_ACTIVITY_SECTIONS, DEFAULT_ACTIVITY_SECTION
from api.profile import profile_api
from datetime import datetime, timedelta
from utils.cache import cache
import logging

@profile_api.route('/activity/<username>', methods=['GET'])
def get_user_activity(username):
    """Get a user's recent activity"""
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Check if the profile is private and not the current user
    from models.profile_customization import ProfileCustomization
    profile = ProfileCustomization.get_or_create(user)
    
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({'success': False, 'error': 'Profile is private'}), 403
    
    # Get user activity (limit to 10 most recent)
    activities = UserActivity.objects(user=user).order_by('-last_active').limit(10)
    
    # Format activities for response
    activity_list = []
    for activity in activities:
        activity_list.append({
            'timestamp': activity.last_active.isoformat(),
            'activity_type': activity.section,
            'description': f"Active in {activity.section}",
            'metadata': {'section': activity.section}
        })
    
    return jsonify({
        'success': True,
        'activities': activity_list
    })

@profile_api.route('/online-status/<username>', methods=['GET'])
def get_online_status(username):
    """Get a user's online status
    
    A user is considered online if they have any activity in the last 30 seconds
    """
    # Check cache first (5 second TTL for online status)
    cache_key = f'online_status:{username}'
    cached_result = cache.get(cache_key)
    
    if cached_result is not None:
        return jsonify(cached_result)
    
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Check if the profile is private and not the current user
    from models.profile_customization import ProfileCustomization
    profile = ProfileCustomization.get_or_create(user)
    
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({'success': False, 'error': 'Profile is private'}), 403
    
    # Get the most recent activity across all sections
    cutoff_time = datetime.utcnow() - timedelta(seconds=30)
    most_recent = UserActivity.objects(user=user, last_active__gte=cutoff_time).only('last_active').first()
    
    is_online = most_recent is not None
    
    result = {
        'success': True,
        'username': username,
        'is_online': is_online,
        'last_active': most_recent.last_active.isoformat() if most_recent else None
    }
    
    # Cache the result for 5 seconds
    cache.set(cache_key, result, ttl=5)
    
    return jsonify(result)

@profile_api.route('/online-status-batch', methods=['POST'])
def get_online_status_batch():
    """Get online status for multiple users in a single request
    
    Request body should contain a list of usernames:
    {
        "usernames": ["user1", "user2", "user3"]
    }
    """
    # Get usernames from request
    data = request.get_json() or {}
    usernames = data.get('usernames', [])
    
    if not usernames:
        return jsonify({
            'success': False,
            'message': 'No usernames provided'
        }), 400
    
    # Limit the number of usernames to prevent abuse
    if len(usernames) > 50:
        return jsonify({
            'success': False,
            'message': 'Too many usernames. Maximum is 50.'
        }), 400
    
    # Get online status for each user
    results = {}
    cutoff_time = datetime.utcnow() - timedelta(seconds=30)
    
    for username in usernames:
        # Check cache first
        cache_key = f'online_status:{username}'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            results[username] = cached_result
            continue
        
        # Find the user
        user = User.objects(username=username).first()
        if not user:
            results[username] = {
                'success': False,
                'error': 'User not found'
            }
            continue
        
        # Check if the profile is private and not the current user
        from models.profile_customization import ProfileCustomization
        profile = ProfileCustomization.get_or_create(user)
        
        if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
            results[username] = {
                'success': False,
                'error': 'Profile is private'
            }
            continue
        
        # Get the most recent activity
        most_recent = UserActivity.objects(user=user, last_active__gte=cutoff_time).only('last_active').first()
        is_online = most_recent is not None
        
        result = {
            'success': True,
            'username': username,
            'is_online': is_online,
            'last_active': most_recent.last_active.isoformat() if most_recent else None
        }
        
        # Cache the result
        cache.set(cache_key, result, ttl=5)
        
        results[username] = result
    
    return jsonify({
        'success': True,
        'results': results
    })

@profile_api.route('/update-activity', methods=['POST'])
@login_required
def update_user_activity():
    """Update the current user's activity status
    
    This endpoint should be called periodically by the client to maintain online status
    """
    # Get the section from the request (default to DEFAULT_ACTIVITY_SECTION)
    data = request.get_json() or {}
    section = data.get('section', DEFAULT_ACTIVITY_SECTION)
    
    # Validate section is in allowed choices
    if section not in VALID_ACTIVITY_SECTIONS:
        return jsonify({
            'success': False,
            'message': f'Invalid section. Must be one of: {", ".join(VALID_ACTIVITY_SECTIONS)}'
        }), 400
    
    try:
        # Update the user's activity
        UserActivity.update_activity(current_user.id, section)
        
        return jsonify({
            'success': True,
            'message': 'Activity updated successfully'
        })
    except Exception as e:
        # Log the error
        import logging
        logging.error(f"Error updating activity: {str(e)}")
        
        return jsonify({
            'success': False,
            'message': 'Failed to update activity',
            'error': str(e)
        }), 500