from flask import jsonify, request, session
from flask_login import login_user, logout_user, login_required, current_user
from . import auth_api
from models.user import User
from models.login_history import LoginHistory
from datetime import datetime
import random
import string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
import logging

def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))

def send_verification_email(email, code):
    """Send verification code via email"""
    try:
        sender_email = os.environ.get('EMAIL_ADDRESS')
        sender_password = os.environ.get('EMAIL_PASSWORD')

        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = email
        msg['Subject'] = 'Verification Code'

        body = f'Your verification code is: {code}'
        msg.attach(MIMEText(body, 'plain'))

        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()

        return True
    except Exception as e:
        logging.error(f"Error sending verification email: {str(e)}")
        return False

@auth_api.route('/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        verification_code = data.get('verification_code')

        if not username or not email or not password or not verification_code:
            return jsonify({'error': 'All fields are required'}), 400

        # Check verification code
        stored_code = session.get(f'verification_code_{email}')
        code_timestamp = session.get(f'verification_code_timestamp_{email}')

        if not stored_code or verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:
            return jsonify({'error': 'Verification code has expired'}), 400

        # Check if email already exists
        if User.objects(email=email).first():
            return jsonify({'error': 'Email already registered'}), 400

        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        user.save()

        # Note: Automatic feature restrictions have been removed
        # Feature restrictions are now only set by admins

        # Clear verification code
        session.pop(f'verification_code_{email}', None)
        session.pop(f'verification_code_timestamp_{email}', None)

        # Log in the user
        login_user(user)

        # Log login history
        try:
            LoginHistory.log_login(user, request)
        except Exception as e:
            logging.warning(f"Failed to log login history: {str(e)}")

        # Country detection is now handled by middleware

        return jsonify({
            'message': 'Registration successful',
            'user': {
                'id': str(user.id),
                'username': user.username,
                'email': user.email
            }
        })
    except Exception as e:
        logging.error(f"Error in register: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_api.route('/login', methods=['POST'])
def login():
    """Log in a user"""
    try:
        data = request.get_json()
        login_id = data.get('login')  # Username or email
        password = data.get('password')
        verification_code = data.get('verification_code')

        if not login_id or not password:
            return jsonify({'error': 'Login and password are required'}), 400

        # Find user
        user = User.find_by_login(login_id)
        if not user:
            return jsonify({'error': 'Invalid credentials'}), 401

        # Check password
        if not user.check_password(password):
            return jsonify({'error': 'Invalid credentials'}), 401

        # Check if 2FA is enabled
        if hasattr(user, 'two_factor_enabled') and user.two_factor_enabled:
            # If 2FA is enabled, verify code
            if not verification_code:
                # Generate and send verification code
                code = generate_verification_code()
                session[f'verification_code_{user.email}'] = code
                session[f'verification_code_timestamp_{user.email}'] = datetime.now().timestamp()

                send_verification_email(user.email, code)

                return jsonify({
                    'message': 'Verification code sent',
                    'two_factor_required': True
                }), 200

            # Verify the code
            stored_code = session.get(f'verification_code_{user.email}')
            code_timestamp = session.get(f'verification_code_timestamp_{user.email}')

            if not stored_code or verification_code != stored_code:
                return jsonify({'error': 'Incorrect verification code'}), 400

            if (datetime.now().timestamp() - code_timestamp) > 600:
                return jsonify({'error': 'Verification code has expired'}), 400

            # Clear verification code
            session.pop(f'verification_code_{user.email}', None)
            session.pop(f'verification_code_timestamp_{user.email}', None)

        # Log in the user
        login_user(user)

        # Log login history
        try:
            LoginHistory.log_login(user, request)
        except Exception as e:
            logging.warning(f"Failed to log login history: {str(e)}")

        # Country detection is now handled by middleware

        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': str(user.id),
                'username': user.username,
                'email': user.email,
                'is_admin': user.is_admin
            }
        })
    except Exception as e:
        logging.error(f"Error in login: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_api.route('/logout', methods=['POST'])
@login_required
def logout():
    """Log out the current user"""
    try:
        # Clear verification codes if any exist
        for key in list(session.keys()):
            if key.startswith('verification_code_'):
                session.pop(key, None)

        # Log out the user
        logout_user()

        return jsonify({'message': 'Logout successful'})
    except Exception as e:
        logging.error(f"Error in logout: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_api.route('/send-verification', methods=['POST'])
def send_verification():
    """Send verification code for registration or password reset"""
    try:
        data = request.get_json()
        email = data.get('email')
        purpose = data.get('purpose', 'register')  # 'register' or 'reset'

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        # For registration, check if email already exists
        if purpose == 'register' and User.objects(email=email).first():
            return jsonify({'error': 'Email already registered'}), 400

        # For password reset, check if email exists
        if purpose == 'reset' and not User.objects(email=email).first():
            return jsonify({'error': 'Email not found'}), 404

        # Generate and store verification code
        code = generate_verification_code()
        session[f'verification_code_{email}'] = code
        session[f'verification_code_timestamp_{email}'] = datetime.now().timestamp()

        # Send verification email
        if send_verification_email(email, code):
            return jsonify({'message': 'Verification code sent'})
        else:
            return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        logging.error(f"Error in send_verification: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_api.route('/reset-password', methods=['POST'])
def reset_password():
    """Reset user password"""
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')
        new_password = data.get('new_password')

        if not email or not verification_code or not new_password:
            return jsonify({'error': 'All fields are required'}), 400

        # Check verification code
        stored_code = session.get(f'verification_code_{email}')
        code_timestamp = session.get(f'verification_code_timestamp_{email}')

        if not stored_code or verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:
            return jsonify({'error': 'Verification code has expired'}), 400

        # Find user
        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Update password
        user.set_password(new_password)
        user.save()

        # Clear verification code
        session.pop(f'verification_code_{email}', None)
        session.pop(f'verification_code_timestamp_{email}', None)

        return jsonify({'message': 'Password reset successful'})
    except Exception as e:
        logging.error(f"Error in reset_password: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_api.route('/check-auth', methods=['GET'])
def check_auth():
    """Check if user is authenticated"""
    if current_user.is_authenticated:
        return jsonify({
            'authenticated': True,
            'user': {
                'id': str(current_user.id),
                'username': current_user.username,
                'email': current_user.email,
                'is_admin': current_user.is_admin
            }
        })
    else:
        return jsonify({'authenticated': False})
