from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from functools import wraps
import logging
from datetime import datetime, timedelta

from models.user_activity import UserActivity
from models.api_log import APILog
from models.user import User
from models.model_usage import ModelUsage
from models.daily_stats import DailyStats
from models.thread import Thread
from models.room import Room
from utils.stats_updater import update_statistics, get_top_users

admin_statistics = Blueprint('admin_statistics', __name__, url_prefix='/api/admin/statistics')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({'error': 'Admin privileges required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# Service usage endpoint has been removed

# Model usage endpoint has been removed

@admin_statistics.route('/user-activity', methods=['GET'])
@login_required
def get_user_activity():
    """
    Get user activity statistics
    """
    try:
        # Get days parameter (default: 7 days)
        days = int(request.args.get('days', 7))

        # Generate last N days for the timeline
        today = datetime.now()
        date_labels = [(today - timedelta(days=i)).strftime('%a') for i in range(days-1, -1, -1)]

        # Get active users count - actual data
        # First try UserActivity model
        active_users_count = UserActivity.get_all_active_users(minutes=15).count()

        # If no active users found, try using API logs as a fallback
        if active_users_count == 0:
            # Get unique users active in the last 15 minutes from API logs
            cutoff_time = today - timedelta(minutes=15)
            pipeline = [
                {'$match': {
                    'timestamp': {'$gte': cutoff_time}
                }},
                {'$group': {
                    '_id': '$user'
                }},
                {'$count': 'unique_users'}
            ]

            result = list(APILog.objects.aggregate(pipeline))
            active_users_count = result[0]['unique_users'] if result else 0

        # Get total messages from API logs - actual data
        total_messages = APILog.objects(
            action__in=['thread_interaction', 'live_interaction', 'friend_chat_message'],
            timestamp__gte=today - timedelta(days=1)
        ).count()

        # Set a default response time since we don't track this yet
        avg_response_time = 'N/A'

        # Generate activity data for each day using unique users per day
        activity_values = []

        for i in range(days):
            day_date = today - timedelta(days=days-1-i)
            day_start = datetime(day_date.year, day_date.month, day_date.day)
            day_end = day_start + timedelta(days=1)

            # Get unique users who were active on this day
            # Use aggregation pipeline to count unique users
            pipeline = [
                {'$match': {
                    'timestamp': {'$gte': day_start, '$lt': day_end}
                }},
                {'$group': {
                    '_id': '$user',
                    'count': {'$sum': 1}
                }},
                {'$count': 'unique_users'}
            ]

            result = list(APILog.objects.aggregate(pipeline))
            unique_users = result[0]['unique_users'] if result else 0

            activity_values.append(unique_users)

        # Get total users count
        total_users = User.objects.count()

        return jsonify({
            'timeline': {
                'labels': date_labels,
                'values': activity_values
            },
            'metrics': {
                'activeUsers': active_users_count,
                'totalMessages': total_messages,
                'avgResponseTime': avg_response_time,
                'totalUsers': total_users
            }
        })

    except Exception as e:
        logger.error(f"Error getting user activity statistics: {str(e)}")
        return jsonify({
            'timeline': {
                'labels': [],
                'values': []
            },
            'metrics': {
                'activeUsers': 0,
                'totalMessages': 0,
                'avgResponseTime': '0ms',
                'totalUsers': 0
            }
        }), 500

@admin_statistics.route('/service-model-usage', methods=['GET'])
@login_required
def get_service_model_usage():
    """
    Get model usage statistics by service over time with different period options
    """
    try:
        # Get parameters
        days = int(request.args.get('days', 30))
        period = request.args.get('period', 'daily')  # daily, weekly, monthly, yearly

        today = datetime.now()
        date_labels = []

        # Configure time periods based on selected period
        if period == 'daily':
            # For daily view, show last N days
            points = min(days, 30)  # Limit to 30 days for daily view

            # Generate labels for each day
            date_labels = [(today - timedelta(days=i)).strftime('%b %d') for i in range(points-1, -1, -1)]

            # Get model usage data for chat service
            chat_model_usage = {}
            live_model_usage = {}

            for i in range(points):
                day_date = today - timedelta(days=points-1-i)
                day_start = datetime(day_date.year, day_date.month, day_date.day)
                day_end = day_start + timedelta(days=1)

                # Process chat service
                pipeline = [
                    {'$match': {
                        'service': 'chat',
                        'timestamp': {'$gte': day_start, '$lt': day_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                day_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in day_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in chat_model_usage:
                            chat_model_usage[model_name] = [0] * points
                        chat_model_usage[model_name][i] = model['count']

                # Process live service
                pipeline = [
                    {'$match': {
                        'service': 'live',
                        'timestamp': {'$gte': day_start, '$lt': day_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                day_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in day_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in live_model_usage:
                            live_model_usage[model_name] = [0] * points
                        live_model_usage[model_name][i] = model['count']

        elif period == 'weekly':
            # For weekly view, show last N weeks
            points = min(days // 7, 12)  # Limit to 12 weeks
            if points < 2:
                points = 2  # Ensure at least 2 weeks

            # Generate labels for each week
            for i in range(points-1, -1, -1):
                week_end = today - timedelta(days=i*7)
                week_start = week_end - timedelta(days=6)
                date_labels.append(f"{week_start.strftime('%b %d')} - {week_end.strftime('%b %d')}")

            # Get model usage data for chat and live services
            chat_model_usage = {}
            live_model_usage = {}

            for i in range(points):
                week_end = today - timedelta(days=i*7)
                week_start = week_end - timedelta(days=6)

                # Process chat service
                pipeline = [
                    {'$match': {
                        'service': 'chat',
                        'timestamp': {'$gte': week_start, '$lt': week_end + timedelta(days=1)}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                week_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in week_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in chat_model_usage:
                            chat_model_usage[model_name] = [0] * points
                        chat_model_usage[model_name][points-1-i] = model['count']

                # Process live service
                pipeline = [
                    {'$match': {
                        'service': 'live',
                        'timestamp': {'$gte': week_start, '$lt': week_end + timedelta(days=1)}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                week_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in week_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in live_model_usage:
                            live_model_usage[model_name] = [0] * points
                        live_model_usage[model_name][points-1-i] = model['count']

        elif period == 'monthly':
            # For monthly view, show last N months
            points = min(days // 30, 12)  # Limit to 12 months
            if points < 2:
                points = 2  # Ensure at least 2 months

            # Generate labels for each month
            for i in range(points):
                month_date = today - timedelta(days=30*i)
                date_labels.insert(0, month_date.strftime('%b %Y'))

            # Get model usage data for chat and live services
            chat_model_usage = {}
            live_model_usage = {}

            for i in range(points):
                month_end = today - timedelta(days=30*i)
                month_start = month_end - timedelta(days=30)

                # Process chat service
                pipeline = [
                    {'$match': {
                        'service': 'chat',
                        'timestamp': {'$gte': month_start, '$lt': month_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                month_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in month_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in chat_model_usage:
                            chat_model_usage[model_name] = [0] * points
                        chat_model_usage[model_name][points-1-i] = model['count']

                # Process live service
                pipeline = [
                    {'$match': {
                        'service': 'live',
                        'timestamp': {'$gte': month_start, '$lt': month_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                month_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in month_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in live_model_usage:
                            live_model_usage[model_name] = [0] * points
                        live_model_usage[model_name][points-1-i] = model['count']

        elif period == 'yearly':
            # For yearly view, show data by quarters for the last year
            points = 4  # 4 quarters

            # Generate labels for each quarter
            current_quarter = (today.month - 1) // 3
            year = today.year

            for i in range(points):
                quarter = (current_quarter - i) % 4 + 1
                quarter_year = year if i <= current_quarter else year - 1
                date_labels.insert(0, f"Q{quarter} {quarter_year}")

            # Get model usage data for chat and live services
            chat_model_usage = {}
            live_model_usage = {}

            for i in range(points):
                quarter = (current_quarter - i) % 4
                quarter_year = year if i <= current_quarter else year - 1

                # Calculate quarter start and end dates
                quarter_start = datetime(quarter_year, quarter * 3 + 1, 1)
                if quarter == 3:  # Q4
                    quarter_end = datetime(quarter_year + 1, 1, 1)
                else:
                    quarter_end = datetime(quarter_year, (quarter + 1) * 3 + 1, 1)

                # Process chat service
                pipeline = [
                    {'$match': {
                        'service': 'chat',
                        'timestamp': {'$gte': quarter_start, '$lt': quarter_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                quarter_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in quarter_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in chat_model_usage:
                            chat_model_usage[model_name] = [0] * points
                        chat_model_usage[model_name][points-1-i] = model['count']

                # Process live service
                pipeline = [
                    {'$match': {
                        'service': 'live',
                        'timestamp': {'$gte': quarter_start, '$lt': quarter_end}
                    }},
                    {'$group': {
                        '_id': '$model_name',
                        'count': {'$sum': 1}
                    }}
                ]

                quarter_models = list(ModelUsage.objects.aggregate(pipeline))

                for model in quarter_models:
                    model_name = model['_id']
                    if model_name and model_name not in ['', 'None', None]:
                        if model_name not in live_model_usage:
                            live_model_usage[model_name] = [0] * points
                        live_model_usage[model_name][points-1-i] = model['count']

        return jsonify({
            'period': period,
            'chat': {
                'days': date_labels,
                'models': chat_model_usage
            },
            'live': {
                'days': date_labels,
                'models': live_model_usage
            }
        })

    except Exception as e:
        logger.error(f"Error getting service model usage statistics: {str(e)}")
        return jsonify({
            'period': 'daily',
            'chat': {
                'days': [],
                'models': {}
            },
            'live': {
                'days': [],
                'models': {}
            }
        }), 500

@admin_statistics.route('/user-model-usage', methods=['GET'])
@login_required
def get_user_model_usage():
    """
    Get model usage statistics by user, filtered by AI model usage only
    """
    try:
        # Get parameters
        days = int(request.args.get('days', 30))
        limit = int(request.args.get('limit', 10))
        service = request.args.get('service', 'all')
        
        # Get top users by AI model usage
        top_users = get_top_users(days=days, limit=limit, service=service)

        return jsonify(top_users)

    except Exception as e:
        logger.error(f"Error getting user model usage statistics: {str(e)}")
        return jsonify([]), 500

@admin_statistics.route('/update-daily-stats', methods=['POST'])
@login_required
@admin_required
def update_daily_stats_endpoint():
    """
    Update daily statistics (admin only)
    This endpoint can be called manually or via a scheduled task
    """
    try:
        # Call the stats updater
        stats_data = update_statistics()

        if stats_data:
            return jsonify({
                'success': True,
                'message': 'Statistics updated successfully',
                'data': stats_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to update statistics'
            }), 500

    except Exception as e:
        logger.error(f"Error updating daily statistics: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error updating statistics: {str(e)}'
        }), 500

# Reset engagement data endpoint has been removed

@admin_statistics.route('/assistant-messages', methods=['GET'])
@login_required
def get_assistant_messages_count():
    """
    Get the count of assistant messages in threads and live sessions
    """
    try:
        # Get time range parameter (default: all time)
        days = int(request.args.get('days', 0))

        # Set time filter if days parameter is provided
        time_filter = {}
        if days > 0:
            cutoff_time = datetime.now() - timedelta(days=days)
            time_filter = {'updated_at': {'$gte': cutoff_time}}
        
        # Use MongoDB aggregation to count assistant messages in threads
        thread_pipeline = [
            {'$match': time_filter},
            {'$project': {
                'assistant_messages': {
                    '$filter': {
                        'input': '$messages',
                        'as': 'message',
                        'cond': {'$eq': ['$$message.role', 'assistant']}
                    }
                }
            }},
            {'$project': {
                'count': {'$size': '$assistant_messages'}
            }},
            {'$group': {
                '_id': None,
                'total': {'$sum': '$count'}
            }}
        ]
        
        thread_result = list(Thread.objects.aggregate(thread_pipeline))
        thread_assistant_count = thread_result[0]['total'] if thread_result else 0
        
        # Use MongoDB aggregation to count assistant messages in live rooms
        room_pipeline = [
            {'$match': time_filter},
            {'$project': {
                'assistant_messages': {
                    '$filter': {
                        'input': '$messages',
                        'as': 'message',
                        'cond': {'$eq': ['$$message.role', 'assistant']}
                    }
                }
            }},
            {'$project': {
                'count': {'$size': '$assistant_messages'}
            }},
            {'$group': {
                '_id': None,
                'total': {'$sum': '$count'}
            }}
        ]
        
        room_result = list(Room.objects.aggregate(room_pipeline))
        live_assistant_count = room_result[0]['total'] if room_result else 0
        
        # Total assistant messages
        total_assistant_count = thread_assistant_count + live_assistant_count
        
        return jsonify({
            'total': total_assistant_count,
            'thread': thread_assistant_count,
            'live': live_assistant_count
        })
        
    except Exception as e:
        logger.error(f"Error getting assistant messages count: {str(e)}")
        return jsonify({
            'total': 0,
            'thread': 0,
            'live': 0,
            'error': str(e)
        }), 500

@admin_statistics.route('/user-countries', methods=['GET'])
@login_required
@admin_required
def get_user_countries():
    """
    Get user distribution by country
    Returns country codes and counts without usernames
    """
    try:
        # Get all users with country codes
        users = User.objects()

        # Count users by country
        country_counts = {}

        for user in users:
            country = user.country_code
            if not country or country.strip() == "":
                country = "unknown"  # Default for users without country

            if country in country_counts:
                country_counts[country] += 1
            else:
                country_counts[country] = 1

        # Format data for the map
        countries_data = []
        for country_code, count in country_counts.items():
            countries_data.append({
                'code': country_code,
                'value': count
            })

        return jsonify({
            'success': True,
            'data': countries_data
        })

    except Exception as e:
        logger.error(f"Error getting user country statistics: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error getting user country statistics: {str(e)}',
            'data': []
        }), 500
