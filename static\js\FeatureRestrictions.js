/**
 * Feature Restrictions Manager
 * Handles the UI and API interactions for feature restrictions
 */
class FeatureRestrictions {
    constructor() {
        // Add Restriction Modal Elements
        this.modal = document.getElementById('featureRestrictionModal');
        this.searchInput = document.getElementById('featureRestrictionUserSearch');
        this.searchResults = document.getElementById('featureRestrictionSearchResults');
        this.selectedUserInfo = document.getElementById('selectedUserInfo');
        this.selectedUserAvatar = document.getElementById('selectedUserAvatar');
        this.selectedUserName = document.getElementById('selectedUserName');
        this.selectedUserEmail = document.getElementById('selectedUserEmail');
        this.restrictionSettings = document.getElementById('restrictionSettings');
        this.maxThreadsInput = document.getElementById('maxThreads');
        this.maxConversationSetsInput = document.getElementById('maxConversationSets');
        this.maxLiveRoomsInput = document.getElementById('maxLiveRooms');
        this.maxLiveConversationSetsInput = document.getElementById('maxLiveConversationSets');
        this.saveButton = document.getElementById('saveFeatureRestriction');
        this.cancelButton = document.getElementById('cancelFeatureRestriction');
        this.closeButton = document.getElementById('closeFeatureRestrictionModal');
        this.addButton = document.getElementById('addFeatureRestriction');
        this.container = document.getElementById('featureRestrictionsContainer');

        // List Modal Elements
        this.listModal = document.getElementById('featureRestrictionListModal');
        this.listContainer = document.getElementById('featureRestrictionListContainer');
        this.closeListButton = document.getElementById('closeFeatureRestrictionListButton');
        this.closeListModalButton = document.getElementById('closeFeatureRestrictionListModal');
        this.viewAllButton = document.getElementById('viewAllFeatureRestrictions');

        this.selectedUser = null;
        this.searchTimeout = null;

        // Add custom scrollbar styles
        this.addScrollbarStyles();

        this.init();
    }

    /**
     * Add custom scrollbar styles for the containers
     */
    addScrollbarStyles() {
        // Create a style element
        const style = document.createElement('style');
        style.textContent = `
            #featureRestrictionsContainer::-webkit-scrollbar,
            #featureRestrictionListContainer::-webkit-scrollbar {
                width: 6px;
            }

            #featureRestrictionsContainer::-webkit-scrollbar-track,
            #featureRestrictionListContainer::-webkit-scrollbar-track {
                background: rgba(30, 41, 59, 0.5);
                border-radius: 3px;
            }

            #featureRestrictionsContainer::-webkit-scrollbar-thumb,
            #featureRestrictionListContainer::-webkit-scrollbar-thumb {
                background: rgba(100, 116, 139, 0.5);
                border-radius: 3px;
            }

            #featureRestrictionsContainer::-webkit-scrollbar-thumb:hover,
            #featureRestrictionListContainer::-webkit-scrollbar-thumb:hover {
                background: rgba(100, 116, 139, 0.8);
            }
        `;

        // Append the style element to the head
        document.head.appendChild(style);
    }

    /**
     * Initialize the feature restrictions manager
     */
    async init() {
        // Initialize admin check if it doesn't exist
        if (!window.adminCheck) {
            console.log('AdminCheck not available in FeatureRestrictions, creating instance');
            window.adminCheck = new AdminCheck();
        }

        // Wait for admin check to initialize
        await window.adminCheck.init();

        // Add event listeners for add restriction modal
        this.addButton.addEventListener('click', () => {
            // Check if user is an admin before showing modal
            if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
                alert('Admin access required to manage feature restrictions');
                return;
            }
            this.showModal();
        });

        this.closeButton.addEventListener('click', () => this.hideModal());
        this.cancelButton.addEventListener('click', () => this.hideModal());
        this.searchInput.addEventListener('input', () => this.handleSearch());
        this.saveButton.addEventListener('click', () => this.saveRestriction());

        // Add event listeners for list modal
        if (this.viewAllButton) {
            this.viewAllButton.addEventListener('click', () => {
                // Check if user is an admin before showing list modal
                if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
                    alert('Admin access required to view feature restrictions');
                    return;
                }
                this.showListModal();
            });
        }

        if (this.closeListButton) {
            this.closeListButton.addEventListener('click', () => this.hideListModal());
        }
        if (this.closeListModalButton) {
            this.closeListModalButton.addEventListener('click', () => this.hideListModal());
        }

        // Load existing restrictions
        this.loadRestrictions();
    }

    /**
     * Show the feature restriction modal
     */
    showModal() {
        this.modal.classList.remove('hidden');
        this.searchInput.value = '';
        this.searchResults.classList.add('hidden');
        this.selectedUserInfo.classList.add('hidden');
        this.restrictionSettings.classList.add('hidden');
        this.saveButton.disabled = true;
        this.selectedUser = null;
    }

    /**
     * Hide the feature restriction modal
     */
    hideModal() {
        this.modal.classList.add('hidden');
    }

    /**
     * Handle user search input
     */
    handleSearch() {
        const query = this.searchInput.value.trim();

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            this.searchResults.innerHTML = `
                <div class="p-3 text-center text-amber-400 text-sm">
                    Admin access required to search users
                </div>
            `;
            this.searchResults.classList.remove('hidden');
            return;
        }

        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Hide search results if query is too short
        if (query.length < 3) {
            this.searchResults.classList.add('hidden');
            return;
        }

        // Set a timeout to avoid too many requests
        this.searchTimeout = setTimeout(() => {
            // Call the API to search for users
            fetch(`/api/admin/search-users?query=${encodeURIComponent(query)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(users => {
                    // Clear previous results
                    this.searchResults.innerHTML = '';

                    if (users.length === 0) {
                        this.searchResults.innerHTML = `
                            <div class="p-3 text-center text-slate-400 text-sm">
                                No users found matching "${query}"
                            </div>
                        `;
                    } else {
                        // Add each user to the results
                        users.forEach(user => {
                            const userItem = document.createElement('div');
                            userItem.className = 'p-2 hover:bg-slate-600 cursor-pointer flex items-center space-x-2';
                            userItem.innerHTML = `
                                <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center overflow-hidden">
                                    ${user.profile_picture ?
                                        `<img src="${user.profile_picture}" alt="${user.username}" class="w-full h-full object-cover">` :
                                        `<i data-lucide="user" class="h-4 w-4 text-slate-300"></i>`
                                    }
                                </div>
                                <div>
                                    <div class="text-sm text-slate-200">${user.username}</div>
                                    <div class="text-xs text-slate-400">${user.email}</div>
                                </div>
                            `;

                            // Add click event to select user
                            userItem.addEventListener('click', () => {
                                this.selectUser(user);
                            });

                            this.searchResults.appendChild(userItem);
                        });

                        // Initialize Lucide icons
                        if (window.lucide) {
                            lucide.createIcons({
                                attrs: {
                                    class: ["h-4", "w-4"]
                                },
                                elements: [this.searchResults]
                            });
                        }
                    }

                    // Show search results
                    this.searchResults.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error searching for users:', error);
                    this.searchResults.innerHTML = `
                        <div class="p-3 text-center text-red-400 text-sm">
                            Error searching for users
                        </div>
                    `;
                    this.searchResults.classList.remove('hidden');
                });
        }, 300);
    }

    /**
     * Select a user from search results
     * @param {Object} user User object
     */
    selectUser(user) {
        this.selectedUser = user;

        // Update selected user info
        if (user.profile_picture) {
            this.selectedUserAvatar.innerHTML = `<img src="${user.profile_picture}" alt="${user.username}" class="w-full h-full object-cover">`;
        } else {
            this.selectedUserAvatar.innerHTML = `<i data-lucide="user" class="h-5 w-5 text-slate-300"></i>`;
        }

        this.selectedUserName.textContent = user.username;
        this.selectedUserEmail.textContent = user.email;

        // Show selected user info and restriction settings
        this.selectedUserInfo.classList.remove('hidden');
        this.restrictionSettings.classList.remove('hidden');
        this.searchResults.classList.add('hidden');

        // Enable save button
        this.saveButton.disabled = false;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-5", "w-5"]
                },
                elements: [this.selectedUserAvatar]
            });
        }

        // Check if user already has restrictions
        this.checkExistingRestrictions(user.id);
    }

    /**
     * Check if user already has restrictions
     * @param {string} userId User ID
     */
    checkExistingRestrictions(userId) {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping checkExistingRestrictions - user is not an admin');
            // Set default values
            this.maxThreadsInput.value = 5;
            this.maxConversationSetsInput.value = 5;
            this.maxLiveRoomsInput.value = 7;
            this.maxLiveConversationSetsInput.value = 15;
            return;
        }

        fetch('/api/admin/feature-restrictions')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                const userRestriction = restrictions.find(r => r.user_id === userId);

                if (userRestriction) {
                    // Set existing values
                    this.maxThreadsInput.value = userRestriction.max_threads;
                    this.maxConversationSetsInput.value = userRestriction.max_conversation_sets;
                    this.maxLiveRoomsInput.value = userRestriction.max_live_rooms || 7;
                    this.maxLiveConversationSetsInput.value = userRestriction.max_live_conversation_sets || 15;
                } else {
                    // Set default values
                    this.maxThreadsInput.value = 5;
                    this.maxConversationSetsInput.value = 5;
                    this.maxLiveRoomsInput.value = 7;
                    this.maxLiveConversationSetsInput.value = 15;
                }
            })
            .catch(error => {
                console.error('Error checking existing restrictions:', error);
                // Set default values
                this.maxThreadsInput.value = 5;
                this.maxConversationSetsInput.value = 5;
                this.maxLiveRoomsInput.value = 7;
                this.maxLiveConversationSetsInput.value = 15;
            });
    }

    /**
     * Save feature restriction
     */
    saveRestriction() {
        if (!this.selectedUser) {
            return;
        }

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            alert('Admin access required to save feature restrictions');
            return;
        }

        const maxThreads = parseInt(this.maxThreadsInput.value);
        const maxConversationSets = parseInt(this.maxConversationSetsInput.value);
        const maxLiveRooms = parseInt(this.maxLiveRoomsInput.value);
        const maxLiveConversationSets = parseInt(this.maxLiveConversationSetsInput.value);

        if (isNaN(maxThreads) || maxThreads < 1 || isNaN(maxConversationSets) || maxConversationSets < 1 ||
            isNaN(maxLiveRooms) || maxLiveRooms < 1 || isNaN(maxLiveConversationSets) || maxLiveConversationSets < 1) {
            alert('Please enter valid values for all fields');
            return;
        }

        // Show loading state
        const originalButtonText = this.saveButton.textContent;
        this.saveButton.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Saving...`;
        this.saveButton.disabled = true;

        // Initialize icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-4", "w-4"]
                },
                elements: [this.saveButton]
            });
        }

        // Call the API to add feature restriction
        fetch('/api/admin/feature-restrictions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: this.selectedUser.id,
                max_threads: maxThreads,
                max_conversation_sets: maxConversationSets,
                max_live_rooms: maxLiveRooms,
                max_live_conversation_sets: maxLiveConversationSets
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Hide modal
                this.hideModal();

                // Reload restrictions
                this.loadRestrictions();

                // If the list modal is visible, reload its content too
                if (this.listModal && !this.listModal.classList.contains('hidden')) {
                    this.loadRestrictionsForList();
                }

                // Show success message
                alert(`Feature restrictions set for user ${this.selectedUser.username}`);
            })
            .catch(error => {
                console.error('Error saving feature restriction:', error);
                alert('Error saving feature restriction');

                // Reset button
                this.saveButton.textContent = originalButtonText;
                this.saveButton.disabled = false;
            });
    }

    /**
     * Show the feature restriction list modal
     */
    showListModal() {
        if (this.listModal) {
            this.listModal.classList.remove('hidden');
            this.loadRestrictionsForList();
        }
    }

    /**
     * Hide the feature restriction list modal
     */
    hideListModal() {
        if (this.listModal) {
            this.listModal.classList.add('hidden');
        }
    }

    /**
     * Load restrictions for the list modal
     */
    loadRestrictionsForList() {
        if (!this.listContainer) return;

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            this.listContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center text-amber-400">
                        <i data-lucide="shield-alert" class="h-5 w-5 mr-2"></i>
                        <span>Admin access required to view feature restrictions</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-5", "w-5"]
                    },
                    elements: [this.listContainer]
                });
            }
            return;
        }

        // Show loading state
        this.listContainer.innerHTML = `
            <div class="flex justify-center items-center py-8">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400">Loading restricted users...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-5", "w-5"]
                },
                elements: [this.listContainer]
            });
        }

        fetch('/api/admin/feature-restrictions')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                if (restrictions.length === 0) {
                    this.listContainer.innerHTML = `
                        <div class="text-center py-8 text-slate-400">
                            No feature restrictions found
                        </div>
                    `;
                    return;
                }

                // Clear container
                this.listContainer.innerHTML = '';

                // Add each restriction to the container
                restrictions.forEach(restriction => {
                    const restrictionItem = document.createElement('div');
                    restrictionItem.className = 'bg-slate-800/50 p-3 rounded-lg border border-slate-700/50';
                    restrictionItem.innerHTML = `
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center overflow-hidden">
                                    ${restriction.profile_picture ?
                                        `<img src="${restriction.profile_picture}" alt="${restriction.username}" class="w-full h-full object-cover">` :
                                        `<i data-lucide="user" class="h-5 w-5 text-slate-300"></i>`
                                    }
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-200">${restriction.username}</div>
                                    <div class="text-xs text-slate-400">${restriction.email}</div>
                                </div>
                            </div>
                            <button class="text-xs bg-slate-700 hover:bg-red-900/50 text-slate-300 hover:text-red-300 px-2 py-0.5 rounded" data-user-id="${restriction.user_id}">Remove</button>
                        </div>
                        <div class="grid grid-cols-2 gap-2 mt-3">
                            <div class="bg-slate-700/30 p-2 rounded">
                                <div class="text-xs text-slate-400">Max Threads</div>
                                <div class="text-sm text-slate-200">${restriction.max_threads}</div>
                            </div>
                            <div class="bg-slate-700/30 p-2 rounded">
                                <div class="text-xs text-slate-400">Max Conversation Sets</div>
                                <div class="text-sm text-slate-200">${restriction.max_conversation_sets}</div>
                            </div>
                            <div class="bg-slate-700/30 p-2 rounded">
                                <div class="text-xs text-slate-400">Max Live Rooms</div>
                                <div class="text-sm text-slate-200">${restriction.max_live_rooms || 7}</div>
                            </div>
                            <div class="bg-slate-700/30 p-2 rounded">
                                <div class="text-xs text-slate-400">Max Live Conv. Sets</div>
                                <div class="text-sm text-slate-200">${restriction.max_live_conversation_sets || 15}</div>
                            </div>
                        </div>
                    `;

                    // Add delete button event
                    const deleteButton = restrictionItem.querySelector('button');
                    deleteButton.addEventListener('click', () => {
                        this.deleteRestriction(restriction.user_id, restriction.username);
                        this.loadRestrictionsForList(); // Reload the list after deletion
                    });

                    this.listContainer.appendChild(restrictionItem);
                });

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-5", "w-5"]
                        },
                        elements: [this.listContainer]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading feature restrictions for list:', error);
                this.listContainer.innerHTML = `
                    <div class="text-center py-8 text-red-400">
                        Error loading feature restrictions
                    </div>
                `;
            });
    }

    /**
     * Load existing feature restrictions
     */
    loadRestrictions() {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping loadRestrictions - user is not an admin');

            // Clear container except for the add button
            const addButton = this.addButton;
            this.container.innerHTML = '';

            // Add a message for non-admin users
            const messageDiv = document.createElement('div');
            messageDiv.className = 'text-center text-amber-400 text-xs py-2';
            messageDiv.innerHTML = 'Admin access required to view feature restrictions';
            this.container.appendChild(messageDiv);

            // Add the button at the end
            this.container.appendChild(addButton);
            return;
        }

        fetch('/api/admin/feature-restrictions')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                // Clear container except for the add button
                const addButton = this.addButton;
                this.container.innerHTML = '';

                // Set container to have fixed height and scrolling
                // Only apply these styles if we have more than 2 restrictions
                if (restrictions.length > 2) {
                    this.container.style.maxHeight = '160px'; // Height for approximately 2 items
                    this.container.style.overflowY = 'auto';
                    this.container.style.paddingRight = '5px'; // Add some padding for the scrollbar
                } else {
                    // Reset styles if we have 2 or fewer restrictions
                    this.container.style.maxHeight = '';
                    this.container.style.overflowY = '';
                    this.container.style.paddingRight = '';
                }

                // Add each restriction to the container
                restrictions.forEach(restriction => {
                    const restrictionItem = document.createElement('div');
                    restrictionItem.className = 'flex items-center justify-between bg-slate-800/50 p-2 rounded mb-2';
                    restrictionItem.innerHTML = `
                        <div>
                            <div class="text-xs text-slate-300">${restriction.username}</div>
                            <div class="text-xs text-slate-400">${restriction.email}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-slate-300">Threads: ${restriction.max_threads}</div>
                            <div class="text-xs text-slate-400">Conv. Sets: ${restriction.max_conversation_sets}</div>
                            <div class="text-xs text-slate-300">Live Rooms: ${restriction.max_live_rooms || 7}</div>
                            <div class="text-xs text-slate-400">Live Conv. Sets: ${restriction.max_live_conversation_sets || 15}</div>
                        </div>
                        <button class="text-xs bg-slate-700 hover:bg-red-900/50 text-slate-300 hover:text-red-300 px-2 py-0.5 rounded ml-2" data-user-id="${restriction.user_id}">×</button>
                    `;

                    // Add delete button event
                    const deleteButton = restrictionItem.querySelector('button');
                    deleteButton.addEventListener('click', () => {
                        this.deleteRestriction(restriction.user_id, restriction.username);
                    });

                    // Insert before add button
                    this.container.appendChild(restrictionItem);
                });

                // Add the button at the end
                this.container.appendChild(addButton);

                // Add a small margin to the button to separate it from the list
                addButton.style.marginTop = restrictions.length > 0 ? '8px' : '0';
            })
            .catch(error => {
                console.error('Error loading feature restrictions:', error);
            });
    }

    /**
     * Delete feature restriction
     * @param {string} userId User ID
     * @param {string} username Username
     */
    deleteRestriction(userId, username) {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            alert('Admin access required to delete feature restrictions');
            return;
        }

        if (!confirm(`Are you sure you want to remove feature restrictions for ${username}?`)) {
            return;
        }

        fetch(`/api/admin/feature-restrictions/${userId}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Reload restrictions in both views
                this.loadRestrictions();

                // If the list modal is visible, reload its content too
                if (this.listModal && !this.listModal.classList.contains('hidden')) {
                    this.loadRestrictionsForList();
                }

                // Show success message
                alert(`Feature restrictions removed for user ${username}`);
            })
            .catch(error => {
                console.error('Error deleting feature restriction:', error);
                alert('Error deleting feature restriction');
            });
    }
}

// Initialize feature restrictions manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.featureRestrictions = new FeatureRestrictions();
});
