/* Access Control Card Styles */
.restriction-card.access-control {
    border-left: 3px solid rgba(239, 68, 68, 0.5); /* Red border for access control */
}

.restriction-card.access-control .restriction-card-title i {
    filter: drop-shadow(0 0 3px rgba(239, 68, 68, 0.3));
}

/* User item styles */
#accessControlContainer .user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: rgba(30, 41, 59, 0.5);
    border-radius: 0.375rem;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

#accessControlContainer .user-item:hover {
    background-color: rgba(30, 41, 59, 0.7);
}

/* Service tag styles */
.service-tag {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    background-color: rgba(30, 41, 59, 0.7);
    color: rgba(203, 213, 225, 1);
}

/* Service checkbox styles */
.service-checkbox {
    cursor: pointer;
}

.service-checkbox:checked {
    background-color: rgba(6, 182, 212, 1);
    border-color: rgba(6, 182, 212, 1);
}

/* Service option card styles */
.service-option {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.service-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(6, 182, 212, 0) 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-option:hover::before {
    opacity: 1;
}

.service-option.border-cyan-500\/70 {
    box-shadow: 0 0 0 1px rgba(6, 182, 212, 0.7), 0 0 8px rgba(6, 182, 212, 0.3);
}

/* Step indicator styles */
#step1Indicator, #step2Indicator, #step3Indicator {
    position: relative;
    z-index: 10;
}

/* Modal transition styles */
#step1Content, #step2Content, #step3Content {
    transition: all 0.3s ease;
    width: 100%;
}

#step1Content.hidden, #step2Content.hidden, #step3Content.hidden {
    display: none !important;
    opacity: 0;
    height: 0;
    overflow: hidden;
}

#step1Content:not(.hidden), #step2Content:not(.hidden), #step3Content:not(.hidden) {
    display: block !important;
    opacity: 1;
    height: auto;
    overflow: visible;
}

/* List modal styles */
#accessControlListContainer .storage-progress {
    height: 0.5rem;
    background-color: rgba(30, 41, 59, 0.5);
    border-radius: 0.25rem;
    overflow: hidden;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
}

#accessControlListContainer .storage-progress-bar {
    height: 100%;
    background: linear-gradient(to right, rgba(6, 182, 212, 0.7), rgba(37, 99, 235, 0.7));
    border-radius: 0.25rem;
}

/* Button styles */
.remove-restriction-btn:hover {
    background-color: rgba(185, 28, 28, 0.3);
    color: rgba(252, 165, 165, 1);
}

/* Toast notification styles */
.fixed.bottom-4.right-4 {
    animation: slideIn 0.3s ease forwards;
    transform: translateX(100%);
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

.opacity-0 {
    opacity: 0;
}
