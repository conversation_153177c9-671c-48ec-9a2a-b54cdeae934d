<!-- Admin Updates Section -->
<div class="admin-updates-section">
    <div class="admin-updates-header">
        <h3 class="admin-updates-title">
            <i data-lucide="megaphone" class="h-5 w-5"></i>
            Service Updates
        </h3>
        <div class="admin-updates-actions">
            <button id="publishUpdateBtn" class="btn-primary">
                <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                Publish Update
            </button>
        </div>
    </div>

    <div class="admin-updates-content">
        <div class="admin-updates-list" id="adminUpdatesList">
            <!-- Updates will be loaded dynamically -->
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading updates...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Publish Update Modal -->
<div id="publishUpdateModal" class="modal hidden">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">
                <i data-lucide="megaphone" class="h-5 w-5"></i>
                Publish Service Update
            </h3>
            <button class="modal-close">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="publishUpdateForm">
                <div class="form-group required">
                    <label for="updateServiceId">
                        <i data-lucide="layers" class="h-4 w-4"></i>
                        Service Category
                    </label>
                    <select id="updateServiceId" name="service_id" class="form-control" required>
                        <option value="">Select a service</option>
                        <!-- Service categories will be loaded dynamically -->
                    </select>
                    <div class="form-help">
                        <i data-lucide="info" class="h-4 w-4"></i>
                        Select the service this update applies to
                    </div>
                </div>
                <div class="form-group required">
                    <label for="updateTitle">
                        <i data-lucide="type" class="h-4 w-4"></i>
                        Title
                    </label>
                    <input type="text" id="updateTitle" name="title" class="form-control" placeholder="Enter a concise update title" required>
                    <div class="form-help">
                        <i data-lucide="info" class="h-4 w-4"></i>
                        Keep titles short and descriptive
                    </div>
                </div>
                <div class="form-group required">
                    <label for="updateDescription">
                        <i data-lucide="file-text" class="h-4 w-4"></i>
                        Description
                    </label>
                    <textarea id="updateDescription" name="description" class="form-control" placeholder="Provide detailed information about the update" rows="4" required></textarea>
                    <div class="form-help">
                        <i data-lucide="info" class="h-4 w-4"></i>
                        Include what changed and why it matters to users
                    </div>
                </div>
                <div class="form-group">
                    <label for="updateVersion">
                        <i data-lucide="tag" class="h-4 w-4"></i>
                        Version (Optional)
                    </label>
                    <input type="text" id="updateVersion" name="version" class="form-control" placeholder="e.g., 1.2.0">
                    <div class="form-help">
                        <i data-lucide="info" class="h-4 w-4"></i>
                        Use semantic versioning (major.minor.patch) if applicable
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary modal-cancel">
                <i data-lucide="x" class="h-4 w-4"></i>
                Cancel
            </button>
            <button id="publishUpdateSubmit" class="btn-primary">
                <i data-lucide="send" class="h-4 w-4"></i>
                Publish Update
            </button>
        </div>
    </div>
</div>

<!-- Delete Update Confirmation Modal -->
<div id="deleteUpdateModal" class="modal hidden">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">
                <i data-lucide="trash-2" class="h-5 w-5"></i>
                Delete Update
            </h3>
            <button class="modal-close">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="bg-red-900/20 border border-red-900/30 rounded-lg p-4 mb-4 flex items-start">
                <i data-lucide="alert-triangle" class="h-5 w-5 text-red-400 mr-3 mt-0.5"></i>
                <div>
                    <h4 class="text-red-300 font-medium mb-1">Warning: Permanent Action</h4>
                    <p class="text-red-200/80 text-sm">Are you sure you want to delete this update? This action cannot be undone and the update will be permanently removed from the system.</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary modal-cancel">
                <i data-lucide="x" class="h-4 w-4"></i>
                Cancel
            </button>
            <button id="deleteUpdateConfirm" class="btn-danger">
                <i data-lucide="trash-2" class="h-4 w-4"></i>
                Delete Permanently
            </button>
        </div>
    </div>
</div>
