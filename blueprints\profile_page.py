from flask import Blueprint, render_template, abort, redirect, url_for, request, jsonify
from flask_login import login_required, current_user
from models.user import User
from models.profile_customization import ProfileCustomization

profile_page_bp = Blueprint('profile_page', __name__, url_prefix='/profile')

@profile_page_bp.route('/<username>')
def view_profile(username):
    """View a user's profile page"""
    # Find the user by username
    user = User.objects(username=username).first()
    if not user:
        # Render a custom user not found page instead of 404
        return render_template('user_not_found.html', username=username)
    
    # Get profile customization
    profile_custom = ProfileCustomization.get_or_create(user)
    
    # Check if profile is private and not the current user
    if not profile_custom.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return render_template('profile_private.html', username=username)
    
    # Render the profile page with customization
    return render_template('profile.html', 
                          user=user, 
                          profile=profile_custom,
                          is_owner=(current_user.is_authenticated and current_user.id == user.id))

# Removed the /edit route to allow it to be handled by the /<username> route
# This allows /profile/edit to show the profile of a user named "edit" instead of redirecting

@profile_page_bp.route('/me/edit')
@login_required
def edit_profile_me():
    """Edit current user's profile with the new URL structure"""
    # Get profile customization
    profile_custom = ProfileCustomization.get_or_create(current_user)
    
    return render_template('profile_edit.html', 
                          user=current_user, 
                          profile=profile_custom)

@profile_page_bp.route('/<username>/edit')
@login_required
def edit_user_profile(username):
    """Handle edit requests for specific usernames"""
    # If the username is the current user's username, proceed to edit
    if username == current_user.username:
        return redirect(url_for('profile_page.edit_profile_me'))
    
    # If the username is "me", redirect to the current user's edit page
    if username.lower() == "me":
        return redirect(url_for('profile_page.edit_profile_me'))
    
    # Check if the requested user exists
    user = User.objects(username=username).first()
    if not user:
        return render_template('user_not_found.html', username=username)
    
    # If the user exists but is not the current user, show the not found page
    # This prevents editing other users' profiles
    return render_template('user_not_found.html', 
                          username=username, 
                          message="You don't have permission to edit this profile.")

@profile_page_bp.route('/preview')
@login_required
def preview_profile():
    """Preview profile with temporary changes"""
    # Get profile customization
    profile_custom = ProfileCustomization.get_or_create(current_user)
    
    # Apply temporary changes from query parameters
    temp_profile = {
        'background_color': request.args.get('bg_color', profile_custom.background_color),
        'text_color': request.args.get('text_color', profile_custom.text_color),
        'accent_color': request.args.get('accent_color', profile_custom.accent_color),
        'profile_container_color': request.args.get('profile_container_color', profile_custom.profile_container_color),
        'profile_header_color': request.args.get('profile_header_color', profile_custom.profile_header_color),
        'profile_about_color': request.args.get('profile_about_color', profile_custom.profile_about_color),
        'profile_content_color': request.args.get('profile_content_color', profile_custom.profile_content_color),
        'profile_friends_color': request.args.get('profile_friends_color', profile_custom.profile_friends_color),
        'layout_type': request.args.get('layout', profile_custom.layout_type),
        'font_family': request.args.get('font', profile_custom.font_family),
        'heading_font': request.args.get('heading_font', profile_custom.heading_font),
    }
    
    return render_template('profile_preview.html', 
                          user=current_user, 
                          profile=profile_custom,
                          temp_profile=temp_profile)