/**
 * NotificationManager.js
 * Manages browser notifications including favicon flashing
 */
class NotificationManager {
    constructor() {
        // Store original favicon
        const faviconElement = document.querySelector('link[rel="icon"]');
        this.originalFavicon = faviconElement ? faviconElement.href : '/static/favicon.ico';

        // Use a different color favicon for notification
        this.alternativeFavicon = '/static/favicon-32x32.png';

        this.isFlashing = false;
        this.flashInterval = null;
        this.flashSpeed = 500; // Flash speed in milliseconds
        this.unreadMessages = 0;
        this.unreadRequests = 0;

        // Initialize
        this.init();
    }

    /**
     * Initialize the notification manager
     */
    init() {
        // Create a backup favicon link if none exists
        if (!document.querySelector('link[rel="icon"]')) {
            const link = document.createElement('link');
            link.rel = 'icon';
            link.href = this.originalFavicon;
            document.head.appendChild(link);
        }

        // Listen for visibility change to stop flashing when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // Only stop flashing if the user is viewing the relevant section
                if (this.shouldStopFlashing()) {
                    this.stopFlashing();
                }
            }
        });

        // Listen for view changes to stop flashing when appropriate
        document.addEventListener('view-changed', (event) => {
            const view = event.detail.view;
            if (view === 'friends') {
                // Reset unread message count when switching to friends view
                this.unreadMessages = 0;

                // Stop flashing if there are no unread friend requests
                if (this.unreadRequests === 0) {
                    this.stopFlashing();
                }
            }
        });
    }

    /**
     * Determine if flashing should stop based on current view and context
     * @returns {boolean} Whether flashing should stop
     */
    shouldStopFlashing() {
        // Get current view
        const friendsView = document.getElementById('friendsView');
        const isInFriendsView = friendsView && !friendsView.classList.contains('hidden');

        // If in friends view and there are no unread requests, stop flashing
        if (isInFriendsView && this.unreadRequests === 0) {
            return true;
        }

        // If document is visible and there are no unread notifications, stop flashing
        if (document.visibilityState === 'visible' && this.unreadMessages === 0 && this.unreadRequests === 0) {
            return true;
        }

        return false;
    }

    /**
     * Start flashing the favicon
     * @param {string} type - Type of notification ('message' or 'request')
     */
    startFlashing(type = 'message') {
        // Increment appropriate counter
        if (type === 'message') {
            this.unreadMessages++;
        } else if (type === 'request') {
            this.unreadRequests++;
        }

        // If already flashing, no need to start again
        if (this.isFlashing) {
            return;
        }

        // Start flashing
        this.isFlashing = true;
        let isOriginal = true;

        // Get all favicon links
        const faviconLinks = Array.from(document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"]'));

        if (faviconLinks.length === 0) {
            // Create a favicon link if none exists
            const link = document.createElement('link');
            link.rel = 'icon';
            link.href = this.originalFavicon;
            document.head.appendChild(link);
            faviconLinks.push(link);
        }

        this.flashInterval = setInterval(() => {
            // Toggle between original and notification favicon for all favicon links
            const newHref = isOriginal ? this.alternativeFavicon : this.originalFavicon;

            faviconLinks.forEach(favicon => {
                favicon.href = newHref;
            });

            // Also update the page title to indicate new messages/requests
            if (isOriginal) {
                if (this.unreadMessages > 0 && this.unreadRequests > 0) {
                    document.title = `(${this.unreadMessages + this.unreadRequests}) New Activity - Kevko Systems`;
                } else if (this.unreadMessages > 0) {
                    document.title = `(${this.unreadMessages}) New Message - Kevko Systems`;
                } else if (this.unreadRequests > 0) {
                    document.title = `(${this.unreadRequests}) Friend Request - Kevko Systems`;
                }
            } else {
                document.title = 'Kevko Systems Dashboard';
            }

            isOriginal = !isOriginal;
        }, this.flashSpeed);
    }

    /**
     * Stop flashing the favicon
     */
    stopFlashing() {
        if (!this.isFlashing) {
            return;
        }

        // Clear the interval
        if (this.flashInterval) {
            clearInterval(this.flashInterval);
            this.flashInterval = null;
        }

        // Reset to original favicon for all favicon links
        const faviconLinks = document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"]');
        faviconLinks.forEach(favicon => {
            favicon.href = this.originalFavicon;
        });

        // Reset the page title
        document.title = 'Kevko Systems Dashboard';

        this.isFlashing = false;
    }

    /**
     * Reset unread message count and stop flashing if appropriate
     */
    resetUnreadMessages() {
        this.unreadMessages = 0;

        // Stop flashing if there are no unread requests
        if (this.unreadRequests === 0) {
            this.stopFlashing();
        }
    }

    /**
     * Reset unread request count and stop flashing if appropriate
     */
    resetUnreadRequests() {
        this.unreadRequests = 0;

        // Stop flashing if there are no unread messages
        if (this.unreadMessages === 0) {
            this.stopFlashing();
        }
    }
}

// Create global notification manager instance
window.notificationManager = new NotificationManager();
