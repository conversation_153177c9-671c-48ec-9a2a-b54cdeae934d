from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTimeField
from models.user import User
from datetime import datetime

class Thread(Document):
    user = ReferenceField(User, required=True)
    title = StringField(default="New Chat")
    messages = ListField(DictField(), default=list)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    participants = ListField(StringField(), default=list)
    
    meta = {
        'collection': 'threads',
        'indexes': [
            {'fields': ['user', '-updated_at']},  # Compound index for fast thread listing
            {'fields': ['user']},                 # Index for user lookup
            {'fields': ['-updated_at']}           # Index for sorting
        ]
    }
    
    def to_dict(self):
        return {
            'id': str(self.id),
            'title': self.title,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'messages': self.messages,
            'participants': self.participants
        }
        
    def to_summary_dict(self):
        """Return only summary information without messages"""
        return {
            'id': str(self.id),
            'title': self.title,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'message_count': len(self.messages)
        }
