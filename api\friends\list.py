from flask import jsonify
from flask_login import current_user
from models.user import User
from models.friend_relationship import FriendRelationship
from api.friends import friends_api

@friends_api.route('/list/<username>', methods=['GET'])
def get_friends_list(username):
    """Get a user's friends list, showing only mutual friends if the viewer is not the profile owner"""
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Get all accepted relationships where the user is either the user or the friend
    friends_as_user = FriendRelationship.objects(user=user, is_accepted=True)
    friends_as_friend = FriendRelationship.objects(friend=user, is_accepted=True)
    
    # Create a set of unique friend IDs
    friend_ids = set()
    
    # Add friends where user is the "user"
    for relationship in friends_as_user:
        friend_ids.add(str(relationship.friend.id))
    
    # Add friends where user is the "friend"
    for relationship in friends_as_friend:
        friend_ids.add(str(relationship.user.id))
    
    # If the viewer is not the profile owner, only show mutual friends
    if not current_user.is_authenticated:
        # If user is not authenticated, return an empty list of friends
        friend_ids = set()
    elif current_user.id != user.id:
        # Get the current user's friends
        viewer_friends_as_user = FriendRelationship.objects(user=current_user, is_accepted=True)
        viewer_friends_as_friend = FriendRelationship.objects(friend=current_user, is_accepted=True)
        
        viewer_friend_ids = set()
        
        # Add friends where viewer is the "user"
        for relationship in viewer_friends_as_user:
            viewer_friend_ids.add(str(relationship.friend.id))
        
        # Add friends where viewer is the "friend"
        for relationship in viewer_friends_as_friend:
            viewer_friend_ids.add(str(relationship.user.id))
        
        # Only keep mutual friends
        friend_ids = friend_ids.intersection(viewer_friend_ids)
    
    # Get friend details
    friends_list = []
    for friend_id in friend_ids:
        friend = User.objects(id=friend_id).first()
        if friend:
            friends_list.append({
                'username': friend.username,
                'display_name': friend.display_name or friend.username,
                'profile_picture': friend.profile_picture
            })
    
    return jsonify({
        'success': True,
        'friends': friends_list
    })