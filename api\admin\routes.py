from flask import jsonify, request, session
from flask_login import login_required, current_user
from . import admin_api
from models.user import User
from models.user_activity import UserActivity
from datetime import datetime
import random
import string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
import logging

def admin_required(f):
    """Decorator to check if user is an admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({'error': 'Admin access required'}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return login_required(decorated_function)

def super_admin_required(f):
    """Decorator to check if user is a super admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_super_admin:
            return jsonify({'error': 'Super admin access required'}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return login_required(decorated_function)

def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))

def send_admin_verification_email(email, code, action, target_email):
    """Send admin verification code via email"""
    try:
        sender_email = os.environ.get('EMAIL_ADDRESS')
        sender_password = os.environ.get('EMAIL_PASSWORD')

        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = email
        msg['Subject'] = 'Admin Action Verification'

        body = f'Your verification code for {action} admin {target_email} is: {code}'
        msg.attach(MIMEText(body, 'plain'))

        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()

        return True
    except Exception as e:
        logging.error(f"Error sending admin verification email: {str(e)}")
        return False

@admin_api.route('/check', methods=['GET'])
@login_required
def check_admin():
    """Check if current user is an admin"""
    is_admin = current_user.is_admin
    is_super_admin = current_user.is_super_admin
    return jsonify({
        'is_admin': is_admin,
        'is_super_admin': is_super_admin,
        'email': current_user.email
    })

@admin_api.route('/request-verification', methods=['POST'])
@admin_required
def request_verification():
    """Request verification code for admin actions"""
    try:
        data = request.get_json()
        action = data.get('action')
        target_email = data.get('email')

        if not action or action not in ['add', 'remove']:
            return jsonify({'error': 'Invalid action'}), 400

        if not target_email:
            return jsonify({'error': 'Email is required'}), 400

        # Generate and store verification code
        code = generate_verification_code()
        session[f'admin_verification_code_{current_user.email}'] = code
        session[f'admin_verification_timestamp_{current_user.email}'] = datetime.now().timestamp()
        session[f'admin_verification_action_{current_user.email}'] = action
        session[f'admin_verification_target_{current_user.email}'] = target_email

        # Send verification email
        if send_admin_verification_email(current_user.email, code, action, target_email):
            return jsonify({'message': 'Verification code sent successfully'})
        return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        logging.error(f"Error in request_verification: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/add-admin', methods=['POST'])
@admin_required
def add_admin():
    """Add a new admin"""
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')

        if not email or not verification_code:
            return jsonify({'error': 'Email and verification code are required'}), 400

        # Check verification code
        stored_code = session.get(f'admin_verification_code_{current_user.email}')
        code_timestamp = session.get(f'admin_verification_timestamp_{current_user.email}')
        stored_action = session.get(f'admin_verification_action_{current_user.email}')
        stored_target = session.get(f'admin_verification_target_{current_user.email}')

        if not stored_code or verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:
            return jsonify({'error': 'Verification code has expired'}), 400

        if stored_action != 'add' or stored_target != email:
            return jsonify({'error': 'Verification code is for a different action or target'}), 400

        # Find user
        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user is already an admin
        if user.is_admin:
            return jsonify({'error': 'User is already an admin'}), 400

        # Make user an admin
        user.is_admin = True
        user.save()

        # Clear verification codes
        session.pop(f'admin_verification_code_{current_user.email}', None)
        session.pop(f'admin_verification_timestamp_{current_user.email}', None)
        session.pop(f'admin_verification_action_{current_user.email}', None)
        session.pop(f'admin_verification_target_{current_user.email}', None)

        return jsonify({'message': f'User {email} is now an admin'})
    except Exception as e:
        logging.error(f"Error in add_admin: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/remove-admin', methods=['POST'])
@admin_required
def remove_admin():
    """Remove an admin"""
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')

        if not email or not verification_code:
            return jsonify({'error': 'Email and verification code are required'}), 400

        # Check verification code
        stored_code = session.get(f'admin_verification_code_{current_user.email}')
        code_timestamp = session.get(f'admin_verification_timestamp_{current_user.email}')
        stored_action = session.get(f'admin_verification_action_{current_user.email}')
        stored_target = session.get(f'admin_verification_target_{current_user.email}')

        if not stored_code or verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:
            return jsonify({'error': 'Verification code has expired'}), 400

        if stored_action != 'remove' or stored_target != email:
            return jsonify({'error': 'Verification code is for a different action or target'}), 400

        # Find user
        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user is an admin
        if not user.is_admin:
            return jsonify({'error': 'User is not an admin'}), 400

        # Check if user is a primary admin
        primary_admins = ['<EMAIL>', '<EMAIL>']
        if email in primary_admins:
            return jsonify({'error': 'Cannot remove primary admin'}), 400

        # Remove admin status
        user.is_admin = False
        user.save()

        # Clear verification codes
        session.pop(f'admin_verification_code_{current_user.email}', None)
        session.pop(f'admin_verification_timestamp_{current_user.email}', None)
        session.pop(f'admin_verification_action_{current_user.email}', None)
        session.pop(f'admin_verification_target_{current_user.email}', None)

        return jsonify({'message': f'Admin status removed from {email}'})
    except Exception as e:
        logging.error(f"Error in remove_admin: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/list-admins', methods=['GET'])
@admin_required
def list_admins():
    """List all admins"""
    try:
        admins = User.objects(is_admin=True)
        primary_admins = ['<EMAIL>', '<EMAIL>']
        return jsonify([{
            'email': admin.email,
            'username': admin.username,
            'is_primary': admin.email in primary_admins,
            'is_super_admin': admin.is_super_admin,
            'show_on_contacts': admin.show_on_contacts
        } for admin in admins])
    except Exception as e:
        logging.error(f"Error in list_admins: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/active-users', methods=['GET'])
@admin_required
def active_users():
    """Get active users across all sections"""
    try:
        # Get the minutes parameter (default: 15)
        minutes = request.args.get('minutes', 15, type=int)

        # Get active users for each section
        chat_users = UserActivity.get_active_users('chat', minutes)
        live_users = UserActivity.get_active_users('live', minutes)
        spotify_users = UserActivity.get_active_users('spotify', minutes)
        friends_users = UserActivity.get_active_users('friends', minutes)

        # Format the response
        result = {
            'chat': [activity.to_dict() for activity in chat_users],
            'live': [activity.to_dict() for activity in live_users],
            'spotify': [activity.to_dict() for activity in spotify_users],
            'friends': [activity.to_dict() for activity in friends_users],
            'total_unique': len(set(
                [str(activity.user.id) for activity in chat_users] +
                [str(activity.user.id) for activity in live_users] +
                [str(activity.user.id) for activity in spotify_users] +
                [str(activity.user.id) for activity in friends_users]
            ))
        }

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in active_users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/user-count', methods=['GET'])
@admin_required
def user_count():
    """Get total user count"""
    try:
        count = User.objects.count()
        return jsonify({'count': count})
    except Exception as e:
        logging.error(f"Error in user_count: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
