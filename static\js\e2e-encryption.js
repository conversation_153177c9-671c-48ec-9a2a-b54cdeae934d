/**
 * End-to-End Encryption Module for Friend Messages
 *
 * This module implements true end-to-end encryption similar to WhatsApp's Signal Protocol.
 * It handles key generation, key exchange, and message encryption/decryption.
 *
 * Key features:
 * - Messages are encrypted/decrypted only in the browser
 * - Each chat has its own encryption keys
 * - The server only sees encrypted content
 * - Encryption keys are never sent to the server
 * - Uses asymmetric encryption (RSA) for key exchange
 * - Uses symmetric encryption (AES-CBC) for message encryption
 */

const E2EEncryption = (function() {
    // Constants
    const KEY_PAIR_STORAGE_KEY = 'e2e_keypair';
    const PUBLIC_KEY_STORAGE_PREFIX = 'e2e_pubkey_';
    const SHARED_SECRET_PREFIX = 'e2e_shared_';
    const IDENTITY_KEY_STORAGE_KEY = 'e2e_identity_key';

    // In-memory cache for shared secrets (cleared on page refresh for security)
    const sharedSecretCache = {};

    // Track authentication failures to detect encryption issues
    const authFailureCount = {};

    /**
     * Generate a new RSA key pair for the current user
     * @returns {Object} The generated key pair
     */
    function generateKeyPair() {
        console.log('Generating new RSA key pair...');

        // Generate an RSA key pair with 2048 bits for strong security
        const keyPair = forge.pki.rsa.generateKeyPair({ bits: 2048 });

        // Convert to PEM format for storage
        const privateKeyPem = forge.pki.privateKeyToPem(keyPair.privateKey);
        const publicKeyPem = forge.pki.publicKeyToPem(keyPair.publicKey);

        // Store in localStorage (in a real app, consider more secure storage)
        const keyPairObj = {
            privateKey: privateKeyPem,
            publicKey: publicKeyPem
        };

        localStorage.setItem(KEY_PAIR_STORAGE_KEY, JSON.stringify(keyPairObj));

        console.log('Key pair generated and stored');
        return keyPairObj;
    }

    /**
     * Get the current user's key pair, generating one if it doesn't exist
     * @returns {Object} The user's key pair
     */
    function getOrCreateKeyPair() {
        const storedKeyPair = localStorage.getItem(KEY_PAIR_STORAGE_KEY);

        if (storedKeyPair) {
            return JSON.parse(storedKeyPair);
        }

        return generateKeyPair();
    }

    /**
     * Get the user's public key in PEM format
     * @returns {String} Public key in PEM format
     */
    function getMyPublicKey() {
        const keyPair = getOrCreateKeyPair();
        return keyPair.publicKey;
    }

    /**
     * Store a friend's public key
     * @param {String} friendId The friend's user ID
     * @param {String} publicKeyPem The friend's public key in PEM format
     */
    function storePublicKey(friendId, publicKeyPem) {
        localStorage.setItem(PUBLIC_KEY_STORAGE_PREFIX + friendId, publicKeyPem);
    }

    /**
     * Get a friend's public key
     * @param {String} friendId The friend's user ID
     * @returns {String|null} The friend's public key or null if not found
     */
    function getFriendPublicKey(friendId) {
        return localStorage.getItem(PUBLIC_KEY_STORAGE_PREFIX + friendId);
    }

    /**
     * Generate a shared secret for a chat
     * @param {String} chatId The chat ID
     * @param {String} friendId The friend's user ID
     * @returns {String} The encrypted shared secret to send to the friend
     */
    function generateSharedSecret(chatId, friendId) {
        try {
            console.log('Generating shared secret for chat:', chatId, 'with friend:', friendId);

            // Check if we already have a shared secret for this chat
            const existingSecret = getSharedSecret(chatId);
            if (existingSecret) {
                console.log('Using existing shared secret for chat:', chatId);

                // Get friend's public key
                const friendPublicKeyPem = getFriendPublicKey(friendId);
                if (!friendPublicKeyPem) {
                    console.error('Friend public key not found');
                    return null;
                }

                // Convert PEM to forge key object
                const friendPublicKey = forge.pki.publicKeyFromPem(friendPublicKeyPem);

                // Convert the existing secret from hex to bytes
                const secretBytes = forge.util.hexToBytes(existingSecret);

                // Encrypt it with friend's public key
                const encrypted = friendPublicKey.encrypt(secretBytes, 'RSA-OAEP');

                // Return the encrypted value
                return forge.util.encode64(encrypted);
            }

            // Get friend's public key
            const friendPublicKeyPem = getFriendPublicKey(friendId);
            if (!friendPublicKeyPem) {
                console.error('Friend public key not found');
                return null;
            }

            // Convert PEM to forge key object
            const friendPublicKey = forge.pki.publicKeyFromPem(friendPublicKeyPem);

            // Generate a truly random shared secret (32 bytes = 256 bits)
            const sharedSecretBytes = forge.random.getBytesSync(32);

            // Add additional entropy from various sources
            const entropySource = new Date().toString() +
                                  navigator.userAgent +
                                  window.screen.width +
                                  window.screen.height +
                                  Math.random().toString() +
                                  performance.now().toString();

            // Mix in the additional entropy
            const md = forge.md.sha256.create();
            md.update(sharedSecretBytes + entropySource);
            const finalSecretBytes = md.digest().getBytes();

            // Convert to hex for storage
            const sharedSecret = forge.util.bytesToHex(finalSecretBytes);
            console.log('Generated new truly random shared secret for chat:', chatId);

            // Store the shared secret locally (never sent to server)
            localStorage.setItem(SHARED_SECRET_PREFIX + chatId, sharedSecret);
            sharedSecretCache[chatId] = sharedSecret;

            // Encrypt the shared secret with friend's public key
            const encrypted = friendPublicKey.encrypt(finalSecretBytes, 'RSA-OAEP');

            // Return the encrypted shared secret
            return forge.util.encode64(encrypted);
        } catch (error) {
            console.error('Error generating shared secret:', error);
            return null;
        }
    }

    /**
     * Process a shared secret received from a friend
     * @param {String} chatId The chat ID
     * @param {String} encryptedSecret The encrypted shared secret from the friend
     * @returns {String} The shared secret
     */
    function processSharedSecret(chatId, encryptedSecret) {
        try {
            console.log('Processing shared secret for chat:', chatId);

            // Get our private key
            const keyPair = getOrCreateKeyPair();
            const privateKeyPem = keyPair.privateKey;
            const privateKey = forge.pki.privateKeyFromPem(privateKeyPem);

            // Decrypt the shared secret using our private key
            const encryptedBytes = forge.util.decode64(encryptedSecret);
            const sharedSecretBytes = privateKey.decrypt(encryptedBytes, 'RSA-OAEP');

            // Convert to hex string for storage
            const sharedSecret = forge.util.bytesToHex(sharedSecretBytes);

            console.log('Successfully processed shared secret for chat:', chatId);

            // Store the shared secret locally (never sent to server)
            localStorage.setItem(SHARED_SECRET_PREFIX + chatId, sharedSecret);
            sharedSecretCache[chatId] = sharedSecret;

            return sharedSecret;
        } catch (error) {
            console.error('Error processing shared secret:', error);
            return null;
        }
    }

    /**
     * Get the shared secret for a chat
     * @param {String} chatId The chat ID
     * @returns {String|null} The shared secret or null if not found
     */
    function getSharedSecret(chatId) {
        // Check in-memory cache first
        if (sharedSecretCache[chatId]) {
            return sharedSecretCache[chatId];
        }

        // Check localStorage
        const secret = localStorage.getItem(SHARED_SECRET_PREFIX + chatId);
        if (secret) {
            sharedSecretCache[chatId] = secret;
            return secret;
        }

        return null;
    }

    /**
     * Encrypt a message using AES-CBC with the shared secret
     * @param {String} message The plaintext message
     * @param {String} chatId The chat ID
     * @returns {String} The encrypted message
     */
    function encryptMessage(message, chatId) {
        try {
            console.log(`Attempting to encrypt message for chat ${chatId}`);

            // Get the shared secret for this chat
            const sharedSecret = getSharedSecret(chatId);
            if (!sharedSecret) {
                console.error(`Shared secret not found for chat ${chatId}`);
                return '[ENCRYPTION_ERROR]';
            }

            // Create a random initialization vector (16 bytes for AES-CBC)
            const iv = forge.random.getBytesSync(16);
            
            // Create a random salt for this message (16 bytes)
            const salt = forge.random.getBytesSync(16);
            
            // Convert shared secret from hex to bytes
            const keyBytes = forge.util.hexToBytes(sharedSecret);
            
            // Derive the key using the shared secret and salt
            const md = forge.md.sha256.create();
            md.update(keyBytes + salt + 'KEVKO_E2E_KEY_DERIVATION');
            const key = md.digest().getBytes();
            
            // Create a cipher object
            const cipher = forge.cipher.createCipher('AES-CBC', key);
            
            // Initialize cipher with IV
            cipher.start({iv: iv});
            
            // Update the cipher with the message
            cipher.update(forge.util.createBuffer(message, 'utf8'));
            
            // Finalize encryption
            cipher.finish();
            
            // Get the encrypted output
            const ciphertext = cipher.output.getBytes();
            
            // Combine salt, IV and ciphertext
            const combined = salt + iv + ciphertext;
            
            // Encode with base64
            const encrypted = forge.util.encode64(combined);
            
            // Return with a prefix to indicate encryption method
            return `E2E_${encrypted}`;
        } catch (error) {
            console.error('Error encrypting message:', error);
            return '[ENCRYPTION_ERROR]';
        }
    }

    /**
     * Decrypt a message using AES-CBC with the shared secret
     * @param {String} encryptedMessage The encrypted message
     * @param {String} chatId The chat ID
     * @returns {String} The decrypted message or error message
     */
    function decryptMessage(encryptedMessage, chatId) {
        try {
            console.log(`Attempting to decrypt message for chat ${chatId}`);

            // Check if encryptedMessage is valid
            if (!encryptedMessage || typeof encryptedMessage !== 'string') {
                console.error('Invalid encrypted message:', encryptedMessage);
                return '[Encrypted message - Invalid format]';
            }

            // Check if this is an E2E encrypted message
            if (!encryptedMessage.startsWith('E2E_')) {
                console.log('Message is not E2E encrypted, returning as is');
                return encryptedMessage;
            }

            // Get the shared secret for this chat
            const sharedSecret = getSharedSecret(chatId);
            if (!sharedSecret) {
                console.error(`Shared secret not found for chat ${chatId}`);
                return '[Encrypted message - No decryption key available]';
            }

            // For debugging, log the shared secret hash (not the actual secret)
            const hashMd = forge.md.sha256.create();
            hashMd.update(sharedSecret);
            const secretHash = hashMd.digest().toHex();
            console.log(`Shared secret hash: ${secretHash}`);

            // Remove the prefix
            const encryptedContent = encryptedMessage.substring(4);
            
            try {
                // Decode from base64
                const encryptedBytes = forge.util.decode64(encryptedContent);
                
                // Check if the message is long enough to contain salt + IV + ciphertext
                if (encryptedBytes.length <= 32) {
                    console.log('Message too short to contain salt and IV');
                    return '[Encrypted message - Invalid format]';
                }
                
                // Extract salt, IV, and ciphertext
                const salt = encryptedBytes.slice(0, 16);
                const iv = encryptedBytes.slice(16, 32);
                const ciphertext = encryptedBytes.slice(32);
                
                // Convert shared secret from hex to bytes
                const keyBytes = forge.util.hexToBytes(sharedSecret);
                
                // Derive the key using the shared secret and salt
                const md = forge.md.sha256.create();
                md.update(keyBytes + salt + 'KEVKO_E2E_KEY_DERIVATION');
                const key = md.digest().getBytes();
                
                // Create decipher
                const decipher = forge.cipher.createDecipher('AES-CBC', key);
                
                // Initialize decipher with IV
                decipher.start({iv: iv});
                
                // Update decipher with ciphertext
                decipher.update(forge.util.createBuffer(ciphertext));
                
                // Finalize decipher
                const result = decipher.finish();
                
                if (result) {
                    // Get plaintext
                    const plaintext = decipher.output.toString('utf8');
                    console.log(`Successfully decrypted message, length: ${plaintext.length}`);
                    
                    // Reset failure counter on success
                    if (authFailureCount[chatId]) {
                        authFailureCount[chatId] = 0;
                    }
                    
                    return plaintext;
                } else {
                    console.error('Decryption failed');
                    
                    // Try fallback method with chat ID as key
                    try {
                        console.log('Trying fallback decryption with chat ID as key');
                        
                        // Generate key from chat_id directly
                        const fallbackMd = forge.md.sha256.create();
                        fallbackMd.update(chatId);
                        const fallbackKey = fallbackMd.digest().getBytes();
                        
                        // Create decipher
                        const fallbackDecipher = forge.cipher.createDecipher('AES-CBC', fallbackKey);
                        
                        // Initialize decipher with IV
                        fallbackDecipher.start({iv: iv});
                        
                        // Update decipher with ciphertext
                        fallbackDecipher.update(forge.util.createBuffer(ciphertext));
                        
                        // Finalize decipher
                        const fallbackResult = fallbackDecipher.finish();
                        
                        if (fallbackResult) {
                            // Get plaintext
                            const fallbackPlaintext = fallbackDecipher.output.toString('utf8');
                            console.log('Fallback decryption successful!');
                            
                            // Return the decrypted message with a warning
                            return fallbackPlaintext + ' [Warning: This message was encrypted with a less secure method]';
                        }
                    } catch (fallbackError) {
                        console.error('Fallback decryption failed:', fallbackError);
                    }
                    
                    // Track decryption failures
                    if (!authFailureCount[chatId]) {
                        authFailureCount[chatId] = 0;
                    }
                    
                    // Increment counter
                    authFailureCount[chatId]++;
                    console.log(`Decryption failure count for chat ${chatId}: ${authFailureCount[chatId]}`);
                    
                    // If we've reached a threshold, auto-reset encryption
                    const AUTH_FAILURE_THRESHOLD = 5;
                    if (authFailureCount[chatId] >= AUTH_FAILURE_THRESHOLD) {
                        console.warn(`Decryption failure threshold reached for chat ${chatId}, auto-resetting encryption`);
                        resetEncryptionForChat(chatId);
                        authFailureCount[chatId] = 0;
                    }
                    
                    return '[Encrypted message - Decryption failed]';
                }
            } catch (error) {
                console.error('Error during decryption:', error);
                return '[Encrypted message - Decryption error]';
            }
        } catch (error) {
            console.error('Error in decryption function:', error);
            return '[Encrypted message - Decryption failed]';
        }
    }

    /**
     * Reset the encryption state for a specific chat
     * @param {String} chatId The chat ID to reset
     * @returns {Boolean} Success status
     */
    function resetEncryptionForChat(chatId) {
        try {
            console.log(`Resetting encryption for chat ${chatId}`);

            // Remove shared secret from localStorage and cache
            localStorage.removeItem(SHARED_SECRET_PREFIX + chatId);
            delete sharedSecretCache[chatId];

            // Reset authentication failure counter
            if (authFailureCount[chatId]) {
                authFailureCount[chatId] = 0;
            }

            return true;
        } catch (error) {
            console.error('Error resetting encryption for chat:', error);
            return false;
        }
    }

    /**
     * Reset all encryption data (use with caution)
     */
    function resetAllEncryption() {
        try {
            console.log('Resetting all encryption data');

            // Clear all localStorage items related to encryption
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith(SHARED_SECRET_PREFIX) ||
                    key.startsWith(PUBLIC_KEY_STORAGE_PREFIX) ||
                    key === KEY_PAIR_STORAGE_KEY) {
                    localStorage.removeItem(key);
                }
            }

            // Clear cache
            Object.keys(sharedSecretCache).forEach(key => {
                delete sharedSecretCache[key];
            });

            // Generate new key pair
            generateKeyPair();

            return true;
        } catch (error) {
            console.error('Error resetting all encryption:', error);
            return false;
        }
    }
    
    /**
     * Re-encrypt a message with the new secure format
     * @param {String} message The plaintext message
     * @param {String} chatId The chat ID
     * @returns {String} The re-encrypted message
     */
    function reEncryptMessage(message, chatId) {
        try {
            console.log(`Re-encrypting message for chat ${chatId}`);
            
            // Simply encrypt the message with the current method
            // This will use the new secure format with salt
            return encryptMessage(message, chatId);
        } catch (error) {
            console.error('Error re-encrypting message:', error);
            return '[ENCRYPTION_ERROR]';
        }
    }

    /**
     * Initialize the encryption module
     * @returns {Object} The public API
     */
    function init() {
        try {
            console.log('Initializing E2E encryption module');
            
            // Ensure we have a key pair
            getOrCreateKeyPair();
            
            console.log('E2E encryption module initialized with the following features:');
            console.log('- True end-to-end encryption');
            console.log('- Messages are encrypted/decrypted only in the browser');
            console.log('- Each chat has its own encryption keys');
            console.log('- The server only sees encrypted content');
            console.log('- Encryption keys are never sent to the server');
            console.log('- Uses asymmetric encryption (RSA) for key exchange');
            console.log('- Uses symmetric encryption (AES-CBC) for message encryption');

            return {
                getPublicKey: getMyPublicKey,
                storePublicKey,
                getFriendPublicKey,
                generateSharedSecret,
                processSharedSecret,
                getSharedSecret,
                encryptMessage,
                decryptMessage,
                resetEncryptionForChat,
                resetAllEncryption,
                reEncryptMessage
            };
        } catch (error) {
            console.error('Error initializing E2E encryption module:', error);
            return null;
        }
    }

    return {
        init
    };
})();
