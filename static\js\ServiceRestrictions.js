/**
 * ServiceRestrictions.js
 * Manages service restrictions for users
 */

class ServiceRestrictions {
    constructor() {
        this.restrictions = new Map();

        // Load restrictions from localStorage
        this.loadRestrictions();

        // Create default restrictions if none exist
        if (this.restrictions.size === 0) {
            this.createDefaultRestrictions();
        }
    }

    /**
     * Load restrictions from localStorage
     */
    loadRestrictions() {
        const storedRestrictions = localStorage.getItem('serviceRestrictions');
        if (storedRestrictions) {
            try {
                const restrictions = JSON.parse(storedRestrictions);
                Object.entries(restrictions).forEach(([serviceId, restrictionData]) => {
                    this.restrictions.set(serviceId, restrictionData);
                });
                console.log(`Loaded restrictions for ${this.restrictions.size} services from localStorage`);
            } catch (e) {
                console.warn('Failed to parse stored restrictions');
            }
        }
    }

    /**
     * Save restrictions to localStorage
     */
    saveRestrictions() {
        const restrictions = {};
        this.restrictions.forEach((restrictionData, serviceId) => {
            restrictions[serviceId] = restrictionData;
        });
        localStorage.setItem('serviceRestrictions', JSON.stringify(restrictions));
    }

    /**
     * Create default restrictions
     */
    createDefaultRestrictions() {
        // Don't create any default restrictions
        console.log('No default restrictions created');

        // Save empty restrictions to localStorage
        this.saveRestrictions();
    }

    /**
     * Get all restrictions
     * @returns {Map} Map of restrictions
     */
    getAllRestrictions() {
        return this.restrictions;
    }

    /**
     * Get restrictions for a service
     * @param {string} serviceId Service ID
     * @returns {Object|null} Restriction object or null if not found
     */
    getServiceRestrictions(serviceId) {
        return this.restrictions.get(serviceId) || null;
    }

    /**
     * Set restrictions for a service
     * @param {string} serviceId Service ID
     * @param {Object} restrictions Restriction object
     */
    setServiceRestrictions(serviceId, restrictions) {
        this.restrictions.set(serviceId, restrictions);
        this.saveRestrictions();
    }

    /**
     * Check if a user has access to a service
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @returns {boolean} True if the user has access
     */
    hasAccess(userId, serviceId) {
        const restrictions = this.getServiceRestrictions(serviceId);
        if (!restrictions || !restrictions.accessControl) {
            return true; // No restrictions, everyone has access
        }

        const { type, users } = restrictions.accessControl;

        if (type === 'whitelist') {
            return users.includes(userId);
        } else if (type === 'blacklist') {
            return !users.includes(userId);
        }

        return true; // Default to allowing access
    }

    /**
     * Get storage limit for a user
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @returns {number|null} Storage limit in GB or null if not applicable
     */
    getStorageLimit(userId, serviceId) {
        const restrictions = this.getServiceRestrictions(serviceId);
        if (!restrictions || !restrictions.storageLimits) {
            return null; // No storage limits
        }

        const { default: defaultLimit, users } = restrictions.storageLimits;

        return users[userId] || defaultLimit || null;
    }

    /**
     * Check if a user can access a service at the current time
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @returns {boolean} True if the user can access the service now
     */
    canAccessNow(userId, serviceId) {
        const restrictions = this.getServiceRestrictions(serviceId);
        if (!restrictions || !restrictions.timeRestrictions) {
            return true; // No time restrictions, always accessible
        }

        const now = new Date();
        const day = now.getDay();
        const isWeekend = day === 0 || day === 6; // 0 = Sunday, 6 = Saturday

        const timeRestriction = isWeekend ?
            restrictions.timeRestrictions.weekends :
            restrictions.timeRestrictions.weekdays;

        if (!timeRestriction.start || !timeRestriction.end) {
            return true; // No specific time restriction for this day type
        }

        // Convert current time to minutes since midnight
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const currentMinutes = hours * 60 + minutes;

        // Convert restriction times to minutes since midnight
        const startParts = timeRestriction.start.split(':');
        const endParts = timeRestriction.end.split(':');

        const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
        const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);

        // Check if current time is within allowed range
        return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    }

    /**
     * Check if a user can access a feature
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @param {string} featureId Feature ID
     * @returns {boolean} True if the user can access the feature
     */
    canAccessFeature(userId, serviceId, featureId) {
        const restrictions = this.getServiceRestrictions(serviceId);
        if (!restrictions || !restrictions.featureRestrictions || !restrictions.featureRestrictions[featureId]) {
            return true; // No feature restrictions, everyone can access
        }

        const featureRestriction = restrictions.featureRestrictions[featureId];
        const { allowed, exceptions } = featureRestriction;

        if (allowed) {
            return !exceptions.includes(userId); // Everyone except exceptions
        } else {
            return exceptions.includes(userId); // Only exceptions
        }
    }

    /**
     * Add a user to a service's access control list
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @param {string} listType 'whitelist' or 'blacklist'
     */
    addUserToAccessList(userId, serviceId, listType = 'whitelist') {
        const restrictions = this.getServiceRestrictions(serviceId) || {};

        if (!restrictions.accessControl) {
            restrictions.accessControl = {
                type: listType,
                users: []
            };
        }

        if (restrictions.accessControl.type !== listType) {
            restrictions.accessControl.type = listType;
            restrictions.accessControl.users = [];
        }

        if (!restrictions.accessControl.users.includes(userId)) {
            restrictions.accessControl.users.push(userId);
            this.setServiceRestrictions(serviceId, restrictions);
        }
    }

    /**
     * Remove a user from a service's access control list
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     */
    removeUserFromAccessList(userId, serviceId) {
        const restrictions = this.getServiceRestrictions(serviceId);
        if (!restrictions || !restrictions.accessControl) {
            return;
        }

        restrictions.accessControl.users = restrictions.accessControl.users.filter(id => id !== userId);
        this.setServiceRestrictions(serviceId, restrictions);
    }

    /**
     * Set storage limit for a user
     * @param {number} userId User ID
     * @param {string} serviceId Service ID
     * @param {number} limitGB Storage limit in GB
     */
    setUserStorageLimit(userId, serviceId, limitGB) {
        const restrictions = this.getServiceRestrictions(serviceId) || {};

        if (!restrictions.storageLimits) {
            restrictions.storageLimits = {
                default: 5,
                users: {}
            };
        }

        restrictions.storageLimits.users[userId] = limitGB;
        this.setServiceRestrictions(serviceId, restrictions);
    }

    /**
     * Set time restrictions for a service
     * @param {string} serviceId Service ID
     * @param {Object} weekdayRestriction Weekday restriction object with start and end times
     * @param {Object} weekendRestriction Weekend restriction object with start and end times
     */
    setTimeRestrictions(serviceId, weekdayRestriction, weekendRestriction) {
        const restrictions = this.getServiceRestrictions(serviceId) || {};

        restrictions.timeRestrictions = {
            weekdays: weekdayRestriction,
            weekends: weekendRestriction
        };

        this.setServiceRestrictions(serviceId, restrictions);
    }

    /**
     * Set feature restriction for a service
     * @param {string} serviceId Service ID
     * @param {string} featureId Feature ID
     * @param {boolean} allowed Whether the feature is allowed by default
     * @param {Array} exceptions Array of user IDs that are exceptions to the rule
     */
    setFeatureRestriction(serviceId, featureId, allowed, exceptions) {
        const restrictions = this.getServiceRestrictions(serviceId) || {};

        if (!restrictions.featureRestrictions) {
            restrictions.featureRestrictions = {};
        }

        restrictions.featureRestrictions[featureId] = {
            allowed,
            exceptions
        };

        this.setServiceRestrictions(serviceId, restrictions);
    }
}

// Create global service restrictions instance
window.serviceRestrictions = new ServiceRestrictions();
