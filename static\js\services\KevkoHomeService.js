/**
 * KevkoHomeService.js
 * Custom implementation of the KevkoHome service
 */
window.KevkoHomeService = class KevkoHomeService extends window.Service {
    constructor(config) {
        super(config);
        this.devices = [];
        this.rooms = [];
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // In a real implementation, this might fetch data from an API
        this.devices = [
            { id: 1, name: 'Living Room Light', type: 'light', status: 'off', room: 'Living Room' },
            { id: 2, name: 'Kitchen Light', type: 'light', status: 'on', room: 'Kitchen' },
            { id: 3, name: 'Bedroom Thermostat', type: 'thermostat', value: 21, room: 'Bedroom' },
            { id: 4, name: 'Front Door', type: 'door', status: 'locked', room: 'Entrance' }
        ];

        this.rooms = [
            { id: 1, name: 'Living Room', devices: 2 },
            { id: 2, name: 'Kitchen', devices: 1 },
            { id: 3, name: 'Bedroom', devices: 1 },
            { id: 4, name: 'Entrance', devices: 1 }
        ];
    }

    /**
     * Render the service card HTML with additional features
     * @returns {string} HTML string
     */
    render() {
        const statusClass = this.status === 'offline' ? 'offline' : '';
        const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
        const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

        let actionButton = '';

        if (this.status === 'online' && this.url) {
            // External link for online services with URL
            actionButton = `
                <a href="${this.url}" target="_blank"
                   class="w-full bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                    <span class="text-sm text-slate-300 group-hover:text-slate-100">Launch ${this.name}</span>
                    <i data-lucide="chevron-right" class="h-4 w-4 ml-1"></i>
                </a>
            `;
        } else {
            // Enhanced coming soon button with device preview
            actionButton = `
                <div class="w-full">
                    <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed mb-2">
                        <span class="text-sm text-slate-300 animate-pulse">${this.status === 'offline' ? 'Offline' : 'Coming Soon'}</span>
                    </div>
                    <button class="w-full bg-slate-700/30 hover:bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-1 text-center transition-colors text-xs text-slate-400 flex items-center justify-center" id="showDevices">
                        <i data-lucide="home" class="h-3 w-3 mr-1"></i>
                        Preview Devices
                    </button>
                </div>
            `;
        }

        return `
            <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                <div class="flex flex-col h-full">
                    <div class="flex items-start justify-between mb-4">
                        <div class="p-2 rounded-lg bg-${this.color}-500/10">
                            <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                        </div>
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                            <span class="text-xs text-slate-400">${statusText}</span>
                        </div>
                    </div>
                    <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                    <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                    ${actionButton}
                    <div id="devices-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                        <h4 class="text-xs font-medium text-slate-300 mb-2">Smart Home Devices</h4>
                        <div class="space-y-1 max-h-32 overflow-y-auto">
                            <!-- Devices will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize devices button
            const showDevicesBtn = element.querySelector('#showDevices');
            if (showDevicesBtn) {
                console.log(`Found devices button for service: ${this.id}`);
                showDevicesBtn.addEventListener('click', () => {
                    this.toggleDevices(element);
                });
            } else {
                console.log(`No devices button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoHomeService ${this.id}:`, error);
        }
    }

    /**
     * Toggle devices visibility
     * @param {HTMLElement} element The service element
     */
    async toggleDevices(element) {
        const devicesContainer = element.querySelector(`#devices-${this.id}`);
        if (!devicesContainer) return;

        const isHidden = devicesContainer.classList.contains('hidden');

        if (isHidden) {
            // Initialize devices if needed
            if (this.devices.length === 0) {
                await this.init();
            }

            // Populate devices
            const devicesContent = devicesContainer.querySelector('.space-y-1');
            if (devicesContent) {
                devicesContent.innerHTML = this.devices.map(device => {
                    let statusIndicator = '';

                    if (device.type === 'light') {
                        const isOn = device.status === 'on';
                        statusIndicator = `<span class="text-xs ${isOn ? 'text-yellow-400' : 'text-slate-500'}">${isOn ? 'On' : 'Off'}</span>`;
                    } else if (device.type === 'thermostat') {
                        statusIndicator = `<span class="text-xs text-red-400">${device.value}°C</span>`;
                    } else if (device.type === 'door') {
                        const isLocked = device.status === 'locked';
                        statusIndicator = `<span class="text-xs ${isLocked ? 'text-green-400' : 'text-red-400'}">${isLocked ? 'Locked' : 'Unlocked'}</span>`;
                    }

                    return `
                        <div class="flex items-center justify-between bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors">
                            <div class="flex items-center">
                                <i data-lucide="${this.getDeviceIcon(device.type)}" class="h-3 w-3 mr-1 text-${this.color}-400"></i>
                                <span class="text-xs text-slate-300">${device.name}</span>
                            </div>
                            ${statusIndicator}
                        </div>
                    `;
                }).join('');

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-3", "w-3"]
                        },
                        elements: [devicesContent]
                    });
                }
            }

            // Show devices
            devicesContainer.classList.remove('hidden');
        } else {
            // Hide devices
            devicesContainer.classList.add('hidden');
        }
    }

    /**
     * Get icon for device type
     * @param {string} deviceType Device type
     * @returns {string} Icon name
     */
    getDeviceIcon(deviceType) {
        switch (deviceType) {
            case 'light':
                return 'lightbulb';
            case 'thermostat':
                return 'thermometer';
            case 'door':
                return 'lock';
            default:
                return 'device';
        }
    }
}
