<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Control your Spotify playback with this intuitive web interface. Play, pause, skip tracks, manage playlists, and control volume with this responsive Spotify player interface.">
    <title>Spotify Control Interface</title>

    <!-- Favicon links -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/spotify.css') }}">
    <!-- Remove the direct SDK script tag, we'll load it dynamically -->
</head>
<body>
    <script>
        window.SPOTIFY_TOKEN = '{{ token }}';

        // Remove the duplicate onSpotifyWebPlaybackSDKReady handler
        // The initialization will be handled by initializeSpotifyPlayer() in spotify.js
    </script>

    <div class="container">
        <div class="menu">
            <button class="menu-toggle" title="Toggle Playlist Menu">
                <i class="fas fa-bars"></i>
            </button>
            <div class="liked-songs" title="Liked Songs" onclick="window.player?.playLikedSongs()"></div>
            <div class="playlists-container">
                <!-- Playlists will be dynamically inserted here -->
            </div>
        </div>
        <div class="content">
            <div class="left-section">
                <!-- First container for Next Up and Device Manager -->
                <div class="link-bar">
                    <!-- Next Up Preview -->
                    <div class="next-track-preview">
                        <div class="next-track-label">Next up</div>
                        <div class="next-track-content">
                            <img id="next-track-artwork" src="/static/images/default-artwork.png" alt="Next Track">
                            <div class="next-track-info">
                                <div id="next-track-name">-</div>
                                <div id="next-track-artist">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- Device Manager -->
                    <div class="device-manager">
                        <div class="device-label">Playing on</div>
                        <button class="device-button" id="device-selector">
                            <i class="fas fa-speaker"></i>
                            <span class="device-name">Select Device</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="device-dropdown" id="device-dropdown">
                            <!-- Devices will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Info Box with Search -->
                <div class="info-box">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <input type="text" id="search-input" placeholder="Search tracks...">
                            <i class="fa fa-search search-icon"></i>
                        </div>
                        <div class="search-results" id="search-results"></div>
                    </div>
                </div>
            </div>
            <div class="main-section">
                <div class="main-window">
                    <div class="player-content-wrapper">
                        <div class="track-artwork">
                            <img id="track-artwork" src="/static/images/default-artwork.jpg" alt="Track Artwork">
                        </div>
                        <div class="progress-section">
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div id="progress"></div>
                                </div>
                                <div id="hover-time" class="hover-time"></div>
                            </div>
                            <div class="track-progress-time">
                                <span id="track-current-time">0:00</span>
                                <span id="track-duration">0:00</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="now-playing">
                    <!-- Menu toggle will be inserted here by JavaScript -->
                    <div class="track-info">
                        <div class="track-name" id="track-name">Track Name</div>
                        <div class="track-artist" id="track-artist">Artist Name</div>
                    </div>
                    <div class="control-buttons">
                        <button onclick="window.player.shuffle()" aria-label="Toggle shuffle mode">
                            <i class="fa fa-random" aria-hidden="true"></i>
                        </button>
                        <button onclick="window.player?.prevTrack()" id="prev-track" aria-label="Play previous track">
                            <i class="fa fa-step-backward" aria-hidden="true"></i>
                        </button>
                        <button onclick="window.player?.togglePlay()" id="play-pause-button" aria-label="Play or pause">
                            <i class="fa fa-play" aria-hidden="true"></i>
                        </button>
                        <button onclick="window.player?.skipToNext()" id="next-track" aria-label="Play next track">
                            <i class="fa fa-step-forward" aria-hidden="true"></i>
                        </button>
                        <button onclick="window.player.repeat()" aria-label="Toggle repeat mode">
                            <i class="fa fa-repeat" aria-hidden="true"></i>
                        </button>
                        <!-- Add mobile volume toggle button -->
                        <button class="mobile-volume-toggle" aria-label="Toggle volume control">
                            <i class="fa fa-volume-up" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="volume-control">
                        <label for="volume" class="sr-only">Volume control</label>
                        <span class="volume-icon" aria-hidden="true"><i class="fa fa-volume-up"></i></span>
                        <input
                            type="range"
                            id="volume"
                            name="volume"
                            min="0"
                            max="100"
                            value="50"
                            aria-valuemin="0"
                            aria-valuemax="100"
                            aria-valuenow="50"
                            aria-label="Volume control"
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load your custom JS -->
    <script src="{{ url_for('static', filename='js/restriction-handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/spotify.js') }}"></script>

    <!-- Notification container for alerts -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md"></div>

    <!-- Restriction Modal -->
    {% include 'includes/restriction_modal.html' %}
</body>
</html>