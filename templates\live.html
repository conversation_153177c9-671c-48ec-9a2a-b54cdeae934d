<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0" />
  <title>KevkoAI Live Chat</title>

  <!-- Favicon links -->
  <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
  <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
  <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
  <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
  <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

  <!-- Immediate styles to prevent flash -->
  <style>
    body {
      background-color: rgb(21,21,21);
      margin: 0;
      padding: 0;
      opacity: 0;
      height: 100vh;
      width: 100vw;
      overflow: hidden;
    }
    html {
      height: 100%;
      width: 100%;
      overflow: hidden;
    }
  </style>

  <!-- Resource hints for faster loading -->
  <link rel="preconnect" href="https://unpkg.com">
  <link rel="preconnect" href="https://cdn.tailwindcss.com">
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  
  <!-- Preload API data -->
  <link rel="preload" href="/api/live/load/rooms" as="fetch" crossorigin="anonymous">

  <!-- Link to the chat.css file with optimized loading -->
  <link rel="preload" href="{{ url_for('static', filename='css/chat.css') }}" as="style">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/live.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/thinking-container.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/model-selector.css') }}" media="print" onload="this.media='all'">

  <!-- External resources with optimized loading -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" as="style">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" media="print" onload="this.media='all'">

  <!-- Defer non-critical JavaScript -->
  <script src="https://cdn.tailwindcss.com" defer></script>
  <!-- Add this in the head section, before your other scripts -->
  <script src="{{ url_for('static', filename='js/vendor/lucide.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/vendor/lucide-custom.js') }}"></script>
  <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" defer></script>

  <!-- Optional: Pass the initial room data from Flask to JavaScript -->
  <script>
    window.initialRoomData = {% if room %}{{ room|tojson|safe }}{% else %}null{% endif %};
    window.currentUserId = "{{ current_user.id }}";
    window.currentUsername = "{{ current_user.username }}";
  </script>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" media="print" onload="this.media='all'">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js" defer></script>
  <script src="https://cdn.socket.io/4.7.2/socket.io.min.js" integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
</head>
<body>
  <!-- Mobile sidebar overlay removed -->
  
  <!-- Container that centers the chat interface -->
  <div class="chat-center">
    <!-- Main Chat Container -->
    <div class="chat-container">

      <!-- Left Sidebar -->
      <div class="sidebar">
        <div class="sidebar-header">
          <div class="sidebar-header-top">
            <h1>KevkoAI Live</h1>
            <button id="closeSidebarBtn" aria-label="Close sidebar" class="mobile-only">
              <i data-lucide="x"></i>
            </button>
          </div>
          <p>Collaborate with others in real-time.</p>
        </div>

        <div class="sidebar-buttons">
          <button id="createRoomBtn" aria-label="Create Room">
            <i data-lucide="plus-circle"></i>
            <span>Create Room</span>
          </button>
        </div>

        <!-- Room List -->
        <div id="roomList">
          <!-- Room items will be dynamically inserted here -->
        </div>

        <!-- User Info -->
        <div class="user-info">
          <div class="user-avatar">
            <i data-lucide="user"></i>
          </div>
          <div class="user-details">
            <div class="user-name">{{ current_user.username }}</div>
            <button onclick="handleLogout()" class="logout-link">
              <i data-lucide="log-out"></i>
              Logout
            </button>
          </div>
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="main-chat">
        <!-- Header -->
        <header>
          <div class="header-left">
            <button id="sidebarToggleBtn" aria-label="Toggle sidebar" class="mobile-only">
              <i data-lucide="menu"></i>
            </button>
            <h2 id="activeRoomName">Live Chat</h2>
          </div>
          <div>
            <button id="inviteBtn" aria-label="Copy invite link" title="Copy room link to clipboard">
              <i data-lucide="link"></i>
              <span>Copy Link</span>
            </button>
            <button id="fullscreenBtn" aria-label="Toggle fullscreen">
              <i data-lucide="expand"></i>
              <span>Fullscreen</span>
            </button>
          </div>
        </header>

        <!-- Room Status -->
        <div id="roomStatus" class="room-status">
          <div class="status-message">Create or join a room to start chatting</div>
        </div>

        <!-- Chat Content -->
        <div id="chatContent">
          <!-- Chat messages will be dynamically inserted here -->
        </div>

        <!-- Input Area -->
        <div class="input-area">
          <div class="input-container">
            <!-- Function buttons removed in favor of the + icon in the input field -->

            <!-- Image Preview Container -->
            <div id="imagePreviewContainer" class="flex gap-2 hidden"></div>

            <!-- Hidden File Input -->
            <input type="file" id="imageFileInput" accept="image/*" multiple class="hidden">

            <div class="input-wrapper">
              <div style="position: relative; flex: 1; width: 100%;">
                <button id="addButton" aria-label="Add content">
                  <i data-lucide="plus" class="icon"></i>
                </button>
                <textarea id="messageInput" rows="1" placeholder="Ask anything..."></textarea>
                <div id="modelSelector" class="current-model-display">
                  <div class="current-model-icon">
                    <i data-lucide="sparkles"></i>
                  </div>
                  <div id="currentModel">GPT-4o Mini</div>
                </div>

                <!-- Model Selector Container -->
                <div class="model-selector-container" id="modelSelectorContainer">
                  <!-- Tooltip for model descriptions -->
                  <div id="modelDescriptionTooltip" class="model-description-tooltip" style="display: none;"></div>
                  <div class="model-selector-options">
                    <div class="model-option-card" data-model="gpt-4o-mini">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="sparkles"></i>
                      </div>
                      <div class="model-option-name">GPT-4o Mini</div>
                      <div class="model-info-icon" data-description="Fast, reliable, great at complex tasks">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="gemini-2.0-flash">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Gemini</div>
                      <div class="model-info-icon" data-description="Excellent with images, creative tasks">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="qwen-qwq-32b">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="cpu"></i>
                      </div>
                      <div class="model-option-name">Qwen</div>
                      <div class="model-info-icon" data-description="Fast reasoning inference, balanced performance">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="gemma2-9b-it">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="brain"></i>
                      </div>
                      <div class="model-option-name">Gemma 2</div>
                      <div class="model-info-icon" data-description="Google's 9B instruction-tuned model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama-3.3-70b-versatile">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="flame"></i>
                      </div>
                      <div class="model-option-name">Llama 3.3 70B</div>
                      <div class="model-info-icon" data-description="Meta's versatile 70B model, 128K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama-3.1-8b-instant">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Llama 3.1 8B</div>
                      <div class="model-info-icon" data-description="Meta's instant 8B model, 128K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama3-70b-8192">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="flame"></i>
                      </div>
                      <div class="model-option-name">Llama 3 70B</div>
                      <div class="model-info-icon" data-description="Meta's 70B model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama3-8b-8192">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Llama 3 8B</div>
                      <div class="model-info-icon" data-description="Meta's 8B model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>

                    <!-- Tooltip container that will be positioned absolutely -->
                    <div id="modelDescriptionTooltip" class="model-description-tooltip" style="display: none;"></div>
                  </div>
                </div>
              </div>
              <button id="sendButton" aria-label="Send message">
                <i data-lucide="arrow-up"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden input for invite link -->
  <input type="text" id="inviteLink" class="hidden-input" readonly aria-hidden="true">

  <!-- Preload rooms data -->
  <script>
    // Preload rooms data to make initial load faster
    const roomsHint = document.querySelector('link[href="/api/live/load/rooms"]');
    if (roomsHint) {
      roomsHint.href = '/api/live/load/rooms';
    }
    
    // Start fetching rooms data immediately
    window.roomsDataPromise = fetch('/api/live/load/rooms', {
      credentials: 'same-origin',
      headers: { 'Accept': 'application/json' }
    }).then(response => response.json());
  </script>

  <!-- Optional: Additional JavaScript -->
  <script src="{{ url_for('static', filename='js/restriction-handler.js') }}"></script>
  <script src="{{ url_for('static', filename='js/live.js') }}" defer></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Ensure smooth animation even if page loads very quickly
      requestAnimationFrame(() => {
        document.body.style.opacity = '1';
      });
    });
  </script>
  <script>
    function handleLogout() {
      window.location.href = "{{ url_for('auth.logout') }}";
    }
  </script>

  <!-- Notification container for alerts -->
  <div id="notification-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md"></div>

  <!-- Restriction Modal -->
  {% include 'includes/restriction_modal.html' %}


</body>
</html>