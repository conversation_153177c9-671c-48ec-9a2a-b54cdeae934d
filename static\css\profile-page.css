/* Profile Page CSS - Vibrant Modern Redesign with Enhanced Dark Mode */

/* Theme Variables */
:root {
    /* Light Theme (Default) - Enhanced Vibrant Palette */
    --light-bg: #f8f9ff;
    --light-content-bg: #ffffff;
    --light-section-bg: #f0f5ff;
    --light-text: #2d3748;
    --light-text-secondary: #718096;
    --light-border: #e2e8f0;
    --light-shadow: rgba(0, 0, 0, 0.05);
    --light-shadow-hover: rgba(0, 0, 0, 0.08);
    --light-header-bg: rgba(255, 255, 255, 0.95);
    --light-stats-bg: rgba(255, 255, 255, 0.85);
    
    /* Dark Theme - Rich Contrast */
    --dark-bg: #0f172a;
    --dark-content-bg: #1a2234;
    --dark-section-bg: #1e293b;
    --dark-text: #f1f5f9;
    --dark-text-secondary: #cbd5e1;
    --dark-border: #334155;
    --dark-shadow: rgba(0, 0, 0, 0.25);
    --dark-shadow-hover: rgba(0, 0, 0, 0.35);
    --dark-header-bg: rgba(26, 34, 52, 0.95);
    --dark-stats-bg: rgba(26, 34, 52, 0.85);
    
    /* Modern Vibrant Color Palette */
    --color-primary: #4f46e5; /* Vibrant indigo */
    --color-primary-light: #818cf8;
    --color-primary-dark: #3730a3;
    --color-secondary: #06b6d4; /* Cyan */
    --color-secondary-light: #67e8f9;
    --color-accent: #f43f5e; /* Rose */
    --color-accent-light: #fb7185;
    --color-success: #10b981; /* Emerald */
    --color-success-light: #34d399;
    --color-warning: #f59e0b; /* Amber */
    --color-warning-light: #fbbf24;
    
    /* Active Theme (defaults to light) */
    --theme-bg: var(--light-bg);
    --theme-content-bg: var(--light-content-bg);
    --theme-section-bg: var(--light-section-bg);
    --theme-text: var(--light-text);
    --theme-text-secondary: var(--light-text-secondary);
    --theme-border: var(--light-border);
    --theme-shadow: var(--light-shadow);
    --theme-shadow-hover: var(--light-shadow-hover);
    --theme-header-bg: var(--light-header-bg);
    --theme-stats-bg: var(--light-stats-bg);
    
    /* Font Variables */
    --font-primary: 'Inter', system-ui, sans-serif;
    --font-heading: 'Space Grotesk', 'Outfit', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    
    /* Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
}

/* Dark Theme Class */
body.dark-theme {
    --theme-bg: var(--dark-bg);
    --theme-content-bg: var(--dark-content-bg);
    --theme-section-bg: var(--dark-section-bg);
    --theme-text: var(--dark-text);
    --theme-text-secondary: var(--dark-text-secondary);
    --theme-border: var(--dark-border);
    --theme-shadow: var(--dark-shadow);
    --theme-shadow-hover: var(--dark-shadow-hover);
    --theme-header-bg: var(--dark-header-bg);
    --theme-stats-bg: var(--dark-stats-bg);
}

/* Base Styles */
body {
    font-family: var(--profile-font, var(--font-primary));
    color: var(--theme-text, var(--profile-text-color, var(--theme-dark)));
    margin: 0;
    padding: 0;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: color 0.3s ease, background-color 0.3s ease;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(79, 70, 229, 0.03) 2%, transparent 0%), 
        radial-gradient(circle at 75px 75px, rgba(6, 182, 212, 0.03) 2%, transparent 0%);
    background-size: 100px 100px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--profile-heading-font, var(--font-heading));
    color: var(--profile-accent-color, var(--color-primary));
    margin-top: 0;
    line-height: 1.2;
    letter-spacing: -0.02em;
    font-weight: 700;
    transition: color 0.3s ease;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 1.75rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1.5rem;
    transition: color 0.3s ease;
    font-size: 1rem;
    line-height: 1.7;
}

a {
    color: var(--color-accent);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: var(--color-accent-light);
}

a:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--color-accent-light);
    transition: width 0.3s ease;
}

a:hover:after {
    width: 100%;
}

/* Container */
.profile-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh; /* Changed from min-height to fixed height */
    padding: var(--space-md) var(--space-md); /* Reduced top/bottom padding */
    box-sizing: border-box;
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(79, 70, 229, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    overflow: hidden; /* Prevent scrolling */
}

/* Profile Card */
.profile-card {
    width: 100%;
    max-width: 1200px;
    height: 95vh; /* Fixed height instead of min-height */
    margin: 0 auto;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 
        0 10px 30px var(--theme-shadow),
        0 6px 12px var(--theme-shadow),
        0 0 0 1px var(--theme-border);
    background-color: var(--profile-container-color, var(--theme-content-bg));
    position: relative;
    display: flex;
    flex-direction: row;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@media (max-width: 992px) {
    .profile-container {
        height: 100vh;
        padding: var(--space-sm);
    }
    
    .profile-card {
        flex-direction: column;
        height: 95vh;
        overflow: hidden;
    }
    
    .profile-header {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid var(--theme-border);
        padding: var(--space-md) var(--space-md);
    }
    
    .profile-content {
        width: 100%;
        height: 60vh;
        padding: var(--space-md);
    }
}

@media (max-width: 576px) {
    .profile-container {
        padding: var(--space-xs);
        height: 100vh;
    }
    
    .profile-card {
        border-radius: 12px;
        height: 95vh;
    }
    
    .profile-header {
        padding: var(--space-sm) var(--space-sm);
        max-height: 35vh;
    }
    
    .profile-content {
        padding: var(--space-sm);
        height: 65vh;
    }
    
    .profile-username {
        font-size: 1.5rem;
    }
    
    .profile-picture-container {
        width: 100px;
        height: 100px;
    }
    
    .profile-section {
        padding: var(--space-sm);
        margin-bottom: var(--space-sm);
    }
}

.profile-card:hover {
    box-shadow: 
        0 15px 35px var(--theme-shadow-hover),
        0 8px 16px var(--theme-shadow-hover),
        0 0 0 1px var(--theme-border);
    transform: translateY(-5px);
}

/* Decorative Elements */
.profile-decoration {
    position: absolute;
    z-index: 0;
    border-radius: 50%;
    filter: blur(40px);
    transition: all 0.8s ease;
}

.profile-decoration-1 {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, var(--color-primary-light) 0%, transparent 70%);
    top: -150px;
    right: -150px;
    opacity: 0.3;
    animation: float-slow 15s ease-in-out infinite alternate;
}

.profile-decoration-2 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, var(--color-secondary-light) 0%, transparent 70%);
    bottom: -100px;
    left: -100px;
    opacity: 0.25;
    animation: float-slow 18s ease-in-out infinite alternate-reverse;
}

.profile-card:hover .profile-decoration-1 {
    transform: translateY(-20px) scale(1.05);
}

.profile-card:hover .profile-decoration-2 {
    transform: translateY(20px) scale(1.05);
}

@keyframes float-slow {
    0% {
        transform: translateY(0) scale(1);
    }
    100% {
        transform: translateY(-30px) scale(1.1);
    }
}

/* Banner */
.profile-banner {
    height: 100%;
    width: 100%;
    background-color: var(--profile-accent-color, var(--color-primary));
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: 0;
    transition: background 0.5s ease-in-out;
    animation: gradientAnimation 15s ease infinite;
}

.profile-banner::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at top right, rgba(255,255,255,0.4), transparent 70%),
        radial-gradient(circle at bottom left, rgba(255,255,255,0.3), transparent 60%);
    z-index: 1;
    opacity: 0.8;
    mix-blend-mode: overlay;
}

.profile-banner::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.2)),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
    z-index: 1;
    opacity: 0.6;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Profile Header */
.profile-header {
    padding: var(--space-lg) var(--space-md); /* Reduced padding */
    background-color: var(--profile-header-color, var(--theme-header-bg));
    position: relative;
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid var(--theme-border);
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: inset -5px 0 15px rgba(0, 0, 0, 0.03);
    overflow-y: auto; /* Allow scrolling if needed */
    height: 100%; /* Take full height */
}

.profile-content {
    flex: 1;
    padding: var(--space-lg); /* Reduced padding */
    display: flex;
    flex-direction: column;
    gap: var(--space-md); /* Reduced gap */
    position: relative;
    z-index: 1;
    background-color: var(--profile-content-color, var(--theme-content-bg));
    overflow-y: auto; /* Allow scrolling within this section */
    height: 100%; /* Take full height */
}

@media (max-width: 992px) {
    .profile-header {
        width: 100%;
        padding: var(--space-xl) var(--space-lg);
    }
}

@media (max-width: 576px) {
    .profile-header {
        padding: var(--space-lg) var(--space-md);
    }
    
    .profile-content {
        padding: var(--space-lg) var(--space-md);
        gap: var(--space-lg);
    }
}

/* Profile Picture */
.profile-picture-wrapper {
    position: relative;
    margin-bottom: var(--space-md); /* Reduced margin */
}

.profile-picture-wrapper::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: linear-gradient(135deg, var(--color-primary-light), var(--color-secondary));
    border-radius: 30px;
    top: -10px;
    left: -10px;
    z-index: 5;
    opacity: 0.5;
    filter: blur(15px);
    animation: pulse-slow 3s ease-in-out infinite alternate;
}

@keyframes pulse-slow {
    0% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

.profile-picture-container {
    position: relative;
    width: 140px; /* Reduced size */
    height: 140px; /* Reduced size */
    z-index: 10;
    border-radius: 30px;
    padding: 5px;
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.15),
        0 6px 10px rgba(0, 0, 0, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: rotate(-3deg);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.profile-picture-container:hover {
    transform: rotate(0deg) scale(1.08);
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.2),
        0 8px 15px rgba(0, 0, 0, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.profile-picture {
    width: 100%;
    height: 100%;
    border-radius: 25px;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    filter: contrast(1.05) saturate(1.1);
}

.profile-picture-container:hover .profile-picture {
    border-color: white;
    transform: scale(1.02);
}

/* Profile Identity */
.profile-identity {
    text-align: center;
    margin-bottom: var(--space-md); /* Reduced margin */
    position: relative;
    width: 100%;
}

.profile-username {
    font-size: 2rem; /* Smaller font */
    margin: 0 0 var(--space-xs) 0; /* Reduced margin */
    color: var(--color-primary);
    font-weight: 800;
    letter-spacing: -0.03em;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.profile-username::after {
    content: '';
    position: absolute;
    width: 40%;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    bottom: -8px;
    left: 30%;
    border-radius: 2px;
    opacity: 0.7;
}

.profile-tag {
    font-size: 0.9rem; /* Smaller font */
    color: var(--color-accent);
    font-weight: 600;
    background: rgba(244, 63, 94, 0.08);
    padding: 0.3rem 0.8rem; /* Reduced padding */
    border-radius: 20px; /* Smaller radius */
    display: inline-block;
    margin: var(--space-sm) 0; /* Reduced margin */
    letter-spacing: 0.5px;
    box-shadow: 
        0 2px 10px rgba(244, 63, 94, 0.1),
        inset 0 0 0 1px rgba(244, 63, 94, 0.2);
    transition: all 0.3s ease;
}

.profile-tag:hover {
    background: rgba(244, 63, 94, 0.12);
    transform: translateY(-2px);
    box-shadow: 
        0 4px 12px rgba(244, 63, 94, 0.15),
        inset 0 0 0 1px rgba(244, 63, 94, 0.3);
}

/* Status */
.profile-status-container {
    margin: var(--space-sm) 0; /* Reduced margin */
}

.profile-status {
    display: inline-flex;
    align-items: center;
    padding: 0.3rem 0.8rem; /* Reduced padding */
    border-radius: 20px; /* Smaller radius */
    background-color: rgba(16, 185, 129, 0.1);
    font-size: 0.85rem; /* Smaller font */
    color: var(--color-success);
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 
        0 2px 5px rgba(16, 185, 129, 0.1), /* Reduced shadow */
        inset 0 0 0 1px rgba(16, 185, 129, 0.2);
    transition: all 0.3s ease;
}

.profile-status:hover {
    background-color: rgba(16, 185, 129, 0.15);
    transform: translateY(-2px);
    box-shadow: 
        0 5px 15px rgba(16, 185, 129, 0.15),
        inset 0 0 0 1px rgba(16, 185, 129, 0.3);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
}

/* Online state */
.status-dot.online {
    background-color: var(--color-success);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-dot.online::after {
    content: '';
    position: absolute;
    width: 18px;
    height: 18px;
    background-color: rgba(16, 185, 129, 0.3);
    border-radius: 50%;
    top: -4px;
    left: -4px;
    animation: pulse 2s infinite;
}

/* Offline state */
.status-dot.offline {
    background-color: var(--theme-gray);
    box-shadow: 0 0 5px rgba(100, 116, 139, 0.3);
}

.status-dot.offline::after {
    content: '';
    position: absolute;
    width: 18px;
    height: 18px;
    background-color: rgba(100, 116, 139, 0.2);
    border-radius: 50%;
    top: -4px;
    left: -4px;
}

/* Loading state */
.status-dot.loading {
    background-color: var(--color-warning);
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
}

.status-dot.loading::after {
    content: '';
    position: absolute;
    width: 18px;
    height: 18px;
    background-color: rgba(245, 158, 11, 0.2);
    border-radius: 50%;
    top: -4px;
    left: -4px;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Actions */
.profile-actions {
    display: flex;
    gap: var(--space-md);
    margin: var(--space-xl) 0;
    justify-content: center;
    width: 100%;
}

.profile-button {
    background: linear-gradient(135deg, var(--color-accent), var(--color-accent-light));
    color: white;
    border: none;
    padding: 0.85rem 1.8rem;
    border-radius: 30px;
    cursor: pointer;
    font-family: var(--font-primary);
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    box-shadow: 
        0 5px 15px rgba(244, 63, 94, 0.25),
        0 3px 5px rgba(244, 63, 94, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.profile-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-accent-light), var(--color-accent));
    opacity: 0;
    z-index: -1;
    transition: opacity 0.4s ease;
}

.profile-button:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 
        0 8px 25px rgba(244, 63, 94, 0.35),
        0 5px 10px rgba(244, 63, 94, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.profile-button:hover::before {
    opacity: 1;
}

.profile-button:active {
    transform: translateY(-2px) scale(1.01);
}

.button-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.profile-button:hover .button-icon {
    transform: rotate(15deg) scale(1.2);
}

/* Social Links */
.social-links {
    display: flex;
    gap: var(--space-md);
    margin: var(--space-xl) 0;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .social-links {
        gap: var(--space-sm);
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
        border-radius: 12px;
    }
}

.social-link {
    color: var(--color-primary);
    font-size: 1.5rem;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    background-color: rgba(79, 70, 229, 0.08);
    box-shadow: 
        0 3px 10px rgba(0, 0, 0, 0.05),
        inset 0 0 0 1px rgba(79, 70, 229, 0.2);
    position: relative;
    overflow: hidden;
}

.social-link-tooltip {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 100;
    pointer-events: none;
}

.social-link-tooltip::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 5px 5px 5px;
    border-style: solid;
    border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
}

.social-link:hover .social-link-tooltip {
    opacity: 1;
    visibility: visible;
    bottom: -30px;
}

.social-link::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-link i {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.social-link:hover {
    color: white;
    transform: translateY(-5px) rotate(10deg) scale(1.1);
    box-shadow: 
        0 8px 20px rgba(79, 70, 229, 0.25),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.social-link:hover::before {
    opacity: 1;
}

.social-link:hover i {
    transform: scale(1.2);
}

/* Stats */
.profile-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm); /* Increased gap */
    margin-top: var(--space-sm); /* Increased margin */
    padding: var(--space-xs) var(--space-sm); /* Increased padding */
    background-color: var(--theme-stats-bg);
    border-radius: 16px; /* Increased radius */
    box-shadow: 
        0 3px 10px var(--theme-shadow),
        0 1px 3px var(--theme-shadow),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    width: auto; /* Auto width to fit content */
    max-width: 90%; /* Ensure it doesn't overflow */
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: visible; /* Allow content to be visible */
    box-sizing: border-box; /* Include padding in width calculation */
    margin-bottom: var(--space-sm); /* Increased bottom margin */
}

.profile-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(6, 182, 212, 0.05));
    opacity: 0;
    transition: opacity 0.4s ease;
}

.profile-stats:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 8px 30px var(--theme-shadow-hover),
        0 4px 10px var(--theme-shadow-hover),
        inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.profile-stats:hover::before {
    opacity: 1;
}

.stat-item {
    text-align: center;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
    padding: var(--space-xxs) var(--space-xs); /* Add minimal padding */
}

.stat-item:hover {
    transform: translateY(-3px);
}

.stat-value {
    font-size: 1.1rem; /* Increased font size */
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--space-xs); /* Increased margin */
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    white-space: nowrap; /* Prevent wrapping */
    line-height: 1.2; /* Increased line height */
}

.stat-value::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.stat-item:hover .stat-value::after {
    width: 80%;
}

.stat-label {
    font-size: 0.75rem; /* Increased font size */
    color: var(--theme-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.8px; /* Increased letter spacing */
    font-weight: 600;
    transition: color 0.3s ease;
    white-space: nowrap; /* Prevent wrapping */
    line-height: 1.2; /* Increased line height */
}

.stat-divider {
    width: 1px;
    height: 25px; /* Increased height */
    background: linear-gradient(to bottom, var(--color-primary-light), transparent);
    border-radius: 1px;
    opacity: 0.4; /* Increased opacity */
    margin: 0 var(--space-xs); /* Increased margin */
}

/* Content Area */
.profile-content {
    padding: var(--space-2xl);
    background-color: var(--theme-content-bg);
    width: 70%;
    overflow-y: auto;
    z-index: 1;
    position: relative;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    background-image: 
        radial-gradient(circle at 90% 10%, rgba(79, 70, 229, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 10% 90%, rgba(6, 182, 212, 0.03) 0%, transparent 30%);
}

/* Sections */
.profile-section {
    background-color: var(--profile-about-color, var(--theme-section-bg));
    border-radius: 16px; /* Smaller radius */
    padding: var(--space-md); /* Reduced padding */
    margin-bottom: var(--space-md); /* Reduced margin */
    box-shadow: 
        0 4px 15px var(--theme-shadow),
        0 2px 5px var(--theme-shadow),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;
}

.profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.03), rgba(6, 182, 212, 0.03));
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.profile-section:hover {
    transform: translateY(-8px);
    box-shadow: 
        0 15px 40px var(--theme-shadow-hover),
        0 8px 20px var(--theme-shadow-hover),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Friends Section */
.friends-section {
    background-color: var(--profile-friends-color, var(--theme-section-bg));
}

.profile-section:hover::before {
    opacity: 1;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-sm); /* Reduced margin */
    position: relative;
    padding-bottom: var(--space-xs); /* Reduced padding */
    border-bottom: 1px solid rgba(79, 70, 229, 0.1);
}

.profile-section-title {
    font-size: 1.2rem; /* Smaller font */
    text-transform: uppercase;
    letter-spacing: 1px; /* Reduced letter spacing */
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    font-weight: 800;
    margin: 0;
    position: relative;
    z-index: 1;
}

.section-decoration {
    position: absolute;
    height: 10px;
    width: 100%;
    bottom: -5px;
    left: 0;
    background: linear-gradient(90deg, var(--color-accent), transparent);
    opacity: 0.15;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.section-header:hover .section-decoration {
    opacity: 0.25;
    width: 120%;
}

.section-content {
    position: relative;
    z-index: 1;
    font-size: 1.05rem;
    line-height: 1.7;
}

/* About Section */
.about-section {
    border-top: 5px solid var(--color-accent);
    border-image: linear-gradient(to right, var(--color-accent), transparent) 1;
}

/* Friends Section */
.friends-section {
    border-top: 5px solid var(--color-primary);
    border-image: linear-gradient(to right, var(--color-primary), transparent) 1;
}

.friends-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-2xl) 0;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(79, 70, 229, 0.1);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) infinite;
    margin-bottom: var(--space-md);
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.1);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Custom Section */
.custom-section {
    border-top: 5px solid var(--color-secondary);
    border-image: linear-gradient(to right, var(--color-secondary), transparent) 1;
}

/* Badges */
.badge-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-md);
    margin-top: var(--space-xl);
}

.badge {
    background-color: rgba(79, 70, 229, 0.08);
    border-radius: 30px;
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    color: var(--color-primary);
    font-weight: 600;
    border: none;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 
        0 3px 10px rgba(0, 0, 0, 0.03),
        inset 0 0 0 1px rgba(79, 70, 229, 0.15);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.badge:hover {
    color: white;
    transform: translateY(-5px) scale(1.05);
    box-shadow: 
        0 8px 20px rgba(79, 70, 229, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.badge:hover::before {
    opacity: 1;
}

/* Friends Grid */
.friends-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: var(--space-lg);
    margin-top: var(--space-lg);
}

.friend-item {
    text-align: center;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
}

.friend-item::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.1), transparent 70%);
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    transform: scale(0.8);
    border-radius: 50%;
}

.friend-item:hover {
    transform: translateY(-8px);
}

.friend-item:hover::before {
    opacity: 1;
    transform: scale(1.2);
}

.friend-avatar-container {
    position: relative;
    margin-bottom: var(--space-sm);
}

.friend-avatar {
    width: 70px;
    height: 70px;
    border-radius: 18px;
    object-fit: cover;
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.05),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 3px solid white;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    filter: contrast(1.05);
}

.friend-status-dot {
    position: absolute;
    bottom: 3px;
    right: 3px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 2;
}

.friend-status-dot.online {
    background-color: var(--color-success);
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.friend-status-dot.offline {
    background-color: var(--theme-gray);
    box-shadow: 0 0 3px rgba(100, 116, 139, 0.3);
}

.friend-status-dot.loading {
    background-color: var(--color-warning);
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
    animation: pulse 1s infinite;
}

.friend-item:hover .friend-avatar {
    border-color: var(--color-primary);
    transform: rotate(8deg) scale(1.1);
    box-shadow: 
        0 12px 30px rgba(79, 70, 229, 0.2),
        0 6px 12px rgba(79, 70, 229, 0.1),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.friend-item:hover .friend-status-dot {
    transform: scale(1.2);
}

.friend-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.friend-name {
    font-size: 0.95rem;
    color: var(--theme-text);
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 0.3rem 0.5rem 0.1rem;
    border-radius: 8px;
}

.friend-username {
    font-size: 0.8rem;
    color: var(--theme-gray);
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.friend-item:hover .friend-name {
    color: var(--color-accent);
    background-color: rgba(244, 63, 94, 0.05);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .profile-card {
        max-width: 95%;
    }
    
    .profile-username {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .profile-card {
        flex-direction: column;
        max-width: 800px;
        min-height: auto;
    }
    
    .profile-header {
        width: 100%;
        padding: var(--space-xl) var(--space-lg);
        border-right: none;
        border-bottom: 1px solid var(--theme-border);
    }
    
    .profile-content {
        width: 100%;
        padding: var(--space-xl) var(--space-lg);
    }
    
    .profile-container {
        padding: var(--space-md) var(--space-sm);
    }
    
    .profile-stats {
        width: auto;
        max-width: 240px;
        margin-bottom: var(--space-sm);
        padding: var(--space-xs) var(--space-sm);
        gap: var(--space-sm);
    }
    
    .profile-picture-wrapper::before {
        width: 140px;
        height: 140px;
    }
    
    .profile-picture-container {
        width: 140px;
        height: 140px;
    }
    
    .friends-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

@media (max-width: 768px) {
    .profile-card {
        border-radius: 20px;
    }
    
    .profile-stats {
        width: auto;
        max-width: 220px;
        padding: var(--space-xs) var(--space-sm);
        gap: var(--space-xs);
    }
    
    .profile-section {
        padding: var(--space-lg);
        margin-bottom: var(--space-xl);
    }
    
    .profile-username {
        font-size: 2rem;
    }
    
    .profile-tag {
        font-size: 1rem;
    }
    
    .badge {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .profile-decoration-1,
    .profile-decoration-2 {
        opacity: 0.2;
    }
}

@media (max-width: 576px) {
    :root {
        --space-2xl: 2rem;
        --space-xl: 1.5rem;
        --space-lg: 1.25rem;
        --space-md: 0.75rem;
    }
    
    .profile-container {
        padding: 0;
    }
    
    .profile-card {
        border-radius: 0;
        box-shadow: none;
        max-width: 100%;
    }
    
    .theme-toggle {
        top: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
    }
    
    .profile-header {
        padding: var(--space-lg) var(--space-md);
    }
    
    .profile-username {
        font-size: 1.8rem;
    }
    
    .profile-username::after {
        height: 3px;
        bottom: -6px;
    }
    
    .profile-tag {
        font-size: 0.9rem;
        padding: 0.4rem 1rem;
    }
    
    .profile-picture-wrapper::before {
        width: 120px;
        height: 120px;
    }
    
    .profile-picture-container {
        width: 120px;
        height: 120px;
    }
    
    .profile-content {
        padding: var(--space-lg) var(--space-md);
    }
    
    .profile-section {
        padding: var(--space-lg) var(--space-md);
        margin-bottom: var(--space-lg);
        border-radius: 16px;
    }
    
    .profile-section-title {
        font-size: 1.2rem;
        letter-spacing: 1px;
    }
    
    .section-content {
        font-size: 1rem;
    }
    
    .badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .profile-button {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .friends-grid {
        grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
        gap: var(--space-md);
    }
    
    .friend-avatar {
        width: 55px;
        height: 55px;
    }
    
    .friend-name {
        font-size: 0.85rem;
    }
    
    .profile-stats {
        width: auto;
        max-width: 200px;
        padding: var(--space-xs) var(--space-sm);
        gap: var(--space-xs);
    }
    
    .stat-value {
        font-size: 1rem;
        margin-bottom: var(--space-xs);
        line-height: 1.2;
    }
    
    .stat-label {
        font-size: 0.7rem;
        letter-spacing: 0.6px;
        line-height: 1.2;
    }
    
    .stat-divider {
        height: 22px;
    }
    
    /* Reduce animations on mobile for better performance */
    .profile-card:hover {
        transform: none;
    }
    
    .profile-section:hover {
        transform: translateY(-3px);
    }
    
    .badge:hover,
    .friend-item:hover,
    .social-link:hover,
    .profile-button:hover {
        transform: translateY(-3px);
    }
}

/* Theme Toggle Button - Removed as per requirements */
/* 
.theme-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--theme-section-bg);
    border: 2px solid var(--theme-border);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 
        0 5px 15px var(--theme-shadow),
        0 2px 5px var(--theme-shadow),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-primary-light), var(--color-secondary-light));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.theme-toggle:hover {
    transform: translateY(-5px) rotate(10deg);
    box-shadow: 
        0 8px 25px var(--theme-shadow-hover),
        0 4px 10px var(--theme-shadow-hover),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.theme-toggle:hover::before {
    opacity: 0.1;
}

.theme-toggle:active {
    transform: translateY(-2px) rotate(5deg);
}

.theme-toggle-icon {
    font-size: 1.3rem;
    color: var(--color-primary);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    z-index: 1;
}

.theme-toggle .sun-icon,
.theme-toggle .moon-icon {
    position: absolute;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}
*/

/* Theme toggle icons - Removed as per requirements */
/*
body:not(.dark-theme) .sun-icon {
    opacity: 1;
    transform: translateY(0) rotate(0) scale(1);
    color: var(--color-warning);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

body:not(.dark-theme) .moon-icon {
    opacity: 0;
    transform: translateY(25px) rotate(-90deg) scale(0.5);
}

body.dark-theme .sun-icon {
    opacity: 0;
    transform: translateY(-25px) rotate(90deg) scale(0.5);
}

body.dark-theme .moon-icon {
    opacity: 1;
    transform: translateY(0) rotate(0) scale(1);
    color: var(--color-secondary);
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}
*/

/* Dark Theme Specific Adjustments */
body.dark-theme .profile-picture {
    border-color: rgba(255, 255, 255, 0.2);
    filter: contrast(1.1) brightness(1.05);
}

body.dark-theme .friend-avatar {
    border-color: rgba(255, 255, 255, 0.2);
    filter: contrast(1.1) brightness(1.05);
}

body.dark-theme .profile-section {
    box-shadow: 
        0 8px 30px rgba(0, 0, 0, 0.3),
        0 4px 10px rgba(0, 0, 0, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

body.dark-theme .badge {
    background-color: rgba(79, 70, 229, 0.15);
    box-shadow: 
        0 3px 10px rgba(0, 0, 0, 0.1),
        inset 0 0 0 1px rgba(79, 70, 229, 0.25);
}

body.dark-theme .profile-stats {
    box-shadow: 
        0 5px 20px rgba(0, 0, 0, 0.3),
        0 2px 5px rgba(0, 0, 0, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

body.dark-theme .social-link {
    background-color: rgba(79, 70, 229, 0.15);
    box-shadow: 
        0 3px 10px rgba(0, 0, 0, 0.1),
        inset 0 0 0 1px rgba(79, 70, 229, 0.25);
}

body.dark-theme .profile-tag {
    background: rgba(244, 63, 94, 0.15);
    box-shadow: 
        0 2px 10px rgba(244, 63, 94, 0.15),
        inset 0 0 0 1px rgba(244, 63, 94, 0.3);
}

body.dark-theme .profile-status {
    background-color: rgba(16, 185, 129, 0.15);
    box-shadow: 
        0 3px 10px rgba(16, 185, 129, 0.15),
        inset 0 0 0 1px rgba(16, 185, 129, 0.3);
}

/* Transition all elements smoothly */
*, *::before, *::after {
    transition-property: background-color, border-color, color, box-shadow, transform, opacity, filter;
    transition-duration: 0.3s;
    transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
}