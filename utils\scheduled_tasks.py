"""
Scheduled tasks for the application
"""
import threading
import time
import logging
import schedule
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_scheduler():
    """Run the scheduler in a separate thread"""
    # No scheduled tasks currently
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute

def start_scheduler():
    """Start the scheduler in a background thread"""
    scheduler_thread = threading.Thread(target=run_scheduler)
    scheduler_thread.daemon = True  # Thread will exit when main thread exits
    scheduler_thread.start()
    logger.info("Scheduler started in background thread")
