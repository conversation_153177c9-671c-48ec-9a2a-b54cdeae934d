from mongoengine import Document, ReferenceField, IntField, DateTimeField, DictField
from datetime import datetime, timezone
import logging
from models.user import User

# Configure logging
logger = logging.getLogger(__name__)

class UserCredit(Document):
    """
    Model for tracking user credits for AI model usage
    """
    user = ReferenceField(User, required=True, unique=True)
    balance = IntField(default=100)  # Current credit balance
    total_earned = IntField(default=100)  # Total credits earned (including initial credits)
    last_updated = DateTimeField(default=lambda: datetime.now(timezone.utc))
    
    meta = {
        'collection': 'user_credits',
        'indexes': [
            'user',
            'balance'
        ]
    }
    
    @classmethod
    def get_or_create(cls, user_id):
        """
        Get existing user credit record or create a new one with default values
        """
        try:
            logger.info(f"Getting or creating user credit for user_id: {user_id}")
            user_credit = cls.objects(user=user_id).first()
            if not user_credit:
                logger.info(f"Creating new user credit for user_id: {user_id}")
                user_credit = cls(
                    user=user_id,
                    balance=100,
                    total_earned=100
                )
                user_credit.save()
                logger.info(f"New user credit created for user_id: {user_id}")
            return user_credit
        except Exception as e:
            logger.error(f"Error getting or creating user credit: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    @classmethod
    def deduct_credits(cls, user_id, amount):
        """
        Deduct credits from user's balance
        Returns True if successful, False if insufficient credits
        """
        try:
            logger.info(f"Deducting {amount} credits from user_id: {user_id}")
            user_credit = cls.get_or_create(user_id)
            if not user_credit:
                logger.error(f"Failed to get or create user credit for user_id: {user_id}")
                return False
                
            # Check if user has enough credits
            if user_credit.balance < amount:
                logger.warning(f"Insufficient credits for user_id: {user_id}. Balance: {user_credit.balance}, Amount: {amount}")
                return False
                
            # Deduct credits
            user_credit.balance -= amount
            user_credit.last_updated = datetime.now(timezone.utc)
            user_credit.save()
            logger.info(f"Successfully deducted {amount} credits from user_id: {user_id}. New balance: {user_credit.balance}")
            return True
        except Exception as e:
            logger.error(f"Error deducting credits: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    @classmethod
    def add_credits(cls, user_id, amount):
        """
        Add credits to user's balance
        """
        try:
            logger.info(f"Adding {amount} credits to user_id: {user_id}")
            user_credit = cls.get_or_create(user_id)
            if not user_credit:
                logger.error(f"Failed to get or create user credit for user_id: {user_id}")
                return False
                
            # Add credits
            user_credit.balance += amount
            user_credit.total_earned += amount
            user_credit.last_updated = datetime.now(timezone.utc)
            user_credit.save()
            logger.info(f"Successfully added {amount} credits to user_id: {user_id}. New balance: {user_credit.balance}")
            return True
        except Exception as e:
            logger.error(f"Error adding credits: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
