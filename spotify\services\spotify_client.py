from flask import session, current_app, request
import spotipy
from spotipy.oauth2 import SpotifyOAuth
import os
import logging
from flask_login import current_user
from datetime import datetime
from models.spotify_credentials import SpotifyCredentials

def create_spotify_oauth():
    host = request.host

    # Force HTTPS for production domain
    if 'onrender.com' in host:
        protocol = 'https'
    elif request.is_secure:
        protocol = 'https'
    else:
        protocol = 'http'

    redirect_uri = f'{protocol}://{host}/spotify/callback'

    return SpotifyOAuth(
        client_id=os.getenv('CLIENT_ID'),
        client_secret=os.getenv('CLIENT_SECRET'),
        redirect_uri=redirect_uri,
        scope=" ".join([
            "streaming",
            "user-read-email",
            "user-read-private",
            "user-read-playback-state",
            "user-modify-playback-state",
            "user-read-currently-playing",
            "app-remote-control",
            "playlist-read-private",
            "user-library-read"
        ]),
        show_dialog=True,  # This forces the account selection dialog
        cache_handler=spotipy.cache_handler.FlaskSessionCacheHandler(session)
    )

def get_spotify_client():
    sp_oauth = create_spotify_oauth()
    token_info = session.get('token_info')

    # If user is logged in, try to get credentials from database
    if current_user.is_authenticated and not token_info:
        credentials = SpotifyCredentials.objects(user=current_user.id).first()
        if credentials:
            token_info = credentials.to_token_info()
            session['token_info'] = token_info

    if not token_info:
        raise Exception('Not authenticated')

    if sp_oauth.is_token_expired(token_info):
        token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
        session['token_info'] = token_info

        # Update credentials in database if user is logged in
        if current_user.is_authenticated:
            SpotifyCredentials.from_token_info(current_user.id, token_info)

    return spotipy.Spotify(auth_manager=sp_oauth)

def save_spotify_credentials(token_info):
    """Save Spotify credentials to database for logged-in user"""
    if current_user.is_authenticated:
        logging.info(f"Saving Spotify credentials for user: {current_user.id}")
        try:
            credentials = SpotifyCredentials.from_token_info(current_user.id, token_info)
            logging.info(f"Spotify credentials saved successfully: {credentials.id}")
            return True
        except Exception as e:
            logging.error(f"Error saving Spotify credentials: {str(e)}")
            return False
    else:
        logging.warning("Cannot save Spotify credentials: User not authenticated")
        return False

