"""
User activity API routes
"""
from flask import jsonify, request
from flask_login import current_user, login_required
from models.user import User
from models.user_activity import UserActivity
from models.constants import VALID_ACTIVITY_SECTIONS, DEFAULT_ACTIVITY_SECTION
from api.activity import activity_api
from datetime import datetime, timedelta
import logging

@activity_api.route('/update', methods=['POST'])
@login_required
def update_user_activity():
    """Update the current user's activity status
    
    This endpoint should be called periodically by the client to maintain online status
    """
    # Get the section from the request (default to DEFAULT_ACTIVITY_SECTION)
    data = request.get_json() or {}
    section = data.get('section', DEFAULT_ACTIVITY_SECTION)
    
    # Validate section is in allowed choices
    if section not in VALID_ACTIVITY_SECTIONS:
        return jsonify({
            'success': False,
            'message': f'Invalid section. Must be one of: {", ".join(VALID_ACTIVITY_SECTIONS)}'
        }), 400
    
    try:
        # Update the user's activity
        UserActivity.update_activity(current_user.id, section)
        
        return jsonify({
            'success': True,
            'message': 'Activity updated successfully'
        })
    except Exception as e:
        # Log the error
        logging.error(f"Error updating activity: {str(e)}")
        
        return jsonify({
            'success': False,
            'message': 'Failed to update activity',
            'error': str(e)
        }), 500

@activity_api.route('/user/<username>', methods=['GET'])
def get_user_activity(username):
    """Get a user's recent activity"""
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Check if the profile is private and not the current user
    from models.profile_customization import ProfileCustomization
    profile = ProfileCustomization.get_or_create(user)
    
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({'success': False, 'error': 'Profile is private'}), 403
    
    # Get user activity (limit to 10 most recent)
    activities = UserActivity.objects(user=user).order_by('-last_active').limit(10)
    
    # Format activities for response
    activity_list = []
    for activity in activities:
        activity_list.append({
            'timestamp': activity.last_active.isoformat(),
            'activity_type': activity.section,
            'description': f"Active in {activity.section}",
            'metadata': {'section': activity.section}
        })
    
    return jsonify({
        'success': True,
        'activities': activity_list
    })

@activity_api.route('/online-status/<username>', methods=['GET'])
def get_online_status(username):
    """Get a user's online status
    
    A user is considered online if they have any activity in the last 30 seconds
    """
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Check if the profile is private and not the current user
    from models.profile_customization import ProfileCustomization
    profile = ProfileCustomization.get_or_create(user)
    
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({'success': False, 'error': 'Profile is private'}), 403
    
    # Get the most recent activity across all sections
    cutoff_time = datetime.utcnow() - timedelta(seconds=30)
    most_recent = UserActivity.objects(user=user, last_active__gte=cutoff_time).first()
    
    is_online = most_recent is not None
    
    return jsonify({
        'success': True,
        'username': username,
        'is_online': is_online,
        'last_active': most_recent.last_active.isoformat() if most_recent else None
    })

@activity_api.route('/active-users/<section>', methods=['GET'])
@login_required
def get_active_users(section):
    """Get users active in a specific section"""
    # Validate section
    if section not in VALID_ACTIVITY_SECTIONS:
        return jsonify({
            'success': False,
            'message': f'Invalid section. Must be one of: {", ".join(VALID_ACTIVITY_SECTIONS)}'
        }), 400
    
    # Get minutes parameter (default to 15)
    minutes = request.args.get('minutes', 15, type=int)
    
    try:
        # Get active users
        activities = UserActivity.get_active_users(section, minutes)
        
        # Format users for response
        users = []
        for activity in activities:
            try:
                user_data = activity.to_dict()
                users.append(user_data)
            except Exception as e:
                logging.error(f"Error formatting user data: {str(e)}")
        
        return jsonify({
            'success': True,
            'section': section,
            'minutes': minutes,
            'users': users
        })
    except Exception as e:
        logging.error(f"Error getting active users: {str(e)}")
        
        return jsonify({
            'success': False,
            'message': 'Failed to get active users',
            'error': str(e)
        }), 500