from flask import request
from flask_socketio import emit, join_room, leave_room
from flask_login import current_user
from models.room import Room
from services.chat import ChatService
from models.api_log import APILog
from models.model_usage import ModelUsage
from models.feature_restriction import FeatureRestriction
import json
import logging
import time

# Initialize chat service
chat_service = ChatService()

# This will be initialized in app.py
socketio = None

def init_socketio(socket_instance):
    """Initialize the socketio instance"""
    global socketio
    socketio = socket_instance
    register_handlers()

def register_handlers():
    """Register all socket event handlers"""
    @socketio.on('connect')
    def handle_connect():
        if not current_user.is_authenticated:
            return False  # Reject connection if user is not authenticated
        logging.info(f"User {current_user.username} connected")

    @socketio.on('disconnect')
    def handle_disconnect():
        logging.info(f"User {current_user.username} disconnected")

    @socketio.on('join_room')
    def handle_join_room(data):
        """Handle a user joining a room"""
        room_id = data.get('room_id')
        if not room_id:
            emit('error', {'message': 'Room ID is required'})
            return

        # Join the Socket.IO room
        join_room(room_id)
        logging.info(f"User {current_user.username} joined room {room_id}")

        # Log the join action in the API logs
        try:
            # Get room information to include creator details
            room = Room.objects(room_id=room_id).first()
            details = {
                'room_id': room_id
            }

            # If room exists and has a creator, include creator information
            if room and room.creator:
                details['creator_id'] = str(room.creator.id)
                details['creator_name'] = room.creator.username
                if hasattr(room.creator, 'profile_picture') and room.creator.profile_picture:
                    details['creator_profile_picture'] = room.creator.profile_picture

            APILog.log_action(
                user=current_user,
                action='live_join',
                service='live',
                details=details
            )
            logging.info(f"Logged live join for user {current_user.username} in room {room_id} with creator details")
        except Exception as log_error:
            logging.error(f"Error logging live join: {str(log_error)}")

        # Notify other users in the room
        emit('user_joined', {
            'user_id': str(current_user.id),
            'username': current_user.username
        }, room=room_id, include_self=False)

    @socketio.on('leave_room')
    def handle_leave_room(data):
        """Handle a user leaving a room"""
        room_id = data.get('room_id')
        if not room_id:
            emit('error', {'message': 'Room ID is required'})
            return

        # First, notify other users in the room before leaving
        emit('user_left', {
            'user_id': str(current_user.id),
            'username': current_user.username
        }, room=room_id, include_self=False)

        # Then leave the Socket.IO room
        leave_room(room_id)
        logging.info(f"User {current_user.username} left room {room_id}")

    @socketio.on('send_message')
    def handle_send_message(data):
        """Handle a user sending a message"""
        room_id = data.get('room_id')
        message = data.get('message', '')
        selected_model = data.get('model', 'gpt-4o-mini')
        images = data.get('images', [])

        # If images are present, force Gemini model
        if images:
            selected_model = 'gemini-2.0-flash'
            logging.info(f"Images detected, forcing Gemini model for user {current_user.username} in room {room_id}")

        logging.info(f"Received message from {current_user.username} in room {room_id}: {message[:30]}... with {len(images)} images")

        if not room_id:
            emit('error', {'message': 'Room ID is required'})
            return

        if not message and not images:
            emit('error', {'message': 'Message or image is required'})
            return

        # Get the room from the database
        room = Room.objects(room_id=room_id).first()
        if not room:
            emit('error', {'message': 'Room not found'})
            return

        # Check if user is allowed to send messages in this room
        if not room.is_member(current_user):
            emit('error', {'message': 'Access denied'})
            return

        # Check if user has reached their message limit
        if not FeatureRestriction.can_add_live_message(current_user.id, room_id):
            emit('error', {'message': 'You have reached the maximum number of messages allowed in this live room.', 'feature_restricted': True})
            return
            
        # Check user credit balance before processing
        from models.user_credit import UserCredit
        from models.model_credit_cost import ModelCreditCost
        
        user_credit = UserCredit.get_or_create(current_user.id)
        
        # Get the credit cost for the selected model
        credit_cost = ModelCreditCost.get_cost(selected_model)
        
        # Check if user has sufficient credits for this model
        if user_credit:
            if user_credit.balance == 0:
                # If user has zero credits, return error with a specific message
                emit('error', {
                    'message': 'You\'ve run out of credits. Please add more credits to continue using AI features.',
                    'insufficient_credits': True,
                    'zero_balance': True
                })
                return
            elif user_credit.balance < credit_cost:
                # If user has credits but not enough for this model, return error
                emit('error', {
                    'message': f'You have insufficient credits to use this model. Please add more credits or use a different model.',
                    'insufficient_credits': True,
                    'balance': user_credit.balance,
                    'cost': credit_cost
                })
                return

        # Add user message to room
        try:
            # Log the interaction in the API logs
            try:
                APILog.log_action(
                    user=current_user,
                    action='live_interaction',
                    service='live',
                    details={
                        'room_id': room_id,
                        'has_message': True,
                        'has_images': len(images) > 0,
                        'image_count': len(images),
                        'model': selected_model
                    }
                )
                logging.info(f"Logged live interaction for user {current_user.username} in room {room_id}")
            except Exception as log_error:
                logging.error(f"Error logging live interaction: {str(log_error)}")

            user_message = room.add_message(current_user, message, images=images)
            logging.info(f"Added message to room {room_id} and broadcasting to all users")

            # Broadcast the message to all users in the room
            emit('new_message', user_message, room=room_id)

            # Process AI response
            logging.info(f"Starting AI response generation with model {selected_model}")
            process_ai_response(room, current_user, selected_model, images)

        except Exception as e:
            logging.error(f"Error sending message: {str(e)}")
            emit('error', {'message': str(e)})

def process_ai_response(room, user, selected_model, images=None):
    """Process AI response to a user message"""
    try:
        # Log model usage for the AI response
        try:
            result = ModelUsage.log_usage(
                user_id=user.id,
                model_name=selected_model,
                service='live'
            )

            if result is False or (isinstance(result, dict) and result.get('status') is False):
                balance = result.get('balance', 0) if isinstance(result, dict) else 0
                cost = result.get('cost', 0) if isinstance(result, dict) else 0
                
                logging.warning(f"User {user.id} has insufficient credits to use model {selected_model}. Balance: {balance}, Cost: {cost}")
                
                # If balance is 0, return a special error code
                if balance == 0:
                    socketio.emit('error', {
                        'message': 'You\'ve run out of credits. Please add more credits to continue using AI features.',
                        'insufficient_credits': True,
                        'zero_balance': True
                    }, room=request.sid)
                else:
                    socketio.emit('error', {
                        'message': 'You have insufficient credits to use this model. Please add more credits or use a different model.',
                        'insufficient_credits': True
                    }, room=request.sid)
                return False
            elif result is None:
                logging.error(f"Failed to log model usage for user {user.id}, model {selected_model}")
            else:
                logging.info(f"Successfully logged model usage and deducted credits for user {user.username} in room {room.room_id}, model: {selected_model}")
        except Exception as log_error:
            logging.error(f"Error logging model usage: {str(log_error)}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")

        # Format messages for AI service
        formatted_messages = []

        # Get information about both users in the room
        creator_username = room.creator.username if room.creator else "Unknown"
        participant_username = room.participant.username if room.participant else "Unknown"

        # Add a special first message with information about the users in the room
        user_info_message = {
            "role": "system",
            "content": f"This is a live chat room with two users: {creator_username} and {participant_username}. " +
                      f"You are inside a room called 'Live Chat' in which you talk to 2 other users. Properly respond to the users with their proper username. Do not get confused. " +
                      f"Always address users by their PROPER usernames in your responses."
        }
        formatted_messages.append(user_info_message)

        # Add all messages from the room with username prefixes
        for msg in room.messages:
            if msg["role"] == "user":
                # Add username prefix to user messages
                formatted_content = f"{msg['username']}: {msg['content']}"
                formatted_messages.append({
                    "role": "user",
                    "content": formatted_content
                })
            elif msg["role"] == "assistant":
                formatted_messages.append({
                    "role": "assistant",
                    "content": msg["content"]
                })

        # Emit a typing indicator first
        socketio.emit('ai_typing', {
            'user_id': str(user.id),
            'room_id': room.room_id
        }, room=room.room_id)

        # Start with an empty message
        assistant_message = ""

        # Check if we have images and need to use the Gemini Image service
        has_images = images and len(images) > 0

        if has_images:
            logging.info(f"Processing message with {len(images)} images using Gemini Image service")
            # Use Gemini Image service for image processing
            generator = chat_service.gemini_image_service.generate_stream({
                "prompt": room.messages[-1]["content"],  # Use the last user message as prompt
                "images": images
            })
        elif selected_model == 'gemini-2.0-flash':
            generator = chat_service.gemini_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt')
        elif selected_model == 'qwen-qwq-32b':
            generator = chat_service.groq_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt')
        else:
            generator = chat_service.gpt_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt')

        # Stream the response chunks
        chunk_count = 0
        for chunk in generator:
            if chunk:  # Make sure we're not sending empty chunks
                chunk_count += 1
                assistant_message += chunk
                # Log the chunk
                logging.info(f"Sending chunk {chunk_count}: {chunk[:20]}...")
                # Emit each chunk immediately with socketio
                socketio.emit('ai_response_chunk', {
                    'text': chunk,
                    'user_id': str(user.id),
                    'room_id': room.room_id
                }, room=room.room_id)
                # No delay needed for modern browsers and WebSocket connections
                # The previous delay of 0.01s per chunk was causing significant slowdowns

        # Save the complete message to room
        ai_message = room.add_ai_response(assistant_message, user.id)

        # Emit the complete message
        socketio.emit('ai_response_complete', {
            'message': ai_message,
            'room_id': room.room_id,
            'user_id': str(user.id)
        }, room=room.room_id)

    except Exception as e:
        logging.error(f"Error generating AI response: {str(e)}")
        socketio.emit('error', {
            'message': f"Error generating AI response: {str(e)}",
            'room_id': room.room_id
        }, room=room.room_id)
