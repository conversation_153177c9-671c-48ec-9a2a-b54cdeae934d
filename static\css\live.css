/* Live Chat specific styles */

/* Main container adjustments */
.chat-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  border-radius: 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgb(28, 28, 32) 0%, rgb(25, 25, 30) 100%);
}

/* Sidebar styling */
.sidebar {
  width: 320px;
  background: linear-gradient(180deg, rgba(25, 25, 30, 0.95) 0%, rgba(28, 28, 32, 0.95) 100%);
  border-right: 1px solid rgba(49, 94, 248, 0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  z-index: 10;
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
}

/* Main chat area background and styling */
.main-chat {
  background: linear-gradient(135deg, rgb(30, 30, 35) 0%, rgb(28, 28, 32) 100%);
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Add subtle pattern overlay to main chat */
.main-chat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  z-index: 0;
  pointer-events: none;
}

/* Sidebar header styling */
.sidebar-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1.25rem;
  border-bottom: 1px solid rgba(49, 94, 248, 0.1);
  position: relative;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 50%;
  height: 2px;
  background: linear-gradient(to right, rgb(49, 94, 248), transparent);
}

.sidebar-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.sidebar-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(80, 120, 255) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  letter-spacing: -0.5px;
}

.sidebar-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

/* Create Room Button */
#createRoomBtn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.85rem 1.25rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
  border: 1px solid rgba(49, 94, 248, 0.3);
  color: rgb(49, 94, 248);
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.75rem;
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.1);
  position: relative;
  overflow: hidden;
}

#createRoomBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

#createRoomBtn:hover {
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.25) 0%, rgba(49, 94, 248, 0.35) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(49, 94, 248, 0.15);
  color: white;
}

#createRoomBtn:hover::before {
  transform: translateX(100%);
}

#createRoomBtn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(49, 94, 248, 0.1);
}

#createRoomBtn i {
  width: 1.25rem;
  height: 1.25rem;
  transition: all 0.3s ease;
}

#createRoomBtn:hover i {
  transform: rotate(15deg) scale(1.1);
}

/* Room status */
.room-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.05) 0%, rgba(49, 94, 248, 0.1) 100%);
  border: 1px solid rgba(49, 94, 248, 0.15);
  border-radius: 0.75rem;
  margin: 1.5rem auto;
  max-width: 85%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.room-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, rgba(49, 94, 248, 0.05) 50%, transparent 100%);
  animation: shimmer 2s infinite linear;
  z-index: 0;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.status-message {
  color: #f3f4f6;
  font-size: 0.95rem;
  font-weight: 500;
  position: relative;
  z-index: 1;
  text-align: center;
  line-height: 1.5;
}

/* User identification in messages */
.message .user-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  opacity: 0.8;
}

.message .user-info .username {
  font-weight: 600;
  margin-right: 0.5rem;
}

.message .user-info .timestamp {
  font-size: 0.7rem;
}

/* Message styling for different users */
.message.current-user {
  margin-left: auto;
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(45, 85, 225) 100%);
  color: rgb(255, 255, 255);
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.25);
  border-radius: 1rem 0.25rem 1rem 1rem;
  position: relative;
  max-width: 65%;
}

.message.current-user::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 0;
  width: 15px;
  height: 15px;
  background: rgb(45, 85, 225);
  border-radius: 0 0 0 15px;
  z-index: -1;
}

.message.other-user {
  margin-right: auto;
  background: linear-gradient(135deg, rgb(50, 50, 56) 0%, rgb(45, 45, 51) 100%);
  color: rgb(255, 255, 255);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem 1rem 1rem 1rem;
  position: relative;
  max-width: 65%;
}

.message.other-user::after {
  content: '';
  position: absolute;
  left: -8px;
  top: 0;
  width: 15px;
  height: 15px;
  background: rgb(50, 50, 56);
  border-radius: 0 0 15px 0;
  z-index: -1;
}

.message.ai-to-current-user {
  margin-right: auto;
  background: linear-gradient(135deg, rgb(38, 38, 44) 0%, rgb(32, 32, 38) 100%);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(49, 94, 248);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem 1rem 1rem 0.5rem;
  max-width: 75%;
}

.message.ai-to-other-user {
  margin-right: auto;
  background: linear-gradient(135deg, rgb(38, 38, 44) 0%, rgb(32, 32, 38) 100%);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(45, 45, 51);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem 1rem 1rem 0.5rem;
  max-width: 75%;
}

/* Hidden input for invite link */
.hidden-input {
  position: absolute;
  left: -9999px;
  opacity: 0;
  height: 0;
  width: 0;
  z-index: -1;
}

/* Header styling */
.main-chat header {
  background: linear-gradient(90deg, rgba(28, 28, 32, 0.9) 0%, rgba(30, 30, 35, 0.9) 100%);
  border-bottom: 1px solid rgba(49, 94, 248, 0.1);
  padding: 1rem 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.main-chat header .header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.main-chat header h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(200, 200, 220) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.main-chat header h2::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30%;
  height: 2px;
  background: linear-gradient(to right, rgb(49, 94, 248), transparent);
  opacity: 0.7;
}

/* Header buttons container */
.main-chat header > div:last-child {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Invite button styling */
#inviteBtn, #fullscreenBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.85rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.15) 100%);
  border: 1px solid rgba(49, 94, 248, 0.2);
  border-radius: 0.5rem;
  color: rgb(200, 210, 255);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

#inviteBtn::before, #fullscreenBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

#inviteBtn:hover, #fullscreenBtn:hover {
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
  color: white;
}

#inviteBtn:hover::before, #fullscreenBtn:hover::before {
  transform: translateX(100%);
}

#inviteBtn i, #fullscreenBtn i {
  color: rgb(49, 94, 248);
  transition: all 0.3s ease;
}

#inviteBtn:hover i, #fullscreenBtn:hover i {
  color: white;
  transform: scale(1.1);
}

#inviteBtn.copied {
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(39, 174, 96, 0.2) 100%);
  border-color: rgba(39, 174, 96, 0.3);
  color: rgb(39, 174, 96);
}

#inviteBtn.copied i {
  color: rgb(39, 174, 96);
}

/* Room list styling */
#roomList {
  margin-top: 1rem;
  padding-right: 0.5rem;
  overflow-y: auto;
  /* Fixed height to show exactly 4 rooms (each room is ~70px with margin) */
  height: 300px;
  max-height: 300px;
  scrollbar-width: thin;
  scrollbar-color: rgba(49, 94, 248, 0.3) transparent;
}

#roomList::-webkit-scrollbar {
  width: 5px;
}

#roomList::-webkit-scrollbar-track {
  background: transparent;
}

#roomList::-webkit-scrollbar-thumb {
  background-color: rgba(49, 94, 248, 0.3);
  border-radius: 10px;
}

#roomList .room-item {
  display: flex;
  align-items: center;
  padding: 0.85rem 1rem;
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

#roomList .room-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, rgb(49, 94, 248), rgb(45, 85, 225));
  opacity: 0;
  transition: opacity 0.3s ease;
}

#roomList .room-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#roomList .room-item:hover::before {
  opacity: 0.5;
}

#roomList .room-item.active {
  background: rgba(49, 94, 248, 0.15);
  border: 1px solid rgba(49, 94, 248, 0.3);
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.1);
}

#roomList .room-item.active::before {
  opacity: 1;
}

#roomList .room-item .room-icon {
  margin-right: 0.75rem;
  color: #f3f4f6;
  background: rgba(49, 94, 248, 0.1);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

#roomList .room-item:hover .room-icon {
  background: rgba(49, 94, 248, 0.2);
}

#roomList .room-item.active .room-icon {
  background: rgba(49, 94, 248, 0.3);
  color: rgb(49, 94, 248);
}

#roomList .room-item .room-details {
  flex: 1;
  overflow: hidden;
}

#roomList .room-item .room-name {
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #f3f4f6;
  transition: color 0.3s ease;
}

#roomList .room-item.active .room-name {
  color: rgb(49, 94, 248);
}

#roomList .room-item .room-participants {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

#roomList .room-item .room-last-updated {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  margin-top: 0.25rem;
}

/* Participant indicator */
.participant-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  margin-left: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.participant-indicator.creator {
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.2) 0%, rgba(49, 94, 248, 0.3) 100%);
  color: rgb(49, 94, 248);
  border: 1px solid rgba(49, 94, 248, 0.2);
}

.participant-indicator.participant {
  background: linear-gradient(135deg, rgba(45, 45, 51, 0.2) 0%, rgba(45, 45, 51, 0.3) 100%);
  color: rgb(200, 200, 200);
  border: 1px solid rgba(45, 45, 51, 0.2);
}

/* Chat content area styling */
#chatContent {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  position: relative;
  z-index: 1;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(49, 94, 248, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(49, 94, 248, 0.03) 0%, transparent 50%);
  animation: fadeIn 0.5s ease-in-out;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(49, 94, 248, 0.3) transparent;
  height: calc(100vh - 180px);
  flex: 1;
}

#chatContent::-webkit-scrollbar {
  width: 6px;
}

#chatContent::-webkit-scrollbar-track {
  background: transparent;
}

#chatContent::-webkit-scrollbar-thumb {
  background-color: rgba(49, 94, 248, 0.3);
  border-radius: 10px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Welcome message styling */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.03) 0%, rgba(49, 94, 248, 0.05) 100%);
  border-radius: 1rem;
  margin: 2rem auto;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.welcome-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M50 75 L75 50 L50 25 L25 50 Z' stroke='rgba(49, 94, 248, 0.05)' stroke-width='2' fill='none'/%3E%3C/svg%3E");
  background-repeat: repeat;
  background-position: center;
  background-size: 100px;
  opacity: 0.5;
  z-index: -1;
}

.welcome-icon {
  width: 5rem;
  height: 5rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.15);
  border: 2px solid rgba(49, 94, 248, 0.2);
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.welcome-icon i {
  width: 2.5rem;
  height: 2.5rem;
  color: rgb(49, 94, 248);
}

.welcome-message h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(80, 120, 255) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome-message p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  max-width: 80%;
  line-height: 1.6;
}

.welcome-features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.welcome-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(49, 94, 248, 0.1);
  border: 1px solid rgba(49, 94, 248, 0.2);
  border-radius: 2rem;
  transition: all 0.3s ease;
}

.welcome-feature:hover {
  transform: translateY(-3px);
  background: rgba(49, 94, 248, 0.15);
  box-shadow: 0 4px 12px rgba(49, 94, 248, 0.1);
}

.welcome-feature i {
  width: 1.25rem;
  height: 1.25rem;
  color: rgb(49, 94, 248);
}

.welcome-feature span {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

/* Empty state styling - only show when no welcome message */
#chatContent:empty::before {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.4);
  font-size: 1.1rem;
  font-style: italic;
  text-align: center;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M50 75 L75 50 L50 25 L25 50 Z' stroke='rgba(49, 94, 248, 0.1)' stroke-width='2' fill='none'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 150px;
}

/* Image handling styles */
.message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.75rem;
  max-width: 100%;
}

.message-image-preview {
  max-width: 220px;
  max-height: 220px;
  border-radius: 0.75rem;
  object-fit: contain;
  border: 2px solid rgba(49, 94, 248, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.25rem;
}

.message-image-preview:hover {
  transform: scale(1.03);
  border-color: rgba(49, 94, 248, 0.4);
  box-shadow: 0 6px 20px rgba(49, 94, 248, 0.2);
}

#imagePreviewContainer {
  position: relative;
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.05) 0%, rgba(49, 94, 248, 0.1) 100%);
  border: 1px solid rgba(49, 94, 248, 0.15);
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

#imagePreviewContainer .image-preview {
  max-width: 120px;
  max-height: 120px;
  border-radius: 0.5rem;
  object-fit: contain;
  border: 2px solid rgba(49, 94, 248, 0.2);
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.25rem;
}

#imagePreviewContainer .image-preview:hover {
  transform: scale(1.05);
  border-color: rgba(49, 94, 248, 0.4);
}

#imagePreviewContainer .relative {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.remove-image-btn {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  transition: all 0.3s ease;
  opacity: 0.8;
  transform: scale(0.9);
}

.remove-image-btn:hover {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.global-remove-image-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 1.75rem;
  height: 1.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  transition: all 0.3s ease;
}

.global-remove-image-btn:hover {
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Function buttons removed in favor of the + icon in the input field */

/* Model selector styling */
.current-model-display {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.05) 0%, rgba(49, 94, 248, 0.1) 100%);
  border: 1px solid rgba(49, 94, 248, 0.15);
  border-radius: 1rem;
  color: #f3f4f6;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-model-display:hover {
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.15) 100%);
  box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
  transform: translateY(-50%) scale(1.05);
}

.current-model-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(49, 94, 248, 0.15);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.current-model-display:hover .current-model-icon {
  background: rgba(49, 94, 248, 0.25);
  transform: rotate(15deg);
}

.current-model-icon i {
  width: 0.9rem;
  height: 0.9rem;
  color: rgb(49, 94, 248);
}

#currentModel {
  transition: all 0.3s ease;
}

.current-model-display:hover #currentModel {
  color: white;
}

.hidden {
  display: none !important;
}

/* Input area styling */
.input-area {
  background: linear-gradient(0deg, rgba(25, 25, 30, 0.9) 0%, rgba(28, 28, 32, 0.8) 100%);
  border-top: 1px solid rgba(49, 94, 248, 0.1);
  padding: 1.25rem;
  position: relative;
  z-index: 2;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.input-container {
  max-width: 90%;
  margin: 0 auto;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-top: 0.5rem;
  width: 100%;
  box-sizing: border-box;
}

/* Add button styling for the + icon */
#addButton {
  position: absolute;
  left: 0.85rem;
  top: 45%;
  transform: translateY(-50%);
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.2) 100%);
  border: 1px solid rgba(49, 94, 248, 0.2);
  color: rgb(49, 94, 248);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(49, 94, 248, 0.1);
  margin: auto 0; /* Ensure vertical centering */
  padding: 0; /* Remove any padding that might affect centering */
}

#addButton:hover {
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
}

#addButton:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 2px 6px rgba(49, 94, 248, 0.1);
}

#addButton i {
  width: 1rem;
  height: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto; /* Center horizontally */
}

#addButton:hover i {
  transform: rotate(90deg);
  color: white;
}

#messageInput {
  width: 100%; /* Changed from 75% to 100% to ensure it works on all devices */
  min-height: 50px;
  padding: 0.85rem 1rem;
  padding-left: 3.25rem; /* Increased left padding to make room for the + icon */
  padding-right: 7rem;
  background: rgba(45, 45, 51, 0.5);
  border: 1px solid rgba(49, 94, 248, 0.2);
  border-radius: 0.75rem;
  color: #f3f4f6;
  font-size: 0.95rem;
  resize: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  box-sizing: border-box; /* Ensure padding is included in width calculation */
  display: block; /* Ensure the input is displayed as a block element */
}

#messageInput:focus {
  outline: none;
  background: rgba(45, 45, 51, 0.7);
  border-color: rgba(49, 94, 248, 0.4);
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.1);
}

#messageInput::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

#sendButton {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(45, 85, 225) 100%);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.2);
  position: relative;
  overflow: hidden;
}

#sendButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

#sendButton:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(49, 94, 248, 0.3);
}

#sendButton:hover::before {
  transform: translateX(100%);
}

#sendButton:active {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 2px 10px rgba(49, 94, 248, 0.2);
}

#sendButton i {
  width: 1.25rem;
  height: 1.25rem;
  transition: all 0.3s ease;
}

#sendButton:hover i {
  transform: translateY(-2px);
}

/* Mobile-only elements */
.mobile-only {
  display: none;
}

/* Mobile styles */
@media (max-width: 768px) {
  .chat-container {
    border-radius: 0;
    width: 100%;
    max-width: none;
    margin: 0;
  }

  /* Show mobile-only elements */
  .mobile-only {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Sidebar toggle button */
  #sidebarToggleBtn {
    background: rgba(49, 94, 248, 0.1);
    border: 1px solid rgba(49, 94, 248, 0.2);
    color: #f3f4f6;
    cursor: pointer;
    padding: 0.6rem;
    margin-right: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  #sidebarToggleBtn:hover {
    background-color: rgba(49, 94, 248, 0.2);
    transform: translateY(-1px);
  }

  #sidebarToggleBtn:active {
    transform: translateY(1px);
  }

  #sidebarToggleBtn i {
    width: 1.75rem;
    height: 1.75rem;
    color: rgb(49, 94, 248);
  }

  /* Close sidebar button */
  #closeSidebarBtn {
    background: none;
    border: none;
    color: #f3f4f6;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
  }

  #closeSidebarBtn:hover {
    background-color: rgba(49, 94, 248, 0.1);
  }

  #closeSidebarBtn i {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* Mobile sidebar overlay - removed */

  .sidebar {
    width: 280px;
    position: fixed;
    left: -280px; /* Start off-screen */
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease; /* Add smooth transition */
    pointer-events: auto; /* Ensure sidebar captures its own clicks */
  }

  /* When sidebar is open */
  .sidebar.open {
    left: 0; /* Move on-screen */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  }

  .room-status {
    margin: 0.75rem;
    padding: 0.75rem;
    max-width: 90%;
  }

  .invite-link-container input {
    font-size: 0.9rem;
  }

  .message-image-preview {
    max-width: 150px;
    max-height: 150px;
  }

  /* Function buttons removed in favor of the + icon in the input field */

  .message.current-user,
  .message.other-user,
  .message.ai-to-current-user,
  .message.ai-to-other-user {
    max-width: 85%;
  }

  #chatContent {
    padding: 1rem;
    height: calc(100vh - 160px);
  }

  .input-area {
    padding: 1rem 0.75rem;
    position: relative;
    z-index: 10;
    min-height: 145px;
  }

  .input-container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0 0.25rem;
  }

  .input-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
  }

  #messageInput {
    min-height: 45px;
    padding: 0.75rem;
    padding-left: 2.5rem; /* Reduced left padding for mobile */
    padding-right: 4.5rem; /* Reduced right padding for mobile */
    font-size: 0.9rem;
    width: 100%; /* Ensure full width */
    box-sizing: border-box; /* Include padding in width calculation */
    position: relative; /* Ensure proper positioning */
    z-index: 5; /* Ensure input is above other elements */
    display: block; /* Ensure the input is displayed */
    overflow: hidden; /* Prevent overflow */
  }

  #addButton {
    width: 1.75rem;
    height: 1.75rem;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%); /* Ensure proper vertical centering */
    position: absolute;
    z-index: 10;
  }

  #addButton i {
    width: 0.9rem;
    height: 0.9rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #sendButton {
    width: 45px;
    height: 45px;
  }

  .current-model-display {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    right: 0.5rem; /* Ensure it's properly positioned on mobile */
    top: 50%;
    transform: translateY(-50%);
    z-index: 10; /* Ensure it's above other elements */
    max-width: 4rem; /* Limit width on mobile */
    overflow: hidden; /* Prevent overflow */
    white-space: nowrap; /* Keep text on one line */
    text-overflow: ellipsis; /* Add ellipsis for overflow text */
  }

  /* Make model selector more compact on mobile */
  .current-model-icon {
    width: 1.25rem;
    height: 1.25rem;
    min-width: 1.25rem;
  }

  .welcome-message {
    padding: 1.5rem 1rem;
    margin: 1rem auto;
  }

  .welcome-message h3 {
    font-size: 1.25rem;
  }

  .welcome-message p {
    font-size: 0.9rem;
    max-width: 95%;
  }

  .welcome-features {
    flex-direction: column;
    gap: 0.75rem;
  }

  .welcome-feature {
    width: 100%;
    justify-content: center;
  }
}

/* Tablet styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .chat-container {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 0;
  }

  .sidebar {
    width: 280px;
  }

  #chatContent {
    height: calc(100vh - 170px);
  }

  .main-chat {
    flex: 1;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  #messageInput {
    padding-right: 3.5rem; /* Further reduce right padding for very small screens */
    padding-left: 2.25rem; /* Further reduce left padding for very small screens */
    font-size: 0.85rem;
  }

  .current-model-display {
    padding: 0.2rem 0.3rem;
    font-size: 0.7rem;
    max-width: 3.5rem;
  }

  #sendButton {
    width: 40px;
    height: 40px;
  }

  #addButton {
    width: 1.5rem;
    height: 1.5rem;
    left: 0.4rem;
  }

  #addButton i {
    width: 0.8rem;
    height: 0.8rem;
  }

  .input-area {
    padding: 0.75rem 0.5rem;
  }
}

/* Extra small mobile styles */
@media (max-width: 360px) {
  #messageInput {
    padding-right: 3rem;
    padding-left: 2rem;
    font-size: 0.8rem;
    min-height: 40px;
  }

  .current-model-display {
    padding: 0.15rem 0.25rem;
    font-size: 0.65rem;
    max-width: 3rem;
  }

  #sendButton {
    width: 36px;
    height: 36px;
  }

  #addButton {
    width: 1.4rem;
    height: 1.4rem;
    left: 0.35rem;
  }

  #addButton i {
    width: 0.75rem;
    height: 0.75rem;
  }
}

/* User info styling */
.user-info {
  margin-top: 1rem; /* Add some space above user info */
  margin-bottom: 0.5rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgb(47 46 46) 0%, rgb(31 31 35 / 71%) 100%);
  border: 1px solid rgba(49, 94, 248, 0.15);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  /* Ensure it stays at the bottom without overlapping */
  z-index: 5;
}

.user-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(49, 94, 248, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.user-info:hover::before {
  transform: translateX(100%);
}

.user-avatar {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, rgba(49, 94, 248, 0.2) 0%, rgba(49, 94, 248, 0.3) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(49, 94, 248, 0.15);
  transition: all 0.3s ease;
  border: 2px solid rgba(49, 94, 248, 0.2);
}

.user-info:hover .user-avatar {
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 6px 15px rgba(49, 94, 248, 0.2);
  border-color: rgba(49, 94, 248, 0.3);
}

.user-avatar i {
  color: rgb(49, 94, 248);
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s ease;
}

.user-info:hover .user-avatar i {
  transform: scale(1.1);
  color: rgb(80, 120, 255);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.user-name {
  color: #f3f4f6;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.user-info:hover .user-name {
  color: white;
  transform: translateX(2px);
}

.logout-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  font-size: 0.85rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.35rem 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  width: fit-content;
}

.logout-link:hover {
  color: white;
  background: rgba(239, 68, 68, 0.2);
  transform: translateX(3px);
}

.logout-link i {
  width: 1rem;
  height: 1rem;
  transition: all 0.3s ease;
}

.logout-link:hover i {
  transform: translateX(2px);
}




