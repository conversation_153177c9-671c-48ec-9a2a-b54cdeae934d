/* Thinking Container Styles */
.thinking-container {
  position: relative;
  background-color: rgb(25, 25, 30);
  border-radius: 8px;
  border: 1px solid rgba(73, 73, 73, 0.5);
  margin: 0.75rem 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgb(32, 32, 38);
  border-bottom: 1px solid rgba(73, 73, 73, 0.5);
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.thinking-header:hover {
  background-color: rgb(40, 40, 46);
}

.thinking-header-icon {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
}

.thinking-header-icon svg {
  width: 1.25rem;
  height: 1.25rem;
  color: rgb(156, 163, 175);
}

.thinking-header-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(156, 163, 175);
  flex-grow: 1;
}

.thinking-header-time {
  font-size: 0.75rem;
  color: rgb(156, 163, 175);
  margin-right: 0.5rem;
  opacity: 0.8;
}

.thinking-toggle {
  transition: transform 0.3s ease;
  color: rgb(156, 163, 175);
}

.thinking-toggle.expanded {
  transform: rotate(180deg);
}

.thinking-content {
  padding: 0;
  color: rgb(200, 200, 200);
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  opacity: 0;
}

.thinking-content.expanded {
  max-height: 500px;
  overflow-y: auto;
  padding: 1rem;
  opacity: 1;
}

/* Add paragraph spacing in thinking content */
.thinking-content p {
  margin-bottom: 0.75rem;
}

/* Style code in thinking content */
.thinking-content code {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

/* Scrollbar styling for thinking content */
.thinking-content::-webkit-scrollbar {
  width: 8px;
}

.thinking-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.thinking-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.thinking-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile styles */
@media (max-width: 768px) {
  .thinking-container {
    margin: 0.5rem 0;
  }

  .thinking-header {
    padding: 0.4rem 0.75rem;
  }

  .thinking-content {
    font-size: 0.8rem;
  }
}
