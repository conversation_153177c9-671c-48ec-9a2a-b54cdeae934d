<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Kevko Systems</title>

    <!-- Favicon links -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="{{ url_for('static', filename='js/login.js') }}" defer></script>
    <style>
        /* Sanfter Farbwechsel um das Hauptfenster */
        @keyframes rainbow-border {
            0% { border-color: #ff0000; }
            16% { border-color: #ff8000; }
            32% { border-color: #ffff00; }
            48% { border-color: #00ff00; }
            64% { border-color: #00ffff; }
            80% { border-color: #0000ff; }
            100% { border-color: #ff0000; }
        }

        .rainbow-border {
            border: 3px solid;
            border-radius: 20px;
            animation: rainbow-border 10s linear infinite;
        }

        /* Leuchteffekt (sanfter & heller) */
        @keyframes glowIn {
            from {
                box-shadow: 0 10px 15px rgba(255, 255, 255, 0);
                opacity: 0;
                transform: translateY(5px);
            }
            to {
                box-shadow: 0 4px 10px rgba(255, 255, 255, 0.7);
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes glowOut {
            from {
                box-shadow: 0 4px 10px rgba(255, 255, 255, 0.7);
                opacity: 1;
                transform: translateY(0);
            }
            to {
                box-shadow: 0 10px 15px rgba(255, 255, 255, 0);
                opacity: 0.5;
                transform: translateY(5px);
            }
        }

        .glow {
            animation: glowIn 0.3s ease-out forwards;
        }

        .glow-remove {
            animation: glowOut 0.3s ease-out forwards;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900 text-white">

    <div class="absolute top-4 right-4">
        {% if current_user.is_authenticated %}
        <a href="{{ url_for('auth.logout') }}"
           class="btn bg-red-600 hover:bg-red-700 text-white text-sm px-4 py-2 rounded-lg shine shadow-large">
            Logout
        </a>
        {% endif %}
    </div>

    <div class="flex items-center justify-center m-4 max-w-4xl">
        <div class="flex flex-col md:flex-row w-[750px] md:h-[580px] bg-neutral-800 bg-opacity-80 backdrop-blur-md rounded-3xl shadow-large shine overflow-hidden relative rainbow-border">

            <!-- Schließen-Button -->
            <button class="bg-neutral-900/30 hover:bg-neutral-700 text-neutral-500 text-xs p-2 rounded-xl flex items-center shine shadow-large absolute top-3.5 right-3.5 z-50">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            </button>

            <!-- Linke Bildhälfte -->
            <div class="hidden md:block md:w-1/2 h-full bg-black/20 relative overflow-hidden">
                <img src="{{ url_for('static', filename='images/image1.png') }}" alt="Kevko Systems" class="w-full h-full object-cover absolute">
            </div>

            <!-- Rechte Login-Hälfte -->
            <div class="md:w-1/2 p-6 sm:p-8 w-full h-full flex flex-col justify-center max-w-[450px] mx-auto">
                <img src="{{ url_for('static', filename='images/Logo Kevko Systems.webp') }}" class="w-12 h-12 mx-auto" alt="Kevko Systems Logo">

                <h2 class="font-medium mb-2 text-neutral-100 text-center">Sign In to Kevko Systems</h2>
                <hr class="border-0 h-[1px] bg-gradient-to-r from-white/0 via-white/10 to-white/0 mb-2">

                <p class="text-sm mb-4 text-white/50 text-center">
                    Kevko Systems is a private project by Kevko, bringing together innovative ideas in technology, automation, and software development.
                </p>

                <!-- Google Login -->
                <a href="{{ url_for('auth.google_login', next=request.args.get('next')) }}" class="btn bg-neutral-950/50 hover:bg-neutral-800 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large mb-4">
                    <svg class="w-4 h-4 mr-2" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                    </svg>
                    Sign in with Google
                </a>

                <!-- Oder-Trenner -->
                <div class="flex items-center my-4">
                    <div class="flex-grow h-px bg-gradient-to-r from-white/0 via-white/10 to-white/0"></div>
                    <span class="px-4 text-sm text-white/50">OR</span>
                    <div class="flex-grow h-px bg-gradient-to-r from-white/0 via-white/10 to-white/0"></div>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="mb-4">
                    <input type="text" name="login" placeholder="Username or Email" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                    <input type="password" name="password" placeholder="Password" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                    <button type="submit" class="btn bg-neutral-950/50 hover:bg-neutral-800 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large">Sign In</button>
                    <div class="text-center mt-2">
                        <button type="button" onclick="showForgotPassword()" class="text-sm text-white/50 hover:text-white nofocus">Forgot password?</button>
                    </div>
                </form>

                <!-- 2FA Verification Modal (Hidden by default) -->
                <div id="twoFactorModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-neutral-800 p-6 rounded-xl shadow-xl max-w-md w-full">
                        <h3 class="text-lg font-medium text-white mb-4">Two-Factor Authentication</h3>
                        <p class="text-sm text-white/70 mb-4">A verification code has been sent to your email. Please enter it below to complete the login process.</p>
                        <div class="mb-4">
                            <input type="text" id="verificationCode" placeholder="Enter Code" class="w-full p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                            <div id="verificationTimer" class="text-sm text-white/50 mt-1"></div>
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" id="verifyCodeBtn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm px-4 py-2 rounded-lg">Verify</button>
                            <button type="button" id="resendCodeBtn" class="flex-1 bg-neutral-700 hover:bg-neutral-600 text-white text-sm px-4 py-2 rounded-lg">Resend Code</button>
                        </div>
                    </div>
                </div>

                <!-- Registration Form (Initially Hidden) -->
                <form action="{{ url_for('auth.register') }}" method="POST" class="mb-4 hidden" id="registerForm">
                    <input type="text" name="username" placeholder="Username" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                    <input type="email" name="email" placeholder="Email" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                    <input type="password" name="password" placeholder="Password" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                    <div class="flex mb-2">
                        <input type="text" name="verification_code" placeholder="Enter Code" class="flex-1 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                        <button type="button" onclick="sendVerificationCode('register')" class="ml-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white text-sm">Send Code</button>
                    </div>
                    <div class="verification-timer text-sm text-white/50"></div>
                    <button type="submit" class="btn bg-neutral-950/50 hover:bg-neutral-800 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large">Register</button>
                </form>

                <!-- Password Reset Form (Initially Hidden) -->
                <div id="forgotPasswordContainer" class="mb-4 hidden">
                    <!-- Step 1: Request verification code -->
                    <div id="resetStep1">
                        <h3 class="text-center text-white mb-3">Reset Your Password</h3>
                        <input type="text" id="resetLogin" placeholder="Username or Email" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                        <button type="button" onclick="requestPasswordReset()" class="btn bg-blue-600 hover:bg-blue-700 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large">Send Verification Code</button>
                    </div>

                    <!-- Step 2: Enter verification code and new password -->
                    <div id="resetStep2" class="hidden">
                        <h3 class="text-center text-white mb-3">Enter Verification Code</h3>
                        <input type="text" id="resetVerificationCode" placeholder="Verification Code" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                        <input type="password" id="resetNewPassword" placeholder="New Password" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                        <input type="password" id="resetConfirmPassword" placeholder="Confirm Password" class="w-full mb-2 p-2 rounded-lg bg-neutral-500/20 border border-neutral-500/30 text-white text-sm font-light" required>
                        <button type="button" onclick="resetPassword()" class="btn bg-blue-600 hover:bg-blue-700 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large">Reset Password</button>
                    </div>

                    <!-- Step 3: Success message -->
                    <div id="resetStep3" class="hidden">
                        <div class="text-center p-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <h3 class="text-xl font-medium text-white mb-2">Password Reset Successful!</h3>
                            <p class="text-white/70 mb-4">Your password has been reset successfully.</p>
                            <button type="button" onclick="backToLogin()" class="btn bg-neutral-950/50 hover:bg-neutral-800 w-full text-white text-sm px-2 py-2 rounded-lg flex items-center justify-center shine shadow-large">Back to Login</button>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="button" onclick="backToLogin()" class="text-sm text-white/50 hover:text-white nofocus">Back to Login</button>
                    </div>
                </div>

                <!-- Toggle Button -->
                <button onclick="toggleForms()" class="text-sm text-white/50 hover:text-white mb-2 nofocus">Don't have an account? Sign Up</button>
            </div>

        </div>
    </div>

    <script src="{{ url_for('static', filename='js/login.js') }}" defer></script>
    <script>
        // Display flash messages if any
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                messages.forEach(message => {
                    alert(message);
                });
            {% endif %}
        {% endwith %}
    </script>

</body>
</html>
