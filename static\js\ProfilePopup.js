/**
 * ProfilePopup.js
 * Handles profile popup/tooltip functionality for friend avatars
 */
class ProfilePopup {
    constructor() {
        this.popup = null;
        this.currentPopup = null;
        this.isVisible = false;
        this.hideTimeout = null;
        this.cache = new Map(); // Cache profile data
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        this.currentTheme = 'blue'; // Default theme

        this.init();
    }
    
    /**
     * Initialize the profile popup system
     */
    init() {
        this.createPopupElement();
        this.setupEventListeners();
        this.detectCurrentTheme();
        console.log('ProfilePopup initialized');
    }

    /**
     * Detect and apply the current theme
     */
    detectCurrentTheme() {
        // Try to get theme from FriendsPanel instance first
        if (window.friendsPanel && window.friendsPanel.userTheme) {
            this.currentTheme = window.friendsPanel.userTheme;
        } else {
            // Fall back to localStorage
            this.currentTheme = localStorage.getItem('userChatTheme') || 'blue';
        }

        console.log('ProfilePopup: Detected theme:', this.currentTheme);
        this.applyTheme();
    }

    /**
     * Apply the current theme to the popup
     */
    applyTheme() {
        if (!this.popup) return;

        // Remove all existing theme classes
        this.popup.classList.remove(
            'theme-purple', 'theme-blue', 'theme-green', 'theme-red',
            'theme-orange', 'theme-pink', 'theme-cyan', 'theme-yellow',
            'theme-indigo', 'theme-teal'
        );

        // Add the current theme class
        this.popup.classList.add(`theme-${this.currentTheme}`);
        console.log('ProfilePopup: Applied theme:', this.currentTheme);
    }
    
    /**
     * Create the popup DOM element
     */
    createPopupElement() {
        this.popup = document.createElement('div');
        this.popup.className = 'profile-popup';
        this.popup.innerHTML = `
            <div class="profile-popup-header">
                <div class="profile-popup-avatar">
                    <div class="avatar-initials"></div>
                </div>
                <div class="profile-popup-info">
                    <div class="profile-popup-name"></div>
                    <div class="profile-popup-username"></div>
                </div>
            </div>
            <div class="profile-popup-about">
                <div class="profile-popup-about-title">About Me</div>
                <div class="profile-popup-about-content"></div>
            </div>
        `;
        
        document.body.appendChild(this.popup);
    }
    
    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Close popup when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.popup.contains(e.target) && !this.isAvatarElement(e.target)) {
                this.hide();
            }
        });
        
        // Close popup on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.isVisible) {
                this.hide();
            }
        });
    }
    
    /**
     * Check if an element is an avatar that should trigger the popup
     */
    isAvatarElement(element) {
        return element.closest('.friend-avatar, .message-avatar, .profile-popup-trigger');
    }
    
    /**
     * Show popup for a specific user
     */
    async show(username, triggerElement) {
        console.log('ProfilePopup: Showing popup for', username);

        if (this.isVisible && this.currentPopup === username) {
            console.log('ProfilePopup: Already showing for this user');
            return; // Already showing for this user
        }

        // Hide any existing popup
        this.hide();

        this.currentPopup = username;
        this.isVisible = true;

        // Update theme before showing
        this.detectCurrentTheme();

        // Position popup near trigger element
        this.positionPopup(triggerElement);

        // Show loading state
        this.showLoading();
        this.popup.classList.add('show');

        try {
            // Fetch user data
            console.log('ProfilePopup: Fetching user data for', username);
            const userData = await this.fetchUserData(username);
            console.log('ProfilePopup: Received user data', userData);

            // Only update if this is still the current popup
            if (this.currentPopup === username) {
                this.populatePopup(userData);
            }
        } catch (error) {
            console.error('ProfilePopup: Error fetching user data:', error);
            if (this.currentPopup === username) {
                this.showError('Failed to load profile data');
            }
        }
    }
    
    /**
     * Hide the popup
     */
    hide() {
        if (!this.isVisible) return;
        
        this.popup.classList.remove('show');
        this.popup.classList.add('hide');
        
        setTimeout(() => {
            this.popup.classList.remove('hide');
            this.isVisible = false;
            this.currentPopup = null;
        }, 200);
    }
    
    /**
     * Position popup relative to trigger element
     */
    positionPopup(triggerElement) {
        const rect = triggerElement.getBoundingClientRect();
        const popupRect = this.popup.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left = rect.left + rect.width / 2 - 140; // Center horizontally (280px / 2 = 140)
        let top = rect.bottom + 8; // Position below trigger
        let arrowClass = 'arrow-top';
        
        // Adjust horizontal position if popup would overflow
        if (left < 8) {
            left = 8;
        } else if (left + 280 > viewportWidth - 8) {
            left = viewportWidth - 288; // 280 + 8 margin
        }
        
        // Adjust vertical position if popup would overflow
        if (top + 400 > viewportHeight - 8) {
            top = rect.top - 8; // Position above trigger
            arrowClass = 'arrow-bottom';
            
            // If still doesn't fit, position at top of viewport
            if (top < 8) {
                top = 8;
                arrowClass = '';
            }
        }
        
        // Remove existing arrow classes
        this.popup.classList.remove('arrow-top', 'arrow-bottom', 'arrow-left', 'arrow-right');
        
        // Add appropriate arrow class
        if (arrowClass) {
            this.popup.classList.add(arrowClass);
        }
        
        this.popup.style.left = `${left}px`;
        this.popup.style.top = `${top}px`;
    }
    
    /**
     * Show loading state
     */
    showLoading() {
        this.popup.innerHTML = '<div class="profile-popup-loading">Loading profile...</div>';
    }
    
    /**
     * Show error state
     */
    showError(message) {
        this.popup.innerHTML = `<div class="profile-popup-error">${message}</div>`;
    }
    
    /**
     * Fetch user data from API
     */
    async fetchUserData(username) {
        // Check cache first
        const cacheKey = username;
        const cached = this.cache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }

        // Fetch from API
        const response = await fetch(`/api/profile/customization/${username}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.error || 'Failed to fetch profile data');
        }

        // Enhance profile data with user info if not present
        if (!data.profile.user) {
            // Fetch basic user info
            try {
                const userResponse = await fetch(`/api/profile/user/${username}`);
                if (userResponse.ok) {
                    const userData = await userResponse.json();
                    if (userData.success && userData.user) {
                        data.profile.user = userData.user;
                    }
                }
            } catch (error) {
                console.warn('Could not fetch user info:', error);
            }
        }

        // Cache the result
        this.cache.set(cacheKey, {
            data: data.profile,
            timestamp: Date.now()
        });

        return data.profile;
    }
    
    /**
     * Populate popup with user data
     */
    populatePopup(profileData) {
        // Recreate popup structure
        this.popup.innerHTML = `
            <div class="profile-popup-header">
                <div class="profile-popup-avatar">
                    <div class="avatar-initials"></div>
                </div>
                <div class="profile-popup-info">
                    <div class="profile-popup-name"></div>
                    <div class="profile-popup-username"></div>
                </div>
            </div>
            <div class="profile-popup-about">
                <div class="profile-popup-about-title">About Me</div>
                <div class="profile-popup-about-content"></div>
            </div>
        `;

        // Get elements
        const avatarContainer = this.popup.querySelector('.profile-popup-avatar');
        const nameElement = this.popup.querySelector('.profile-popup-name');
        const usernameElement = this.popup.querySelector('.profile-popup-username');
        const aboutContent = this.popup.querySelector('.profile-popup-about-content');

        // Set user info - handle case where user data might not be available
        let displayName, username;

        if (profileData.user) {
            displayName = profileData.user.display_name || profileData.user.username || 'Unknown User';
            username = profileData.user.username || 'unknown';
        } else {
            // Fallback: try to get username from the current popup context
            displayName = this.currentPopup || 'Unknown User';
            username = this.currentPopup || 'unknown';
        }

        nameElement.textContent = displayName;
        usernameElement.textContent = `@${username}`;

        // Set avatar
        if (profileData.user?.profile_picture) {
            avatarContainer.innerHTML = `<img src="${profileData.user.profile_picture}" alt="${displayName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                       <div class="avatar-initials" style="display: none;">${this.getInitials(displayName)}</div>`;
        } else {
            avatarContainer.innerHTML = `<div class="avatar-initials">${this.getInitials(displayName)}</div>`;
        }

        // Set about content
        const aboutText = profileData.custom_sections?.about;
        if (aboutText && aboutText.trim()) {
            aboutContent.innerHTML = aboutText;
        } else {
            aboutContent.innerHTML = '<span class="profile-popup-about-empty">This user prefers to keep an air of mystery about them.</span>';
        }

        // Reapply theme after content update
        this.applyTheme();
    }
    
    /**
     * Get initials from a name
     */
    getInitials(name) {
        if (!name) return '?';
        
        const words = name.trim().split(/\s+/);
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }
    
    /**
     * Add click handler to an avatar element
     */
    addAvatarClickHandler(avatarElement, username) {
        if (!avatarElement || !username) {
            console.warn('ProfilePopup: Invalid avatar element or username', { avatarElement, username });
            return;
        }

        console.log('ProfilePopup: Adding click handler for', username);

        avatarElement.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('ProfilePopup: Avatar clicked for', username);
            this.show(username, avatarElement);
        });

        // Add visual indicator that avatar is clickable
        avatarElement.style.cursor = 'pointer';
        avatarElement.classList.add('profile-popup-trigger');
    }

    /**
     * Update theme externally (called when user changes theme)
     */
    updateTheme(newTheme) {
        if (newTheme && newTheme !== this.currentTheme) {
            this.currentTheme = newTheme;
            this.applyTheme();
            console.log('ProfilePopup: Theme updated to:', newTheme);
        }
    }
}

// Create global instance
window.profilePopup = new ProfilePopup();
