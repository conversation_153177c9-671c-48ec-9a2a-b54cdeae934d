// User Not Found Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Ensure the container is properly centered when the page loads
    const container = document.querySelector('.not-found-container');
    
    // Function to ensure proper centering
    function ensureCentering() {
        if (!container) return;
        
        // Force a repaint by accessing offsetHeight
        container.style.display = 'none';
        container.offsetHeight; // This line forces a repaint
        container.style.display = 'block';
        
        // Re-apply the centering with fixed positioning
        container.style.position = 'fixed';
        container.style.top = '50%';
        container.style.left = '50%';
        container.style.transform = 'translate(-50%, -50%)';
        
        // Add GPU acceleration for smoother rendering
        container.style.willChange = 'transform';
        container.style.backfaceVisibility = 'hidden';
        container.style.webkitFontSmoothing = 'subpixel-antialiased';
    }
    
    // Apply centering immediately
    ensureCentering();
    
    // Also apply after a short delay to handle any layout shifts
    setTimeout(ensureCentering, 100);
    
    // And again after all resources have loaded
    window.addEventListener('load', ensureCentering);
    
    // Add smooth hover effects with JavaScript instead of CSS
    if (container) {
        container.addEventListener('mouseenter', function() {
            this.style.transform = 'translate(-50%, -52%) scale(1.01)';
            this.style.boxShadow = 'var(--shadow-lg)';
        });
        
        container.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(-50%, -50%)';
            this.style.boxShadow = 'var(--shadow-md)';
        });
    }
    
    // Handle window resize events to maintain centering
    window.addEventListener('resize', ensureCentering);
});