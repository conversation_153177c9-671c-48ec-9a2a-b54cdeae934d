/**
 * Fix for the admin panel access control functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel fix script loaded');

    // Function to fix the Add User button
    function fixAddUserButton() {
        console.log('Fixing Add User button');

        // Find all buttons with "Add User" text
        const addUserButtons = document.querySelectorAll('button');
        let addUserButton = null;

        for (const button of addUserButtons) {
            if (button.textContent.trim() === 'Add User') {
                addUserButton = button;
                break;
            }
        }

        if (addUserButton) {
            console.log('Found Add User button, adding event listener');

            // Remove existing event listeners by cloning the button
            const newButton = addUserButton.cloneNode(true);
            addUserButton.parentNode.replaceChild(newButton, addUserButton);

            // Add new event listener
            newButton.addEventListener('click', function() {
                console.log('Add User button clicked');

                // Get the current service ID
                const serviceSelector = document.querySelector('#adminSettingsSection select');
                const serviceId = serviceSelector ? serviceSelector.value : 'all';

                // Call the dashboard method directly
                if (window.dashboard) {
                    console.log('Calling showAddRestrictedUserModal with serviceId:', serviceId);
                    window.dashboard.showAddRestrictedUserModal(serviceId);
                } else {
                    console.error('Dashboard instance not found');
                }
            });
        } else {
            console.error('Add User button not found');
        }
    }

    // Function to remove placeholder users
    function removePlaceholderUsers() {
        console.log('Removing placeholder users');

        // Find all elements in the access control section
        // Use a more general approach to find user tags
        const accessControlSection = document.querySelector('#accessControlSection');
        if (!accessControlSection) {
            console.log('Access control section not found');
            return;
        }

        // Find all span elements that might contain usernames
        const allSpans = accessControlSection.querySelectorAll('span');

        // Filter for those that look like user tags
        for (const span of allSpans) {
            const username = span.textContent.trim();
            if (username.includes('@kevko') || username.includes('@johnsmith')) {
                console.log('Found placeholder user:', username);

                // Find the parent tag that should be removed (likely a div with rounded corners)
                let tagToRemove = span;
                let depth = 0;

                // Look up to 3 levels up for a div with appropriate classes
                while (depth < 3 && tagToRemove.parentElement) {
                    tagToRemove = tagToRemove.parentElement;

                    // If this element has rounded class, it's likely our target
                    if (tagToRemove.classList.contains('rounded')) {
                        console.log('Removing placeholder user element');
                        tagToRemove.remove();
                        break;
                    }

                    depth++;
                }
            }
        }
    }

    // Wait for the dashboard to be fully initialized
    const checkInterval = setInterval(function() {
        if (window.dashboard) {
            console.log('Dashboard found, applying fixes');
            clearInterval(checkInterval);

            try {
                // Apply fixes
                fixAddUserButton();
                removePlaceholderUsers();

                // Also fix when switching services
                const serviceSelector = document.querySelector('#adminSettingsSection select');
                if (serviceSelector) {
                    serviceSelector.addEventListener('change', function() {
                        // Wait a bit for the UI to update
                        setTimeout(function() {
                            try {
                                fixAddUserButton();
                                removePlaceholderUsers();
                            } catch (error) {
                                console.error('Error applying fixes after service change:', error);
                            }
                        }, 500);
                    });
                }
            } catch (error) {
                console.error('Error applying initial fixes:', error);
            }
        }
    }, 500);

    // Apply fixes again when clicking on admin settings tab
    const adminSettingsTab = document.getElementById('adminSettingsTab');
    if (adminSettingsTab) {
        adminSettingsTab.addEventListener('click', function() {
            // Wait a bit for the UI to update
            setTimeout(function() {
                try {
                    fixAddUserButton();
                    removePlaceholderUsers();
                } catch (error) {
                    console.error('Error applying fixes after admin tab click:', error);
                }
            }, 500);
        });
    }
});
