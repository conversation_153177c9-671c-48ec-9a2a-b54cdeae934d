from flask import jsonify
from flask_login import login_required, current_user
from models.thread import Thread
from models.room import Room
import logging
from . import profile_api

@profile_api.route('/clear-threads', methods=['POST'])
@login_required
def clear_threads():
    """Delete all threads for the current user"""
    try:
        # Count threads before deletion
        thread_count = Thread.objects(user=current_user.id).count()

        # Delete all threads
        Thread.objects(user=current_user.id).delete()

        return jsonify({
            'message': f'Successfully deleted {thread_count} threads',
            'count': thread_count
        })
    except Exception as e:
        logging.error(f"Error clearing threads: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/clear-rooms', methods=['POST'])
@login_required
def clear_rooms():
    """Delete all rooms created by the current user"""
    try:
        # Count rooms before deletion
        room_count = Room.objects(creator=current_user.id).count()

        # Delete all rooms created by the user
        Room.objects(creator=current_user.id).delete()

        return jsonify({
            'message': f'Successfully deleted {room_count} rooms',
            'count': room_count
        })
    except Exception as e:
        logging.error(f"Error clearing rooms: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500