"""
Error handlers for API endpoints
"""
from flask import jsonify, request
import logging
import inspect
from functools import wraps

def method_not_allowed_handler(allowed_methods):
    """
    Create a decorator that restricts API endpoints to specific HTTP methods.
    Returns a 405 Method Not Allowed response for unsupported methods.
    
    Args:
        allowed_methods (list): List of allowed HTTP methods (e.g., ['GET', 'POST'])
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method not in allowed_methods:
                logging.warning(f"Method not allowed: {request.method} for {request.path}")
                # Check if the request is from a script by examining headers
                user_agent = request.headers.get('User-Agent', '').lower()
                # List of common script/bot user agents
                script_agents = ['python', 'curl', 'wget', 'postman', 'insomnia', 'axios', 'fetch', 'node']
                
                is_script = any(agent in user_agent for agent in script_agents)
                
                if is_script:
                    # For scripts, return a JSON response
                    return jsonify({
                        'success': False,
                        'error': 'Method Not Allowed',
                        'message': f'The method {request.method} is not allowed for this endpoint. Allowed methods: {", ".join(allowed_methods)}'
                    }), 405
                else:
                    # For browser requests, use standard error handler
                    return jsonify({
                        'success': False,
                        'error': 'Method Not Allowed',
                        'message': 'The method is not allowed for the requested URL'
                    }), 405
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def apply_method_restrictions(app):
    """
    Apply method restrictions to all API routes in the Flask app.
    This ensures that all API endpoints only respond to their specified HTTP methods.
    
    Args:
        app (Flask): The Flask application
    """
    # Get all registered routes
    for rule in app.url_map.iter_rules():
        if rule.endpoint.startswith('api.'):
            # This is an API endpoint
            view_func = app.view_functions.get(rule.endpoint)
            if view_func:
                # Get the allowed methods for this route
                allowed_methods = list(rule.methods - {'HEAD', 'OPTIONS'})
                
                # Apply the method_not_allowed decorator
                decorated_view_func = method_not_allowed_handler(allowed_methods)(view_func)
                
                # Replace the original view function with the decorated one
                app.view_functions[rule.endpoint] = decorated_view_func
                logging.info(f"Applied method restriction to {rule.endpoint} - Methods: {allowed_methods}")

def register_error_handlers(app):
    """Register error handlers for the Flask app"""
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 Bad Request errors"""
        return jsonify({
            'success': False,
            'error': 'Bad Request',
            'message': str(error)
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """Handle 401 Unauthorized errors"""
        return jsonify({
            'success': False,
            'error': 'Unauthorized',
            'message': 'Authentication required'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        """Handle 403 Forbidden errors"""
        return jsonify({
            'success': False,
            'error': 'Forbidden',
            'message': 'You do not have permission to access this resource'
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 Not Found errors"""
        return jsonify({
            'success': False,
            'error': 'Not Found',
            'message': 'The requested resource was not found'
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """Handle 405 Method Not Allowed errors"""
        return jsonify({
            'success': False,
            'error': 'Method Not Allowed',
            'message': 'The method is not allowed for the requested URL'
        }), 405
    
    @app.errorhandler(500)
    def internal_server_error(error):
        """Handle 500 Internal Server Error errors"""
        # Log the error
        logging.error(f"Internal Server Error: {str(error)}")
        
        return jsonify({
            'success': False,
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500
    
    # Register a handler for ValidationError from MongoEngine
    @app.errorhandler(Exception)
    def handle_exception(error):
        """Handle all other exceptions"""
        # Check if it's a MongoEngine ValidationError
        if hasattr(error, '__module__') and error.__module__ == 'mongoengine.errors' and error.__class__.__name__ == 'ValidationError':
            # Log the error
            logging.error(f"ValidationError: {str(error)}")
            
            return jsonify({
                'success': False,
                'error': 'Validation Error',
                'message': str(error)
            }), 400
        
        # For all other exceptions, return a 500 error
        logging.error(f"Unhandled Exception: {str(error)}")
        
        return jsonify({
            'success': False,
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500