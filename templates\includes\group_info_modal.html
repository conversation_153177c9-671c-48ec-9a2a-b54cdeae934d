<!-- Group Info Modal -->
<div id="groupInfoModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
        <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
            <h3 class="text-lg font-medium text-slate-200 flex items-center">
                <i data-lucide="info" class="h-5 w-5 mr-2 text-purple-400"></i>
                Group Information
            </h3>
            <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="p-5">
            <!-- Group Creation Date -->
            <div class="mb-4 text-sm text-slate-400">
                <span>Created on: </span>
                <span id="groupCreationDate">Loading...</span>
            </div>
            
            <!-- Group Members List -->
            <div class="mb-4">
                <h4 class="text-sm font-medium text-slate-300 mb-2">Members</h4>
                <div id="groupMembersList" class="bg-slate-700/50 border border-slate-600/50 rounded-md p-2 max-h-60 overflow-y-auto">
                    <!-- Members will be loaded here -->
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center">
                            <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                            <span class="text-slate-500 text-sm">Loading members...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex justify-between">
                <button id="exitGroupBtn" class="friend-action-btn px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md shadow-md shadow-black/10 transition-all">
                    <i data-lucide="log-out" class="h-4 w-4 mr-2 inline"></i>
                    Exit Group
                </button>
                <button id="deleteGroupBtn" class="friend-action-btn px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md shadow-md shadow-red-500/10 transition-all hidden">
                    <i data-lucide="trash-2" class="h-4 w-4 mr-2 inline"></i>
                    Delete Group
                </button>
            </div>
        </div>
    </div>
</div>
