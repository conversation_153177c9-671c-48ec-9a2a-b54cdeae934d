/**
 * KevkoCloud Service
 * Secure cloud storage service
 */

// Import dependencies
// None for now

// Service class definition
window.KevkoCloudService = class KevkoCloudService extends window.Service {
    constructor(config) {
        super(config);
        this.files = [];
        this.storage = { used: 0, total: 0 };
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // In a real implementation, this might fetch data from an API
        this.files = [
            { id: 1, name: 'Documents', type: 'folder', size: '120 MB', modified: '2023-04-01' },
            { id: 2, name: 'Photos', type: 'folder', size: '450 MB', modified: '2023-04-02' },
            { id: 3, name: 'Project.zip', type: 'file', size: '25 MB', modified: '2023-04-03' }
        ];

        this.storage = { used: 595, total: 1000 }; // MB
    }

    /**
     * Render the service card HTML
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering service: ${this.id}`);
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            let actionButton = '';

            if (this.status === 'online' && this.url) {
                // External link for online services with URL
                actionButton = `
                    <div class="w-full">
                        <a href="${this.url}" target="_blank"
                           class="w-full bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group mb-2">
                            <span class="text-sm text-slate-300 group-hover:text-slate-100">Launch ${this.name}</span>
                            <i data-lucide="chevron-right" class="h-4 w-4 ml-1"></i>
                        </a>
                    </div>
                `;
            } else {
                // Disabled button for offline or coming soon services
                actionButton = `
                    <div class="w-full">
                        <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed mb-2">
                            <span class="text-sm text-slate-300 animate-pulse">${this.status === 'offline' ? 'Offline' : 'Coming Soon'}</span>
                        </div>
                    </div>
                `;
            }

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                        <div id="storage-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                            <h4 class="text-xs font-medium text-slate-300 mb-2">Storage Usage</h4>
                            <div class="space-y-2">
                                <!-- Storage info will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize storage button
            const showStorageBtn = element.querySelector('#showStorage');
            if (showStorageBtn) {
                console.log(`Found storage button for service: ${this.id}`);
                showStorageBtn.addEventListener('click', () => {
                    this.toggleStorage(element);
                });
            } else {
                console.log(`No storage button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoCloudService ${this.id}:`, error);
        }
    }

    /**
     * Toggle storage visibility
     * @param {HTMLElement} element The service element
     */
    async toggleStorage(element) {
        const storageContainer = element.querySelector(`#storage-${this.id}`);
        if (!storageContainer) return;

        const isHidden = storageContainer.classList.contains('hidden');

        if (isHidden) {
            // Initialize storage if needed
            if (this.storage.total === 0) {
                await this.init();
            }

            // Populate storage info
            const storageContent = storageContainer.querySelector('.space-y-2');
            if (storageContent) {
                const usedPercentage = (this.storage.used / this.storage.total) * 100;
                const barColor = usedPercentage > 80 ? 'bg-red-500' : 'bg-green-500';

                storageContent.innerHTML = `
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-slate-300">${this.storage.used} MB used</span>
                        <span class="text-slate-400">of ${this.storage.total} MB</span>
                    </div>
                    <div class="h-1.5 w-full bg-slate-700 rounded-full overflow-hidden">
                        <div class="${barColor}" style="width: ${usedPercentage}%"></div>
                    </div>
                    <div class="mt-2">
                        <h5 class="text-xs font-medium text-slate-300 mb-1">Recent Files</h5>
                        ${this.files.map(file => `
                            <div class="flex items-center justify-between bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors mb-1">
                                <div class="flex items-center">
                                    <i data-lucide="${file.type === 'folder' ? 'folder' : 'file'}" class="h-3 w-3 mr-1 text-${this.color}-400"></i>
                                    <span class="text-xs text-slate-300">${file.name}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-xs text-slate-500 mr-2">${file.size}</span>
                                    <span class="text-xs text-slate-500">${file.modified}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-3", "w-3"]
                        },
                        elements: [storageContent]
                    });
                }
            }

            // Show storage
            storageContainer.classList.remove('hidden');
        } else {
            // Hide storage
            storageContainer.classList.add('hidden');
        }
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoCloud',
            name: 'KevkoCloud',
            description: 'Secure cloud storage',
            icon: 'cloud',
            color: 'green',
            category: 'Storage',
            version: '1.0.0',
            author: 'Kevko',
            dependencies: [],
            features: [
                'Secure file storage',
                'File sharing',
                'Automatic backups',
                'Cross-device sync'
            ],
            screenshots: [
                '/img/services/kevkoCloud/screenshot1.jpg'
            ]
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }

    /**
     * Get service downloads
     * @returns {Array} Service downloads
     */
    static getDownloads() {
        return [
            {
                name: 'KevkoCloud Desktop Sync',
                description: 'Sync your files with your desktop',
                icon: 'monitor',
                size: '32 MB',
                version: '1.2.1',
                date: '2023-03-20',
                url: '#'
            },
            {
                name: 'KevkoCloud Mobile App',
                description: 'Access your files on the go',
                icon: 'smartphone',
                size: '15 MB',
                version: '1.2.0',
                date: '2023-03-15',
                url: '#'
            }
        ];
    }
};
