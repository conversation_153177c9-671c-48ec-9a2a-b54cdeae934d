import os
import sys
from dotenv import load_dotenv
from mongoengine import connect, disconnect
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.user_activity import UserActivity

def setup_user_activity():
    """Set up UserActivity model and indexes"""
    # Load environment variables
    load_dotenv()
    
    # Connect to MongoDB
    disconnect()
    connect(
        db='kevko_systems',
        host=os.getenv('MONGO_URI'),
        alias='default'
    )
    
    # Ensure indexes are created
    UserActivity.ensure_indexes()
    print("UserActivity indexes created successfully")

if __name__ == '__main__':
    setup_user_activity()
    print("\nRun this script to set up the UserActivity model and indexes")
    print("Usage: python scripts/setup_user_activity.py")
