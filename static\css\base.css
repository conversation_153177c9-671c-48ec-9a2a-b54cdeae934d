/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
    background-color: #121212;
    color: #ffffff;
}

/* Navigation styles */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

.nav-left .logo {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Main content area */
main {
    padding-top: 60px; /* Account for fixed nav */
    min-height: calc(100vh - 60px);
}

/* Common utility classes */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Links */
a {
    color: #114cec;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2032d1;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1DB954;
    color: white;
}

.btn-primary:hover {
    background-color: #1ed760;
}