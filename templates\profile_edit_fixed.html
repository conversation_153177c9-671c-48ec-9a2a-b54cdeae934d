{% extends "base.html" %}

{% block title %}Edit Profile{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&family=Outfit:wght@400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Custom CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/profile-edit.css') }}">
{% endblock %}

{% block content %}
<div class="edit-container">
    <div class="edit-header">
        <h1 class="edit-title">Customize Your Profile</h1>
        <p class="edit-subtitle">Make your profile uniquely yours with these customization options</p>
    </div>
    
    <form id="profile-edit-form" class="edit-form">
        <!-- Profile Info Section -->
        <div class="edit-section profile-info-section">
            <div class="profile-info-header">
                <div class="profile-picture-edit">
                    <img src="{{ user.profile_picture or url_for('static', filename='images/default-avatar.png') }}" 
                         alt="{{ user.username }}" crossorigin="anonymous">
                    <div class="profile-picture-overlay">
                        <span class="profile-picture-icon"><i class="fas fa-camera"></i></span>
                    </div>
                    <input type="file" id="profile-picture-input" accept="image/*" style="display: none;">
                </div>
                
                <div class="profile-info-details">
                    <h2 class="profile-info-name">{{ user.display_name or user.username }}</h2>
                    <p class="profile-info-username">@{{ user.username }}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label for="display_name" class="form-label">Display Name</label>
                <p class="form-hint">This is how your name will appear on your profile</p>
                <input type="text" id="display_name" name="display_name" class="form-input" 
                       value="{{ user.display_name or '' }}" placeholder="Your display name">
            </div>
            
            <input type="hidden" name="username" value="{{ user.username }}">
        </div>
        
        <!-- Appearance Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Appearance</h3>
            
            <div class="form-group">
                <label for="background_type" class="form-label">Background Type</label>
                <select id="background_type" name="background_type" class="form-select">
                    <option value="color" {% if profile.background_type == 'color' %}selected{% endif %}>Solid Color</option>
                    <option value="gradient" {% if profile.background_type == 'gradient' %}selected{% endif %}>Gradient</option>
                    <option value="image" {% if profile.background_type == 'image' %}selected{% endif %}>Image</option>
                    <option value="pattern" {% if profile.background_type == 'pattern' %}selected{% endif %}>Pattern</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="background_color" class="form-label">Background Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.background_color }};"></div>
                    <input type="color" id="background_color" name="background_color" 
                           value="{{ profile.background_color }}" class="form-input">
                </div>
            </div>
            
            <div class="form-group">
                <label for="background_gradient" class="form-label">Background Gradient</label>
                <input type="text" id="background_gradient" name="background_gradient" class="form-input" 
                       value="{{ profile.background_gradient }}" 
                       placeholder="linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)">
            </div>
            
            <div class="form-group">
                <label for="background_image" class="form-label">Background Image URL</label>
                <input type="url" id="background_image" name="background_image" class="form-input" 
                       value="{{ profile.background_image }}" placeholder="https://example.com/image.jpg">
            </div>
            
            <div class="form-group">
                <label for="text_color" class="form-label">Text Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.text_color }};"></div>
                    <input type="color" id="text_color" name="text_color" 
                           value="{{ profile.text_color }}" class="form-input">
                </div>
            </div>
            
            <div class="form-group">
                <label for="accent_color" class="form-label">Accent Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.accent_color }};"></div>
                    <input type="color" id="accent_color" name="accent_color" 
                           value="{{ profile.accent_color }}" class="form-input">
                </div>
            </div>
            
            <div class="form-group">
                <label for="layout_type" class="form-label">Layout Style</label>
                <select id="layout_type" name="layout_type" class="form-select">
                    <option value="standard" {% if profile.layout_type == 'standard' %}selected{% endif %}>Standard</option>
                    <option value="compact" {% if profile.layout_type == 'compact' %}selected{% endif %}>Compact</option>
                    <option value="expanded" {% if profile.layout_type == 'expanded' %}selected{% endif %}>Expanded</option>
                    <option value="minimal" {% if profile.layout_type == 'minimal' %}selected{% endif %}>Minimal</option>
                </select>
            </div>
        </div>
        
        <!-- Typography Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Typography</h3>
            
            <div class="form-group">
                <label for="font_family" class="form-label">Main Font</label>
                <select id="font_family" name="font_family" class="form-select">
                    <option value="Inter, system-ui, sans-serif" {% if 'Inter' in profile.font_family %}selected{% endif %}>Inter</option>
                    <option value="'Space Grotesk', system-ui, sans-serif" {% if 'Space Grotesk' in profile.font_family %}selected{% endif %}>Space Grotesk</option>
                    <option value="'Outfit', system-ui, sans-serif" {% if 'Outfit' in profile.font_family %}selected{% endif %}>Outfit</option>
                    <option value="'Poppins', system-ui, sans-serif" {% if 'Poppins' in profile.font_family %}selected{% endif %}>Poppins</option>
                    <option value="'Montserrat', system-ui, sans-serif" {% if 'Montserrat' in profile.font_family %}selected{% endif %}>Montserrat</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="heading_font" class="form-label">Heading Font</label>
                <select id="heading_font" name="heading_font" class="form-select">
                    <option value="'Space Grotesk', system-ui, sans-serif" {% if 'Space Grotesk' in profile.heading_font %}selected{% endif %}>Space Grotesk</option>
                    <option value="'Outfit', system-ui, sans-serif" {% if 'Outfit' in profile.heading_font %}selected{% endif %}>Outfit</option>
                    <option value="Inter, system-ui, sans-serif" {% if 'Inter' in profile.heading_font %}selected{% endif %}>Inter</option>
                    <option value="'Poppins', system-ui, sans-serif" {% if 'Poppins' in profile.heading_font %}selected{% endif %}>Poppins</option>
                    <option value="'Montserrat', system-ui, sans-serif" {% if 'Montserrat' in profile.heading_font %}selected{% endif %}>Montserrat</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="custom_css" class="form-label">Custom CSS</label>
                <p class="form-hint">Advanced users can add custom CSS styles</p>
                <textarea id="custom_css" name="custom_css" class="form-textarea" 
                          placeholder="/* Your custom CSS here */
.profile-card { /* custom styles */ }">{{ profile.custom_css }}</textarea>
            </div>
        </div>
        
        <!-- Content Display Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Content Display</h3>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_activity" 
                           {% if profile.show_activity %}checked{% endif %}>
                    Show Activity
                </label>
                <p class="form-hint">Display your recent activity on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_friends" 
                           {% if profile.show_friends %}checked{% endif %}>
                    Show Friends
                </label>
                <p class="form-hint">Display your friends list on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_stats" 
                           {% if profile.show_stats %}checked{% endif %}>
                    Show Statistics
                </label>
                <p class="form-hint">Display usage statistics on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="is_public" 
                           {% if profile.is_public %}checked{% endif %}>
                    Public Profile
                </label>
                <p class="form-hint">Make your profile visible to everyone</p>
            </div>
        </div>
        
        <!-- Social Links Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Social Links</h3>
            
            <div id="social-links-container" class="social-links-container">
                {% if profile.social_links %}
                    {% for platform, url in profile.social_links.items() %}
                    <div class="social-link-item">
                        <div class="social-link-icon">
                            {% set platform_lower = platform.lower() %}
                            {% if platform_lower == 'x' or platform_lower == 'twitter' %}
                                <i class="fab fa-x-twitter"></i>
                            {% elif platform_lower == 'instagram' %}
                                <i class="fab fa-instagram"></i>
                            {% elif platform_lower == 'facebook' %}
                                <i class="fab fa-facebook"></i>
                            {% elif platform_lower == 'linkedin' %}
                                <i class="fab fa-linkedin"></i>
                            {% elif platform_lower == 'github' %}
                                <i class="fab fa-github"></i>
                            {% elif platform_lower == 'youtube' %}
                                <i class="fab fa-youtube"></i>
                            {% elif platform_lower == 'twitch' %}
                                <i class="fab fa-twitch"></i>
                            {% elif platform_lower == 'discord' %}
                                <i class="fab fa-discord"></i>
                            {% elif platform_lower == 'tiktok' %}
                                <i class="fab fa-tiktok"></i>
                            {% elif platform_lower == 'reddit' %}
                                <i class="fab fa-reddit"></i>
                            {% else %}
                                <i class="fas fa-link"></i>
                            {% endif %}
                        </div>
                        <select class="form-input social-link-platform" name="social_platform[]">
                            <option value="Twitter" {% if platform_lower == 'twitter' or platform_lower == 'x' %}selected{% endif %}>Twitter</option>
                            <option value="Instagram" {% if platform_lower == 'instagram' %}selected{% endif %}>Instagram</option>
                            <option value="Facebook" {% if platform_lower == 'facebook' %}selected{% endif %}>Facebook</option>
                            <option value="LinkedIn" {% if platform_lower == 'linkedin' %}selected{% endif %}>LinkedIn</option>
                            <option value="GitHub" {% if platform_lower == 'github' %}selected{% endif %}>GitHub</option>
                            <option value="YouTube" {% if platform_lower == 'youtube' %}selected{% endif %}>YouTube</option>
                            <option value="Twitch" {% if platform_lower == 'twitch' %}selected{% endif %}>Twitch</option>
                            <option value="Discord" {% if platform_lower == 'discord' %}selected{% endif %}>Discord</option>
                            <option value="TikTok" {% if platform_lower == 'tiktok' %}selected{% endif %}>TikTok</option>
                            <option value="Reddit" {% if platform_lower == 'reddit' %}selected{% endif %}>Reddit</option>
                            <option value="Other" {% if platform_lower not in ['twitter', 'x', 'instagram', 'facebook', 'linkedin', 'github', 'youtube', 'twitch', 'discord', 'tiktok', 'reddit'] %}selected{% endif %}>Other</option>
                        </select>
                        <input type="url" class="form-input social-link-url" name="social_url[]" value="{{ url }}" placeholder="https://">
                        <button type="button" class="social-link-remove" aria-label="Remove social link">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            
            <button type="button" id="add-social-link" class="add-social-link">
                <i class="fas fa-plus"></i> Add Social Link
            </button>
        </div>
        
        <!-- Custom Sections -->
        <div class="edit-section">
            <h3 class="edit-section-title">Custom Sections</h3>
            <p class="form-hint">Add custom sections to your profile to showcase your interests, skills, or anything else</p>
            
            <div id="custom-sections-container" class="custom-sections-container">
                {% if profile.custom_sections %}
                    {% for section_name, section_content in profile.custom_sections.items() %}
                    <div class="custom-section-item">
                        <div class="custom-section-header">
                            <input type="text" class="form-input custom-section-title-input" 
                                   name="section_title[]" value="{{ section_name|title }}" placeholder="Section Title">
                            <button type="button" class="custom-section-remove" aria-label="Remove section">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <textarea class="form-textarea" name="section_content[]" 
                                  placeholder="Section content (HTML supported)">{{ section_content }}</textarea>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            
            <button type="button" id="add-custom-section" class="add-custom-section">
                <i class="fas fa-plus"></i> Add Custom Section
            </button>
        </div>
        
        <!-- Preview Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Preview</h3>
            <p class="form-hint">See how your profile will look with the current settings</p>
            
            <button type="button" id="preview-button" class="btn btn-secondary">
                <i class="fas fa-eye"></i> Generate Preview
            </button>
            
            <iframe id="preview-frame" class="preview-frame" src="{{ url_for('profile_page.preview_profile') }}"></iframe>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{{ url_for('profile_page.view_profile', username=user.username) }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Save Changes
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/profile-edit.js') }}"></script>
{% endblock %}