/* Thinking Container Styles */
.thinking-container {
  margin: 1rem 0;
  background-color: rgba(30, 30, 40, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 850px;
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: rgba(25, 25, 35, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  user-select: none;
}

.thinking-header-icon {
  display: flex;
  align-items: center;
  margin-right: 0.75rem;
}

.thinking-header-icon svg {
  width: 18px;
  height: 18px;
  color: #64748b;
}

.thinking-header-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #94a3b8;
  flex-grow: 1;
}

.thinking-toggle {
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.thinking-toggle svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.thinking-toggle.expanded {
  transform: rotate(180deg);
}

.thinking-content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  font-family: monospace;
  font-size: 0.875rem;
  color: #cbd5e1;
  white-space: pre-wrap;
  line-height: 1.5;
}

.thinking-content.expanded {
  padding: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

/* Scrollbar styling for thinking content */
.thinking-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.thinking-content::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 3px;
}

.thinking-content::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

/* Thinking animation */
@keyframes thinking-pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.thinking-header-title.thinking {
  animation: thinking-pulse 1.5s infinite ease-in-out;
}
