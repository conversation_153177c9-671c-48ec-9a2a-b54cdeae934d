# Message Obfuscation System

This system implements basic message obfuscation for the friends chat feature. The obfuscation ensures that casual database viewers cannot easily read the message content.

## How It Works

1. **Encoding**: Messages are encoded using Base64 encoding before being stored in the database.
2. **Prefix**: Encoded messages are prefixed with "ENC_" to indicate they are encoded.
3. **Decoding**: Messages are automatically decoded when retrieved through the API.

## Implementation Details

- The encoding/decoding happens at the model level, so it's transparent to the rest of the application.
- Each message has an `encrypted` flag to indicate whether it's encoded.
- The system handles both regular user messages and system messages.

## Security Considerations

- This is a simple obfuscation technique, not true encryption. It provides privacy from casual observation but not from determined attackers.
- For stronger security, consider implementing proper encryption with secure key management.

## Migrating Existing Messages

To encode existing messages in the database, run the migration script:

```bash
python scripts/encrypt_existing_messages.py
```

## Troubleshooting

If you encounter issues with message encoding or decoding:

1. Check that the message format is correct (encoded messages should start with "ENC_").
2. Look for error messages in the logs related to encoding/decoding.

## Future Improvements

- Implement true encryption with proper key management
- Add support for end-to-end encryption with client-side keys
- Implement secure key storage separate from the database
