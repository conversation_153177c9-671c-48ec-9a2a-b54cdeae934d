/**
 * Service Engagement Tracker
 * Tracks when a user visits a service and stays for at least 5 minutes
 */
class ServiceEngagementTracker {
    constructor() {
        this.currentService = null;
        this.engagementStartTime = null;
        this.engagementId = null;
        this.checkInterval = null;
        this.ENGAGEMENT_THRESHOLD_MS = 5 * 60 * 1000; // 5 minutes in milliseconds
        this.HEARTBEAT_INTERVAL_MS = 30 * 1000; // 30 seconds in milliseconds

        // Initialize
        this.init();
    }

    /**
     * Initialize the tracker
     */
    init() {
        // Listen for view changes
        document.addEventListener('view-changed', (event) => {
            const newView = event.detail.view;
            this.handleViewChange(newView);
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Listen for page unload
        window.addEventListener('beforeunload', () => {
            this.completeCurrentEngagement();
        });

        // Start tracking the current view
        const dashboard = window.dashboardInstance;
        if (dashboard && dashboard.currentView) {
            this.handleViewChange(dashboard.currentView);
        }
    }

    /**
     * Handle view changes
     * @param {string} newView - The new view
     */
    handleViewChange(newView) {
        // Complete the current engagement if there is one
        if (this.currentService) {
            this.completeCurrentEngagement();
        }

        // Map view to service
        const serviceMap = {
            'dashboard': 'dashboard',
            'store': 'store',
            'profile': 'profile',
            'friends': 'friends',
            'settings': 'settings',
            'contact': 'contact',
            'statistics': 'statistics'
        };

        // Start tracking the new service
        const newService = serviceMap[newView] || newView;
        this.startEngagement(newService);
    }

    /**
     * Handle visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // User left the page, pause the engagement
            clearInterval(this.checkInterval);
        } else {
            // User returned to the page, resume the engagement
            this.startHeartbeat();
        }
    }

    /**
     * Start tracking a user's engagement with a service
     * @param {string} service - The service to track
     */
    startEngagement(service) {
        this.currentService = service;
        this.engagementStartTime = Date.now();

        try {
            // Call the API to start tracking
            fetch('/api/statistics/engagement/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    service: service
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.engagementId = data.engagement_id;
                    this.startHeartbeat();
                } else {
                    console.warn('Failed to start engagement tracking:', data.error);
                    // Start heartbeat anyway to track locally
                    this.startHeartbeat();
                }
            })
            .catch(error => {
                console.error('Error starting engagement tracking:', error);
                // Start heartbeat anyway to track locally
                this.startHeartbeat();
            });
        } catch (error) {
            console.error('Exception in startEngagement:', error);
            // Start heartbeat anyway to track locally
            this.startHeartbeat();
        }
    }

    /**
     * Start the heartbeat to check engagement duration
     */
    startHeartbeat() {
        // Clear any existing interval
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }

        // Start a new interval
        this.checkInterval = setInterval(() => {
            const currentTime = Date.now();
            const duration = currentTime - this.engagementStartTime;

            // If the user has been engaged for at least 5 minutes, complete the engagement
            if (duration >= this.ENGAGEMENT_THRESHOLD_MS) {
                this.completeCurrentEngagement();

                // Start a new engagement for the same service
                this.startEngagement(this.currentService);
            }
        }, this.HEARTBEAT_INTERVAL_MS);
    }

    /**
     * Complete the current engagement
     */
    completeCurrentEngagement() {
        if (!this.engagementId) return;

        // Call the API to complete the engagement
        fetch('/api/statistics/engagement/complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                engagement_id: this.engagementId,
                end_time: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reset the current engagement
                this.engagementId = null;
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }
        })
        .catch(error => {
            console.error('Error completing engagement tracking:', error);

            // Reset the current engagement even if there's an error
            // This prevents the tracker from getting stuck
            this.engagementId = null;
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
                this.checkInterval = null;
            }
        });
    }
}

// Initialize the tracker when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.serviceEngagementTracker = new ServiceEngagementTracker();
});
