from flask import jsonify, request, session
from flask_login import login_required, current_user
from . import admin_bp
from models.user import User
from models.user_activity import UserActivity
from datetime import datetime
import random
import string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os

def admin_required(f):
    """Decorator to check if user is an admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({'error': 'Admin access required'}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return login_required(decorated_function)

def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))

def send_admin_verification_email(email, code, action, target_email=None):
    """Send verification email for admin actions"""
    try:
        sender_email = os.environ.get('EMAIL_USER')
        sender_password = os.environ.get('EMAIL_PASSWORD')

        if not sender_email or not sender_password:
            print("Email credentials not set in environment variables")
            return False

        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = email

        if action == 'add':
            msg['Subject'] = 'Admin Verification - Add Admin'
            body = f"""
            <html>
            <body>
                <h2>Admin Verification</h2>
                <p>You are about to add <strong>{target_email}</strong> as an admin.</p>
                <p>Your verification code is: <strong>{code}</strong></p>
                <p>This code will expire in 10 minutes.</p>
                <p>If you did not request this action, please ignore this email.</p>
            </body>
            </html>
            """
        elif action == 'remove':
            msg['Subject'] = 'Admin Verification - Remove Admin'
            body = f"""
            <html>
            <body>
                <h2>Admin Verification</h2>
                <p>You are about to remove <strong>{target_email}</strong> as an admin.</p>
                <p>Your verification code is: <strong>{code}</strong></p>
                <p>This code will expire in 10 minutes.</p>
                <p>If you did not request this action, please ignore this email.</p>
            </body>
            </html>
            """
        else:
            msg['Subject'] = 'Admin Verification'
            body = f"""
            <html>
            <body>
                <h2>Admin Verification</h2>
                <p>Your verification code is: <strong>{code}</strong></p>
                <p>This code will expire in 10 minutes.</p>
                <p>If you did not request this action, please ignore this email.</p>
            </body>
            </html>
            """

        msg.attach(MIMEText(body, 'html'))

        with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
            server.login(sender_email, sender_password)
            server.send_message(msg)

        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

@admin_bp.route('/check', methods=['GET'])
@login_required
def check_admin():
    """Check if current user is an admin"""
    is_admin = current_user.is_admin
    return jsonify({
        'is_admin': is_admin,
        'email': current_user.email
    })

@admin_bp.route('/request-verification', methods=['POST'])
@admin_required
def request_verification():
    """Request verification code for admin actions"""
    try:
        data = request.get_json()
        action = data.get('action')
        target_email = data.get('email')

        if not action or action not in ['add', 'remove']:
            return jsonify({'error': 'Invalid action'}), 400

        if not target_email:
            return jsonify({'error': 'Email is required'}), 400

        # Generate and store verification code
        code = generate_verification_code()
        session[f'admin_verification_code_{current_user.email}'] = code
        session[f'admin_verification_timestamp_{current_user.email}'] = datetime.now().timestamp()
        session[f'admin_verification_action_{current_user.email}'] = action
        session[f'admin_verification_target_{current_user.email}'] = target_email

        # Send verification email
        if send_admin_verification_email(current_user.email, code, action, target_email):
            return jsonify({'message': 'Verification code sent successfully'})
        return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        print(f"Error in request_verification: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/add-admin', methods=['POST'])
@admin_required
def add_admin():
    """Add a new admin"""
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')

        if not email or not verification_code:
            return jsonify({'error': 'Email and verification code are required'}), 400

        # Verify the stored verification code
        stored_code = session.get(f'admin_verification_code_{current_user.email}')
        code_timestamp = session.get(f'admin_verification_timestamp_{current_user.email}')
        stored_action = session.get(f'admin_verification_action_{current_user.email}')
        stored_target = session.get(f'admin_verification_target_{current_user.email}')

        if not stored_code or not code_timestamp or not stored_action or not stored_target:
            return jsonify({'error': 'Verification code has expired'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:  # 10 minutes
            return jsonify({'error': 'Verification code has expired'}), 400

        if verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if stored_action != 'add' or stored_target != email:
            return jsonify({'error': 'Verification code is for a different action or email'}), 400

        # Find user by email
        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user is already an admin
        if user.is_admin:
            return jsonify({'error': 'User is already an admin'}), 400

        # Make user an admin
        user.is_admin = True
        user.save()

        # Clear verification codes
        session.pop(f'admin_verification_code_{current_user.email}', None)
        session.pop(f'admin_verification_timestamp_{current_user.email}', None)
        session.pop(f'admin_verification_action_{current_user.email}', None)
        session.pop(f'admin_verification_target_{current_user.email}', None)

        return jsonify({'message': f'User {email} is now an admin'})
    except Exception as e:
        print(f"Error in add_admin: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/remove-admin', methods=['POST'])
@admin_required
def remove_admin():
    """Remove an admin"""
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')

        if not email or not verification_code:
            return jsonify({'error': 'Email and verification code are required'}), 400

        # Verify the stored verification code
        stored_code = session.get(f'admin_verification_code_{current_user.email}')
        code_timestamp = session.get(f'admin_verification_timestamp_{current_user.email}')
        stored_action = session.get(f'admin_verification_action_{current_user.email}')
        stored_target = session.get(f'admin_verification_target_{current_user.email}')

        if not stored_code or not code_timestamp or not stored_action or not stored_target:
            return jsonify({'error': 'Verification code has expired'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:  # 10 minutes
            return jsonify({'error': 'Verification code has expired'}), 400

        if verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        if stored_action != 'remove' or stored_target != email:
            return jsonify({'error': 'Verification code is for a different action or email'}), 400

        # Find user by email
        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user is an admin
        if not user.is_admin:
            return jsonify({'error': 'User is not an admin'}), 400

        # Check if user is one of the primary admins
        primary_admins = ['<EMAIL>', '<EMAIL>']
        if email in primary_admins:
            return jsonify({'error': 'Cannot remove a primary admin'}), 400

        # Remove admin status
        user.is_admin = False
        user.save()

        # Clear verification codes
        session.pop(f'admin_verification_code_{current_user.email}', None)
        session.pop(f'admin_verification_timestamp_{current_user.email}', None)
        session.pop(f'admin_verification_action_{current_user.email}', None)
        session.pop(f'admin_verification_target_{current_user.email}', None)

        return jsonify({'message': f'Admin status removed from {email}'})
    except Exception as e:
        print(f"Error in remove_admin: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/list-admins', methods=['GET'])
@admin_required
def list_admins():
    """List all admins"""
    try:
        admins = User.objects(is_admin=True)
        primary_admins = ['<EMAIL>', '<EMAIL>']
        return jsonify([{
            'email': admin.email,
            'username': admin.username,
            'is_primary': admin.email in primary_admins
        } for admin in admins])
    except Exception as e:
        print(f"Error in list_admins: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/contacts', methods=['GET'])
def admin_contacts():
    """Get admin contact information for the contact page"""
    try:
        admins = User.objects(is_admin=True).only('email', 'username', 'profile_picture')
        primary_admins = ['<EMAIL>', '<EMAIL>']

        # Sort admins to put primary admins first
        sorted_admins = sorted(admins, key=lambda admin: 0 if admin.email in primary_admins else 1)

        return jsonify([{
            'email': admin.email,
            'username': admin.username,
            'name': admin.username.split('@')[0] if '@' in admin.username else admin.username,
            'is_primary': admin.email in primary_admins,
            'profile_picture': admin.profile_picture if hasattr(admin, 'profile_picture') else None
        } for admin in sorted_admins])
    except Exception as e:
        print(f"Error in admin_contacts: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/active-users', methods=['GET'])
@admin_required
def active_users():
    """Get active users across all sections"""
    try:
        # Get the minutes parameter (default: 15)
        minutes = request.args.get('minutes', 15, type=int)

        # Get active users for each section
        chat_users = UserActivity.get_active_users('chat', minutes)
        live_users = UserActivity.get_active_users('live', minutes)
        spotify_users = UserActivity.get_active_users('spotify', minutes)

        # Format the response
        result = {
            'chat': [activity.to_dict() for activity in chat_users],
            'live': [activity.to_dict() for activity in live_users],
            'spotify': [activity.to_dict() for activity in spotify_users],
            'total_unique': len(set(
                [str(activity.user.id) for activity in chat_users] +
                [str(activity.user.id) for activity in live_users] +
                [str(activity.user.id) for activity in spotify_users]
            ))
        }

        return jsonify(result)
    except Exception as e:
        print(f"Error in active_users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_bp.route('/search-users', methods=['GET'])
@admin_required
def search_users():
    """Search for users by username or email"""
    try:
        # Get the search query
        query = request.args.get('query', '')
        if not query or len(query) < 3:
            return jsonify({'error': 'Search query must be at least 3 characters'}), 400

        # Search for users by username or email
        users = User.objects.filter(
            __raw__={
                '$or': [
                    {'username': {'$regex': query, '$options': 'i'}},
                    {'email': {'$regex': query, '$options': 'i'}}
                ]
            }
        ).limit(10)  # Limit to 10 results

        # Format the response
        result = [{
            'id': str(user.id),
            'username': user.username,
            'email': user.email,
            'profile_picture': user.profile_picture if hasattr(user, 'profile_picture') else None,
            'created_at': user.created_at.isoformat() if hasattr(user, 'created_at') else None,
            'is_admin': user.is_admin if hasattr(user, 'is_admin') else False
        } for user in users]

        return jsonify(result)
    except Exception as e:
        print(f"Error in search_users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# Admin updates endpoints have been moved to api/admin/updates.py
