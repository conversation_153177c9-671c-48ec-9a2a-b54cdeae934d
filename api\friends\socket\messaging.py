"""
Socket.IO message handlers for the friends API.
This module contains handlers for sending and receiving messages between friends and in group chats.
"""
from flask import request
from flask_login import current_user
from flask_socketio import emit, join_room
from models.friend_chat import Friend<PERSON>hat
from models.group_chat import GroupChat
from models.user import User
from models.restricted_user import RestrictedUser
import logging

def init_messaging_handlers(socketio):
    """Initialize Socket.IO messaging event handlers for friends"""

    @socketio.on('friend_send_message')
    def handle_friend_send_message(data):
        """Handle friend message sending"""
        if not current_user.is_authenticated:
            emit('error', {
                'message': 'Authentication required'
            })
            return

        # Check if user is restricted from friends service
        if RestrictedUser.is_restricted(current_user.email, 'friends'):
            emit('error', {
                'message': 'You are restricted from using the friends service'
            })
            return

        # Extract data
        chat_id = data.get('chat_id')
        message_content = data.get('message', '')
        images = data.get('images', [])

        # Validate data
        if not chat_id:
            emit('error', {
                'message': 'Chat ID is required'
            })
            return

        # Require either a message or images
        if not message_content.strip() and not images:
            emit('error', {
                'message': 'Message or images are required'
            })
            return

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            emit('error', {
                'message': 'Chat not found'
            })
            return

        # Check if user is allowed to send messages to this chat
        if not (str(chat.user1.id) == str(current_user.id) or str(chat.user2.id) == str(current_user.id)):
            emit('error', {
                'message': 'Not authorized to send messages to this chat'
            })
            return

        try:
            # Add message to chat with retry mechanism
            max_retries = 3
            retry_count = 0
            message_obj = None
            
            while retry_count < max_retries:
                try:
                    # Add message to chat with a database write lock
                    message_obj = chat.add_message(current_user, message_content, images)
                    # Ensure the message is saved by forcing a save operation
                    chat.save()
                    break  # Exit the retry loop if successful
                except Exception as e:
                    retry_count += 1
                    logging.error(f"Error adding message to friend chat (attempt {retry_count}): {str(e)}")
                    if retry_count >= max_retries:
                        raise  # Re-raise the exception if we've exhausted retries
                    import time
                    time.sleep(0.1)  # Small delay before retry

            # Emit to sender for confirmation
            emit('friend_message_sent', {
                'success': True,
                'chat_id': chat_id,
                'message': message_obj
            })

            # Determine the recipient
            recipient_id = str(chat.user2.id) if str(chat.user1.id) == str(current_user.id) else str(chat.user1.id)
            recipient_room = f"user_{recipient_id}"

            # Only emit to the recipient's room - this prevents duplication
            emit('friend_new_message', {
                'chat_id': chat_id,
                'message': message_obj
            }, room=recipient_room)

            logging.info(f"Message sent from {current_user.username} to chat {chat_id} (recipient: {recipient_id})")

        except Exception as e:
            logging.error(f"Error sending message: {str(e)}")
            emit('error', {
                'message': f'Error sending message: {str(e)}'
            })

    @socketio.on('group_send_message')
    def handle_group_send_message(data):
        """Handle group chat message sending"""
        if not current_user.is_authenticated:
            emit('error', {
                'message': 'Authentication required'
            })
            return

        # Check if user is restricted from friends service
        if RestrictedUser.is_restricted(current_user.email, 'friends'):
            emit('error', {
                'message': 'You are restricted from using the friends service'
            })
            return

        # Extract data
        chat_id = data.get('chat_id')
        message_content = data.get('message', '')
        images = data.get('images', [])
        temp_id = data.get('temp_id')  # Get temporary ID if provided by client

        # Log the received data for debugging
        logging.info(f"Received group_send_message: chat_id={chat_id}, message={message_content}, images={images}, temp_id={temp_id}")

        # Validate data
        if not chat_id:
            emit('error', {
                'message': 'Chat ID is required'
            })
            return

        # Require either a message or images
        if not message_content.strip() and (not images or len(images) == 0):
            emit('error', {
                'message': 'Message or images are required'
            })
            return

        # Get the group chat
        try:
            chat = GroupChat.objects(chat_id=chat_id).first()
            if not chat:
                logging.error(f"Group chat not found: {chat_id}")
                emit('error', {
                    'message': 'Group chat not found'
                })
                return

            # Check if user is a member of this group chat
            if not any(str(member.id) == str(current_user.id) for member in chat.members):
                logging.error(f"User {current_user.username} is not a member of group chat {chat_id}")
                emit('error', {
                    'message': 'Not authorized to send messages to this group chat'
                })
                return

            # Add message to chat with retry mechanism
            max_retries = 3
            retry_count = 0
            message_obj = None
            
            while retry_count < max_retries:
                try:
                    # Add message to chat with a database write lock
                    message_obj = chat.add_message(current_user, message_content, images=images)
                    # Ensure the message is saved by forcing a save operation
                    chat.save()
                    logging.info(f"Added message to group chat {chat_id}: {message_obj}")
                    break  # Exit the retry loop if successful
                except Exception as e:
                    retry_count += 1
                    logging.error(f"Error adding message to group chat (attempt {retry_count}): {str(e)}")
                    if retry_count >= max_retries:
                        raise  # Re-raise the exception if we've exhausted retries
                    import time
                    time.sleep(0.1)  # Small delay before retry
            
            # Emit to sender for confirmation
            emit('group_message_sent', {
                'success': True,
                'chat_id': chat_id,
                'message': message_obj,
                'temp_id': temp_id  # Include the temporary ID in the response
            })

            # Emit to all members of the group chat
            chat_room = f"group_{chat_id}"
            socketio.emit('group_new_message', {
                'chat_id': chat_id,
                'message': message_obj
            }, room=chat_room)

            logging.info(f"Group message sent from {current_user.username} to group chat {chat_id}")

        except Exception as e:
            logging.error(f"Error sending group message: {str(e)}")
            emit('error', {
                'message': f'Error sending group message: {str(e)}'
            })

    @socketio.on('group_typing')
    def handle_group_typing(data):
        """Handle group chat typing indicator"""
        if not current_user.is_authenticated:
            return

        chat_id = data.get('chat_id')
        typing = data.get('typing', False)

        if not chat_id:
            return

        try:
            # Get the group chat
            chat = GroupChat.objects(chat_id=chat_id).first()
            if not chat:
                return

            # Check if user is a member of this group chat
            if not any(str(member.id) == str(current_user.id) for member in chat.members):
                return

            # Emit typing indicator to all members of the group chat
            chat_room = f"group_{chat_id}"
            emit('group_typing', {
                'chat_id': chat_id,
                'user_id': str(current_user.id),
                'username': current_user.username,
                'typing': typing
            }, room=chat_room, include_self=False)

        except Exception as e:
            logging.error(f"Error handling group typing: {str(e)}")

    @socketio.on('friend_typing')
    def handle_friend_typing(data):
        """Handle friend typing indicator"""
        if not current_user.is_authenticated:
            return

        chat_id = data.get('chat_id')
        is_typing = data.get('is_typing', False)

        if not chat_id:
            return

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            return

        # Check if user is allowed to send typing indicators to this chat
        if not (str(chat.user1.id) == str(current_user.id) or str(chat.user2.id) == str(current_user.id)):
            return

        # Determine the recipient
        recipient_id = str(chat.user2.id) if str(chat.user1.id) == str(current_user.id) else str(chat.user1.id)
        recipient_room = f"user_{recipient_id}"

        # Emit typing indicator to recipient
        emit('friend_typing', {
            'chat_id': chat_id,
            'user_id': str(current_user.id),
            'is_typing': is_typing
        }, room=recipient_room)