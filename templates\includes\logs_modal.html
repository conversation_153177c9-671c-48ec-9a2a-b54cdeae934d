<div id="logsModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-900 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">System Logs</h3>
            <button id="closeLogsModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>

        <!-- Filters -->
        <div class="flex flex-wrap gap-2 mb-4">
            <div class="flex items-center">
                <label for="logsServiceFilter" class="text-sm text-slate-400 mr-2">Service:</label>
                <select id="logsServiceFilter" class="bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-2 py-1">
                    <option value="">All</option>
                    <option value="chat">Chat</option>
                    <option value="live">Live Chat</option>
                    <option value="spotify">Spotify</option>
                </select>
            </div>
            <div class="flex items-center ml-4">
                <label for="logsActionFilter" class="text-sm text-slate-400 mr-2">Action:</label>
                <select id="logsActionFilter" class="bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-2 py-1">
                    <option value="">All</option>
                    <option value="thread_create">Thread Create</option>
                    <option value="thread_interaction">Thread Interaction</option>
                    <option value="live_create">Live Create</option>
                    <option value="live_join">Live Join</option>
                    <option value="live_interaction">Live Interaction</option>
                    <option value="playback_control">Playback Control</option>
                </select>
            </div>
            <div class="flex items-center ml-4">
                <label for="logsTimeFilter" class="text-sm text-slate-400 mr-2">Time:</label>
                <select id="logsTimeFilter" class="bg-slate-800 border border-slate-700 rounded text-sm text-slate-200 px-2 py-1">
                    <option value="24">Last 24 hours</option>
                    <option value="48">Last 48 hours</option>
                    <option value="72">Last 72 hours</option>
                    <option value="168">Last week</option>
                </select>
            </div>
            <div class="ml-auto flex space-x-2">
                <button id="clearLogsBtn" class="bg-red-600/80 hover:bg-red-500 text-white rounded px-3 py-1 text-sm flex items-center">
                    <i data-lucide="trash-2" class="h-3 w-3 mr-1"></i>
                    Clear Logs
                </button>
                <button id="refreshLogs" class="bg-slate-700 hover:bg-slate-600 text-slate-300 rounded px-3 py-1 text-sm flex items-center">
                    <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Logs Container -->
        <div class="overflow-y-auto flex-grow">
            <div id="logsContainer" class="space-y-2">
                <!-- Logs will be loaded here -->
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400">Loading logs...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
