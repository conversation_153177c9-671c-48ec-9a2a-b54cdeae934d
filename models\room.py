from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTimeField, <PERSON><PERSON>anField
from models.user import User
from datetime import datetime
import shortuuid

class Room(Document):
    room_id = StringField(required=True, unique=True, default=lambda: shortuuid.uuid())
    creator = ReferenceField(User, required=True)
    participant = ReferenceField(User)
    title = StringField(default="Live Chat")
    messages = ListField(DictField(), default=list)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    is_active = BooleanField(default=True)

    meta = {
        'collection': 'rooms',
        'indexes': [
            'room_id',
            'creator',
            'participant',
            'updated_at',
            {'fields': ['creator', 'updated_at']},
            {'fields': ['participant', 'updated_at']}
        ],
        'ordering': ['-updated_at']
    }

    def to_dict(self):
        try:
            return {
                'room_id': self.room_id,
                'creator_id': str(self.creator.id) if self.creator else None,
                'creator_name': self.creator.username if self.creator else None,
                'participant_id': str(self.participant.id) if self.participant else None,
                'participant_name': self.participant.username if self.participant else None,
                'title': self.title,
                'created_at': self.created_at.isoformat() if self.created_at else None,
                'updated_at': self.updated_at.isoformat() if self.updated_at else None,
                'messages': self.messages,
                'is_active': self.is_active
            }
        except Exception as e:
            print(f"Error in to_dict: {str(e)}")
            return {
                'room_id': self.room_id,
                'error': 'Error converting room to dictionary'
            }

    def add_message(self, user, content, model=None, images=None):
        """Add a message to the room from a user"""
        if not user:
            raise ValueError("User not found")

        message = {
            "role": "user",
            "user_id": str(user.id),
            "username": user.username,
            "content": content,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Add images if provided
        if images:
            message["images"] = images

        self.messages.append(message)
        self.updated_at = datetime.utcnow()
        self.save()
        return message

    def add_ai_response(self, content, responding_to_user_id):
        """Add an AI response to the room"""
        message = {
            "role": "assistant",
            "responding_to": str(responding_to_user_id),
            "content": content,
            "timestamp": datetime.utcnow().isoformat()
        }

        self.messages.append(message)
        self.updated_at = datetime.utcnow()
        self.save()
        return message

    def is_member(self, user):
        """Check if a user is a member of this room"""
        try:
            if not user:
                return False

            user_id_str = str(user.id)
            creator_id = str(self.creator.id) if self.creator else None
            participant_id = str(self.participant.id) if self.participant else None

            return creator_id == user_id_str or (participant_id and participant_id == user_id_str)
        except Exception as e:
            print(f"Error checking membership: {str(e)}")
            return False

    def is_full(self):
        """Check if the room has reached its maximum capacity (2 users)"""
        return self.participant is not None
