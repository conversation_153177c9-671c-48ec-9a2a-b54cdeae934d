/**
 * Service Configuration v1.1
 * This file contains the configuration for all services.
 * It can be modified to add, remove, or update services without changing the core code.
 */
window.ServiceConfig = {
    version: '1.1.1', // Added version number
    // Default service configurations
    services: [
        {
            id: 'kevko<PERSON><PERSON>',
            name: '<PERSON>v<PERSON><PERSON><PERSON>',
            description: 'AI-powered chat assistant',
            icon: 'bot',
            color: 'purple',
            status: 'online',
            url: '/chat',
            type: 'external',
            openInNewTab: true,
            category: 'Productivity',
            version: '1.0.0',
            author: 'Kevko',
            className: 'KevkoAIService',
            features: [
                'AI chat',
                'Multiple models',
                'Conversation history',
                'File uploads'
            ],
            longDescription: 'Kev<PERSON><PERSON><PERSON> is a state-of-the-art conversational assistant powered by advanced language models. It can help with a wide range of tasks from answering questions and providing information to assisting with creative writing and code development. The assistant maintains context throughout conversations, allowing for natural back-and-forth exchanges. KevkoAI opens in a new tab for a dedicated chat experience with minimal distractions.'
        },
        {
            id: 'kevko<PERSON>y',
            name: '<PERSON>v<PERSON><PERSON><PERSON>',
            description: 'Custom Spotify interface',
            icon: 'music',
            color: 'cyan',
            status: 'online',
            url: '/spotify',
            type: 'external',
            className: 'KevkoFyService'
        },
        {
            id: 'kevkoHome',
            name: 'KevkoHome',
            description: 'Smart home management',
            icon: 'home',
            color: 'amber',
            status: 'offline',
            url: null,
            type: 'internal',
            className: 'KevkoHomeService'
        },
        {
            id: 'kevkoNotes',
            name: 'KevkoNotes',
            description: 'Notizen und Erinnerungen',
            icon: 'clipboard',
            color: 'pink',
            status: 'offline',
            url: null,
            type: 'internal',
            className: 'KevkoNotesService'
        },
        {
            id: 'kevkoCloud',
            name: 'KevkoCloud',
            description: 'Secure cloud storage',
            icon: 'cloud',
            color: 'green',
            status: 'offline',
            url: null,
            type: 'external',
            className: 'KevkoCloudService'
        }
    ],

    // Service class mapping
    classMapping: {
        'KevkoHomeService': window.KevkoHomeService,
        'KevkoFyService': window.KevkoFyService,
        'KevkoWeatherService': window.KevkoWeatherService,
        'KevkoNotesService': window.KevkoNotesService,
        'KevkoCloudService': window.KevkoCloudService,
        'KevkoAIService': window.KevkoAIService
    },

    /**
     * Get all available services for the store
     * @returns {Array} Array of service metadata
     */
    getAvailableServices() {
        const services = [];
        const serviceIds = new Set(); // Track service IDs to prevent duplicates

        // Add services with metadata
        for (const serviceClass in this.classMapping) {
            if (this.classMapping[serviceClass].getMetadata) {
                const metadata = this.classMapping[serviceClass].getMetadata();
                if (!serviceIds.has(metadata.id)) {
                    services.push(metadata);
                    serviceIds.add(metadata.id);
                }
            }
        }

        // Add services without metadata
        for (const service of this.services) {
            if (!service.className && !serviceIds.has(service.id)) {
                services.push({
                    id: service.id,
                    name: service.name,
                    description: service.description,
                    icon: service.icon,
                    color: service.color,
                    category: 'Uncategorized',
                    version: '1.0.0',
                    author: 'Kevko',
                    features: []
                });
                serviceIds.add(service.id);
            }
        }

        return services;
    },

    /**
     * Get updates from services
     * @param {Array} userServices - Optional array of service IDs that the user has added to their dashboard
     * @returns {Promise<Array>} Promise that resolves to an array of updates
     */
    async getAllUpdates(userServices = null) {
        try {
            // If no services are provided, return empty array
            if (userServices && userServices.length === 0) {
                console.log('No services provided, returning empty updates array');
                return [];
            }

            // Build the service_ids parameter for the API
            // Keep original casing as the backend now handles case-insensitive matching
            const serviceIdsParam = userServices ? userServices.join(',') : '';

            console.log('Requesting updates for services:', serviceIdsParam);

            // Fetch updates from the API
            // Encode the service IDs parameter to handle special characters
            const encodedServiceIds = encodeURIComponent(serviceIdsParam);
            const response = await fetch(`/api/user/updates?service_ids=${encodedServiceIds}`);
            const data = await response.json();

            if (!data.success) {
                console.error('Error fetching updates:', data.error);
                return [];
            }

            // Process the updates to add icon and color
            const updates = data.updates.map(update => {
                // Handle service_id properly to get the correct service name and class
                // For IDs like 'kevkoAI', we need to preserve the casing
                const serviceId = update.service_id;

                // Find the matching service in our configuration
                const matchingService = this.services.find(s => s.id.toLowerCase() === serviceId.toLowerCase());

                // Use the proper name from our configuration if available
                const serviceName = matchingService ? matchingService.name :
                    // Fallback to capitalizing first letter if no match found
                    serviceId.charAt(0).toUpperCase() + serviceId.slice(1);

                // Get the service class name
                const serviceClass = matchingService ? matchingService.className : serviceName + 'Service';

                // Format the date
                const publishedDate = new Date(update.published_at);
                const now = new Date();
                const diffDays = Math.floor((now - publishedDate) / (1000 * 60 * 60 * 24));

                let formattedDate;
                if (diffDays === 0) {
                    formattedDate = 'Today';
                } else if (diffDays === 1) {
                    formattedDate = 'Yesterday';
                } else if (diffDays < 7) {
                    formattedDate = 'Last week';
                } else if (diffDays < 14) {
                    formattedDate = '2 weeks ago';
                } else if (diffDays < 21) {
                    formattedDate = '3 weeks ago';
                } else if (diffDays < 60) {
                    formattedDate = 'Last month';
                } else if (diffDays < 90) {
                    formattedDate = '2 months ago';
                } else {
                    formattedDate = '3 months ago';
                }

                return {
                    id: update.id,
                    service: serviceName,
                    icon: this.getServiceIcon(serviceClass),
                    color: this.getServiceColor(serviceClass),
                    date: formattedDate,
                    title: update.title,
                    description: update.description,
                    version: update.version
                };
            });

            // Sort updates by date (newest first)
            updates.sort((a, b) => {
                const dateOrder = { 'Today': 0, 'Yesterday': 1, 'Last week': 2, '2 weeks ago': 3, '3 weeks ago': 4, 'Last month': 5, '1 month ago': 5, '2 months ago': 6, '3 months ago': 7 };
                return (dateOrder[a.date] || 100) - (dateOrder[b.date] || 100);
            });

            return updates;
        } catch (error) {
            console.error('Error fetching updates:', error);
            return [];
        }
    },

    /**
     * Get all downloads from all services
     * @returns {Array} Array of downloads
     */
    getAllDownloads() {
        const downloads = [];

        // Get downloads from services with getDownloads method
        for (const serviceClass in this.classMapping) {
            if (this.classMapping[serviceClass].getDownloads) {
                const serviceDownloads = this.classMapping[serviceClass].getDownloads();
                const serviceName = serviceClass.replace('Service', '');

                for (const download of serviceDownloads) {
                    downloads.push({
                        service: serviceName,
                        serviceColor: this.getServiceColor(serviceClass),
                        ...download
                    });
                }
            }
        }

        // Sort downloads by date (newest first)
        downloads.sort((a, b) => new Date(b.date) - new Date(a.date));

        return downloads;
    },

    /**
     * Get icon for a service class
     * @param {string} serviceClass Service class name
     * @returns {string} Icon name
     */
    getServiceIcon(serviceClass) {
        const serviceName = serviceClass.replace('Service', '');
        // Case-insensitive matching for service name or class
        const service = this.services.find(s =>
            (s.className && s.className.toLowerCase() === serviceClass.toLowerCase()) ||
            (s.name && s.name.toLowerCase() === serviceName.toLowerCase()) ||
            (s.id && s.id.toLowerCase() === serviceName.toLowerCase())
        );
        return service ? service.icon : 'box';
    },

    /**
     * Get color for a service class
     * @param {string} serviceClass Service class name
     * @returns {string} Color name
     */
    getServiceColor(serviceClass) {
        const serviceName = serviceClass.replace('Service', '');
        // Case-insensitive matching for service name or class
        const service = this.services.find(s =>
            (s.className && s.className.toLowerCase() === serviceClass.toLowerCase()) ||
            (s.name && s.name.toLowerCase() === serviceName.toLowerCase()) ||
            (s.id && s.id.toLowerCase() === serviceName.toLowerCase())
        );
        return service ? service.color : 'slate';
    }
};
