"""
Stats Updater Module
Handles updating statistics data in the database
"""
import logging
from datetime import datetime, timedelta, timezone
from models.user_activity import UserActivity
from models.api_log import APILog
from models.user import User
from models.model_usage import ModelUsage
from models.service_usage import ServiceUsage
from models.service_engagement import ServiceEngagement
from models.daily_stats import DailyStats

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_statistics():
    """
    Update daily statistics in the database
    This function should be called periodically (e.g., every 5 minutes)
    """
    try:
        logger.info("Updating statistics data...")
        today = datetime.now(timezone.utc)

        # Get active users count
        # First try UserActivity model
        active_users = UserActivity.get_all_active_users(minutes=60).count()

        # If no active users found, try using API logs as a fallback
        if active_users == 0:
            # Get unique users active in the last 60 minutes from API logs
            cutoff_time = today - timedelta(minutes=60)
            pipeline = [
                {'$match': {
                    'timestamp': {'$gte': cutoff_time}
                }},
                {'$group': {
                    '_id': '$user'
                }},
                {'$count': 'unique_users'}
            ]

            result = list(APILog.objects.aggregate(pipeline))
            active_users = result[0]['unique_users'] if result else 0

        # Get total messages from API logs for today
        today_start = datetime(today.year, today.month, today.day, tzinfo=timezone.utc)
        total_messages = APILog.objects(
            action__in=['thread_interaction', 'live_interaction', 'friend_chat_message'],
            timestamp__gte=today_start
        ).count()

        # We don't track response time yet, so set it to 0
        avg_response_time = 0

        # Calculate service usage from ServiceEngagement, ServiceUsage, and APILog
        service_usage = {}
        services = ['chat', 'live', 'spotify', 'friends', 'discord']

        # First try to get data from ServiceEngagement
        # Only count engagements that were completed (user stayed for at least 5 minutes)
        try:
            for service in services:
                count = ServiceEngagement.objects(
                    service=service,
                    start_time__gte=today_start,
                    is_completed=True
                ).count()
                service_usage[service] = count

            # If we have engagement data, use it exclusively
            if sum(service_usage.values()) > 0:
                logger.info("Using ServiceEngagement data for service usage statistics")
        except Exception as e:
            logger.warning(f"Error getting service usage from ServiceEngagement: {str(e)}")

        # If no engagement data is available, fall back to ServiceUsage and APILog
        logger.info("No engagement data available, falling back to ServiceUsage and APILog")

        # Reset service_usage
        service_usage = {}

        # First get counts from ServiceUsage
        try:
            for service in services:
                count = ServiceUsage.objects(
                    service=service,
                    timestamp__gte=today_start
                ).count()
                service_usage[service] = count
        except Exception as e:
            logger.warning(f"Error getting service usage from ServiceUsage: {str(e)}")

        # Then add counts from APILog
        for service in services:
            api_count = APILog.objects(
                service=service,
                timestamp__gte=today_start
            ).count()

            # Add to existing count or set if not exists
            if service in service_usage:
                service_usage[service] += api_count
            else:
                service_usage[service] = api_count

        # Calculate model usage
        model_usage = {}
        model_logs = ModelUsage.objects(timestamp__gte=today_start)

        for log in model_logs:
            model_name = log.model_name
            if model_name in model_usage:
                model_usage[model_name] += 1
            else:
                model_usage[model_name] = 1

        # If no model usage data is available, just use an empty dictionary
        if not model_usage:
            model_usage = {}

        # Create stats data dictionary
        stats_data = {
            'active_users': active_users,
            'total_messages': total_messages,
            'avg_response_time': avg_response_time,
            'service_usage': service_usage,
            'model_usage': model_usage
        }

        # Update or create daily stats
        DailyStats.update_or_create(today, stats_data)

        logger.info(f"Statistics updated successfully: {stats_data}")
        return stats_data

    except Exception as e:
        logger.error(f"Error updating statistics: {str(e)}")
        return None

def get_top_users(days=30, limit=10, service=None):
    """
    Get top users by AI model usage
    
    Args:
        days (int): Number of days to look back
        limit (int): Maximum number of users to return
        service (str, optional): Filter by specific service (e.g., 'kevkoAI', 'kevkoFy')
    """
    try:
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)

        # Get user activity from ModelUsage collection (AI models only)
        match_criteria = {'timestamp': {'$gte': cutoff_time}}
        
        # Add service filter if specified
        if service and service not in ['all', None]:
            match_criteria['service'] = service
            
        pipeline = [
            {'$match': match_criteria},
            {'$group': {
                '_id': '$user',
                'count': {'$sum': 1},
                'models': {'$addToSet': '$model_name'}
            }},
            {'$sort': {'count': -1}},
            {'$limit': limit}
        ]

        top_users_data = list(ModelUsage.objects.aggregate(pipeline))

        # Format the response with user details
        result = []

        for user_data in top_users_data:
            user_id = user_data['_id']
            if not user_id:
                continue

            user = User.objects(id=user_id).first()
            if not user:
                continue

            # Get preferred model for this user
            model_pipeline = [
                {'$match': {
                    'user': user_id,
                    'timestamp': {'$gte': cutoff_time}
                }},
                {'$group': {
                    '_id': '$model_name',
                    'count': {'$sum': 1}
                }},
                {'$sort': {'count': -1}},
                {'$limit': 1}
            ]

            preferred_model = None
            model_data = list(ModelUsage.objects.aggregate(model_pipeline))

            if model_data and len(model_data) > 0:
                preferred_model = model_data[0]['_id']

            result.append({
                'user_id': str(user.id),
                'username': user.username,
                'display_name': user.display_name if hasattr(user, 'display_name') else None,
                'profile_picture': user.profile_picture if hasattr(user, 'profile_picture') else None,
                'total_usage': user_data['count'],
                'preferred_model': preferred_model
            })

        return result

    except Exception as e:
        logger.error(f"Error getting top users: {str(e)}")
        return []

if __name__ == "__main__":
    # This allows running the updater directly for testing
    update_statistics()
