from flask import jsonify, request, Response, stream_with_context, make_response
from flask_login import login_required, current_user
from . import live_api
from models.room import Room
from models.user import User
from models.model_usage import ModelUsage
from datetime import datetime, timedelta
from services.chat import ChatService
from utils.decorators import check_service_access, check_live_room_limit, check_live_message_limit
from utils.api_logger import log_api_request
import json
import logging
import time
import functools

# Configure logging
logging.basicConfig(level=logging.INFO)

chat_service = ChatService()

# Simple in-memory cache for API responses
cache = {}

def timed_cache(seconds=10):
    """Simple time-based cache decorator"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create a cache key from function name and arguments
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}:{current_user.id}"

            # Check if we have a valid cached response
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < seconds:
                    return result

            # Call the function and cache the result
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        return wrapper
    return decorator

def invalidate_room_cache(user_id=None):
    """Invalidate cache entries related to rooms for a specific user or all users"""
    keys_to_remove = []

    for key in cache:
        if 'load_rooms' in key or 'room_details' in key:
            if user_id is None or str(user_id) in key:
                keys_to_remove.append(key)

    for key in keys_to_remove:
        if key in cache:
            del cache[key]

@live_api.route('/load/rooms', methods=['GET'])
@login_required
@check_service_access('live')
def load_rooms():
    """Get all rooms where the current user is a member with additional metadata"""
    try:
        # Use a single query with OR operator for better performance
        from mongoengine.queryset.visitor import Q

        # Use only() to fetch only the fields we need, excluding messages completely
        all_rooms = Room.objects(
            Q(creator=current_user) | Q(participant=current_user)
        ).only(
            'room_id', 'title', 'creator', 'participant',
            'created_at', 'updated_at', 'is_active'
        ).order_by('-updated_at')

        # Get room IDs for message count aggregation
        room_ids = [room.id for room in all_rooms]

        # Use MongoDB aggregation to get message counts efficiently in a single query
        message_counts = {}
        if room_ids:
            pipeline = [
                {'$match': {'_id': {'$in': room_ids}}},
                {'$project': {'_id': 1, 'message_count': {'$size': '$messages'}}}
            ]

            # Execute the aggregation pipeline
            message_count_results = Room._get_collection().aggregate(pipeline)

            # Create a mapping of room ID to message count
            for result in message_count_results:
                message_counts[str(result['_id'])] = result['message_count']

        # Prefetch all user data in a single query
        user_ids = set()
        for room in all_rooms:
            if room.creator:
                user_ids.add(str(room.creator.id))
            if room.participant:
                user_ids.add(str(room.participant.id))

        # Get all users in a single query
        users_dict = {}
        if user_ids:
            users = User.objects(id__in=list(user_ids)).only('id', 'username')
            for user in users:
                users_dict[str(user.id)] = user.username

        # Format the response
        rooms_data = []
        for room in all_rooms:
            room_id = str(room.id)
            creator_id = str(room.creator.id) if room.creator else None
            participant_id = str(room.participant.id) if room.participant else None

            rooms_data.append({
                'room_id': room.room_id,
                'title': room.title,
                'creator_id': creator_id,
                'creator_name': users_dict.get(creator_id) if creator_id else None,
                'participant_id': participant_id,
                'participant_name': users_dict.get(participant_id) if participant_id else None,
                'message_count': message_counts.get(room_id, 0),
                'created_at': room.created_at.isoformat() if room.created_at else None,
                'updated_at': room.updated_at.isoformat() if room.updated_at else None,
                'is_active': room.is_active
            })

        # Create response with cache headers (5 seconds cache)
        resp = make_response(jsonify({
            'rooms': rooms_data,
            'room_count': len(rooms_data)
        }))
        resp.headers['Cache-Control'] = 'private, max-age=5'
        return resp
    except Exception as e:
        logging.error(f"Error getting rooms: {str(e)}")
        return jsonify({'error': str(e)}), 500

@live_api.route('/room/<room_id>', methods=['GET'])
@login_required
@check_service_access('live')
def room_details(room_id):
    """Get a specific room by ID with messages but without image data"""
    try:
        # Use projection to exclude image data from the query
        room = Room.objects(room_id=room_id).first()

        if not room:
            return jsonify({'error': 'Room not found'}), 404

        # Check if user is allowed to access this room
        user_id_str = str(current_user.id)
        creator_id = str(room.creator.id) if room.creator else None
        participant_id = str(room.participant.id) if room.participant else None

        if not (creator_id == user_id_str or (participant_id and participant_id == user_id_str)):
            return jsonify({'error': 'Access denied'}), 403

        # Use MongoDB aggregation to filter out image data directly in the database
        # This is much faster than processing in Python
        pipeline = [
            {'$match': {'room_id': room_id}},
            {'$project': {
                'room_id': 1,
                'title': 1,
                'creator': 1,
                'participant': 1,
                'created_at': 1,
                'updated_at': 1,
                'is_active': 1,
                'messages': {
                    '$map': {
                        'input': '$messages',
                        'as': 'msg',
                        'in': {
                            'content': '$$msg.content',
                            'responding_to': '$$msg.responding_to',
                            'timestamp': '$$msg.timestamp',
                            'user_id': '$$msg.user_id',
                            'username': '$$msg.username',
                            'role': {'$ifNull': ['$$msg.role', 'user']}
                        }
                    }
                }
            }}
        ]

        # Execute aggregation
        result = list(Room.objects.aggregate(pipeline))

        if not result:
            return jsonify({'error': 'Room data could not be processed'}), 500

        room_data = result[0]

        # Convert ObjectId to string for JSON serialization
        room_data['_id'] = str(room_data['_id'])

        # Add user information
        room_data['creator_id'] = creator_id
        room_data['creator_name'] = room.creator.username if room.creator else None
        room_data['participant_id'] = participant_id
        room_data['participant_name'] = room.participant.username if room.participant else None

        # Format dates
        room_data['created_at'] = room.created_at.isoformat() if room.created_at else None
        room_data['updated_at'] = room.updated_at.isoformat() if room.updated_at else None

        # Remove MongoDB-specific fields
        if 'creator' in room_data:
            del room_data['creator']
        if 'participant' in room_data:
            del room_data['participant']

        return jsonify(room_data)
    except Exception as e:
        logging.error(f"Error getting room details: {str(e)}")
        return jsonify({'error': str(e)}), 500

@live_api.route('/room', methods=['POST'])
@login_required
@check_service_access('live')
@check_live_room_limit()
@log_api_request('live_create', 'live')
def create_room():
    """Create a new room"""
    try:
        # Create the room
        room = Room(creator=current_user).save()

        # Invalidate cache for this user
        invalidate_room_cache(current_user.id)

        # Return optimized room data
        return jsonify({
            'room_id': room.room_id,
            'title': room.title,
            'creator_id': str(current_user.id),
            'creator_name': current_user.username,
            'participant_id': None,
            'participant_name': None,
            'created_at': room.created_at.isoformat() if room.created_at else None,
            'updated_at': room.updated_at.isoformat() if room.updated_at else None,
            'is_active': room.is_active,
            'messages': []
        })
    except Exception as e:
        logging.error(f"Error creating room: {str(e)}")
        return jsonify({'error': str(e)}), 500

@live_api.route('/room/<room_id>/invite', methods=['POST'])
@login_required
@check_service_access('live')
def invite_to_room(room_id):
    """Invite a user to a room"""
    data = request.json
    username = data.get('username')

    if not username:
        return jsonify({'error': 'Username is required'}), 400

    # Check if room exists
    room = Room.objects(room_id=room_id).first()
    if not room:
        return jsonify({'error': 'Room not found'}), 404

    # Check if user is allowed to invite others to this room
    if str(room.creator.id) != str(current_user.id):
        return jsonify({'error': 'Only the room creator can invite others'}), 403

    # Check if user exists
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'error': 'User not found'}), 404

    # Check if user is already in the room
    if room.is_member(user):
        return jsonify({'error': 'User is already in the room'}), 400

    # Add user to room
    room.participant = user
    room.save()

    # Invalidate cache for both users
    invalidate_room_cache(current_user.id)
    invalidate_room_cache(user.id)

    # Return optimized response
    room_data = {
        'room_id': room.room_id,
        'title': room.title,
        'creator_id': str(room.creator.id),
        'creator_name': room.creator.username,
        'participant_id': str(user.id),
        'participant_name': user.username,
        'created_at': room.created_at.isoformat() if room.created_at else None,
        'updated_at': room.updated_at.isoformat() if room.updated_at else None,
        'is_active': room.is_active
    }

    return jsonify({'success': True, 'room': room_data})

@live_api.route('/room/<room_id>', methods=['DELETE'])
@login_required
@check_service_access('live')
def delete_room(room_id):
    """Delete a room"""
    room = Room.objects(room_id=room_id).first()

    if not room:
        return jsonify({'error': 'Room not found'}), 404

    # Check if user is allowed to delete this room
    if str(room.creator.id) != str(current_user.id):
        return jsonify({'error': 'Only the room creator can delete the room'}), 403

    # Get participant ID before deleting
    participant_id = room.participant.id if room.participant else None

    # Delete the room
    room.delete()

    # Invalidate cache for creator
    invalidate_room_cache(current_user.id)

    # If there was a participant, invalidate their cache too
    if participant_id:
        invalidate_room_cache(participant_id)

    return jsonify({'success': True})

@live_api.route('/room/<room_id>/leave', methods=['POST'])
@login_required
@check_service_access('live')
def leave_room(room_id):
    """Leave a room (as participant)"""
    room = Room.objects(room_id=room_id).first()

    if not room:
        return jsonify({'error': 'Room not found'}), 404

    # Check if user is the participant
    if room.participant and str(room.participant.id) == str(current_user.id):
        # Get creator ID for cache invalidation
        creator_id = room.creator.id if room.creator else None

        # Remove participant
        room.participant = None
        room.save()

        # Invalidate cache for both users
        invalidate_room_cache(current_user.id)
        if creator_id:
            invalidate_room_cache(creator_id)

        return jsonify({'success': True})
    else:
        return jsonify({'error': 'You are not a participant in this room'}), 403

@live_api.route('/send', methods=['POST'])
@login_required
@check_service_access('live')
@check_live_message_limit()
@log_api_request('live_interaction', 'live')
def send_message():
    """Send a message in a room and get AI response"""
    data = request.json
    room_id = data.get('room_id')
    message = data.get('message', '')
    selected_model = data.get('model', 'gpt-4o-mini')

    if not room_id:
        return jsonify({'error': 'Room ID is required'}), 400

    room = Room.objects(room_id=room_id).first()

    if not room:
        return jsonify({'error': 'Room not found'}), 404

    # Check if user is allowed to send messages in this room
    if not room.is_member(current_user):
        return jsonify({'error': 'Access denied'}), 403

    # Check user credit balance before processing
    from models.user_credit import UserCredit
    from models.model_credit_cost import ModelCreditCost
    
    user_credit = UserCredit.get_or_create(current_user.id)
    
    # Get the credit cost for the selected model
    credit_cost = ModelCreditCost.get_cost(selected_model)
    
    # Check if user has sufficient credits for this model
    if user_credit:
        if user_credit.balance == 0:
            # If user has zero credits, return 404 with a specific message
            return jsonify({
                'error': 'You\'ve run out of credits. Please add more credits to continue using AI features.',
                'insufficient_credits': True,
                'zero_balance': True
            }), 404
        elif user_credit.balance < credit_cost:
            # If user has credits but not enough for this model, return 404
            return jsonify({
                'error': f'You have insufficient credits to use this model. Please add more credits or use a different model.',
                'insufficient_credits': True,
                'balance': user_credit.balance,
                'cost': credit_cost
            }), 404
    
    # Add message to room
    room.add_message(message, current_user.id)

    # Invalidate cache for all users in this room
    invalidate_room_cache(current_user.id)
    if room.participant:
        invalidate_room_cache(room.participant.id)
    if room.creator:
        invalidate_room_cache(room.creator.id)

    def generate_response():
        try:
            # Get conversation history
            messages = room.get_messages()

            # Format messages for AI service
            formatted_messages = []
            for msg in messages:
                formatted_messages.append({
                    'role': 'user' if msg.get('is_user') else 'assistant',
                    'content': msg.get('content'),
                    'name': msg.get('username')
                })

            # Use the appropriate AI service based on the selected model and use the live-specific system prompt
            if selected_model == 'gemini-2.0-flash':
                generator = chat_service.gemini_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt')
            elif selected_model in ['qwen-qwq-32b', 'gemma2-9b-it', 'llama-3.3-70b-versatile',
                                   'llama-3.1-8b-instant', 'llama3-70b-8192', 'llama3-8b-8192']:
                generator = chat_service.groq_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt', model=selected_model)
            else:
                generator = chat_service.gpt_service.generate_stream(formatted_messages, system_prompt_file='prompts/live-system-prompt.txt')

            assistant_message = ""
            for chunk in generator:
                assistant_message += chunk
                yield f"data: {json.dumps({'text': chunk, 'user_id': str(current_user.id)})}\n\n"

            # Save the complete message to room
            room.add_ai_response(assistant_message, current_user.id)

            # Invalidate cache for all users in this room
            invalidate_room_cache(current_user.id)
            if room.participant:
                invalidate_room_cache(room.participant.id)
            if room.creator:
                invalidate_room_cache(room.creator.id)

            yield f"data: {json.dumps({'full_message': assistant_message, 'room_id': room.room_id, 'user_id': str(current_user.id)})}\n\n"

            # Log model usage after the response is complete
            try:
                result = ModelUsage.log_usage(
                    user_id=current_user.id,
                    model_name=selected_model,
                    service='live'
                )
                if result is False:
                    logging.warning(f"User {current_user.id} has insufficient credits to use model {selected_model}")
                    yield f"data: {json.dumps({'error': 'You have insufficient credits to use this model. Please add more credits or use a different model.', 'insufficient_credits': True})}\n\n"
                elif result is None:
                    logging.error(f"Failed to log model usage for user {current_user.id}, model {selected_model}")
                else:
                    logging.info(f"Successfully logged model usage and deducted credits for user {current_user.id}, model {selected_model}")
            except Exception as e:
                import traceback
                logging.error(f"Exception logging model usage: {str(e)}")
                logging.error(f"Traceback: {traceback.format_exc()}")

        except Exception as e:
            logging.error(f"Error generating response: {str(e)}")
            error_message = f"Error generating response: {str(e)}"
            yield f"data: {json.dumps({'error': error_message})}\n\n"

    return Response(
        stream_with_context(generate_response()),
        mimetype='text/event-stream'
    )
