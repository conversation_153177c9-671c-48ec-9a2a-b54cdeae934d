from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON>an<PERSON>ield
from models.user import User

class AdminContact(Document):
    """
    Model for storing admin contact information
    """
    user = ReferenceField(User, required=True)
    title = StringField(default="Administrator")
    description = StringField(default="")
    is_visible = BooleanField(default=True)
    
    meta = {
        'collection': 'admin_contacts',
        'indexes': [
            'user'
        ]
    }
    
    def to_dict(self):
        """
        Convert to dictionary
        """
        return {
            'id': str(self.id),
            'user_id': str(self.user.id),
            'username': self.user.username,
            'display_name': self.user.display_name if hasattr(self.user, 'display_name') else self.user.username,
            'email': self.user.email,
            'profile_picture': self.user.profile_picture if hasattr(self.user, 'profile_picture') else None,
            'title': self.title,
            'description': self.description,
            'is_visible': self.is_visible
        }
