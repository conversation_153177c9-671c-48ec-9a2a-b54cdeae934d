/* Model Selector Styles */
.model-selector-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgb(38, 38, 44);
  border-radius: 12px;
  padding: 16px;
  width: 600px; /* Base width */
  max-width: 85vw; /* Ensure it doesn't overflow the viewport */
  max-height: 80vh; /* Limit height */
  overflow-y: auto; /* Allow scrolling if needed */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  transition: opacity 0.2s, transform 0.2s;
  margin: 0; /* Reset any margins */
  right: auto; /* Reset any right positioning */
  bottom: auto; /* Reset any bottom positioning */

  /* Center in the chat content area on desktop */
  @media (min-width: 1024px) {
    left: calc(50% + 150px); /* Adjust for sidebar width */
  }

  /* Responsive adjustments */
  @media (max-width: 650px) {
    width: 500px;
    padding: 14px;
  }

  @media (max-width: 500px) {
    width: 90vw;
    padding: 12px;
  }

  /* Additional mobile adjustments */
  @media (max-width: 400px) {
    width: 95vw;
    max-height: 70vh;
    padding: 10px;
  }
}

/* Animation for the container when it becomes visible */
.model-selector-container.visible {
  animation: popUpAnimation 0.2s ease-out forwards;
}

@keyframes popUpAnimation {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.model-selector-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 3 columns */
  gap: 16px;

  /* Responsive adjustments */
  @media (max-width: 650px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  @media (max-width: 500px) {
    grid-template-columns: repeat(2, 1fr); /* Switch to 2 columns on smaller screens */
    gap: 10px;
  }

  @media (max-width: 400px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

.model-option-card {
  background-color: rgb(45, 45, 51);
  border-radius: 10px;
  padding: 16px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100px;

  /* Subtle hover effect */
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  /* Responsive adjustments */
  @media (max-width: 650px) {
    padding: 14px;
    min-height: 90px;
  }

  @media (max-width: 500px) {
    padding: 12px;
    min-height: 80px;
    border-radius: 8px;
  }

  @media (max-width: 400px) {
    padding: 10px;
    min-height: 70px;
  }
}

.model-option-card:hover {
  background-color: rgb(55, 55, 61);
}

.model-option-card.selected {
  position: relative;
  background-color: rgb(45, 45, 51);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.model-option-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border-radius: 7px; /* Slightly smaller than the card's border-radius */
  pointer-events: none; /* Allow clicks to pass through */
  z-index: 1;
}

/* GPT-4o Mini - Purple/Blue gradient */
.model-option-card.selected[data-model="gpt-4o-mini"] {
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.model-option-card.selected[data-model="gpt-4o-mini"]::before {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.15), rgba(79, 70, 229, 0.05));
}

/* Gemini - Red gradient */
.model-option-card.selected[data-model="gemini-2.0-flash"] {
  border: 1px solid rgba(220, 38, 38, 0.3);
}

.model-option-card.selected[data-model="gemini-2.0-flash"]::before {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.15), rgba(220, 38, 38, 0.05));
}

/* Qwen - Cyan gradient */
.model-option-card.selected[data-model="qwen-qwq-32b"] {
  border: 1px solid rgba(56, 189, 248, 0.3);
}

.model-option-card.selected[data-model="qwen-qwq-32b"]::before {
  background: linear-gradient(135deg, rgba(56, 189, 248, 0.15), rgba(56, 189, 248, 0.05));
}

/* Gemma 2 - Green gradient */
.model-option-card.selected[data-model="gemma2-9b-it"] {
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.model-option-card.selected[data-model="gemma2-9b-it"]::before {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.05));
}

/* Llama 3.3 70B - Orange gradient */
.model-option-card.selected[data-model="llama-3.3-70b-versatile"] {
  border: 1px solid rgba(249, 115, 22, 0.3);
}

.model-option-card.selected[data-model="llama-3.3-70b-versatile"]::before {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.15), rgba(249, 115, 22, 0.05));
}

/* Llama 3.1 8B - Yellow gradient */
.model-option-card.selected[data-model="llama-3.1-8b-instant"] {
  border: 1px solid rgba(234, 179, 8, 0.3);
}

.model-option-card.selected[data-model="llama-3.1-8b-instant"]::before {
  background: linear-gradient(135deg, rgba(234, 179, 8, 0.15), rgba(234, 179, 8, 0.05));
}

/* Llama 3 70B - Pink gradient */
.model-option-card.selected[data-model="llama3-70b-8192"] {
  border: 1px solid rgba(236, 72, 153, 0.3);
}

.model-option-card.selected[data-model="llama3-70b-8192"]::before {
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.15), rgba(236, 72, 153, 0.05));
}

/* Llama 3 8B - Indigo gradient */
.model-option-card.selected[data-model="llama3-8b-8192"] {
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.model-option-card.selected[data-model="llama3-8b-8192"]::before {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(99, 102, 241, 0.05));
}

/* PlayAI TTS - Purple gradient */
.model-option-card.selected[data-model="playai-tts"] {
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.model-option-card.selected[data-model="playai-tts"]::before {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(139, 92, 246, 0.05));
}

.model-selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgb(220, 38, 38);
  display: none;
  z-index: 2; /* Ensure it's above the gradient overlay */
}

.model-option-card.selected .model-selection-indicator {
  display: block;
}

.model-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-bottom: 12px;
  position: relative;
  z-index: 2; /* Ensure it's above the gradient overlay */
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  padding: 6px;

  /* Responsive adjustments */
  @media (max-width: 650px) {
    width: 28px;
    height: 28px;
    margin-bottom: 10px;
    padding: 5px;
  }

  @media (max-width: 500px) {
    width: 24px;
    height: 24px;
    margin-bottom: 8px;
    padding: 4px;
  }

  @media (max-width: 400px) {
    width: 22px;
    height: 22px;
    margin-bottom: 6px;
    padding: 4px;
  }
}

.model-option-name {
  font-size: 16px;
  font-weight: 600;
  color: #f3f4f6;
  margin-bottom: 8px;
  white-space: normal; /* Allow text to wrap */
  word-wrap: break-word; /* Break long words if needed */
  overflow: visible; /* Ensure text is not truncated */
  width: 100%; /* Take full width of parent */
  position: relative;
  z-index: 2; /* Ensure it's above the gradient overlay */

  /* Responsive adjustments */
  @media (max-width: 650px) {
    font-size: 15px;
    margin-bottom: 6px;
  }

  @media (max-width: 500px) {
    font-size: 14px;
    margin-bottom: 5px;
    line-height: 1.3;
  }

  @media (max-width: 400px) {
    font-size: 13px;
    margin-bottom: 4px;
    line-height: 1.2;
  }
}

.model-description-tooltip {
  position: absolute; /* Position relative to the model selector container */
  font-size: 13px;
  color: #f3f4f6;
  line-height: 1.5;
  background-color: rgb(30, 30, 35);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  z-index: 1100; /* Higher than other elements */
  min-width: 220px;
  max-width: 320px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  white-space: normal; /* Allow text to wrap */
  word-wrap: break-word; /* Break long words if needed */
  animation: tooltipFadeIn 0.3s ease-out forwards;
  /* Position relative to the model card */
  top: auto;
  left: auto;
  transform: none;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Speech bubble triangle */
.model-description-tooltip:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: rgb(30, 30, 35) transparent transparent transparent;
}

/* Responsive adjustments for tooltip */
@media (max-width: 500px) {
  .model-description-tooltip {
    font-size: 12px;
    padding: 10px 14px;
    min-width: 180px;
    max-width: 260px;
  }
}

.model-info-icon {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #9ca3af;
  opacity: 0.7;
  transition: all 0.2s;
  z-index: 2; /* Ensure it's above the gradient overlay */
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  padding: 3px;

  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  /* Responsive adjustments */
  @media (max-width: 650px) {
    width: 18px;
    height: 18px;
    bottom: 8px;
    right: 8px;
  }

  @media (max-width: 500px) {
    width: 16px;
    height: 16px;
    bottom: 6px;
    right: 6px;
  }

  @media (max-width: 400px) {
    width: 14px;
    height: 14px;
    bottom: 5px;
    right: 5px;
  }
}

.model-info-icon:hover {
  opacity: 1;
}

/* Model selector header */
.model-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.model-selector-header h3 {
  font-size: 18px;
  font-weight: 500;
  color: #f3f4f6;
  margin: 0;
}

.close-model-selector {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-model-selector:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #f3f4f6;
}

/* Hide the selector by default */
.model-selector-container {
  display: none;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.95);
}

.model-selector-container.visible {
  display: block !important;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1) !important;
  top: 50% !important;
  right: auto !important;
  bottom: auto !important;

  /* Default centering for mobile */
  left: 50% !important;

  /* Center in the chat content area on desktop */
  @media (min-width: 1024px) {
    left: calc(50% + 150px) !important; /* Adjust for sidebar width */
  }
}

/* Add a backdrop when the selector is visible */
.model-selector-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
  backdrop-filter: blur(2px);
}

.model-selector-backdrop.visible {
  display: block;
}

/* Prevent body scrolling when model selector is open */
body.model-selector-open {
  overflow: hidden;
}

/* Current model display in input area */
.current-model-display {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: transparent;
  color: rgb(156, 163, 175);
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10; /* Ensure it's above other elements */
  max-width: 120px; /* Limit width */
  overflow: hidden; /* Prevent overflow */
  white-space: nowrap; /* Keep text on one line */
  text-overflow: ellipsis; /* Add ellipsis for overflow text */
}

/* Mobile adjustments for model display */
@media (max-width: 768px) {
  .current-model-display {
    padding: 4px 6px;
    max-width: 80px;
    font-size: 12px;
  }
}

.current-model-display:hover {
  background-color: rgba(45, 45, 51, 0.5);
}

.current-model-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Model button styling */
.model-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  position: relative;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.model-btn:hover {
  background-color: rgba(45, 45, 51, 0.5);
}

/* Hide model name by default on smaller screens */
@media (max-width: 768px) {
  #currentModel {
    display: none;
  }

  .model-btn:hover #currentModel,
  .model-btn:focus #currentModel,
  .model-btn:active #currentModel {
    display: block;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: rgb(38, 38, 44);
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
  }
}
