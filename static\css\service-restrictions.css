/* Service Restrictions Styles */
:root {
  --primary-gradient: linear-gradient(135deg, #0ea5e9, #3b82f6);
  --secondary-gradient: linear-gradient(135deg, #8b5cf6, #d946ef);
  --success-gradient: linear-gradient(135deg, #10b981, #059669);
  --warning-gradient: linear-gradient(135deg, #f59e0b, #d97706);
  --danger-gradient: linear-gradient(135deg, #ef4444, #b91c1c);
  --card-bg: rgba(30, 41, 59, 0.5);
  --card-border: rgba(51, 65, 85, 0.5);
  --card-hover: rgba(51, 65, 85, 0.8);
}

/* Main container styles */
.service-restrictions-container {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 0.75rem;
  border: 1px solid rgba(51, 65, 85, 0.5);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Header styles */
.service-restrictions-header {
  background: rgba(30, 41, 59, 0.7);
  border-bottom: 1px solid rgba(51, 65, 85, 0.7);
  padding: 1rem 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.service-restrictions-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #f1f5f9;
  font-size: 1.125rem;
}

.service-restrictions-title i {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.service-restrictions-actions {
  display: flex;
  gap: 0.5rem;
}

/* Button styles */
.btn-restriction {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: rgba(51, 65, 85, 0.7);
  color: #cbd5e1;
}

.btn-secondary:hover {
  background: rgba(71, 85, 105, 0.8);
  color: white;
}

/* Service selector styles */
.service-selector {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(51, 65, 85, 0.7);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin: 1rem;
  transition: all 0.3s ease;
}

.service-selector:hover {
  border-color: rgba(56, 189, 248, 0.5);
  box-shadow: 0 0 0 1px rgba(56, 189, 248, 0.1);
}

.service-selector-label {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
}

.service-select {
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(51, 65, 85, 0.8);
  border-radius: 0.375rem;
  color: #e2e8f0;
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2394a3b8'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.service-select:focus {
  outline: none;
  border-color: rgba(56, 189, 248, 0.8);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
}

/* Restriction cards grid */
.restriction-cards {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  padding: 0 1rem 1rem 1rem;
}

@media (min-width: 768px) {
  .restriction-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Restriction card styles */
.restriction-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(51, 65, 85, 0.5);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.restriction-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0.7;
}

.restriction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(56, 189, 248, 0.3);
}

.restriction-card.access-control::before {
  background: var(--primary-gradient);
}

.restriction-card.storage-limits::before {
  background: var(--success-gradient);
}

.restriction-card.time-restrictions::before {
  background: var(--warning-gradient);
}

.restriction-card.feature-restrictions::before {
  background: var(--secondary-gradient);
}

.restriction-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.restriction-card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.restriction-card-title i {
  width: 1.25rem;
  height: 1.25rem;
}

.restriction-card-actions {
  display: flex;
  gap: 0.25rem;
}

.btn-card {
  background: rgba(15, 23, 42, 0.8);
  color: #94a3b8;
  border: none;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-card:hover {
  background: rgba(30, 41, 59, 0.9);
  color: #e2e8f0;
}

.btn-card.btn-delete:hover {
  background: rgba(185, 28, 28, 0.2);
  color: #f87171;
}

.restriction-card-description {
  color: #94a3b8;
  font-size: 0.75rem;
  margin-bottom: 0.75rem;
}

.restriction-card-content {
  margin-top: 0.75rem;
}

/* User list styles */
.user-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
}

.user-item:last-child {
  border-bottom: none;
}

.user-name {
  color: #cbd5e1;
  font-size: 0.75rem;
}

.user-limit {
  color: #94a3b8;
  font-size: 0.75rem;
}

/* Add button styles */
.btn-add-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  width: 100%;
  background: rgba(30, 41, 59, 0.7);
  color: #cbd5e1;
  border: 1px dashed rgba(71, 85, 105, 0.5);
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.75rem;
}

.btn-add-item:hover {
  background: rgba(51, 65, 85, 0.8);
  border-color: rgba(56, 189, 248, 0.5);
  color: #e2e8f0;
}

/* Animation for cards */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.restriction-card {
  animation: fadeIn 0.3s ease-out forwards;
}

.restriction-card:nth-child(1) {
  animation-delay: 0.1s;
}

.restriction-card:nth-child(2) {
  animation-delay: 0.2s;
}

.restriction-card:nth-child(3) {
  animation-delay: 0.3s;
}

.restriction-card:nth-child(4) {
  animation-delay: 0.4s;
}

/* Progress bar for storage limits */
.storage-progress {
  width: 100%;
  height: 6px;
  background: rgba(30, 41, 59, 0.7);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.storage-progress-bar {
  height: 100%;
  border-radius: 3px;
  background: var(--success-gradient);
  transition: width 0.3s ease;
}

/* Logs section */
.logs-section {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(51, 65, 85, 0.5);
  border-radius: 0.5rem;
  margin: 1.5rem 1rem 1rem 1rem;
  overflow: hidden;
}

.logs-header {
  background: rgba(30, 41, 59, 0.7);
  border-bottom: 1px solid rgba(51, 65, 85, 0.7);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logs-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #e2e8f0;
  font-size: 1rem;
}

.logs-content {
  padding: 0.75rem 1rem;
}

.logs-description {
  color: #94a3b8;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.logs-preview {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(51, 65, 85, 0.7);
  border-radius: 0.375rem;
  padding: 0.75rem;
  max-height: 10rem;
  overflow-y: auto;
  font-family: monospace;
  font-size: 0.75rem;
  color: #94a3b8;
}

.log-entry {
  padding: 0.25rem 0;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  display: flex;
  gap: 0.5rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #64748b;
}

.log-type {
  color: #38bdf8;
}

.log-type.error {
  color: #f87171;
}

.log-type.warning {
  color: #fbbf24;
}

.log-message {
  color: #cbd5e1;
  flex-grow: 1;
}

/* Add new restriction button */
.btn-add-restriction {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  background: rgba(30, 41, 59, 0.7);
  color: #e2e8f0;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 1rem 1rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-add-restriction:hover {
  background: rgba(51, 65, 85, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 23, 42, 0.9);
  color: #e2e8f0;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  margin-bottom: 0.5rem;
}

.tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: rgba(15, 23, 42, 0.9) transparent transparent transparent;
  z-index: 10;
  pointer-events: none;
  margin-bottom: -5px;
}
