import logging
import json
import csv
import dicttoxml
import io
import smtplib
import os
import random
import threading
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from flask import session
from models.user import User
from models.thread import Thread
from models.room import Room
from models.friend_relationship import FriendRelationship
from models.friend_chat import FriendChat
from models.feature_restriction import FeatureRestriction
from models.spotify_credentials import SpotifyCredentials
from models.user_activity import UserActivity

def collect_user_data(user):
    """Collect all data for a user"""
    data = {
        'user': {
            'id': str(user.id),
            'username': user.username,
            'email': user.email,
            'display_name': user.display_name,
            'profile_picture': user.profile_picture,
            'is_admin': user.is_admin,
            'created_at': user.created_at.isoformat() if hasattr(user, 'created_at') else None,
            'last_login': user.last_login.isoformat() if hasattr(user, 'last_login') else None,
            'two_factor_enabled': user.two_factor_enabled
        },
        'threads': [],
        'rooms': [],
        'friend_relationships': [],
        'friend_chats': [],
        'feature_restrictions': None,
        'spotify_credentials': None,
        'uploaded_images': []
    }

    # Get user's uploaded files (images)
    from models import UploadedFile
    user_files = UploadedFile.objects(uploaded_by=user.id)
    for file in user_files:
        if file.content_type.startswith('image/'):
            data['uploaded_images'].append({
                'file_id': file.file_id,
                'filename': file.filename,
                'content_type': file.content_type,
                'created_at': file.created_at.isoformat() if file.created_at else None,
                'url': f"/api/upload/{file.file_id}",
                'data_url': file.get_data_url()  # Include base64 data URL for direct viewing
            })

    # Get threads
    threads = Thread.objects(user=user.id)
    for thread in threads:
        thread_data = thread.to_dict()

        # Process messages to include clickable image links
        for message in thread_data['messages']:
            if message.get('role') == 'user' and message.get('images'):
                # Convert image data to clickable links
                for i, img in enumerate(message['images']):
                    if 'data' in img:
                        # Replace base64 data with a reference to the uploaded_images section
                        message['images'][i] = {
                            'mime_type': img.get('mime_type', 'image/jpeg'),
                            'note': 'Image data available in the uploaded_images section'
                        }

        data['threads'].append(thread_data)

    # Get rooms
    rooms = Room.objects(creator=user.id)
    for room in rooms:
        room_data = room.to_dict()

        # Process messages to include clickable image links
        for message in room_data['messages']:
            if message.get('role') == 'user' and message.get('user_id') == str(user.id) and message.get('images'):
                # Convert image data to clickable links
                for i, img in enumerate(message['images']):
                    if 'data' in img:
                        # Replace base64 data with a reference to the uploaded_images section
                        message['images'][i] = {
                            'mime_type': img.get('mime_type', 'image/jpeg'),
                            'note': 'Image data available in the uploaded_images section'
                        }

        data['rooms'].append(room_data)

    # Get friend relationships
    friends_as_user = FriendRelationship.objects(user=user.id)
    friends_as_friend = FriendRelationship.objects(friend=user.id)

    for relationship in friends_as_user:
        data['friend_relationships'].append({
            'id': str(relationship.id),
            'friend': {
                'id': str(relationship.friend.id),
                'username': relationship.friend.username,
                'display_name': relationship.friend.display_name
            },
            'created_at': relationship.created_at.isoformat() if hasattr(relationship, 'created_at') else None,
            'is_accepted': relationship.is_accepted,
            'direction': 'outgoing'
        })

    for relationship in friends_as_friend:
        data['friend_relationships'].append({
            'id': str(relationship.id),
            'friend': {
                'id': str(relationship.user.id),
                'username': relationship.user.username,
                'display_name': relationship.user.display_name
            },
            'created_at': relationship.created_at.isoformat() if hasattr(relationship, 'created_at') else None,
            'is_accepted': relationship.is_accepted,
            'direction': 'incoming'
        })

    # Get friend chats
    chats = FriendChat.get_chats_for_user(user.id)
    for chat in chats:
        chat_data = chat.to_dict()

        # Process messages to include only user's own images and make them clickable
        for message in chat_data['messages']:
            if message.get('user_id') == str(user.id) and message.get('images'):
                # Convert image data to clickable links
                for i, img in enumerate(message['images']):
                    if 'data' in img:
                        # Replace base64 data with a reference to the uploaded_images section
                        message['images'][i] = {
                            'mime_type': img.get('mime_type', 'image/jpeg'),
                            'note': 'Image data available in the uploaded_images section'
                        }
            elif message.get('user_id') != str(user.id) and message.get('images'):
                # Remove other users' images completely
                message.pop('images', None)

        data['friend_chats'].append(chat_data)

    # Get feature restrictions
    feature_restriction = FeatureRestriction.objects(user=user.id).first()
    if feature_restriction:
        data['feature_restrictions'] = {
            'max_threads': feature_restriction.max_threads,
            'max_conversation_sets': feature_restriction.max_conversation_sets,
            'max_live_rooms': feature_restriction.max_live_rooms,
            'max_live_conversation_sets': feature_restriction.max_live_conversation_sets,
            'created_at': feature_restriction.created_at.isoformat() if hasattr(feature_restriction, 'created_at') else None,
            'created_by': feature_restriction.created_by
        }

    # Get Spotify credentials (without sensitive tokens)
    spotify_creds = SpotifyCredentials.objects(user=user.id).first()
    if spotify_creds:
        data['spotify_credentials'] = {
            'created_at': spotify_creds.created_at.isoformat() if hasattr(spotify_creds, 'created_at') else None,
            'updated_at': spotify_creds.updated_at.isoformat() if hasattr(spotify_creds, 'updated_at') else None,
            'scope': spotify_creds.scope
        }

    return data

def send_data_export_email(email, data, subject=None, message=None):
    """Send data export email with attachments"""
    try:
        # Create JSON file
        json_data = json.dumps(data, indent=2)
        json_attachment = MIMEApplication(json_data.encode('utf-8'))
        json_attachment.add_header('Content-Disposition', 'attachment', filename='kevko_data_export.json')

        # Create XML file
        xml_data = dicttoxml.dicttoxml(data, custom_root='kevko_data', attr_type=False)
        xml_attachment = MIMEApplication(xml_data)
        xml_attachment.add_header('Content-Disposition', 'attachment', filename='kevko_data_export.xml')

        # Create CSV file for user data (flattened structure)
        csv_buffer = io.StringIO()
        csv_writer = csv.writer(csv_buffer)

        # Write user data
        csv_writer.writerow(['User Data'])
        for key, value in data['user'].items():
            csv_writer.writerow([key, value])

        # Write uploaded images summary
        csv_writer.writerow([])
        csv_writer.writerow(['Uploaded Images'])
        csv_writer.writerow(['File ID', 'Filename', 'Content Type', 'Created At', 'URL'])
        for image in data.get('uploaded_images', []):
            csv_writer.writerow([
                image['file_id'],
                image['filename'],
                image['content_type'],
                image['created_at'],
                image['url']
            ])

        # Write thread summary
        csv_writer.writerow([])
        csv_writer.writerow(['Thread Summary'])
        csv_writer.writerow(['Thread ID', 'Title', 'Created At', 'Updated At', 'Message Count'])
        for thread in data['threads']:
            csv_writer.writerow([
                thread['id'],
                thread['title'],
                thread['created_at'],
                thread['updated_at'],
                len(thread['messages'])
            ])

        # Write room summary
        csv_writer.writerow([])
        csv_writer.writerow(['Room Summary'])
        csv_writer.writerow(['Room ID', 'Title', 'Created At', 'Updated At', 'Message Count', 'Is Active'])
        for room in data['rooms']:
            csv_writer.writerow([
                room['room_id'],
                room['title'],
                room['created_at'],
                room['updated_at'],
                len(room['messages']),
                room['is_active']
            ])

        # Write friend relationship summary
        csv_writer.writerow([])
        csv_writer.writerow(['Friend Relationships'])
        csv_writer.writerow(['Friend Username', 'Friend Display Name', 'Created At', 'Is Accepted', 'Direction'])
        for relationship in data['friend_relationships']:
            csv_writer.writerow([
                relationship['friend']['username'],
                relationship['friend']['display_name'],
                relationship['created_at'],
                relationship['is_accepted'],
                relationship['direction']
            ])

        csv_attachment = MIMEApplication(csv_buffer.getvalue().encode('utf-8'))
        csv_attachment.add_header('Content-Disposition', 'attachment', filename='kevko_data_export.csv')

        # Create email
        msg = MIMEMultipart()
        msg['From'] = os.environ.get('EMAIL_ADDRESS', '<EMAIL>')
        msg['To'] = email
        msg['Subject'] = subject or 'Your Kevko Systems Data Export'

        # Email body
        body = message or """
        Hello,

        Attached is your requested data export from Kevko Systems. You'll find your data in three formats:

        1. JSON format (kevko_data_export.json)
        2. XML format (kevko_data_export.xml)
        3. CSV format (kevko_data_export.csv)

        The CSV file contains a simplified view of your data, while the JSON and XML files contain your complete data.

        IMAGES: Your images are included in the 'uploaded_images' section of the JSON and XML files.
        Each image has a 'data_url' field that contains a clickable link to view the image directly.
        You can copy and paste these links into your browser to view the images.

        Thank you for using Kevko Systems!
        """
        msg.attach(MIMEText(body, 'plain'))

        # Attach files
        msg.attach(json_attachment)
        msg.attach(xml_attachment)
        msg.attach(csv_attachment)

        # Send email
        smtp_server = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.environ.get('MAIL_PORT', 587))
        smtp_username = os.environ.get('EMAIL_ADDRESS')  # Use EMAIL_ADDRESS from .env
        smtp_password = os.environ.get('EMAIL_PASSWORD')  # Use EMAIL_PASSWORD from .env

        # For development/testing, log the email content instead of sending
        if not smtp_username or not smtp_password:
            logging.info(f"Email would be sent to {email} with data export (SMTP credentials not configured)")
            logging.info(f"Check that EMAIL_ADDRESS and EMAIL_PASSWORD are set in .env file")
            return True

        try:
            logging.info(f"Attempting to send email to {email} using SMTP server {smtp_server}:{smtp_port}")
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            logging.info(f"Logging in with username: {smtp_username}")
            server.login(smtp_username, smtp_password)
            logging.info(f"Sending email message")
            server.send_message(msg)
            server.quit()
            logging.info(f"Email sent successfully to {email}")
            return True
        except Exception as e:
            logging.error(f"Error sending email: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Error sending data export email: {str(e)}")
        return False

def generate_verification_code():
    """Generate a random 6-digit verification code"""
    return str(random.randint(100000, 999999))

def send_verification_code(email, code):
    """Send verification code to user's email"""
    try:
        # Create email
        msg = MIMEMultipart()
        msg['From'] = os.environ.get('EMAIL_ADDRESS', '<EMAIL>')
        msg['To'] = email
        msg['Subject'] = 'Verification Code for Data Deletion'

        # Email body
        body = f"""
        Hello,

        You have requested to delete your data from Kevko Systems.

        Your verification code is: {code}

        If you did not request this action, please ignore this email.

        Thank you for using Kevko Systems!
        """
        msg.attach(MIMEText(body, 'plain'))

        # Send email
        smtp_server = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.environ.get('MAIL_PORT', 587))
        smtp_username = os.environ.get('EMAIL_ADDRESS')
        smtp_password = os.environ.get('EMAIL_PASSWORD')

        # For development/testing, log the email content instead of sending
        if not smtp_username or not smtp_password:
            logging.info(f"Verification code {code} would be sent to {email} (SMTP credentials not configured)")
            # Store the code in session for verification
            session[f'verification_code_{email}'] = code
            return True

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()

        # Store the code in session for verification
        session[f'verification_code_{email}'] = code

        return True
    except Exception as e:
        logging.error(f"Error sending verification code: {str(e)}")
        return False

def get_stored_verification_code(email):
    """Get stored verification code for user"""
    return session.get(f'verification_code_{email}')

def schedule_data_deletion(user_id):
    """Schedule data deletion for a user (5 minutes delay)"""
    def delete_user_data(user_id):
        # Wait for 5 minutes
        time.sleep(300)  # 5 minutes = 300 seconds

        try:
            logging.info(f"Starting data deletion for user {user_id}")

            # Get user
            user = User.objects(id=user_id).first()
            if not user:
                logging.error(f"User {user_id} not found for deletion")
                return

            # Delete threads
            thread_count = Thread.objects(user=user_id).count()
            Thread.objects(user=user_id).delete()
            logging.info(f"Deleted {thread_count} threads for user {user_id}")

            # Delete rooms
            room_count = Room.objects(creator=user_id).count()
            Room.objects(creator=user_id).delete()
            logging.info(f"Deleted {room_count} rooms for user {user_id}")

            # Delete friend relationships
            friend_count = FriendRelationship.objects(user=user_id).count() + FriendRelationship.objects(friend=user_id).count()
            FriendRelationship.objects(user=user_id).delete()
            FriendRelationship.objects(friend=user_id).delete()
            logging.info(f"Deleted {friend_count} friend relationships for user {user_id}")

            # Delete friend chats
            chat_count = FriendChat.objects(user1=user_id).count() + FriendChat.objects(user2=user_id).count()
            FriendChat.objects(user1=user_id).delete()
            FriendChat.objects(user2=user_id).delete()
            logging.info(f"Deleted {chat_count} friend chats for user {user_id}")

            # Delete feature restrictions
            FeatureRestriction.objects(user=user_id).delete()
            logging.info(f"Deleted feature restrictions for user {user_id}")

            # Delete Spotify credentials
            SpotifyCredentials.objects(user=user_id).delete()
            logging.info(f"Deleted Spotify credentials for user {user_id}")

            # Delete user activity
            UserActivity.objects(user=user_id).delete()
            logging.info(f"Deleted user activity for user {user_id}")

            # Delete user
            user.delete()
            logging.info(f"Deleted user {user_id}")

            logging.info(f"Data deletion completed for user {user_id}")
        except Exception as e:
            logging.error(f"Error during data deletion for user {user_id}: {str(e)}")

    # Start a new thread for deletion
    thread = threading.Thread(target=delete_user_data, args=(user_id,))
    thread.daemon = True
    thread.start()

    logging.info(f"Data deletion scheduled for user {user_id} in 5 minutes")
    return True