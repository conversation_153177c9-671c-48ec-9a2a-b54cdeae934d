<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0" />
  <title>KevkoAI Chat Interface</title>

  <!-- Favicon links -->
  <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
  <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
  <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
  <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
  <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

  <!-- Immediate styles to prevent flash -->
  <style>
    body {
      background-color: rgb(21,21,21);
      margin: 0;
      opacity: 0;
    }
  </style>

  <!-- Resource hints for faster loading -->
  <link rel="preconnect" href="https://unpkg.com">
  <link rel="preconnect" href="https://cdn.tailwindcss.com">
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">

  <!-- Critical CSS for LCP element -->
  <style>
    /* Critical styles for thread-title to prevent layout shift */
    .thread-title {
      font-size: 16px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      display: block;
      contain: layout style;
    }
    /* Basic layout styles to prevent layout shift */
    .thread-item {
      display: flex;
      flex-direction: column;
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      contain: layout style;
    }
    /* Placeholder styles */
    .thread-item.placeholder {
      background-color: rgba(255, 255, 255, 0.05);
      animation: placeholder-pulse 1.5s infinite ease-in-out;
    }
    @keyframes placeholder-pulse {
      0% { opacity: 0.5; }
      50% { opacity: 0.7; }
      100% { opacity: 0.5; }
    }
  </style>

  <!-- Link to the chat.css file with optimized loading -->
  <link rel="preload" href="{{ url_for('static', filename='css/chat.css') }}" as="style">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/thinking-container.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/model-selector.css') }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/audio-player.css') }}" media="print" onload="this.media='all'">

  <!-- External resources with optimized loading -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" as="style">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" media="print" onload="this.media='all'">

  <!-- Defer non-critical JavaScript -->
  <script src="https://cdn.tailwindcss.com" defer></script>
  <!-- Add this in the head section, before your other scripts -->
  <script src="{{ url_for('static', filename='js/vendor/lucide.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/vendor/lucide-custom.js') }}"></script>
  <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" defer></script>

  <!-- Optional: Pass the initial thread ID from Flask to JavaScript -->
  <script>
    // Register service worker for caching
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/static/js/sw.js')
          .then(registration => {
            console.log('ServiceWorker registered with scope:', registration.scope);
          })
          .catch(error => {
            console.error('ServiceWorker registration failed:', error);
          });
      });
    }
    
    window.initialThreadId = "{{ thread_id or '' }}";

    // Enhanced preloading mechanism
    (function() {
      // Use a resource hint to preconnect to the API endpoint
      const apiPreconnect = document.createElement('link');
      apiPreconnect.rel = 'preconnect';
      apiPreconnect.href = '/api';
      document.head.appendChild(apiPreconnect);
      
      // Add resource hints for the API endpoints
      const threadsHint = document.createElement('link');
      threadsHint.rel = 'preload';
      threadsHint.href = '/api/chat/threads';
      threadsHint.as = 'fetch';
      threadsHint.crossOrigin = 'same-origin';
      document.head.appendChild(threadsHint);
      
      // Start fetching threads data with priority and timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout
      
      window.threadsDataPromise = fetch('/api/chat/threads', {
          signal: controller.signal,
          priority: 'high',
          cache: 'default'
        })
        .then(response => {
          clearTimeout(timeoutId);
          return response.json();
        })
        .catch(error => {
          console.error('Failed to preload threads:', error);
          // If aborted due to timeout, retry with lower priority
          if (error.name === 'AbortError') {
            return fetch('/api/chat/threads')
              .then(response => response.json())
              .catch(e => {
                console.error('Retry failed:', e);
                return [];
              });
          }
          return [];
        });

      // If we have a thread ID, preload that specific thread too
      if (window.initialThreadId) {
        const threadHint = document.createElement('link');
        threadHint.rel = 'preload';
        threadHint.href = `/api/chat/thread/${window.initialThreadId}`;
        threadHint.as = 'fetch';
        threadHint.crossOrigin = 'same-origin';
        document.head.appendChild(threadHint);
        
        window.threadDataPromise = fetch(`/api/chat/thread/${window.initialThreadId}`, {
            priority: 'high',
            cache: 'default'
          })
          .then(response => response.json())
          .catch(error => {
            console.error('Failed to preload thread:', error);
            return null;
          });
      }
    })();
  </script>
  {% if shared_thread %}
  <script>
    window.sharedThread = {{ shared_thread|tojson|safe }};
  </script>
  {% endif %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" media="print" onload="this.media='all'">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js" defer></script>
</head>
<body>
  <!-- Container that centers the chat interface -->
  <div class="chat-center">
    <!-- Main Chat Container -->
    <div class="chat-container">

      <!-- Left Sidebar -->
      <div class="sidebar">
        <div class="sidebar-header">
          <div class="sidebar-header-top">
            <h1>KevkoAI</h1>
            <button id="closeSidebarBtn" aria-label="Close sidebar" class="mobile-only">
              <i data-lucide="x"></i>
            </button>
          </div>
          <p>Build your own AI assistant.</p>
        </div>

        <div class="sidebar-buttons">
          <button aria-label="Personas">
            <i data-lucide="user-2"></i>
            Personas
          </button>
          <button aria-label="Discovery">
            <i data-lucide="grid"></i>
            Discovery
          </button>
        </div>

        <!-- Chat List -->
        <div id="chatList">
          <!-- Chat items will be dynamically inserted here -->
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
          <button id="sidebarSettingsBtn" aria-label="Settings">
            <i data-lucide="settings"></i>
            <span>Settings</span>
          </button>
          <button id="createChatBtn" aria-label="Create new chat">
            <i data-lucide="plus"></i>
            Create Chat
          </button>
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="main-chat">
        <!-- Header -->
        <header>
          <div class="header-left">
            <button id="sidebarToggleBtn" aria-label="Toggle sidebar" class="mobile-only">
              <i data-lucide="menu"></i>
            </button>
            <h2 id="activeChatName">New Chat</h2>
          </div>
          <div>
            <button id="shareChatBtn" aria-label="Share chat">
              <i data-lucide="share"></i>
              <span>Share</span>
            </button>
            <button id="fullscreenBtn" aria-label="Toggle fullscreen">
              <i data-lucide="expand"></i>
              <span>Fullscreen</span>
            </button>
          </div>
        </header>

        <!-- Chat Content -->
        <div id="chatContent">
          <!-- Chat messages will be dynamically inserted here -->
        </div>

        <!-- Input Area -->
        <div class="input-area">
          <div class="input-container">
            <div class="function-buttons">
              <button id="imageBtn" aria-label="Upload image">
                <i data-lucide="image"></i>
                <span>Image</span>
              </button>
              <button id="joinLiveBtn" aria-label="Join Live Chat">
                <i data-lucide="users"></i>
                <span>Live</span>
              </button>
              <div id="modelSelector" class="model-btn">
                <div class="current-model-icon">
                  <i data-lucide="brain"></i>
                </div>
                <div id="currentModel">GPT-4o Mini</div>

                <!-- Model Selector Backdrop -->
                <div class="model-selector-backdrop" id="modelSelectorBackdrop"></div>
                
                <!-- Model Selector Container -->
                <div class="model-selector-container" id="modelSelectorContainer">
                  <div class="model-selector-header">
                    <h3>Select Model</h3>
                    <button class="close-model-selector" id="closeModelSelector" aria-label="Close model selector">
                      <i data-lucide="x"></i>
                    </button>
                  </div>
                  <!-- Tooltip for model descriptions -->
                  <div id="modelDescriptionTooltip" class="model-description-tooltip" style="display: none;"></div>
                  <div class="model-selector-options">
                    <div class="model-option-card" data-model="gpt-4o-mini">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="sparkles"></i>
                      </div>
                      <div class="model-option-name">GPT-4o Mini</div>
                      <div class="model-info-icon" data-description="Fast, reliable, great at complex tasks">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="gemini-2.0-flash">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Gemini</div>
                      <div class="model-info-icon" data-description="Excellent with images, creative tasks">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="qwen-qwq-32b">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="cpu"></i>
                      </div>
                      <div class="model-option-name">Qwen</div>
                      <div class="model-info-icon" data-description="Fast reasoning inference, balanced performance">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="gemma2-9b-it">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="brain"></i>
                      </div>
                      <div class="model-option-name">Gemma 2</div>
                      <div class="model-info-icon" data-description="Google's 9B instruction-tuned model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama-3.3-70b-versatile">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="flame"></i>
                      </div>
                      <div class="model-option-name">Llama 3.3 70B</div>
                      <div class="model-info-icon" data-description="Meta's versatile 70B model, 128K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama-3.1-8b-instant">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Llama 3.1 8B</div>
                      <div class="model-info-icon" data-description="Meta's instant 8B model, 128K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama3-70b-8192">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="flame"></i>
                      </div>
                      <div class="model-option-name">Llama 3 70B</div>
                      <div class="model-info-icon" data-description="Meta's 70B model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    <div class="model-option-card" data-model="llama3-8b-8192">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="zap"></i>
                      </div>
                      <div class="model-option-name">Llama 3 8B</div>
                      <div class="model-info-icon" data-description="Meta's 8B model, 8K context">
                        <i data-lucide="info"></i>
                      </div>
                    </div>
                    
                    <div class="model-option-card" data-model="playai-tts">
                      <div class="model-selection-indicator"></div>
                      <div class="model-option-icon">
                        <i data-lucide="volume-2"></i>
                      </div>
                      <div class="model-option-name">PlayAI TTS</div>
                      <div class="model-info-icon" data-description="Llama 3.3 70B with voice response using Angelo-PlayAI">
                        <i data-lucide="info"></i>
                      </div>
                    </div>

                    <!-- Tooltip container that will be positioned absolutely -->
                    <div id="modelDescriptionTooltip" class="model-description-tooltip" style="display: none;"></div>
                  </div>
                </div>
              </div>
            </div>
            <div id="imagePreviewContainer" class="flex gap-2 hidden"></div>

            <!-- Hidden File Inputs -->
            <input type="file" id="imageFileInput" accept="image/*" multiple class="hidden">

            <!-- Warning Modal -->
            <div id="liveWarningModal" class="warning-modal">
              <div class="modal-backdrop"></div>
              <div class="modal-content">
                <p>You are about to join Live Chat mode where you can chat with AI and an invited user in real-time. Continue?</p>
                <div class="modal-actions">
                  <button id="cancelLiveBtn">Cancel</button>
                  <button id="confirmLiveBtn">Continue</button>
                </div>
              </div>
            </div>

            <div class="input-wrapper">
              <div style="position: relative; flex: 1;">
                <button id="addButton" aria-label="Add content">
                  <i data-lucide="plus" class="icon"></i>
                </button>
                <textarea id="messageInput" rows="1" placeholder="Ask anything..."></textarea>
              </div>
              <button id="sendButton" aria-label="Send message">
                <i data-lucide="arrow-up"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Modal -->
  <div id="deleteModal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
      <div>
        <div>
          <i data-lucide="trash-2"></i>
        </div>
        <h3>Delete Chat</h3>
        <p>Are you sure you want to delete this chat? This action cannot be undone.</p>
        <div>
          <button id="cancelDelete" aria-label="Cancel deletion">Cancel</button>
          <button id="confirmDelete" aria-label="Confirm deletion">Delete</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <h3>Settings</h3>
        <button id="closeSettings" aria-label="Close settings">
          <i data-lucide="x"></i>
        </button>
      </div>
      <div>
        <div class="danger-zone">
          <h4>Danger Zone</h4>
          <button id="deleteAllChats" aria-label="Delete all chats">
            <i data-lucide="trash-2"></i>
            Delete All Chats
          </button>
        </div>
      </div>
    </div>
  </div>



  <!-- Optional: Additional JavaScript -->
  <script src="{{ url_for('static', filename='js/restriction-handler.js') }}"></script>
  <script src="{{ url_for('static', filename='js/audio-player.js') }}" defer></script>
  <script src="{{ url_for('static', filename='js/chat.js') }}" defer></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Ensure smooth animation even if page loads very quickly
      requestAnimationFrame(() => {
        document.body.style.opacity = '1';
      });
    });
  </script>

  <!-- Notification container for alerts -->
  <div id="notification-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md"></div>

  <!-- Restriction Modal -->
  {% include 'includes/restriction_modal.html' %}

  <!-- Feature Restriction Notification -->
  {% include 'includes/feature_restriction_notification.html' %}


</body>
</html>
