/**
 * KevkoNotes Service
 * Notes and reminders service
 */

// Import dependencies
// None for now

// Service class definition
window.KevkoNotesService = class KevkoNotesService extends window.Service {
    constructor(config) {
        super(config);
        this.notes = [];
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // In a real implementation, this might fetch data from an API
        this.notes = [
            { id: 1, title: 'Shopping List', content: 'Milk, Eggs, Bread', date: '2023-04-01' },
            { id: 2, title: 'Meeting Notes', content: 'Discuss project timeline', date: '2023-04-02' },
            { id: 3, title: 'Ideas', content: 'New app features', date: '2023-04-03' }
        ];
    }

    /**
     * Render the service card HTML
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering service: ${this.id}`);
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            let actionButton = '';

            if (this.status === 'online' && this.url) {
                // External link for online services with URL
                actionButton = `
                    <div class="w-full">
                        <a href="${this.url}" target="_blank"
                           class="w-full bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group mb-2">
                            <span class="text-sm text-slate-300 group-hover:text-slate-100">Launch ${this.name}</span>
                            <i data-lucide="chevron-right" class="h-4 w-4 ml-1"></i>
                        </a>

                    </div>
                `;
            } else {
                // Disabled button for offline or coming soon services
                actionButton = `
                    <div class="w-full">
                        <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed mb-2">
                            <span class="text-sm text-slate-300 animate-pulse">${this.status === 'offline' ? 'Offline' : 'Coming Soon'}</span>
                        </div>
                    </div>
                `;
            }

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                        <div id="notes-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                            <h4 class="text-xs font-medium text-slate-300 mb-2">Your Notes</h4>
                            <div class="space-y-1 max-h-32 overflow-y-auto">
                                <!-- Notes will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize notes button
            const showNotesBtn = element.querySelector('#showNotes');
            if (showNotesBtn) {
                console.log(`Found notes button for service: ${this.id}`);
                showNotesBtn.addEventListener('click', () => {
                    this.toggleNotes(element);
                });
            } else {
                console.log(`No notes button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoNotesService ${this.id}:`, error);
        }
    }

    /**
     * Toggle notes visibility
     * @param {HTMLElement} element The service element
     */
    async toggleNotes(element) {
        const notesContainer = element.querySelector(`#notes-${this.id}`);
        if (!notesContainer) return;

        const isHidden = notesContainer.classList.contains('hidden');

        if (isHidden) {
            // Initialize notes if needed
            if (this.notes.length === 0) {
                await this.init();
            }

            // Populate notes
            const notesContent = notesContainer.querySelector('.space-y-1');
            if (notesContent) {
                notesContent.innerHTML = this.notes.map(note => `
                    <div class="bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors">
                        <div class="flex items-center justify-between">
                            <span class="text-xs font-medium text-slate-300">${note.title}</span>
                            <span class="text-xs text-slate-500">${note.date}</span>
                        </div>
                        <p class="text-xs text-slate-400 mt-1">${note.content}</p>
                    </div>
                `).join('');
            }

            // Show notes
            notesContainer.classList.remove('hidden');
        } else {
            // Hide notes
            notesContainer.classList.add('hidden');
        }
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoNotes',
            name: 'KevkoNotes',
            description: 'Notes and reminders',
            icon: 'clipboard',
            color: 'pink',
            category: 'Productivity',
            version: '1.0.0',
            author: 'Kevko',
            dependencies: [],
            features: [
                'Note taking',
                'Reminders',
                'To-do lists',
                'Sync across devices'
            ],
            screenshots: [
                '/img/services/kevkoNotes/screenshot1.jpg'
            ]
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }

    /**
     * Get service downloads
     * @returns {Array} Service downloads
     */
    static getDownloads() {
        return [
            {
                name: 'KevkoNotes Desktop App',
                description: 'Take notes on your desktop',
                icon: 'monitor',
                size: '18 MB',
                version: '1.0.5',
                date: '2023-03-25',
                url: '#'
            },
            {
                name: 'KevkoNotes Mobile App',
                description: 'Take notes on the go',
                icon: 'smartphone',
                size: '10 MB',
                version: '1.0.5',
                date: '2023-03-25',
                url: '#'
            }
        ];
    }
};
