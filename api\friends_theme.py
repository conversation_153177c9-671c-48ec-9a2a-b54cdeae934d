from flask import Blueprint, jsonify, request
from models.sql_user import SQLUser
from models.friends import FriendTheme
from database import db
from flask_login import login_required, current_user

friends_theme_bp = Blueprint('friends_theme', __name__)

@friends_theme_bp.route('/api/friends/theme', methods=['POST'])
@login_required
def save_theme():
    """Save the user's chat theme preference"""
    if not current_user.is_authenticated:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.json
    theme = data.get('theme')
    
    if not theme:
        return jsonify({'error': 'Theme is required'}), 400
    
    # Check if theme is valid
    valid_themes = ['blue', 'purple', 'green', 'red', 'orange', 'teal', 'pink', 'indigo', 'amber']
    if theme not in valid_themes:
        return jsonify({'error': 'Invalid theme'}), 400
    
    # Save theme to database
    try:
        # Print current user type and ID for debugging
        print(f"Current user type: {type(current_user)}, ID: {current_user.id}, ID type: {type(current_user.id)}")
        
        # Convert ObjectId to string if needed
        current_user_id = str(current_user.id) if hasattr(current_user.id, '__str__') else current_user.id
        
        # Check if user already has a theme
        existing_theme = FriendTheme.query.filter_by(user_id=current_user_id).first()
        
        if existing_theme:
            # Update existing theme
            existing_theme.theme = theme
        else:
            # Create new theme
            new_theme = FriendTheme(user_id=current_user_id, theme=theme)
            db.session.add(new_theme)
        
        db.session.commit()
        return jsonify({'success': True, 'theme': theme}), 200
    
    except Exception as e:
        db.session.rollback()
        print(f"Error saving theme: {str(e)}")
        return jsonify({'error': 'Failed to save theme'}), 500

@friends_theme_bp.route('/api/friends/theme/me', methods=['GET'])
@login_required
def get_my_theme():
    """Get the current user's theme"""
    if not current_user.is_authenticated:
        return jsonify({'error': 'Not authenticated'}), 401
    
    try:
        # Convert ObjectId to string if needed
        current_user_id = str(current_user.id) if hasattr(current_user.id, '__str__') else current_user.id
        
        # Get current user's theme
        user_theme = FriendTheme.query.filter_by(user_id=current_user_id).first()
        
        if user_theme:
            return jsonify({'success': True, 'theme': user_theme.theme}), 200
        else:
            # Return default theme if not set
            return jsonify({'success': True, 'theme': 'blue'}), 200
    
    except Exception as e:
        print(f"Error getting user theme: {str(e)}")
        return jsonify({'error': 'Failed to get theme'}), 500

@friends_theme_bp.route('/api/friends/themes', methods=['GET'])
@login_required
def get_themes():
    """Get themes for all friends"""
    if not current_user.is_authenticated:
        return jsonify({'error': 'Not authenticated'}), 401
    
    try:
        # Print current user type and ID for debugging
        print(f"Current user type: {type(current_user)}, ID: {current_user.id}, ID type: {type(current_user.id)}")
        
        # Convert ObjectId to string if needed
        current_user_id = str(current_user.id) if hasattr(current_user.id, '__str__') else current_user.id
        
        # Get current user's friends from SQLAlchemy
        user = SQLUser.query.get(current_user_id)
        if not user:
            # Try to find by string ID
            print(f"User not found with ID {current_user_id}, trying to find by string ID")
            return jsonify({'success': True, 'themes': {}}), 200
        
        # Get friend IDs - ensure they are all strings or integers, not ObjectId
        friend_ids = []
        for friend in user.friends:
            friend_id = friend.id
            if hasattr(friend_id, '__str__'):
                friend_id = str(friend_id)
            friend_ids.append(friend_id)
        
        # Add current user ID
        if current_user_id not in friend_ids:
            friend_ids.append(current_user_id)
        
        # Get themes for all friends AND the current user
        # Use individual queries to avoid IN clause with mixed types
        themes = []
        for friend_id in friend_ids:
            theme = FriendTheme.query.filter_by(user_id=friend_id).first()
            if theme:
                themes.append(theme)
        
        # Create a dictionary of friend_id: theme
        theme_dict = {str(theme.user_id): theme.theme for theme in themes}
        
        return jsonify({'success': True, 'themes': theme_dict}), 200
    
    except Exception as e:
        print(f"Error getting themes: {str(e)}")
        return jsonify({'error': 'Failed to get themes'}), 500
        
@friends_theme_bp.route('/api/friends/themes/<friend_id>', methods=['GET'])
@login_required
def get_friend_theme(friend_id):
    """Get a specific friend's theme"""
    if not current_user.is_authenticated:
        return jsonify({'error': 'Not authenticated'}), 401
    
    try:
        # Get the friend's theme
        friend_theme = FriendTheme.query.filter_by(user_id=friend_id).first()
        
        if friend_theme:
            return jsonify({'success': True, 'theme': friend_theme.theme}), 200
        else:
            # Return default theme if not set
            return jsonify({'success': True, 'theme': 'blue'}), 200
    
    except Exception as e:
        print(f"Error getting friend theme: {str(e)}")
        return jsonify({'error': 'Failed to get friend theme'}), 500