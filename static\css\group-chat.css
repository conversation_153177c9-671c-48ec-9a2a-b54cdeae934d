/* Group Chat Styles */

/* Group chat item in the friend list */
.group-chat-item {
    background: linear-gradient(to right, rgba(124, 58, 237, 0.2), rgba(139, 92, 246, 0.1)) !important;
    border-color: rgba(139, 92, 246, 0.3) !important;
    margin-bottom: 8px;
    position: relative;
    transition: all 0.2s ease;
}

.group-chat-item:hover {
    background: linear-gradient(to right, rgba(124, 58, 237, 0.3), rgba(139, 92, 246, 0.2)) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Group chat avatar */
.group-chat-avatar {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

/* Group chat avatar container */
#activeFriendAvatar {
    width: 2rem;
    height: 2rem;
    margin-right: 0.5rem;
    position: relative;
    border-radius: 50%;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive sizing for the avatar container */
@media (min-width: 640px) {
    #activeFriendAvatar {
        width: 2.5rem;
        height: 2.5rem;
        margin-right: 0.75rem;
    }
}

.group-chat-avatar i {
    color: white;
}

.group-chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    box-shadow: none;
    border-radius: 50%;
}

/* Change profile picture button */
#changeGroupPicBtn {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

/* Group chat member count badge - hidden */
.group-chat-badge {
    display: none; /* Hide the badge */
}

/* Group chat name */
.group-chat-item .text-sm {
    color: #e2e8f0 !important;
    font-weight: 600;
}

/* Group chat last message */
.group-chat-item .text-xs {
    color: #94a3b8 !important;
}

/* Group chat section header */
.group-chats-header {
    font-size: 11px;
    font-weight: 600;
    color: #8b5cf6;
    margin-bottom: 8px;
    padding-left: 4px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

/* Group chat section */
.group-chats-section {
    margin-bottom: 16px;
}

/* Active group chat */
.group-chat-item.active {
    background: linear-gradient(to right, rgba(124, 58, 237, 0.4), rgba(139, 92, 246, 0.3)) !important;
    border-color: rgba(139, 92, 246, 0.5) !important;
}

/* Group chat with new messages */
.group-chat-item.has-new-messages {
    position: relative;
    border-color: rgba(139, 92, 246, 0.6) !important;
    background: linear-gradient(to right, rgba(124, 58, 237, 0.3), rgba(139, 92, 246, 0.2)) !important;
}

.group-chat-item.has-new-messages::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background-color: #8b5cf6;
    border-radius: 50%;
    animation: pulse 1.5s infinite ease-in-out;
}

/* Create group chat button */
.create-group-btn {
    background: linear-gradient(to right, #8b5cf6, #7c3aed) !important;
}

.create-group-btn:hover {
    background: linear-gradient(to right, #7c3aed, #6d28d9) !important;
}
