from flask import jsonify, request
from flask_login import login_required, current_user
from . import admin_api
from models.service_update import ServiceUpdate
from models.user import User
from api.admin.routes import admin_required
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@admin_api.route('/updates', methods=['GET'])
@login_required
def get_updates():
    """
    Get all service updates
    If service_id is provided, filter by service
    """
    try:
        # Get optional service_id filter
        service_id = request.args.get('service_id')

        # Query updates
        query = {}
        if service_id:
            query['service_id'] = service_id

        # Get updates from database
        updates = ServiceUpdate.objects(**query).order_by('-published_at')

        # Convert to list of dictionaries
        updates_list = [update.to_dict() for update in updates]

        return jsonify({
            'success': True,
            'updates': updates_list
        })
    except Exception as e:
        logger.error(f"Error getting updates: {str(e)}")
        return jsonify([]), 200

@admin_api.route('/updates', methods=['POST'])
@login_required
@admin_required
def create_update():
    """
    Create a new service update
    Required fields: service_id, title, description
    Optional fields: version
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['service_id', 'title', 'description']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400

        # Create new update
        update = ServiceUpdate(
            service_id=data['service_id'],
            title=data['title'],
            description=data['description'],
            version=data.get('version', ''),
            published_by=current_user.id
        )

        # Save to database
        update.save()

        logger.info(f"Created new update for service {data['service_id']}")

        return jsonify({
            'success': True,
            'message': 'Update created successfully',
            'update': update.to_dict()
        })
    except Exception as e:
        logger.error(f"Error creating update: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_api.route('/updates/<update_id>', methods=['DELETE'])
@login_required
@admin_required
def delete_update(update_id):
    """
    Delete a service update
    """
    try:
        # Find the update
        update = ServiceUpdate.objects(id=update_id).first()

        if not update:
            return jsonify({
                'success': False,
                'error': 'Update not found'
            }), 404

        # Delete the update
        update.delete()

        logger.info(f"Deleted update {update_id}")

        return jsonify({
            'success': True,
            'message': 'Update deleted successfully'
        })
    except Exception as e:
        logger.error(f"Error deleting update: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_api.route('/updates/services', methods=['GET'])
@login_required
@admin_required
def get_available_services():
    """
    Get a list of available services for the updates dropdown
    """
    try:
        # This is a simplified version - in a real implementation,
        # you would fetch this from a database or configuration
        services = [
            {'id': 'kevkoAI', 'name': 'KevkoAI'},
            {'id': 'kevkoFy', 'name': 'KevkoFy'},
            {'id': 'kevkoHome', 'name': 'KevkoHome'},
            {'id': 'kevkoNotes', 'name': 'KevkoNotes'},
            {'id': 'kevkoCloud', 'name': 'KevkoCloud'},
            {'id': 'kevkoWeather', 'name': 'KevkoWeather'},
            {'id': 'system', 'name': 'System'}
        ]

        return jsonify(services), 200
    except Exception as e:
        logger.error(f"Error getting available services: {str(e)}")
        return jsonify([]), 200
