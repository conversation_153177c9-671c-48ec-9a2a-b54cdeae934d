/**
 * Activity Tracker
 * 
 * Tracks user activity and sends updates to the server
 */

class ActivityTracker {
    constructor(options = {}) {
        // Default options
        this.options = {
            updateInterval: 30000, // 30 seconds
            endpoint: '/api/activity/update',
            defaultSection: 'chat',
            debug: false,
            ...options
        };
        
        // Current section
        this.currentSection = this.options.defaultSection;
        
        // Timer reference
        this.updateTimer = null;
        
        // Initialize
        this.init();
    }
    
    /**
     * Initialize the activity tracker
     */
    init() {
        // Start the update timer
        this.startUpdateTimer();
        
        // Log initialization if debug is enabled
        this.log('Activity tracker initialized');
    }
    
    /**
     * Start the update timer
     */
    startUpdateTimer() {
        // Clear any existing timer
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        // Start a new timer
        this.updateTimer = setInterval(() => {
            this.updateActivity();
        }, this.options.updateInterval);
        
        // Update activity immediately
        this.updateActivity();
    }
    
    /**
     * Stop the update timer
     */
    stopUpdateTimer() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }
    
    /**
     * Set the current section
     * 
     * @param {string} section - The current section
     */
    setSection(section) {
        // Update the current section
        this.currentSection = section;
        
        // Update activity immediately
        this.updateActivity();
        
        this.log(`Section changed to: ${section}`);
    }
    
    /**
     * Update activity on the server
     */
    updateActivity() {
        // Send the update to the server
        fetch(this.options.endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                section: this.currentSection
            }),
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            this.log('Activity updated successfully', data);
        })
        .catch(error => {
            console.error('Error updating activity:', error);
        });
    }
    
    /**
     * Log a message if debug is enabled
     * 
     * @param {string} message - The message to log
     * @param {object} data - Optional data to log
     */
    log(message, data = null) {
        if (this.options.debug) {
            if (data) {
                console.log(`[ActivityTracker] ${message}`, data);
            } else {
                console.log(`[ActivityTracker] ${message}`);
            }
        }
    }
}

// Create a global activity tracker instance
let activityTracker = null;

// Initialize the activity tracker when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Get the current section from the page if available
    const currentSection = document.body.dataset.section || 'chat';
    
    // Create the activity tracker
    activityTracker = new ActivityTracker({
        defaultSection: currentSection,
        debug: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    });
    
    // Expose the activity tracker to the global scope
    window.activityTracker = activityTracker;
});