/*
 * Chat Grid Layout - Modern grid-based styling for chat messages
 * This file implements a responsive grid layout for the chat UI
 */

/* Messages Container - Grid Layout */
#messagesContainer {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
    max-width: 100%;
    margin: 0 auto;
    overflow-y: auto;
    height: 100%;
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
}

#messagesContainer::-webkit-scrollbar {
    width: 4px;
}

#messagesContainer::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 4px;
}

#messagesContainer::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 4px;
}

/* Message Row - Contains avatar and message content */
.message {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 8px;
    max-width: 100%;
    position: relative;
    transition: all 0.2s ease;
    margin-bottom: 0; /* Remove default margin, grid gap handles spacing */
}

/* Friend messages (default layout with avatar on left) */
.message.friend {
    grid-template-columns: auto 1fr;
    justify-content: start;
}

/* Self messages (align to right) */
.message.self {
    grid-template-columns: 1fr auto;
    justify-content: end;
}

/* Message Content Container */
.message-content {
    padding: 12px 16px;
    border-radius: 12px;
    position: relative;
    word-break: break-word;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    max-width: 85%;
}

/* Self message content styling */
.message.self .message-content {
    border-top-right-radius: 4px;
    justify-self: end;
    color: white;
}

/* Friend message content styling */
.message.friend .message-content {
    border-top-left-radius: 4px;
    justify-self: start;
    color: #e2e8f0;
}

/* Message Avatar */
.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    align-self: flex-end;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Self message avatar positioning */
.message.self .message-avatar {
    order: 2; /* Move avatar to the end for self messages */
    margin-left: 8px;
}

/* Friend message avatar positioning */
.message.friend .message-avatar {
    order: 0; /* Keep avatar at the beginning for friend messages */
    margin-right: 8px;
}

/* Message Time */
.message-time {
    font-size: 0.7rem;
    opacity: 0.8;
    margin-top: 4px;
    grid-column: 2;
    justify-self: end;
    color: rgba(226, 232, 240, 0.8);
}

.message.self .message-time {
    grid-column: 1;
}

/* Message Images */
.message-images {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
    margin-top: 8px;
    max-width: 100%;
}

.message-image-preview {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-image-preview:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* System Messages */
.message.system {
    grid-template-columns: 1fr;
    justify-items: center;
    margin: 16px 0;
}

.message.system .message-content {
    background: rgba(51, 65, 85, 0.5);
    border: 1px solid rgba(100, 116, 139, 0.3);
    color: #94a3b8;
    font-size: 0.85rem;
    padding: 8px 16px;
    border-radius: 16px;
    max-width: 80%;
    text-align: center;
}

/* Date Separator */
.date-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 24px 0;
    color: #94a3b8;
    font-size: 0.8rem;
    grid-column: 1 / -1;
}

.date-separator::before,
.date-separator::after {
    content: "";
    flex-grow: 1;
    height: 1px;
    background: rgba(148, 163, 184, 0.3);
    margin: 0 12px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(51, 65, 85, 0.5);
    border-radius: 12px;
    margin-top: 8px;
    max-width: 100px;
    grid-column: 2;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    margin: 0 2px;
    background: #94a3b8;
    border-radius: 50%;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #messagesContainer {
        padding: 12px;
        gap: 12px;
    }

    .message-content {
        padding: 10px 14px;
        max-width: 90%;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }

    .date-separator {
        margin: 16px 0;
        font-size: 0.75rem;
    }
}

/* Larger screens */
@media (min-width: 1200px) {
    #messagesContainer {
        margin: 0 auto;
    }

    .message-content {
        max-width: 70%;
    }
}

/* Chat Theme Carousel Styling */
.theme-carousel-container {
    position: relative;
    width: 100%;
    padding: 20px 0;
    overflow: hidden;
}

.theme-carousel {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
    height: 240px;
}

/* Theme Cards */
.theme-card {
    position: absolute;
    width: 180px;
    transition: all 0.5s ease;
    opacity: 0.7;
    transform: scale(0.8);
    z-index: 1;
}

.theme-card.prev,
.theme-card.next {
    opacity: 0.7;
    transform: scale(0.85);
    z-index: 2;
}

.theme-card.active {
    opacity: 1;
    transform: scale(1);
    z-index: 3;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.theme-card.prev {
    transform: translateX(-120%) scale(0.85);
}

.theme-card.next {
    transform: translateX(120%) scale(0.85);
}

.theme-card.prev-out {
    transform: translateX(-220%) scale(0.7);
    opacity: 0.3;
    z-index: 1;
}

.theme-card.next-out {
    transform: translateX(220%) scale(0.7);
    opacity: 0.3;
    z-index: 1;
}

/* Theme Card Content */
.theme-card-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.theme-card.active .theme-card-content {
    border-color: rgba(99, 102, 241, 0.7);
}

/* Navigation Controls */
.theme-carousel-nav {
    display: flex;
    justify-content: space-between;
    width: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    pointer-events: none;
}

.nav-button {
    background: rgba(30, 30, 45, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    pointer-events: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-button:hover {
    background: rgba(99, 102, 241, 0.8);
    transform: scale(1.1);
}

.nav-button:active {
    transform: scale(0.95);
}

.nav-button.prev-btn {
    margin-left: 10px;
}

.nav-button.next-btn {
    margin-right: 10px;
}

/* Theme Indicators */
.theme-indicators {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    gap: 8px;
}

.theme-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-indicator.active {
    background-color: rgba(99, 102, 241, 0.8);
    transform: scale(1.2);
}
