<div id="serviceDetailsModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg p-6 w-[700px] max-w-full max-h-[90vh] flex flex-col">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div id="serviceDetailsIcon" class="p-2 rounded-lg mr-3">
                    <!-- Icon will be populated by JS -->
                </div>
                <h3 id="serviceDetailsTitle" class="text-xl font-medium text-slate-100">Service Details</h3>
            </div>
            <button id="closeServiceDetailsModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="overflow-y-auto flex-grow pr-1">
            <div class="space-y-4">
                <!-- Service info will be populated by JS -->
                <div id="serviceDetailsContent">
                    <div class="flex justify-center items-center py-8">
                        <div class="flex items-center">
                            <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                            <span class="text-slate-400">Loading service details...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="pt-4 mt-2 border-t border-slate-700 flex justify-between">
            <button id="serviceDetailsBackButton" class="bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md px-4 py-2">
                <i data-lucide="arrow-left" class="h-4 w-4 mr-2 inline"></i>
                Back to Store
            </button>
            <div id="serviceDetailsActionButtons">
                <!-- Action buttons will be populated by JS -->
            </div>
        </div>
    </div>
</div>
