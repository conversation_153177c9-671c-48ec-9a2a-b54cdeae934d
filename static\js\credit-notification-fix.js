/**
 * credit-notification-fix.js
 * Ensures proper integration between CreditNotification and CreditManager
 */

/**
 * Direct function to show the add credits modal
 * This bypasses the admin check in CreditManager.showAddCreditsModal
 */
function showAddCreditsModalDirect() {
    console.log('Showing add credits modal directly');
    
    const modal = document.getElementById('addCreditsModal');
    if (!modal) {
        console.error('Add credits modal not found in the DOM');
        return false;
    }
    
    // Reset form fields
    const userSearchInput = document.getElementById('creditUserSearch');
    if (userSearchInput) {
        userSearchInput.value = '';
    }
    
    const userSelect = document.getElementById('creditUser');
    if (userSelect) {
        userSelect.innerHTML = '<option value="">Select a user</option>';
    }
    
    const creditAmount = document.getElementById('creditAmount');
    if (creditAmount) {
        creditAmount.value = '10';
    }
    
    const creditReason = document.getElementById('creditReason');
    if (creditReason) {
        creditReason.value = 'Admin adjustment';
    }
    
    const searchResultsContainer = document.getElementById('userSearchResults');
    if (searchResultsContainer) {
        searchResultsContainer.innerHTML = '';
    }
    
    // Show the modal - both by removing class and setting style to flex
    modal.classList.remove('hidden');
    modal.style.display = 'flex';
    
    // Using the same approach as admin modals
    // No need to move the modal or set specific positioning styles
    // The fixed positioning with flex centering is handled by the CSS classes
    
    console.log('Modal positioned at the center of the screen (not fixed)');
    
    // Initialize Lucide icons if available
    if (window.lucide) {
        lucide.createIcons({
            attrs: {
                class: ["h-5", "w-5"]
            },
            elements: [modal]
        });
    }
    
    console.log('Add credits modal should now be visible');
    return true;
}

// Wait for both components to be initialized
document.addEventListener('DOMContentLoaded', () => {
    console.log('Credit notification fix script loaded');
    
    // Check if components exist
    if (window.creditNotification) {
        console.log('CreditNotification found');
    }
    
    if (window.creditManager) {
        console.log('CreditManager found');
    }
    
    // Make our direct function available globally
    window.showAddCreditsModalDirect = showAddCreditsModalDirect;
    
    // Override the addCreditsBtn click handler to ensure it uses the correct function
    const addCreditsBtn = document.getElementById('addCreditsBtn');
    if (addCreditsBtn) {
        console.log('Add credits button found, attaching event listener');
        
        // Remove existing event listeners (if possible)
        const newAddCreditsBtn = addCreditsBtn.cloneNode(true);
        addCreditsBtn.parentNode.replaceChild(newAddCreditsBtn, addCreditsBtn);
        
        // Add new event listener with the correct function name
        newAddCreditsBtn.addEventListener('click', () => {
            console.log('Add credits button clicked');
            
            // Hide the notification modal first
            if (window.creditNotification) {
                window.creditNotification.hideModal();
            }
            
            // Try multiple approaches to show the add credits modal
            let modalShown = false;
            
            // 1. Try using CreditManager if available
            if (window.creditManager && typeof window.creditManager.showAddCreditsModal === 'function') {
                console.log('Attempting to open modal via CreditManager');
                try {
                    window.creditManager.showAddCreditsModal();
                    modalShown = true;
                } catch (e) {
                    console.error('Error using CreditManager:', e);
                }
            }
            
            // 2. If that didn't work, try our direct function
            if (!modalShown) {
                console.log('Attempting to open modal directly');
                modalShown = showAddCreditsModalDirect();
            }
            
            // 3. If all else fails, redirect to the dashboard
            if (!modalShown) {
                console.log('Fallback: Redirecting to dashboard credits tab');
                window.location.href = '/dashboard?tab=credits';
            }
        });
    } else {
        console.warn('Add credits button not found in the DOM');
    }
    
    // Also check for the admin panel add credits button
    const adminAddCreditsBtn = document.getElementById('adminAddCreditsBtn');
    if (adminAddCreditsBtn) {
        console.log('Admin add credits button found');
    }
});