from mongoengine import Document, DateT<PERSON><PERSON><PERSON>, IntField, Dict<PERSON><PERSON>
from datetime import datetime

class DailyStats(Document):
    """
    Model for storing daily statistics
    """
    date = DateTimeField(required=True, unique=True)
    active_users = IntField(default=0)
    total_messages = IntField(default=0)
    avg_response_time = IntField(default=0)  # in milliseconds
    service_usage = DictField()  # {'chat': 100, 'live': 50, ...}
    model_usage = DictField()  # {'GPT-4 Mini': 80, 'Gemini': 70, ...}
    
    meta = {
        'collection': 'daily_stats',
        'indexes': [
            'date'
        ]
    }
    
    @classmethod
    def update_or_create(cls, date, stats_data):
        """
        Update or create daily stats
        """
        try:
            # Normalize date to start of day
            if isinstance(date, datetime):
                normalized_date = datetime(date.year, date.month, date.day)
            else:
                normalized_date = date
                
            # Try to find existing stats for this date
            stats = cls.objects(date=normalized_date).first()
            
            if stats:
                # Update existing stats
                stats.active_users = stats_data.get('active_users', stats.active_users)
                stats.total_messages = stats_data.get('total_messages', stats.total_messages)
                stats.avg_response_time = stats_data.get('avg_response_time', stats.avg_response_time)
                stats.service_usage = stats_data.get('service_usage', stats.service_usage)
                stats.model_usage = stats_data.get('model_usage', stats.model_usage)
                stats.save()
            else:
                # Create new stats
                stats = cls(
                    date=normalized_date,
                    active_users=stats_data.get('active_users', 0),
                    total_messages=stats_data.get('total_messages', 0),
                    avg_response_time=stats_data.get('avg_response_time', 0),
                    service_usage=stats_data.get('service_usage', {}),
                    model_usage=stats_data.get('model_usage', {})
                )
                stats.save()
                
            return stats
        except Exception as e:
            print(f"Error updating daily stats: {str(e)}")
            return None
            
    @classmethod
    def get_stats_for_period(cls, start_date, end_date):
        """
        Get stats for a specific period
        """
        try:
            return cls.objects(date__gte=start_date, date__lte=end_date).order_by('date')
        except Exception as e:
            print(f"Error getting stats for period: {str(e)}")
            return []
