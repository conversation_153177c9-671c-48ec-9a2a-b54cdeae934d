from flask import render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from . import chat_views
from models.shared_thread import SharedThread
from models.user_activity import UserActivity
from models.restricted_user import RestrictedUser

@chat_views.route('/')
@chat_views.route('/thread/<thread_id>')
@login_required
def chat(thread_id=None):
    # Check if user is restricted from chat service
    if RestrictedUser.is_restricted(current_user.email, 'chat'):
        return render_template('chat.html', thread_id=thread_id, restricted=True, service_name='Chat')

    # Track user activity
    UserActivity.update_activity(current_user, 'chat')
    return render_template('chat.html', thread_id=thread_id)

@chat_views.route('/shared/<share_id>')
@login_required
def chat_shared(share_id):
    # Get the shared thread first
    shared_thread = SharedThread.objects(share_id=share_id).first()

    # Check if user is restricted from chat service
    if RestrictedUser.is_restricted(current_user.email, 'chat'):
        return render_template('chat.html', shared_thread=shared_thread.to_dict() if shared_thread else None, restricted=True, service_name='Chat')

    # Track user activity
    UserActivity.update_activity(current_user, 'chat')

    if not shared_thread:
        return redirect(url_for('chat_views.chat'))
    return render_template('chat.html', shared_thread=shared_thread.to_dict())

