/* Group Members CSS */

/* Group members container in header */
.group-members-container {
    display: none; /* Hide the members container */
    align-items: center;
    margin-left: 10px;
    cursor: pointer;
    position: relative;
    z-index: 9000;
}

/* Individual member avatar in the header */
.group-member-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 2px solid #1e293b;
    background-color: #475569;
    overflow: hidden;
    transition: transform 0.2s ease;
}

/* Create overlapping effect */
.group-member-avatar:not(:first-child) {
    margin-left: -12px;
}

/* Hover effect for the avatars */
.group-members-container:hover .group-member-avatar {
    transform: translateY(-2px);
}

/* Member avatar image */
.group-member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fallback for when no image is available */
.group-member-avatar .initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    color: white;
    font-size: 12px;
    font-weight: 600;
}

/* More members indicator */
.more-members {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 2px solid #1e293b;
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    color: white;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -12px;
}

/* Group members popup - hidden */
.group-members-popup {
    display: none; /* Hide the popup */
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    max-height: 400px;
    overflow-y: auto;
}

/* Show popup when active */
.group-members-popup.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    z-index: 10000; /* Ensure it's above everything */
}

/* Popup header */
.group-members-popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #334155;
}

.group-members-popup-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #e2e8f0;
    margin: 0;
}

.group-members-popup-close {
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.group-members-popup-close:hover {
    color: #e2e8f0;
    background-color: rgba(148, 163, 184, 0.1);
}

/* Members list */
.group-members-list {
    max-height: 300px;
    overflow-y: auto;
}

/* Individual member item */
.group-member-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    margin-bottom: 4px;
}

.group-member-item:hover {
    background-color: rgba(51, 65, 85, 0.5);
}

.group-member-item-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
}

.group-member-item-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.group-member-item-info {
    flex-grow: 1;
    overflow: hidden;
}

.group-member-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #e2e8f0;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.group-member-item-username {
    font-size: 12px;
    color: #94a3b8;
    margin: 0;
}

/* Creator badge */
.creator-badge {
    font-size: 10px;
    background-color: #7c3aed;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

/* Scrollbar styling */
.group-members-list::-webkit-scrollbar {
    width: 4px;
}

.group-members-list::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
}

.group-members-list::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 4px;
}
