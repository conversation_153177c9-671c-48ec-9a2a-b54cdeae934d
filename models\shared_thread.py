from mongoengine import Document, <PERSON><PERSON>ield, ListField, DictField, DateT<PERSON>Field
from datetime import datetime
import shortuuid

class SharedThread(Document):
    share_id = StringField(required=True, unique=True, default=lambda: shortuuid.uuid())
    title = StringField(required=True)
    messages = ListField(DictField(), default=list)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {'collection': 'shared_threads'}
    
    def to_dict(self):
        return {
            'share_id': self.share_id,
            'title': self.title,
            'messages': self.messages,
            'created_at': self.created_at.isoformat()
        }