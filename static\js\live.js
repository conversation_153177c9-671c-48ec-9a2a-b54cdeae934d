// Initialize Lucide icons when the DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initIcons);
} else {
    // If DOMContentLoaded has already fired, run immediately
    setTimeout(initIcons, 0);
}

function initIcons() {
    // Create all icons at once
    if (typeof lucide !== 'undefined') {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    } else {
        // If lucide isn't loaded yet, try again later
        setTimeout(initIcons, 100);
    }
}

class LiveChatInterface {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatContent = document.getElementById('chatContent');
        this.roomList = document.getElementById('roomList');
        this.roomStatus = document.getElementById('roomStatus');
        this.activeRoomName = document.getElementById('activeRoomName');
        this.createRoomBtn = document.getElementById('createRoomBtn');
        this.inviteBtn = document.getElementById('inviteBtn');
        this.inviteLink = document.getElementById('inviteLink');

        // Image handling elements
        this.imageFileInput = document.getElementById('imageFileInput');
        this.imagePreviewContainer = document.getElementById('imagePreviewContainer');
        this.addButton = document.getElementById('addButton');

        this.messages = [];
        this.currentRoomId = null;
        this.currentUserId = window.currentUserId;
        this.currentUsername = window.currentUsername;
        this.currentImages = null;

        // Initialize Socket.IO connection with optimized settings for reliability
        console.log('Initializing Socket.IO connection...');
        this.socket = io(window.location.origin, {
            transports: ['polling', 'websocket'], // Try polling first for better compatibility
            reconnection: true,
            reconnectionAttempts: 10, // More attempts for better reliability
            reconnectionDelay: 1000, // Slightly increased reconnection delay
            reconnectionDelayMax: 5000, // Increased max delay for better stability
            timeout: 20000, // Increased timeout for better reliability
            forceNew: true, // Create a new connection to avoid conflicts
            autoConnect: true, // Connect immediately
            upgrade: true, // Allow transport upgrades
            rememberUpgrade: true, // Remember successful upgrades
            pingInterval: 5000, // Match server ping interval
            pingTimeout: 10000, // Match server ping timeout
            maxHttpBufferSize: 50 * 1024 * 1024 // 50MB buffer for large messages with images
        });
        console.log('Socket.IO connection initialized');
        this.setupSocketEvents();

        this.setupEventListeners();
        this.setupModelSelector();
        this.setupImageHandling();

        // Check if we're in a room based on URL
        const isInRoom = window.location.pathname.includes('/room/');
        
        // Always load rooms data first
        this.loadRooms();

        // Then handle room-specific loading
        if (window.initialRoomData) {
            // If we have initial room data from server, use it to load the room
            this.loadRoom(window.initialRoomData.room_id);
        } else if (!isInRoom) {
            // Only show welcome message if we're not in a room
            this.showWelcomeMessage();
        } else {
            // We're in a room but don't have initialRoomData
            // Extract room ID from URL and load it
            const roomId = window.location.pathname.split('/').pop();
            if (roomId) {
                this.loadRoom(roomId);
            }
        }
    }

    setupEventListeners() {
        // Message sending
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Room creation
        this.createRoomBtn.addEventListener('click', () => {
            this.createNewRoom();
            
            // Close sidebar on mobile when create room button is clicked
            if (window.innerWidth <= 768) {
                document.querySelector('.sidebar').classList.remove('open');
            }
        });

        // Invite functionality - direct copy to clipboard
        this.inviteBtn.addEventListener('click', () => this.copyRoomLink());

        // Close modal buttons
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            document.querySelectorAll('.modal').forEach(modal => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Fullscreen button
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
            }
        });

        // Handle page unload/navigation to properly notify when user leaves
        window.addEventListener('beforeunload', () => {
            if (this.currentRoomId) {
                this.socket.emit('leave_room', { room_id: this.currentRoomId });
            }
        });

        // Handle navigation using browser history
        window.addEventListener('popstate', (event) => {
            if (this.currentRoomId) {
                this.socket.emit('leave_room', { room_id: this.currentRoomId });
                this.currentRoomId = null;
            }

            // If there's a roomId in the state, load that room
            if (event.state && event.state.roomId) {
                this.loadRoom(event.state.roomId, false);
            } else {
                // Otherwise, just load the room list
                this.loadRooms();
            }
        });
    }

    setupModelSelector() {
        const modelSelector = document.getElementById('modelSelector');
        const currentModel = document.getElementById('currentModel');
        const modelSelectorContainer = document.getElementById('modelSelectorContainer');
        const modelOptionCards = document.querySelectorAll('.model-option-card');
        const modelInfoIcons = document.querySelectorAll('.model-info-icon');
        const tooltip = document.getElementById('modelDescriptionTooltip');

        // Set initial selected model
        updateSelectedModelCard(currentModel.textContent);

        // Toggle model selector when clicking on the current model
        modelSelector.addEventListener('click', (e) => {
            e.stopPropagation();
            modelSelectorContainer.classList.toggle('visible');
            // Initialize Lucide icons
            if (window.lucide) {
                window.lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    }
                });
            }
        });

        // Handle info icon clicks
        modelInfoIcons.forEach(infoIcon => {
            infoIcon.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent card selection when clicking info icon

                // Get description from data attribute
                const description = infoIcon.getAttribute('data-description');

                // Set content (moved to the positioning section)

                // Position the tooltip above the info icon
                const card = infoIcon.closest('.model-option-card');

                // Move the tooltip to be a child of the model selector container for proper positioning
                modelSelectorContainer.appendChild(tooltip);

                // Get positions for accurate placement
                const cardRect = card.getBoundingClientRect();
                const containerRect = modelSelectorContainer.getBoundingClientRect();

                // Get the info icon position
                const infoIconRect = infoIcon.getBoundingClientRect();

                // Calculate position relative to the container
                const left = infoIconRect.left - containerRect.left + (infoIconRect.width / 2);
                const top = cardRect.top - containerRect.top - 50; // Position well above the card

                // Set tooltip content first
                tooltip.textContent = description;
                
                // Make tooltip visible to calculate its dimensions
                tooltip.style.visibility = 'visible';
                tooltip.style.display = 'block';
                
                // Get tooltip dimensions
                const tooltipHeight = tooltip.offsetHeight;
                
                // Set position relative to the card
                tooltip.style.position = 'absolute';
                tooltip.style.top = `${cardRect.top - containerRect.top - tooltipHeight - 10}px`;
                tooltip.style.left = `${cardRect.left - containerRect.left + (cardRect.width / 2)}px`;
                tooltip.style.transform = 'translateX(-50%)';
                tooltip.style.bottom = 'auto';

                // Adjust width based on content length
                const textLength = description.length;
                if (textLength > 35) {
                    tooltip.style.width = '300px'; // Wider for longer descriptions
                } else if (textLength > 25) {
                    tooltip.style.width = '250px'; // Medium width for medium descriptions
                } else {
                    tooltip.style.width = '200px'; // Default width for short descriptions
                }

                // Show the tooltip
                tooltip.style.display = 'block';
            });
        });

        // Handle model selection
        modelOptionCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't select model if clicking on info icon
                if (e.target.closest('.model-info-icon')) {
                    return;
                }

                const selectedModel = card.getAttribute('data-model');
                let displayName;

                if (selectedModel === 'gpt-4o-mini') {
                    displayName = 'GPT-4o Mini';
                } else if (selectedModel === 'gemini-2.0-flash') {
                    displayName = 'Gemini';
                } else if (selectedModel === 'qwen-qwq-32b') {
                    displayName = 'Qwen';
                } else if (selectedModel === 'gemma2-9b-it') {
                    displayName = 'Gemma 2';
                } else if (selectedModel === 'llama-3.3-70b-versatile') {
                    displayName = 'Llama 3.3 70B';
                } else if (selectedModel === 'llama-3.1-8b-instant') {
                    displayName = 'Llama 3.1 8B';
                } else if (selectedModel === 'llama3-70b-8192') {
                    displayName = 'Llama 3 70B';
                } else if (selectedModel === 'llama3-8b-8192') {
                    displayName = 'Llama 3 8B';
                }

                // Update the current model display
                currentModel.textContent = displayName;
                currentModel.setAttribute('data-model', selectedModel);

                // Update the selected card in the modal
                updateSelectedModelCard(displayName);

                // Update the icon in the current model display
                const modelIcon = modelSelector.querySelector('.current-model-icon i');
                if (modelIcon) {
                    let iconName;
                    if (selectedModel === 'gpt-4o-mini') {
                        iconName = 'sparkles';
                    } else if (selectedModel === 'gemini-2.0-flash') {
                        iconName = 'zap';
                    } else if (selectedModel === 'qwen-qwq-32b') {
                        iconName = 'cpu';
                    } else if (selectedModel === 'gemma2-9b-it') {
                        iconName = 'brain';
                    } else if (selectedModel === 'llama-3.3-70b-versatile' || selectedModel === 'llama3-70b-8192') {
                        iconName = 'flame';
                    } else if (selectedModel === 'llama-3.1-8b-instant' || selectedModel === 'llama3-8b-8192') {
                        iconName = 'zap';
                    }

                    modelIcon.setAttribute('data-lucide', iconName);
                    // Re-initialize the icon
                    if (window.lucide) {
                        window.lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            elements: [modelIcon.parentElement]
                        });
                    }
                }

                // Close the selector
                modelSelectorContainer.classList.remove('visible');
            });
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Hide model selector if visible
                if (modelSelectorContainer.classList.contains('visible')) {
                    modelSelectorContainer.classList.remove('visible');
                }

                // Hide tooltip
                document.getElementById('modelDescriptionTooltip').style.display = 'none';
            }
        });

        // Close when clicking outside
        document.addEventListener('click', (e) => {
            // Close model selector when clicking outside
            if (modelSelectorContainer.classList.contains('visible') &&
                !modelSelectorContainer.contains(e.target) &&
                !modelSelector.contains(e.target)) {
                modelSelectorContainer.classList.remove('visible');
            }

            // Hide tooltip when clicking outside of info icons
            if (!e.target.closest('.model-info-icon')) {
                document.getElementById('modelDescriptionTooltip').style.display = 'none';
            }
        });

        // Function to update the selected model card
        function updateSelectedModelCard(displayName) {
            let modelName;

            if (displayName === 'GPT-4o Mini') {
                modelName = 'gpt-4o-mini';
            } else if (displayName === 'Gemini') {
                modelName = 'gemini-2.0-flash';
            } else if (displayName === 'Qwen') {
                modelName = 'qwen-qwq-32b';
            } else if (displayName === 'Gemma 2') {
                modelName = 'gemma2-9b-it';
            } else if (displayName === 'Llama 3.3 70B') {
                modelName = 'llama-3.3-70b-versatile';
            } else if (displayName === 'Llama 3.1 8B') {
                modelName = 'llama-3.1-8b-instant';
            } else if (displayName === 'Llama 3 70B') {
                modelName = 'llama3-70b-8192';
            } else if (displayName === 'Llama 3 8B') {
                modelName = 'llama3-8b-8192';
            }

            modelOptionCards.forEach(card => {
                if (card.getAttribute('data-model') === modelName) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }
    }

    setupSocketEvents() {
        // Connection events with enhanced debugging
        this.socket.on('connect', () => {
            console.log('Connected to WebSocket server');
            console.log('Socket ID:', this.socket.id);
            this.showNotification('Connected to live chat server', 'success');
        });

        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            this.showNotification('Connection error: ' + error.message, 'error');
        });

        this.socket.on('connect_timeout', () => {
            console.error('Connection timeout');
            this.showNotification('Connection timeout', 'error');
        });

        this.socket.on('disconnect', (reason) => {
            console.log('Disconnected from WebSocket server. Reason:', reason);
            this.showNotification('Disconnected from server: ' + reason, 'warning');
        });

        this.socket.on('error', (data) => {
            console.error('Socket error:', data.message || data);
            this.showNotification(data.message || 'Socket error occurred', 'error');
        });

        this.socket.on('reconnect', (attemptNumber) => {
            console.log('Reconnected to server after', attemptNumber, 'attempts');
            this.showNotification('Reconnected to server', 'success');
        });

        this.socket.on('reconnect_attempt', (attemptNumber) => {
            console.log('Attempting to reconnect:', attemptNumber);
        });

        this.socket.on('reconnect_error', (error) => {
            console.error('Reconnection error:', error);
        });

        this.socket.on('reconnect_failed', () => {
            console.error('Failed to reconnect');
            this.showNotification('Failed to reconnect to server', 'error');
        });

        // Room events
        this.socket.on('user_joined', (data) => {
            console.log(`User ${data.username} joined the room`);
            this.showNotification(`${data.username} joined the room`);
            this.refreshRoom(); // Refresh to update room status
        });

        this.socket.on('user_left', (data) => {
            console.log(`User ${data.username} left the room`);
            this.showNotification(`${data.username} left the room`);

            // Update room status immediately
            this.refreshRoom();

            // Update the UI to show the user has left
            // We'll fetch the latest room data to get the current state
            this.updateRoomAfterUserLeft(data.user_id, data.username);
        });

        // Message events
        this.socket.on('new_message', (message) => {
            // Only display if it's not from the current user
            if (message.user_id !== this.currentUserId) {
                // Add to messages array
                this.messages.push(message);

                // Display the message
                this.displayMessage(message);

                // Apply Prism highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }

                // Scroll to bottom
                this.chatContent.scrollTop = this.chatContent.scrollHeight;
            }
        });

        // AI response events
        this.socket.on('ai_typing', (data) => {
            // Only show typing indicator if it's responding to the other user
            if (data.user_id !== this.currentUserId) {
                // Create AI response placeholder with typing indicator
                const aiMessageDiv = document.createElement('div');
                aiMessageDiv.className = 'message ai-to-other-user';
                aiMessageDiv.setAttribute('data-temp-ai', 'true');
                aiMessageDiv.setAttribute('data-for-user', data.user_id);

                const aiUserInfo = document.createElement('div');
                aiUserInfo.className = 'user-info';
                aiUserInfo.innerHTML = `
                    <div class="username">AI Assistant</div>
                    <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                `;

                const aiContentDiv = document.createElement('div');
                aiContentDiv.className = 'message-content';

                // Add typing indicator
                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'typing-indicator';
                typingIndicator.innerHTML = '<span></span><span></span><span></span>';
                aiContentDiv.appendChild(typingIndicator);

                aiMessageDiv.appendChild(aiUserInfo);
                aiMessageDiv.appendChild(aiContentDiv);

                this.chatContent.appendChild(aiMessageDiv);
                this.chatContent.scrollTop = this.chatContent.scrollHeight;

                // Initialize an object to store accumulated text for each user
                if (!this.otherUserResponses) {
                    this.otherUserResponses = {};
                }
                this.otherUserResponses[data.user_id] = '';
            }
        });

        this.socket.on('ai_response_chunk', (data) => {
            // Only process if it's responding to the other user
            if (data.user_id !== this.currentUserId) {
                console.log('Received chunk for other user:', data.text);

                // Find the temporary AI message element for this specific user
                const tempAiMessage = document.querySelector(`[data-temp-ai="true"][data-for-user="${data.user_id}"]`);

                if (tempAiMessage) {
                    const contentDiv = tempAiMessage.querySelector('.message-content');

                    // Remove typing indicator if present
                    const typingIndicator = contentDiv.querySelector('.typing-indicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }

                    // Accumulate the text for this user
                    this.otherUserResponses[data.user_id] += data.text;

                    // Update the content
                    contentDiv.innerHTML = this.formatMessage(this.otherUserResponses[data.user_id]);

                    // Apply Prism highlighting
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightAll();
                    }

                    // Scroll to bottom
                    this.chatContent.scrollTop = this.chatContent.scrollHeight;
                }
            }
        });

        this.socket.on('ai_response_complete', (data) => {
            // Only process if it's responding to the other user
            if (data.user_id !== this.currentUserId) {
                // Find and remove the temporary AI message element
                const tempAiMessage = document.querySelector(`[data-temp-ai="true"][data-for-user="${data.user_id}"]`);
                if (tempAiMessage) {
                    tempAiMessage.remove();
                }

                // Clean up the accumulated text
                if (this.otherUserResponses && this.otherUserResponses[data.user_id]) {
                    delete this.otherUserResponses[data.user_id];
                }

                // Add the complete message to our messages array
                this.messages.push(data.message);

                // Display the complete message
                this.displayMessage(data.message);

                // Apply Prism highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }

                // Scroll to bottom
                this.chatContent.scrollTop = this.chatContent.scrollHeight;
            }
        });
    }

    async loadRooms() {
        try {
            console.time('loadRooms');
            
            // Create a promise for fetching rooms
            let roomsPromise;
            
            // Use a cached promise if available (similar to threadsDataPromise in chat.js)
            if (window.roomsDataPromise) {
                roomsPromise = window.roomsDataPromise;
                window.roomsDataPromise = null;
            } else {
                roomsPromise = fetch('/api/live/load/rooms')
                    .then(response => response.json());
            }
            
            // Use Promise.race to implement a timeout
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Fetch timeout')), 5000)
            );
            
            // Wait for the data with timeout
            const data = await Promise.race([roomsPromise, timeoutPromise]);
            
            // Rooms are already sorted by the server, but we'll sort again just to be sure
            const rooms = (data.rooms || []);
            console.timeEnd('loadRooms');

            this.roomList.innerHTML = '';

            // Only show welcome message if we're not in a room
            // We check the URL to determine if we're in a room
            const isInRoom = window.location.pathname.includes('/room/');

            if (!isInRoom && !this.currentRoomId) {
                this.showWelcomeMessage();
            }

            if (rooms.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-message';
                emptyMessage.textContent = 'No rooms yet. Create one to get started!';
                this.roomList.appendChild(emptyMessage);
                return;
            }

            rooms.forEach(room => {
                const roomItem = document.createElement('div');
                roomItem.className = `room-item ${this.currentRoomId === room.room_id ? 'active' : ''}`;
                roomItem.setAttribute('data-room-id', room.room_id);

                // Determine participant count and status
                let participantText = 'You';
                if (room.creator_id === this.currentUserId) {
                    participantText += ' (Creator)';
                    if (room.participant_id) {
                        participantText += ` and ${room.participant_name}`;
                    }
                } else {
                    participantText += ` and ${room.creator_name} (Creator)`;
                }

                roomItem.innerHTML = `
                    <div class="room-icon">
                        <i data-lucide="message-square"></i>
                    </div>
                    <div class="room-details">
                        <div class="room-name"><strong>${room.title}</strong></div>
                        <div class="room-participants">${participantText}</div>
                        <div class="room-last-updated" style="font-size: 0.75rem; color: #888; margin-top: 2px;">
                            ${new Date(room.updated_at).toLocaleString()}
                        </div>
                    </div>
                `;

                roomItem.addEventListener('click', () => {
                    this.loadRoom(room.room_id);
                    
                    // Close sidebar on mobile when a room is selected
                    if (window.innerWidth <= 768) {
                        document.querySelector('.sidebar').classList.remove('open');
                    }
                });

                this.roomList.appendChild(roomItem);
            });

            // Reinitialize icons for the new elements
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    }
                });
            }

        } catch (error) {
            console.error('Failed to load rooms:', error);
            this.showNotification('Failed to load rooms', 'error');
        }
    }

    async loadRoom(roomId, pushState = true) {
        try {
            console.time('loadRoom');
            // If we're already in a room, leave it first
            if (this.currentRoomId) {
                this.socket.emit('leave_room', { room_id: this.currentRoomId });
            }

            // Remove welcome message if it exists
            const welcomeMessage = this.chatContent.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const response = await fetch(`/api/live/room/${roomId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const room = await response.json();
            console.timeEnd('loadRoom');

            // Update current room ID and messages
            this.currentRoomId = room.room_id;
            this.messages = room.messages;

            // Update room name in header
            this.activeRoomName.textContent = room.title || `Room ${room.room_id.substring(0, 8)}`;

            // Update room status
            this.updateRoomStatus(room);

            // Clear the chat content and display all messages
            this.chatContent.innerHTML = '';

            // Display all messages from the room
            // No need to check for duplicates here since we're clearing the chat content
            if (this.messages && this.messages.length > 0) {
                this.messages.forEach(msg => {
                    // Make sure we're handling the new message format correctly
                    const messageData = {
                        content: msg.content,
                        role: msg.role,
                        user_id: msg.user_id,
                        username: msg.username,
                        timestamp: msg.timestamp,
                        responding_to: msg.responding_to
                    };
                    this.displayMessage(messageData);
                });
            } else {
                // Show empty room message
                this.roomStatus.style.display = 'flex';
                this.roomStatus.querySelector('.status-message').textContent = 'No messages yet. Start the conversation!';
            }

            // Apply Prism highlighting to all code blocks after inserting them
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }

            // Update room list to show active room
            this.loadRooms();

            // Update URL and browser history
            if (pushState) {
                const newUrl = `/live/room/${roomId}`;
                window.history.pushState({ roomId }, '', newUrl);
            }

            // Enable/disable invite button based on whether user is creator
            this.inviteBtn.disabled = room.creator_id !== this.currentUserId;
            this.inviteBtn.style.opacity = room.creator_id === this.currentUserId ? '1' : '0.5';

            // Join the Socket.IO room
            this.socket.emit('join_room', { room_id: roomId });
            
            // Reload the room list to update the order
            setTimeout(() => this.loadRooms(), 500);

        } catch (error) {
            console.error('Failed to load room:', error);
            this.showNotification('Failed to load room', 'error');
        }
    }

    async refreshRoom() {
        try {
            const response = await fetch(`/api/live/room/${this.currentRoomId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const room = await response.json();

            // Filter out temporary messages from our current messages
            const realMessages = this.messages.filter(msg => !msg._temp);

            // If there are new messages from the server
            if (room.messages.length > realMessages.length) {
                // Find truly new messages by comparing with our real messages
                // This prevents duplicating messages we've already displayed
                const newServerMessages = room.messages.slice(realMessages.length);

                // Replace our messages array with the server's messages
                this.messages = room.messages;

                // Display only the new messages
                newServerMessages.forEach(msg => {
                    // Check if we already have this message displayed (avoid duplicates)
                    const isDuplicate = Array.from(this.chatContent.children).some(el => {
                        // Try to match based on content, role and timestamp if available
                        const contentEl = el.querySelector('.message-content');
                        const userInfoEl = el.querySelector('.user-info .username');
                        return contentEl &&
                               contentEl.textContent.includes(msg.content) &&
                               userInfoEl &&
                               ((msg.role === 'user' && userInfoEl.textContent.includes(msg.username)) ||
                                (msg.role === 'assistant' && userInfoEl.textContent.includes('AI Assistant')));
                    });

                    if (!isDuplicate) {
                        this.displayMessage(msg);
                    }
                });

                // Apply Prism highlighting to all code blocks after inserting them
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }

                // Scroll to bottom
                this.chatContent.scrollTop = this.chatContent.scrollHeight;
            }

            // Update room status
            this.updateRoomStatus(room);

        } catch (error) {
            console.error('Failed to refresh room:', error);
        }
    }

    updateRoomStatus(room) {
        const isCreator = room.creator_id === this.currentUserId;
        const hasParticipant = !!room.participant_id;

        if (isCreator) {
            if (hasParticipant) {
                this.roomStatus.innerHTML = `
                    <div class="status-message">
                        <span class="participant-indicator creator">Creator</span>
                        You are chatting with ${room.participant_name}
                    </div>
                `;
            } else {
                this.roomStatus.innerHTML = `
                    <div class="status-message">
                        <span class="participant-indicator creator">Creator</span>
                        Waiting for someone to join. Share the invite link to get started!
                    </div>
                `;
            }
        } else {
            this.roomStatus.innerHTML = `
                <div class="status-message">
                    <span class="participant-indicator participant">Participant</span>
                    You are chatting with ${room.creator_name}
                </div>
            `;
        }
    }

    async updateRoomAfterUserLeft(userId, username) {
        if (!this.currentRoomId) return;

        try {
            // Fetch the latest room data
            const response = await fetch(`/api/live/room/${this.currentRoomId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const room = await response.json();

            // Check if the user who left was the creator or participant
            if (userId === room.creator_id) {
                // Creator left, update UI accordingly
                this.roomStatus.innerHTML = `
                    <div class="status-message">
                        <span class="participant-indicator participant">Participant</span>
                        The creator (${username}) has left the room
                    </div>
                `;
            } else if (userId === room.participant_id || (!room.participant_id && this.currentUserId === room.creator_id)) {
                // Participant left or was removed, update UI accordingly
                this.roomStatus.innerHTML = `
                    <div class="status-message">
                        <span class="participant-indicator creator">Creator</span>
                        The participant (${username}) has left the room
                    </div>
                `;
            }
        } catch (error) {
            console.error('Failed to update room after user left:', error);
        }
    }

    async createNewRoom() {
        try {
            const response = await fetch('/api/live/room', {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const room = await response.json();

            // Update URL to /live for new rooms
            window.history.pushState({}, '', '/live');

            this.loadRoom(room.room_id);
            this.loadRooms();

            // Copy room link automatically for new rooms
            this.copyRoomLink();

        } catch (error) {
            console.error('Failed to create room:', error);
            this.showNotification('Failed to create room', 'error');
        }
    }

    async copyRoomLink() {
        if (!this.currentRoomId) {
            this.showNotification('Create a room first', 'error');
            return;
        }

        // Generate invite link
        const inviteUrl = `${window.location.origin}/live/room/${this.currentRoomId}`;
        this.inviteLink.value = inviteUrl;

        try {
            // Try to use the modern Clipboard API
            await navigator.clipboard.writeText(inviteUrl);
            console.log('Successfully copied using Clipboard API');
        } catch (err) {
            console.warn('Clipboard API failed, trying fallback:', err);
            // Use the fallback method which has multiple backup strategies
            await this.fallbackCopy(inviteUrl);
        }

        // Visual feedback
        this.inviteBtn.classList.add('copied');

        // Update text and icon if they exist
        const spanElement = this.inviteBtn.querySelector('span');
        const iconElement = this.inviteBtn.querySelector('i');

        if (spanElement) {
            spanElement.textContent = 'Copied!';
        }

        if (iconElement) {
            iconElement.setAttribute('data-lucide', 'check');

            // Reinitialize icon
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    }
                });
            }
        }

        // Reset button after 2 seconds
        setTimeout(() => {
            this.inviteBtn.classList.remove('copied');

            if (spanElement) {
                spanElement.textContent = 'Copy Link';
            }

            if (iconElement) {
                iconElement.setAttribute('data-lucide', 'link');

                // Reinitialize icon
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        }
                    });
                }
            }
        }, 2000);

        this.showNotification('Invite link copied to clipboard');
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message && !this.currentImages) return;

        if (!this.currentRoomId) {
            this.showNotification('Join or create a room first', 'error');
            return;
        }

        // Clear input
        this.messageInput.value = '';

        // Get selected model
        let selectedModel = document.getElementById('currentModel').getAttribute('data-model') || 'gpt-4o-mini';

        // If images are present, force Gemini model
        const hasImages = !!this.currentImages;
        const imageData = this.currentImages; // Store a reference to use in the message

        if (hasImages) {
            selectedModel = 'gemini-2.0-flash';
            // Update the UI to show Gemini is selected
            document.getElementById('currentModel').textContent = 'Gemini';
            document.getElementById('currentModel').setAttribute('data-model', 'gemini-2.0-flash');

            // Clear the image preview
            this.imagePreviewContainer.innerHTML = '';
            this.imagePreviewContainer.classList.add('hidden');
        }

        // Create a user message object
        const userMessage = {
            role: 'user',
            user_id: this.currentUserId,
            username: this.currentUsername,
            content: message,
            timestamp: new Date().toISOString()
        };

        // Add images to the message if present
        if (hasImages && imageData) {
            userMessage.images = imageData;
        }

        // Add to our local messages array
        this.messages.push(userMessage);

        // Create and display the message element
        const { messageDiv } = this.createMessageElement(message, true);

        // Add image previews to user message if present
        if (hasImages && imageData) {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'message-images';
            imageData.forEach(img => {
                const imgElement = document.createElement('img');
                imgElement.src = `data:${img.mime_type};base64,${img.data}`;
                imgElement.className = 'message-image-preview';
                imageContainer.appendChild(imgElement);
            });
            messageDiv.appendChild(imageContainer);
        }

        this.chatContent.appendChild(messageDiv);

        // Scroll to bottom
        this.chatContent.scrollTop = this.chatContent.scrollHeight;

        // Create AI response placeholder with typing indicator
        const aiMessageDiv = document.createElement('div');
        aiMessageDiv.className = 'message ai-to-current-user';
        aiMessageDiv.setAttribute('data-temp-ai', 'true');
        aiMessageDiv.setAttribute('data-for-user', this.currentUserId);

        const aiUserInfo = document.createElement('div');
        aiUserInfo.className = 'user-info';
        aiUserInfo.innerHTML = `
            <div class="username">AI Assistant</div>
            <div class="timestamp">${new Date().toLocaleTimeString()}</div>
        `;

        const aiContentDiv = document.createElement('div');
        aiContentDiv.className = 'message-content';

        // Add typing indicator
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = '<span></span><span></span><span></span>';
        aiContentDiv.appendChild(typingIndicator);

        aiMessageDiv.appendChild(aiUserInfo);
        aiMessageDiv.appendChild(aiContentDiv);

        this.chatContent.appendChild(aiMessageDiv);
        this.chatContent.scrollTop = this.chatContent.scrollHeight;

        // Send the message via WebSocket with high priority
        this.socket.volatile.emit('send_message', {
            message,
            room_id: this.currentRoomId,
            model: selectedModel,
            images: this.currentImages || []
        });

        // Clear the current images after sending
        this.currentImages = null;
        
        // Reload the room list to update the order
        setTimeout(() => this.loadRooms(), 500);

        // We'll use a variable to accumulate the AI response text
        let accumulatedText = '';

        // Create a one-time event handler for this specific message
        const chunkHandler = (data) => {
            // Only process if it's responding to the current user
            if (data.user_id === this.currentUserId) {
                console.log('Received chunk:', data.text);

                // Find the temporary AI message element for this specific user
                const tempAiMessage = document.querySelector(`[data-temp-ai="true"][data-for-user="${data.user_id}"]`);

                if (tempAiMessage) {
                    const contentDiv = tempAiMessage.querySelector('.message-content');

                    // Remove typing indicator if present
                    const typingIndicator = contentDiv.querySelector('.typing-indicator');
                    if (typingIndicator) {
                        typingIndicator.remove();
                    }

                    // Accumulate the text
                    accumulatedText += data.text;

                    // Check if we're starting a thinking block
                    const thinkStartRegex = /<think>/;
                    const thinkEndRegex = /<\/think>/;

                    // If we detect a new thinking block starting
                    if (thinkStartRegex.test(data.text) && !contentDiv.querySelector('.thinking-container')) {
                        // Create a temporary thinking container that will be updated as more content arrives
                        const tempThinkingContainer = document.createElement('div');
                        tempThinkingContainer.className = 'thinking-container';

                        tempThinkingContainer.innerHTML = `
                            <div class="thinking-header" onclick="toggleThinking(this.parentElement)">
                                <div class="thinking-header-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
                                        <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-2.04Z"></path>
                                        <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-2.04Z"></path>
                                    </svg>
                                </div>
                                <div class="thinking-header-title">Thinking</div>
                                <div class="thinking-toggle">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>
                            <div class="thinking-content"></div>
                        `;

                        // Insert at the beginning of the content
                        if (contentDiv.firstChild) {
                            contentDiv.insertBefore(tempThinkingContainer, contentDiv.firstChild);
                        } else {
                            contentDiv.appendChild(tempThinkingContainer);
                        }

                        // No timer needed
                    }

                    // If we have a thinking container, update its content
                    const thinkingContainer = contentDiv.querySelector('.thinking-container');
                    if (thinkingContainer) {
                        const thinkingContent = thinkingContainer.querySelector('.thinking-content');
                        const thinkingHeaderTitle = thinkingContainer.querySelector('.thinking-header-title');

                        // Extract thinking content from accumulated text
                        const fullThinkRegex = /<think>([\s\S]*?)(?:<\/think>|$)/;
                        const fullThinkMatch = accumulatedText.match(fullThinkRegex);

                        if (fullThinkMatch && fullThinkMatch[1]) {
                            // Update thinking content
                            const extractedThinking = fullThinkMatch[1].trim();
                            thinkingContent.textContent = extractedThinking;

                            // If thinking is complete, update title
                            if (thinkEndRegex.test(accumulatedText)) {
                                // Update title
                                thinkingHeaderTitle.textContent = 'Thinking Done';
                            }
                        }
                    }

                    // Format the message for display
                    contentDiv.innerHTML = this.formatMessage(accumulatedText);

                    // Apply Prism highlighting
                    if (typeof Prism !== 'undefined') {
                        Prism.highlightAll();
                    }

                    // Scroll to bottom
                    this.chatContent.scrollTop = this.chatContent.scrollHeight;
                }
            }
        };

        // Add the event listener
        this.socket.on('ai_response_chunk', chunkHandler);

        // Handle complete AI response
        this.socket.once('ai_response_complete', (data) => {
            // Only process if it's responding to the current user
            if (data.user_id === this.currentUserId) {
                // Remove the chunk handler to avoid memory leaks
                this.socket.off('ai_response_chunk', chunkHandler);

                // Find and remove the temporary AI message element
                const tempAiMessage = document.querySelector(`[data-temp-ai="true"][data-for-user="${data.user_id}"]`);
                if (tempAiMessage) {
                    tempAiMessage.remove();
                }

                // Add the complete message to our messages array
                this.messages.push(data.message);

                // Display the complete message
                this.displayMessage(data.message);

                // Apply Prism highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }

                // Scroll to bottom
                this.chatContent.scrollTop = this.chatContent.scrollHeight;
                
                // Reload the room list to update the order
                setTimeout(() => this.loadRooms(), 500);
            }
        });
    }

    createMessageElement(content, isCurrentUser = false, username = null, timestamp = null) {
        const messageDiv = document.createElement('div');

        if (isCurrentUser) {
            messageDiv.className = 'message current-user';
        } else {
            messageDiv.className = 'message other-user';
        }

        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';

        userInfo.innerHTML = `
            <div class="username">${username || (isCurrentUser ? this.currentUsername : 'Other User')}</div>
            <div class="timestamp">${timestamp || new Date().toLocaleTimeString()}</div>
        `;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = this.formatMessage(content);

        messageDiv.appendChild(userInfo);
        messageDiv.appendChild(contentDiv);

        return { messageDiv, contentDiv };
    }

    displayMessage(message) {
        const isCurrentUser = message.role === 'user' && message.user_id === this.currentUserId;
        const isOtherUser = message.role === 'user' && message.user_id !== this.currentUserId;
        const isAiToCurrentUser = message.role === 'assistant' && message.responding_to === this.currentUserId;
        const isAiToOtherUser = message.role === 'assistant' && message.responding_to !== this.currentUserId;

        const messageDiv = document.createElement('div');

        if (isCurrentUser) {
            messageDiv.className = 'message current-user';
        } else if (isOtherUser) {
            messageDiv.className = 'message other-user';
        } else if (isAiToCurrentUser) {
            messageDiv.className = 'message ai-to-current-user';
        } else if (isAiToOtherUser) {
            messageDiv.className = 'message ai-to-other-user';
        }

        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';

        if (message.role === 'user') {
            userInfo.innerHTML = `
                <div class="username">${message.username || 'User'}</div>
                <div class="timestamp">${new Date(message.timestamp).toLocaleTimeString()}</div>
            `;
        } else {
            userInfo.innerHTML = `
                <div class="username">AI Assistant</div>
                <div class="timestamp">${new Date(message.timestamp).toLocaleTimeString()}</div>
            `;
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = this.formatMessage(message.content);

        messageDiv.appendChild(userInfo);
        messageDiv.appendChild(contentDiv);

        // Add images if present in the message
        if (message.images && message.images.length > 0 && message.role === 'user') {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'message-images';
            message.images.forEach(img => {
                const imgElement = document.createElement('img');
                imgElement.src = `data:${img.mime_type};base64,${img.data}`;
                imgElement.className = 'message-image-preview';
                imageContainer.appendChild(imgElement);
            });
            messageDiv.appendChild(imageContainer);
        }

        this.chatContent.appendChild(messageDiv);

        // Scroll to bottom
        this.chatContent.scrollTop = this.chatContent.scrollHeight;
    }

    setupImageHandling() {
        // Add button on the left side of the input field
        if (this.addButton) {
            this.addButton.addEventListener('click', () => {
                this.imageFileInput.click();
            });
        }

        this.imageFileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            if (files.length > 2) {
                alert('Maximum 2 images allowed');
                return;
            }

            this.handleImageFiles(files);
        });
    }

    // Helper method to show welcome message
    showWelcomeMessage() {
        this.chatContent.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">
                <i data-lucide="message-square"></i>
            </div>
            <h3>Welcome to KevkoAI Live Chat</h3>
            <p>Create a new room or select an existing one to start collaborating in real-time.</p>
            <div class="welcome-features">
                <div class="welcome-feature">
                    <i data-lucide="users"></i>
                    <span>Collaborate with others</span>
                </div>
                <div class="welcome-feature">
                    <i data-lucide="sparkles"></i>
                    <span>Powered by AI models</span>
                </div>
                <div class="welcome-feature">
                    <i data-lucide="image"></i>
                    <span>Share images</span>
                </div>
            </div>
        </div>`;

        // Initialize icons in the welcome message
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                root: this.chatContent
            });
        }

        // Reset room name in header
        this.activeRoomName.textContent = 'Live Chat';

        // Hide room status
        this.roomStatus.style.display = 'none';
    }

    async handleImageFiles(files) {
        this.imagePreviewContainer.innerHTML = '';
        this.imagePreviewContainer.classList.remove('hidden');

        // Add a global remove button at the top right of the image preview container
        const globalRemoveBtn = document.createElement('button');
        globalRemoveBtn.className = 'global-remove-image-btn';
        globalRemoveBtn.setAttribute('aria-label', 'Remove all images');
        globalRemoveBtn.innerHTML = '<i data-lucide="x"></i>';
        globalRemoveBtn.onclick = () => {
            this.imagePreviewContainer.innerHTML = '';
            this.imagePreviewContainer.classList.add('hidden');
            this.currentImages = null;
        };

        // Initialize the X icon immediately
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                elements: [globalRemoveBtn]
            });
        }

        this.imagePreviewContainer.appendChild(globalRemoveBtn);

        const imagePromises = files.map(file => {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'relative';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'image-preview';

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-image-btn';
                    removeBtn.setAttribute('aria-label', 'Remove image');
                    removeBtn.innerHTML = '<i data-lucide="x"></i>';
                    removeBtn.onclick = () => {
                        previewDiv.remove();
                        if (this.imagePreviewContainer.children.length <= 1) { // Only the global remove button left
                            this.imagePreviewContainer.innerHTML = '';
                            this.imagePreviewContainer.classList.add('hidden');
                            this.currentImages = null;
                        }
                    };

                    previewDiv.appendChild(img);
                    previewDiv.appendChild(removeBtn);
                    this.imagePreviewContainer.appendChild(previewDiv);

                    // Initialize the X icon immediately for this specific button
                    if (window.lucide) {
                        window.lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            elements: [removeBtn]
                        });
                    }

                    resolve({
                        data: e.target.result.split(',')[1],
                        mime_type: file.type
                    });
                };
                reader.readAsDataURL(file);
            });
        });

        // Store the image data for sending
        this.currentImages = await Promise.all(imagePromises);

        // Automatically switch to Gemini model
        document.getElementById('currentModel').textContent = 'Gemini';
        document.getElementById('currentModel').setAttribute('data-model', 'gemini-2.0-flash');

        // Update the icon in the current model display
        const modelSelector = document.getElementById('modelSelector');
        const modelIcon = modelSelector.querySelector('.current-model-icon i');
        if (modelIcon) {
            modelIcon.setAttribute('data-lucide', 'zap');
            // Re-initialize the icon
            if (window.lucide) {
                window.lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    elements: [modelIcon.parentElement]
                });
            }
        }

        // Update the selected card in the modal if it's visible
        const modelOptionCards = document.querySelectorAll('.model-option-card');
        modelOptionCards.forEach(card => {
            if (card.getAttribute('data-model') === 'gemini-2.0-flash') {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    }

    formatMessage(content) {
        if (!content) return '';

        // First, extract and process thinking blocks
        let processedContent = content;
        let thinkingContent = null;

        // Check for thinking blocks
        const thinkRegex = /<think>([\s\S]*?)<\/think>/;
        const thinkMatch = content.match(thinkRegex);

        if (thinkMatch && thinkMatch[1]) {
            // Extract thinking content
            thinkingContent = thinkMatch[1].trim();

            // Remove thinking block from the text
            processedContent = content.replace(thinkRegex, '').trim();
        }

        // Use marked.js to convert markdown to HTML
        if (typeof marked !== 'undefined') {
            // Configure marked options
            marked.setOptions({
                breaks: true,
                gfm: true,
                headerIds: false,
                highlight: function(code, lang) {
                    if (Prism && Prism.languages[lang]) {
                        return Prism.highlight(code, Prism.languages[lang], lang);
                    }
                    return code;
                }
            });

            // Process with marked
            let html = marked.parse(processedContent);

            // Wrap code blocks with our custom container
            html = html.replace(/<pre><code class="language-([^"]+)">([^<]+)<\/code><\/pre>/g,
                '<div class="code-block-container"><div class="code-block-header"><div class="code-block-title">$1</div></div><pre class="code-block language-$1"><code class="language-$1">$2</code></pre></div>');

            // Add thinking container if thinking content exists
            if (thinkingContent) {
                const thinkingContainer = `
                    <div class="thinking-container">
                        <div class="thinking-header" onclick="toggleThinking(this.parentElement)">
                            <div class="thinking-header-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
                                    <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-2.04Z"></path>
                                    <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-2.04Z"></path>
                                </svg>
                            </div>
                            <div class="thinking-header-title">Thinking Done</div>
                            <div class="thinking-toggle">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </div>
                        </div>
                        <div class="thinking-content">${thinkingContent}</div>
                    </div>
                `;
                html = thinkingContainer + html;
            }

            return html;
        }

        // Fallback if marked is not available
        let result = processedContent
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;')
            .replace(/\n/g, '<br>');

        // Add thinking container if thinking content exists (even in fallback mode)
        if (thinkingContent) {
            const escapedThinking = thinkingContent
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;')
                .replace(/\n/g, '<br>');

            const thinkingContainer = `
                <div class="thinking-container">
                    <div class="thinking-header" onclick="toggleThinking(this.parentElement)">
                        <div class="thinking-header-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
                                <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-2.04Z"></path>
                                <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-2.04Z"></path>
                            </svg>
                        </div>
                        <div class="thinking-header-title">Thinking Done</div>
                        <div class="thinking-toggle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                    </div>
                    <div class="thinking-content">${escapedThinking}</div>
                </div>
            `;
            result = thinkingContainer + result;
        }

        return result;
    }

    fallbackCopy(text) {
        // Make sure the input is visible and selected
        this.inviteLink.style.position = 'fixed';
        this.inviteLink.style.left = '0';
        this.inviteLink.style.top = '0';
        this.inviteLink.style.opacity = '0';
        this.inviteLink.style.height = 'auto';
        this.inviteLink.style.width = 'auto';
        this.inviteLink.style.zIndex = '100';

        // Set the value and select it
        this.inviteLink.value = text;
        this.inviteLink.select();
        this.inviteLink.setSelectionRange(0, 99999); // For mobile devices

        // Execute copy command
        const successful = document.execCommand('copy');

        // Log result and hide the input again
        if (successful) {
            console.log('Successfully copied using fallback method');
        } else {
            console.error('Failed to copy using fallback method');
        }

        // Reset the input styling
        this.inviteLink.style.position = 'absolute';
        this.inviteLink.style.left = '-9999px';
        this.inviteLink.style.top = 'auto';
        this.inviteLink.style.opacity = '0';
        this.inviteLink.style.height = '0';
        this.inviteLink.style.width = '0';
        this.inviteLink.style.zIndex = '-1';

        // Remove focus
        this.inviteLink.blur();
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}



// Function to toggle thinking container expansion
function toggleThinking(container) {
    const content = container.querySelector('.thinking-content');
    const toggle = container.querySelector('.thinking-toggle');

    // Toggle expanded class
    content.classList.toggle('expanded');
    toggle.classList.toggle('expanded');

    // Set appropriate height
    if (content.classList.contains('expanded')) {
        // Get the scroll height to determine the full height of the content
        const scrollHeight = content.scrollHeight;
        content.style.maxHeight = scrollHeight + 'px';

        // Add a small delay to ensure smooth animation
        setTimeout(() => {
            // Recalculate in case content changes (like code formatting)
            content.style.maxHeight = content.scrollHeight + 'px';
        }, 50);
    } else {
        content.style.maxHeight = '0';
    }

    // Initialize any Lucide icons that might be in the content
    if (typeof lucide !== 'undefined' && content.classList.contains('expanded')) {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    }

    // Apply syntax highlighting to code blocks if Prism is available
    if (typeof Prism !== 'undefined' && content.classList.contains('expanded')) {
        Prism.highlightAllUnder(content);
    }
}

// Initialize live chat interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Lucide icons first to ensure they're available
    if (typeof lucide !== 'undefined') {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    }

    // Initialize live chat interface
    window.liveChatInterface = new LiveChatInterface();

    // Setup mobile sidebar - defined outside to avoid duplicate event listeners
    window.setupMobileSidebar = () => {
        const sidebar = document.querySelector('.sidebar');
        const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
        const closeSidebarBtn = document.getElementById('closeSidebarBtn');
        // Overlay removed

        // Function to open sidebar
        function openSidebar() {
            sidebar.classList.add('open');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when sidebar is open
        }

        // Function to close sidebar
        function closeSidebar() {
            sidebar.classList.remove('open');
            document.body.style.overflow = ''; // Restore scrolling
        }

        // Toggle sidebar function
        function toggleSidebar(e) {
            e.stopPropagation(); // Prevent event bubbling
            if (sidebar.classList.contains('open')) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        if (sidebarToggleBtn) {
            // Remove any existing event listeners and add new one
            sidebarToggleBtn.addEventListener('click', toggleSidebar);
        }

        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', closeSidebar);
        }

        // Overlay click handler removed

        // Close sidebar when clicking outside on mobile (fallback)
        document.addEventListener('click', (e) => {
            const isMobile = window.innerWidth <= 768;
            const toggleBtn = document.getElementById('sidebarToggleBtn');
            
            // Check if click is outside sidebar and not on the toggle button
            if (isMobile && 
                sidebar.classList.contains('open') && 
                !sidebar.contains(e.target) && 
                toggleBtn && !toggleBtn.contains(e.target)) {
                closeSidebar();
            }
        });

        // Close sidebar when room is selected on mobile
        const roomList = document.getElementById('roomList');
        if (roomList) {
            roomList.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 && e.target.closest('.room-item')) {
                    closeSidebar();
                }
            });
        }
    };

    window.setupMobileSidebar();
    
    // Add window resize handler to ensure sidebar state is correct
    window.addEventListener('resize', () => {
        const sidebar = document.querySelector('.sidebar');
        // Overlay removed
        
        // If window is resized to desktop view, reset sidebar state
        if (window.innerWidth > 768 && sidebar.classList.contains('open')) {
            sidebar.classList.remove('open');
            document.body.style.overflow = '';
        }
    });
});
