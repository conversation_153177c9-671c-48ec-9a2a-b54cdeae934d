from flask import jsonify, request
from flask_login import login_required, current_user
import logging
from . import profile_api
from .profile_utils import (
    collect_user_data, 
    send_data_export_email, 
    generate_verification_code, 
    send_verification_code, 
    get_stored_verification_code, 
    schedule_data_deletion
)

@profile_api.route('/request-data-export', methods=['POST'])
@login_required
def request_data_export():
    """Request a data export for the current user"""
    try:
        # Collect user data
        user_data = collect_user_data(current_user)

        # Send data to user's email
        success = send_data_export_email(current_user.email, user_data)

        if success:
            return jsonify({
                'success': True,
                'message': 'Data export request submitted. You will receive an email with your data shortly.'
            })
        else:
            return jsonify({
                'error': 'Failed to send data export email'
            }), 500
    except Exception as e:
        logging.error(f"Error requesting data export: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/request-data-deletion', methods=['POST'])
@login_required
def request_data_deletion():
    """Request data deletion for the current user"""
    try:
        # Check if user is using email login (not Google)
        is_google_user = current_user.google_id is not None

        # For email login users, require verification
        if not is_google_user and current_user.two_factor_enabled:
            # Check if verification code was provided
            data = request.get_json()
            verification_code = data.get('verification_code')

            if not verification_code:
                # Send verification code to user's email
                code = generate_verification_code()
                send_verification_code(current_user.email, code)

                return jsonify({
                    'requires_verification': True,
                    'message': 'A verification code has been sent to your email. Please enter it to confirm data deletion.'
                })

            # Verify the code
            stored_code = get_stored_verification_code(current_user.email)
            if not stored_code or verification_code != stored_code:
                return jsonify({
                    'error': 'Invalid verification code',
                    'requires_verification': True
                }), 400

        # Collect user data for export before deletion
        user_data = collect_user_data(current_user)

        # Send data to user's email
        success = send_data_export_email(
            current_user.email,
            user_data,
            subject='Your Kevko Systems Data (Pre-Deletion Backup)',
            message='Your account data will be deleted in 5 minutes. This is a backup of your data before deletion.'
        )

        if not success:
            return jsonify({
                'error': 'Failed to send data backup email before deletion'
            }), 500

        # Schedule data deletion (5 minutes delay)
        schedule_data_deletion(current_user.id)

        return jsonify({
            'success': True,
            'message': 'Data deletion request submitted. Your data will be deleted in 5 minutes. A backup of your data has been sent to your email.'
        })
    except Exception as e:
        logging.error(f"Error requesting data deletion: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500