from database import db
from datetime import datetime
from models.sql_user import SQLUser

class FriendRequest(db.Model):
    __tablename__ = 'friend_requests'
    
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, accepted, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    sender = db.relationship('SQLUser', foreign_keys=[sender_id], backref='sent_requests')
    receiver = db.relationship('SQLUser', foreign_keys=[receiver_id], backref='received_requests')
    
    def to_dict(self):
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'sender': {
                'id': self.sender.id,
                'username': self.sender.username,
                'display_name': self.sender.display_name,
                'profile_picture': self.sender.profile_picture
            } if self.sender else None,
            'receiver': {
                'id': self.receiver.id,
                'username': self.receiver.username,
                'display_name': self.receiver.display_name,
                'profile_picture': self.receiver.profile_picture
            } if self.receiver else None
        }

class FriendTheme(db.Model):
    __tablename__ = 'friend_themes'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    theme = db.Column(db.String(20), default='blue')  # blue, purple, green, red, orange, teal, pink, indigo, amber
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    user = db.relationship('SQLUser', backref='theme_preference')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'theme': self.theme,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }