import os
import sys
from dotenv import load_dotenv
from mongoengine import connect, disconnect
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from models.user import User

def setup_super_admins():
    """Set up super admin users"""
    # Load environment variables
    load_dotenv()

    # Connect to MongoDB
    disconnect()
    connect(
        db='kevko_systems',
        host=os.getenv('MONGO_URI'),
        alias='default'
    )

    # List of super admin emails
    super_admin_emails = ['<EMAIL>', '<EMAIL>']

    for super_admin_email in super_admin_emails:
        # Find user by email
        user = User.objects(email=super_admin_email).first()

        if not user:
            print(f"User with email {super_admin_email} not found")
            continue

        # Set as admin and super admin
        if not user.is_admin:
            user.is_admin = True
            print(f"User {super_admin_email} is now an admin")
        
        if user.is_super_admin:
            print(f"User {super_admin_email} is already a super admin")
        else:
            user.is_super_admin = True
            user.save()
            print(f"User {super_admin_email} is now a super admin")

if __name__ == '__main__':
    setup_super_admins()
    print("\nRun this script to set up the super admin users (0hamogh@gmail.<NAME_EMAIL>)")
    print("Usage: python setup_super_admins.py")
