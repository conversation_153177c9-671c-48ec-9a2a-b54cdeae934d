/* Profile Edit Page CSS - Modern, Responsive Design */

/* Base Variables */
:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --secondary-color: #06b6d4;
    --accent-color: #f43f5e;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #e53935;
    --dark-bg: #1e1e2e;
    --card-bg: rgba(30, 30, 46, 0.7);
    --input-bg: rgba(30, 30, 46, 0.8);
    --border-color: rgba(255, 255, 255, 0.1);
    --text-primary: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

/* Base Styles */
body {
    background-color: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Inter', system-ui, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(79, 70, 229, 0.03) 2%, transparent 0%), 
        radial-gradient(circle at 75px 75px, rgba(6, 182, 212, 0.03) 2%, transparent 0%);
    background-size: 100px 100px;
}

/* Container */
.edit-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header */
.edit-header {
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.edit-title {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.edit-subtitle {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-top: 0;
}

/* Form Layout */
.edit-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* Section Styling */
.edit-section {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 1.75rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.edit-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.edit-section-title {
    font-size: 1.5rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.75rem;
    position: relative;
    display: inline-block;
}

.edit-section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
}

.edit-subsection-title {
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary-light);
    position: relative;
    display: inline-block;
    padding-left: 12px;
}

.edit-subsection-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.75rem;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 0.95rem;
    letter-spacing: 0.02em;
}

.form-hint {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    margin-bottom: 0.75rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--input-bg);
    color: var(--text-primary);
    font-family: inherit;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* Color Picker */
.color-picker-wrapper {
    display: flex;
    align-items: center;
}

.color-preview {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    margin-right: 12px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-fast);
}

.color-preview:hover {
    transform: scale(1.1);
}

/* Color Preview Container */
.color-preview-container {
    margin-bottom: 1.5rem;
    text-align: center;
}

.secondary-button {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.secondary-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border-color: var(--primary-light);
}

.secondary-button i {
    font-size: 0.875rem;
}

.background-preview {
    height: 60px;
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    background: linear-gradient(to right, #ffffff 0%, #ffffff 20%, #f8f9fa 20%, #f8f9fa 40%, #f8f9fa 40%, #f8f9fa 60%, #ffffff 60%, #ffffff 80%, #f8f9fa 80%, #f8f9fa 100%);
}

.background-preview:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Checkbox */
.form-checkbox {
    margin-right: 0.5rem;
    accent-color: var(--primary-color);
    width: 18px;
    height: 18px;
}

/* Form Actions */
.form-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2.5rem;
}

/* Buttons */
.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    backdrop-filter: blur(5px);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c62828);
    color: white;
}

/* Preview Frame */
.preview-frame {
    width: 100%;
    height: 300px;
    border: none;
    border-radius: var(--radius-lg);
    margin-top: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.preview-frame:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Social Links */
.social-links-container {
    margin-top: 1rem;
    display: grid;
    gap: 1rem;
}

.social-link-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    padding: 1rem;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.social-link-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.social-link-icon {
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.social-link-item:hover .social-link-icon {
    background: var(--primary-color);
    color: white;
    transform: rotate(5deg);
}

.social-link-platform {
    width: 25%;
    margin-right: 0.75rem;
}

.social-link-url {
    width: 55%;
    margin-right: 0.75rem;
}

.social-link-remove {
    width: 42px;
    height: 42px;
    background: rgba(229, 57, 53, 0.1);
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--transition-normal);
}

.social-link-remove:hover {
    background: rgba(229, 57, 53, 0.2);
    transform: scale(1.1) rotate(5deg);
}

.add-social-link {
    margin-top: 1rem;
    background: rgba(79, 70, 229, 0.1);
    border: 1px dashed rgba(79, 70, 229, 0.3);
    color: var(--primary-color);
    padding: 0.875rem;
    width: 100%;
    text-align: center;
    cursor: pointer;
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-social-link:hover {
    background: rgba(79, 70, 229, 0.15);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

/* Custom Sections */
.custom-sections-container {
    margin-top: 1rem;
}

.custom-section-item {
    margin-bottom: 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1.25rem;
    background: rgba(255, 255, 255, 0.03);
    transition: all var(--transition-normal);
}

.custom-section-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.custom-section-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    align-items: center;
}

.custom-section-title-input {
    width: 70%;
}

.custom-section-remove {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    font-size: 1.5rem;
    transition: all var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.custom-section-remove:hover {
    background: rgba(229, 57, 53, 0.1);
    transform: rotate(90deg);
}

.add-custom-section {
    margin-top: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px dashed var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem;
    width: 100%;
    text-align: center;
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-custom-section:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Profile Info Section */
.profile-info-section {
    grid-column: 1 / -1;
    margin-bottom: 2rem;
}

.profile-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.profile-picture-edit {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    margin-right: 1.5rem;
    border: 3px solid var(--primary-color);
    box-shadow: var(--shadow-md);
}

.profile-picture-edit img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-picture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
    cursor: pointer;
}

.profile-picture-edit:hover .profile-picture-overlay {
    opacity: 1;
}

.profile-picture-icon {
    color: white;
    font-size: 1.5rem;
}

.profile-info-details {
    flex: 1;
}

.profile-info-name {
    font-size: 1.75rem;
    margin: 0 0 0.25rem 0;
    font-weight: 700;
}

.profile-info-username {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .edit-container {
        padding: 1rem;
    }
    
    .edit-form {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .edit-title {
        font-size: 1.75rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .profile-info-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-picture-edit {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .social-link-item {
        flex-wrap: wrap;
    }
    
    .social-link-icon {
        margin-bottom: 0.5rem;
    }
    
    .social-link-platform {
        width: calc(50% - 0.75rem);
        margin-bottom: 0.5rem;
    }
    
    .social-link-url {
        width: 100%;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

/* Loading Indicator */
.loading-indicator {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.message-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-left: 4px solid var(--success-color);
    color: #34d399;
}

.message-error {
    background-color: rgba(229, 57, 53, 0.1);
    border-left: 4px solid var(--danger-color);
    color: #ef5350;
}

.message-icon {
    font-size: 1.25rem;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    border-radius: var(--radius-md);
    padding: 0.5rem;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    font-size: 0.875rem;
    box-shadow: var(--shadow-md);
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.edit-section {
    animation: fadeIn 0.5s ease-out forwards;
}

.edit-section:nth-child(2) {
    animation-delay: 0.1s;
}

.edit-section:nth-child(3) {
    animation-delay: 0.2s;
}

.edit-section:nth-child(4) {
    animation-delay: 0.3s;
}

.edit-section:nth-child(5) {
    animation-delay: 0.4s;
}