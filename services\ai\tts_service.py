import os
import base64
import tempfile
import uuid
import shutil
from pathlib import Path
from groq import Groq

class TTSService:
    def __init__(self):
        self.client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        self.model = "playai-tts"
        self.voice = "Angelo-PlayAI"
        self.response_format = "wav"
        self.max_chars = 1199  # Maximum characters to send to TTS model
        
        # Create audio storage directory if it doesn't exist
        self.audio_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'audio')
        os.makedirs(self.audio_dir, exist_ok=True)
    
    def generate_speech(self, text, store_in_db=False):
        """
        Generate speech from text using PlayAI TTS model
        
        Args:
            text (str): The text to convert to speech
            store_in_db (bool): If True, return base64 data for storing in DB. 
                               If False, store file on disk and return path.
        
        Returns:
            dict: A dictionary containing audio data or file path and metadata
        """
        try:
            # Limit text to max_chars
            limited_text = text[:self.max_chars]
            
            # Create a temporary file to store the audio
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # Generate speech
            response = self.client.audio.speech.create(
                model=self.model,
                voice=self.voice,
                response_format=self.response_format,
                input=limited_text,
            )
            
            # Write response to file using the correct method
            response.write_to_file(temp_path)
            
            # Get file size in KB
            file_size = os.path.getsize(temp_path) / 1024
            
            if store_in_db and file_size < 15000:  # Only store in DB if less than ~15MB
                # Read the file and encode to base64 for DB storage
                with open(temp_path, 'rb') as audio_file:
                    audio_data = audio_file.read()
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                
                # Clean up the temporary file
                os.unlink(temp_path)
                
                return {
                    'audio_data': audio_base64,
                    'format': self.response_format,
                    'size_kb': round(file_size, 2),
                    'voice': self.voice,
                    'model': self.model,
                    'storage_type': 'db'
                }
            else:
                # Generate a unique filename
                filename = f"{uuid.uuid4()}.{self.response_format}"
                file_path = os.path.join(self.audio_dir, filename)
                
                # Copy the temp file to the static directory
                shutil.copy2(temp_path, file_path)
                
                # Clean up the temporary file
                os.unlink(temp_path)
                
                # Return the relative path for serving the file
                relative_path = f"/static/audio/{filename}"
                
                return {
                    'file_path': relative_path,
                    'format': self.response_format,
                    'size_kb': round(file_size, 2),
                    'voice': self.voice,
                    'model': self.model,
                    'storage_type': 'file'
                }
            
        except Exception as e:
            print(f"Error generating speech: {str(e)}")
            return None