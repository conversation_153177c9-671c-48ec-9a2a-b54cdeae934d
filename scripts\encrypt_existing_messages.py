import os
import sys
import logging
from dotenv import load_dotenv
from mongoengine import connect, disconnect

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Connect to MongoDB
disconnect()
connect(
    db='kevko_systems',
    host=os.getenv('MONGO_URI'),
    alias='default'
)

# Import models after connecting to the database
from models.friend_chat import FriendChat

def encrypt_existing_messages():
    """Encrypt all existing messages in the database"""
    logging.info("Starting encryption of existing messages...")

    # Get all chats
    chats = FriendChat.objects()
    chat_count = len(chats)
    logging.info(f"Found {chat_count} chats to process")

    total_messages = 0
    encrypted_messages = 0

    for i, chat in enumerate(chats, 1):
        logging.info(f"Processing chat {i}/{chat_count}: {chat.chat_id}")

        # Process messages in this chat
        updated = False
        for message in chat.messages:
            total_messages += 1

            # Skip already encrypted messages
            if message.get('encrypted', False):
                continue

            # Skip system messages that don't have content
            if not message.get('content'):
                continue

            # Encrypt the message content
            try:
                original_content = message['content']
                encrypted_content = FriendChat.encrypt_message(original_content, chat.chat_id)

                # Update the message
                message['content'] = encrypted_content
                message['encrypted'] = True

                encrypted_messages += 1
                updated = True

                logging.debug(f"Encrypted message: {original_content[:20]}... -> {encrypted_content[:20]}...")
            except Exception as e:
                logging.error(f"Error encrypting message in chat {chat.chat_id}: {str(e)}")

        # Save the chat if any messages were updated
        if updated:
            try:
                chat.save()
                logging.info(f"Saved chat {chat.chat_id} with encrypted messages")
            except Exception as e:
                logging.error(f"Error saving chat {chat.chat_id}: {str(e)}")

    logging.info(f"Encryption complete. Processed {total_messages} messages, encrypted {encrypted_messages} messages.")

if __name__ == "__main__":
    encrypt_existing_messages()
