"""
Test script to verify model cost updates are properly applied when deducting credits
"""
import sys
from datetime import datetime, timezone
from mongoengine import connect

# Connect to the database
from database import init_db
init_db()

from models.user import User
from models.user_credit import UserCredit
from models.model_credit_cost import ModelCreditCost
from models.model_usage import ModelUsage
from models.credit_transaction import CreditTransaction

def test_model_cost_update():
    """Test that model cost updates are properly applied when deducting credits"""
    print("\n=== Testing Model Cost Update ===")
    
    # Get the first admin user
    admin_user = User.objects(is_admin=True).first()
    if not admin_user:
        print("No admin user found. Please create an admin user first.")
        return
    
    # Get the first non-admin user
    user = User.objects(is_admin=False).first()
    if not user:
        print("No non-admin user found. Please create a regular user first.")
        return
    
    print(f"Admin user: {admin_user.username} ({admin_user.id})")
    print(f"Regular user: {user.username} ({user.id})")
    
    # Ensure the user has enough credits
    user_credit = UserCredit.get_or_create(user.id)
    initial_balance = user_credit.balance
    print(f"Initial credit balance: {initial_balance}")
    
    if initial_balance < 20:
        print("Adding 100 credits to ensure we have enough for testing")
        UserCredit.add_credits(user.id, 100)
        user_credit = UserCredit.get_or_create(user.id)
        print(f"New credit balance: {user_credit.balance}")
    
    # Test model name
    test_model = "test-model-cost-update"
    
    # Set initial cost
    initial_cost = 5
    print(f"\nSetting initial cost for {test_model} to {initial_cost}")
    ModelCreditCost.set_cost(test_model, initial_cost)
    
    # Verify initial cost
    cost = ModelCreditCost.get_cost(test_model)
    print(f"Retrieved cost: {cost}")
    
    # Log usage with initial cost
    print(f"\nLogging usage with initial cost ({initial_cost})")
    result = ModelUsage.log_usage(
        user_id=user.id,
        model_name=test_model,
        service='chat'
    )
    
    if not result:
        print("Failed to log usage")
        return
    
    # Get updated balance
    user_credit = UserCredit.get_or_create(user.id)
    balance_after_first_usage = user_credit.balance
    print(f"Balance after first usage: {balance_after_first_usage}")
    print(f"Credits deducted: {initial_balance - balance_after_first_usage}")
    
    # Update cost
    new_cost = 10
    print(f"\nUpdating cost for {test_model} to {new_cost}")
    ModelCreditCost.set_cost(test_model, new_cost)
    
    # Verify updated cost
    updated_cost = ModelCreditCost.get_cost(test_model)
    print(f"Retrieved updated cost: {updated_cost}")
    
    # Log usage with updated cost
    print(f"\nLogging usage with updated cost ({new_cost})")
    result = ModelUsage.log_usage(
        user_id=user.id,
        model_name=test_model,
        service='chat'
    )
    
    if not result:
        print("Failed to log usage")
        return
    
    # Get final balance
    user_credit = UserCredit.get_or_create(user.id)
    final_balance = user_credit.balance
    print(f"Final balance: {final_balance}")
    print(f"Credits deducted in second usage: {balance_after_first_usage - final_balance}")
    
    # Verify the correct amount was deducted
    if balance_after_first_usage - final_balance == new_cost:
        print("\n✅ SUCCESS: The correct updated cost was deducted")
    else:
        print(f"\n❌ ERROR: Expected {new_cost} credits to be deducted, but {balance_after_first_usage - final_balance} were deducted")
    
    # Check the credit transactions
    transactions = CreditTransaction.objects(
        user=user.id,
        model_name=test_model
    ).order_by('-timestamp').limit(2)
    
    print("\nCredit Transactions:")
    for i, tx in enumerate(transactions):
        print(f"Transaction {i+1}: Amount: {tx.amount}, Model: {tx.model_name}, Time: {tx.timestamp}")
    
    # Clean up - reset the cost
    print("\nCleaning up - resetting cost")
    ModelCreditCost.set_cost(test_model, initial_cost)

if __name__ == "__main__":
    test_model_cost_update()