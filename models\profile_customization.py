from mongoengine import Document, <PERSON><PERSON>ield, <PERSON><PERSON><PERSON>, Dict<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from models.user import User

class ProfileCustomization(Document):
    user = ReferenceField(User, required=True, unique=True)
    
    # Basic customization
    background_color = StringField(default="#f5f5f5")
    text_color = StringField(default="#333333")
    accent_color = StringField(default="#4a86e8")
    
    # Profile section colors
    profile_container_color = StringField(default="#ffffff")
    profile_header_color = StringField(default="#f8f9fa")
    profile_about_color = StringField(default="#f8f9fa")
    profile_content_color = StringField(default="#ffffff")
    profile_friends_color = StringField(default="#f8f9fa")
    
    # Background options
    background_type = StringField(default="color", choices=["color", "gradient", "image", "pattern"])
    background_gradient = StringField(default="linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)")
    background_image = StringField(default="")
    background_pattern = String<PERSON>ield(default="")
    
    # Layout options
    layout_type = StringField(default="standard", choices=["standard", "compact", "expanded", "minimal"])
    
    # Font options
    font_family = StringField(default="Inter, system-ui, sans-serif")
    heading_font = StringField(default="Inter, system-ui, sans-serif")
    
    # Content display options
    show_activity = BooleanField(default=True)
    show_friends = BooleanField(default=True)
    show_stats = BooleanField(default=True)
    
    # Custom CSS
    custom_css = StringField(default="")
    
    # Custom sections
    custom_sections = DictField(default={})
    
    # Profile visibility
    is_public = BooleanField(default=True)
    
    # Social links
    social_links = DictField(default={})
    
    meta = {
        'collection': 'profile_customizations',
        'indexes': [
            'user'
        ]
    }
    
    @classmethod
    def get_or_create(cls, user):
        """Get existing profile customization or create default one"""
        profile = cls.objects(user=user).first()
        if not profile:
            profile = cls(user=user)
            profile.save()
        return profile