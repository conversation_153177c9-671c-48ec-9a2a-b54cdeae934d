/**
 * AdminCheck.js
 * Provides utility functions for checking admin status and permissions
 */
class AdminCheck {
    constructor() {
        this.isAdmin = false;
        this.isSuperAdmin = false;
        this.initialized = false;
    }

    /**
     * Initialize admin status
     * @returns {Promise<boolean>} Whether the user is an admin
     */
    async init() {
        if (this.initialized) {
            console.log('AdminCheck already initialized, isAdmin:', this.isAdmin);
            return this.isAdmin;
        }

        try {
            console.log('Checking admin status...');
            const response = await fetch('/api/admin/check');
            
            if (!response.ok) {
                console.error('Admin check response not OK:', response.status);
                throw new Error(`Admin check failed with status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Admin check response:', data);
            
            this.isAdmin = data.is_admin === true;
            this.isSuperAdmin = data.is_super_admin === true;
            this.initialized = true;
            
            console.log('Admin status initialized:', this.isAdmin);
            return this.isAdmin;
        } catch (error) {
            console.error('Error checking admin status:', error);
            this.isAdmin = false;
            this.isSuperAdmin = false;
            this.initialized = true;
            return false;
        }
    }

    /**
     * Check if the current user is an admin
     * @returns {boolean} Whether the user is an admin
     */
    isUserAdmin() {
        return this.isAdmin;
    }

    /**
     * Check if the current user is a super admin
     * @returns {boolean} Whether the user is a super admin
     */
    isUserSuperAdmin() {
        return this.isSuperAdmin;
    }

    /**
     * Prevent execution of admin-only code for non-admin users
     * @param {Function} callback Function to execute if user is admin
     * @returns {Promise<void>}
     */
    async executeIfAdmin(callback) {
        await this.init();
        if (this.isAdmin) {
            callback();
        } else {
            console.log('Admin access required for this operation');
        }
    }
}

// Create global instance
window.adminCheck = new AdminCheck();
