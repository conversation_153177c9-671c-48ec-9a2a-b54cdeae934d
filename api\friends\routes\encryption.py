"""
Encryption routes.
This module contains routes for managing end-to-end encryption.
"""
from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from .. import friends_api
from models.friend_chat import FriendChat
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
from datetime import datetime, timezone
import logging


@friends_api.route('/chat/<chat_id>/public-key', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_public_key', 'friends')
def store_public_key(chat_id):
    """Store the current user's public key for E2E encryption"""
    try:
        data = request.json
        logging.info(f"Received public key data: {data}")

        if not data:
            logging.error("No JSON data received in request")
            return jsonify({'error': 'No JSON data received'}), 400

        public_key = data.get('public_key')

        if not public_key:
            logging.error(f"Missing public_key in request: {data}")
            return jsonify({'error': 'public_key is required'}), 400
    except Exception as e:
        logging.error(f"Error processing public key request: {str(e)}")
        return jsonify({'error': f'Error processing request: {str(e)}'}), 400

    # Get the chat
    chat = FriendChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is a member of this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to access this chat'}), 403

    # Store the public key
    success = chat.store_public_key(current_user, public_key)
    if not success:
        return jsonify({'error': 'Failed to store public key'}), 500

    # Get the other user's public key if available
    other_user_key = chat.get_other_user_public_key(current_user)

    return jsonify({
        'success': True,
        'message': 'Public key stored successfully',
        'other_user_key': other_user_key,
        'e2e_ready': chat.is_e2e_ready()
    })


@friends_api.route('/chat/<chat_id>/public-key', methods=['GET'])
@login_required
@check_service_access('friends')
def get_public_keys(chat_id):
    """Get public keys for a chat"""
    # Get the chat
    chat = FriendChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is a member of this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to access this chat'}), 403

    # Get the public keys
    my_key = chat.get_public_key(current_user)
    other_user_key = chat.get_other_user_public_key(current_user)

    return jsonify({
        'success': True,
        'my_key': my_key,
        'other_user_key': other_user_key,
        'e2e_ready': chat.is_e2e_ready()
    })


@friends_api.route('/chat/<chat_id>/shared-secret', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_shared_secret', 'friends')
def store_shared_secret(chat_id):
    """Store an encrypted shared secret for E2E encryption"""
    try:
        data = request.json
        logging.info(f"Received shared secret data for chat {chat_id}")

        encrypted_secret = data.get('encrypted_secret')

        if not encrypted_secret:
            logging.error("Missing encrypted_secret in request")
            return jsonify({'error': 'encrypted_secret is required'}), 400

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            logging.error(f"Chat not found: {chat_id}")
            return jsonify({'error': 'Chat not found'}), 404

        # Check if user is a member of this chat
        if not chat.is_member(current_user):
            logging.error(f"User {current_user.username} is not a member of chat {chat_id}")
            return jsonify({'error': 'Not authorized to access this chat'}), 403

        # Get the other user
        other_user = chat.get_other_user(current_user)
        if not other_user:
            logging.error(f"Could not determine other user for chat {chat_id}")
            return jsonify({'error': 'Could not determine other user'}), 500

        # Store the encrypted secret in the database
        # We'll store it in a field based on which user is sending it
        if str(current_user.id) == str(chat.user1.id):
            chat.user1_shared_secret = encrypted_secret
        else:
            chat.user2_shared_secret = encrypted_secret

        # Check if this is the first time enabling E2E
        first_time_enabling = chat.e2e_enabled != "enabled"

        # Set E2E as enabled
        chat.e2e_enabled = "enabled"
        chat.updated_at = datetime.now(timezone.utc)

        # Add a system message only if this is the first time enabling E2E
        message_obj = None
        if first_time_enabling:
            timestamp = datetime.now(timezone.utc)
            message_obj = {
                "user_id": "system",
                "username": "System",
                "display_name": "System",
                "content": "End-to-end encryption has been enabled for this chat.",
                "timestamp": timestamp.isoformat(),
                "is_system": True,
                "message_type": "info",
                "encrypted": False  # Don't encrypt this message
            }
            chat.messages.append(message_obj)

        # Save the chat
        chat.save()

        # Emit a socket event to notify the other user about the shared secret
        try:
            from app import socketio
            socketio.emit('e2e_shared_secret', {
                'chat_id': chat_id,
                'from_user_id': str(current_user.id),
                'encrypted_secret': encrypted_secret
            }, room=f'user_{other_user.id}')
            logging.info(f"Emitted e2e_shared_secret event to user {other_user.username}")
        except Exception as e:
            logging.error(f"Error emitting socket event: {str(e)}")

        return jsonify({
            'success': True,
            'message': 'Shared secret received and stored',
            'system_message': message_obj
        })
    except Exception as e:
        logging.error(f"Error processing shared secret: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500


@friends_api.route('/chat/<chat_id>/reset-encryption', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_reset_encryption', 'friends')
def reset_chat_encryption(chat_id):
    """Reset encryption for a chat"""
    try:
        logging.info(f"Resetting encryption for chat {chat_id}")

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            logging.error(f"Chat not found: {chat_id}")
            return jsonify({'error': 'Chat not found'}), 404

        # Check if user is a member of this chat
        if not chat.is_member(current_user):
            logging.error(f"User {current_user.username} is not a member of chat {chat_id}")
            return jsonify({'error': 'Not authorized to access this chat'}), 403

        # Reset encryption fields
        chat.e2e_enabled = "pending"
        chat.user1_shared_secret = None
        chat.user2_shared_secret = None

        # Keep public keys so users don't have to exchange them again
        # The client will generate new shared secrets

        # Add a system message
        timestamp = datetime.now(timezone.utc)
        message_obj = {
            "user_id": "system",
            "username": "System",
            "display_name": "System",
            "content": "End-to-end encryption has been reset by " + (current_user.display_name or current_user.username) + ". Messages will be sent unencrypted until encryption is re-established.",
            "timestamp": timestamp.isoformat(),
            "is_system": True,
            "message_type": "warning",
            "encrypted": False
        }

        chat.messages.append(message_obj)
        chat.updated_at = timestamp
        chat.save()

        # Notify the other user
        other_user = chat.get_other_user(current_user)
        if other_user:
            try:
                from app import socketio
                socketio.emit('friend_encryption_reset', {
                    'chat_id': chat_id,
                    'by_user_id': str(current_user.id),
                    'by_user_name': current_user.display_name or current_user.username,
                    'system_message': message_obj
                }, room=f'user_{other_user.id}')
                logging.info(f"Emitted friend_encryption_reset event to user {other_user.username}")
            except Exception as e:
                logging.error(f"Error emitting socket event: {str(e)}")

        return jsonify({
            'success': True,
            'message': 'Encryption reset successfully',
            'system_message': message_obj
        })
    except Exception as e:
        logging.error(f"Error resetting encryption: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500


@friends_api.route('/chat/<chat_id>/update-message-encryption', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_update_message_encryption', 'friends')
def update_message_encryption(chat_id):
    """Update the encryption of a message"""
    try:
        logging.info(f"Updating message encryption for chat {chat_id}")

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        old_encrypted = data.get('old_encrypted')
        new_encrypted = data.get('new_encrypted')

        if not old_encrypted or not new_encrypted:
            return jsonify({'error': 'Missing required fields'}), 400

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            logging.error(f"Chat not found: {chat_id}")
            return jsonify({'error': 'Chat not found'}), 404

        # Check if user is a member of this chat
        if not chat.is_member(current_user):
            logging.error(f"User {current_user.username} is not a member of chat {chat_id}")
            return jsonify({'error': 'Not authorized to access this chat'}), 403

        # Find the message with the old encrypted content
        updated = False
        for i, message in enumerate(chat.messages):
            if message.get('content') == old_encrypted:
                # Update the message with the new encrypted content
                chat.messages[i]['content'] = new_encrypted
                updated = True
                break

        if not updated:
            return jsonify({'error': 'Message not found'}), 404

        # Save the chat
        chat.save()

        return jsonify({
            'success': True,
            'message': 'Message encryption updated successfully'
        })
    except Exception as e:
        logging.error(f"Error updating message encryption: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500


@friends_api.route('/chat/<chat_id>/reset-encryption', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_reset_encryption', 'friends')
def reset_encryption(chat_id):
    """Reset encryption for a chat"""
    try:
        logging.info(f"Resetting encryption for chat {chat_id}")

        # Get the chat
        chat = FriendChat.objects(chat_id=chat_id).first()
        if not chat:
            logging.error(f"Chat not found: {chat_id}")
            return jsonify({'error': 'Chat not found'}), 404

        # Check if user is a member of this chat
        if not chat.is_member(current_user):
            logging.error(f"User {current_user.username} is not a member of chat {chat_id}")
            return jsonify({'error': 'Not authorized to access this chat'}), 403

        # Add a system message about encryption reset
        system_message = {
            'user_id': None,
            'username': 'System',
            'display_name': 'System',
            'content': 'Encryption has been reset by ' + (current_user.display_name or current_user.username),
            'timestamp': datetime.now(timezone.utc),
            'type': 'system',
            'status': 'delivered',
            'is_encrypted': False
        }

        # Add the system message to the chat
        chat.messages.append(system_message)

        # Save the chat
        chat.save()

        # Notify the other user via socket
        try:
            # Get the other user in the chat
            other_user = None
            for member in chat.members:
                if str(member.id) != str(current_user.id):
                    other_user = member
                    break

            if other_user:
                # Emit a socket event to the other user
                socketio = current_app.extensions['socketio']
                socketio.emit('friend_encryption_reset', {
                    'chat_id': chat_id,
                    'reset_by': {
                        'id': str(current_user.id),
                        'username': current_user.username,
                        'display_name': current_user.display_name
                    }
                }, room=f"user_{other_user.id}")
        except Exception as e:
            logging.error(f"Error notifying other user about encryption reset: {str(e)}")

        return jsonify({
            'success': True,
            'message': 'Encryption reset successfully'
        })
    except Exception as e:
        logging.error(f"Error resetting encryption: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500