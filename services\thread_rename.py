from services.ai.gemini_service import GeminiService

class ThreadRenameService:
    def __init__(self):
        self.gemini_service = GeminiService()

    def generate_title(self, messages):
        if not messages:
            return "New Chat"

        # Create a prompt that asks <PERSON> to analyze the conversation and generate a title
        analysis_prompt = {
            "role": "user",
            "content": """Analyze this conversation and create a short, descriptive title (max 40 chars).
Rules:
- Be specific but concise
- Capture the main topic/purpose
- Use title case
- Don't use quotes
- Don't include generic words like "Discussion About" or "Conversation On"

Conversation:
""" + "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
        }

        try:
            # Generate title using Gemini
            title = ""
            for chunk in self.gemini_service.generate_stream([analysis_prompt]):
                title += chunk

            # Clean up the title
            title = title.strip()
            title = title[:50]  # Enforce max length
            return title or "New Chat"

        except Exception as e:
            print(f"Error generating thread title: {str(e)}")
            return "New Chat"

    def rename_thread(self, thread):
        """
        Renames a thread based on its content using AI analysis.
        Returns the updated thread.
        """
        if not thread.messages:
            return thread

        new_title = self.generate_title(thread.messages)
        thread.title = new_title
        thread.save()
        
        return thread