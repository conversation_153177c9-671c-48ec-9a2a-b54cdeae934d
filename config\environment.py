import os
from urllib.parse import urlparse

def is_production():
    return os.getenv('FLASK_ENV') == 'production'

def get_domain_config():
    production_domain = os.getenv('PRODUCTION_DOMAIN', 'kevkowebsite-3a05.onrender.com')
    port = int(os.getenv('PORT', 2000))
    
    config = {
        'is_production': is_production(),
        'domain': production_domain if is_production() else f'localhost:{port}',
        'scheme': 'https' if is_production() else 'http',
        'host': '0.0.0.0',
        'port': port
    }
    
    return config

