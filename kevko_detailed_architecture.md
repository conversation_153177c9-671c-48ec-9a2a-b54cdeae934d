# Kevko System Detailed Architecture

```mermaid
graph TB
    %% Main Application Components
    App[app.py - Main Flask Application]
    MongoDB[(MongoDB Database)]
    SocketIO[Socket.IO Server]

    %% API Blueprints
    AuthAPI[Auth API\n/api/auth]
    ChatAPI[Chat API\n/api/chat]
    LiveAPI[Live API\n/api/live]
    FriendsAPI[Friends API\n/api/friends]
    ProfileAPI[Profile API\n/api/profile]
    AdminAPI[Admin API\n/api/admin]
    SpotifyAPI[Spotify API\n/api/spotify]
    UserAPI[User API\n/api/user]
    StatisticsAPI[Statistics API\n/api/admin/statistics]
    UploadAPI[Upload API\n/api/upload]

    %% Socket Handlers
    LiveSocket[Live Socket Handlers]
    FriendsSocket[Friends Socket Handlers]

    %% Services
    ChatService[Chat Service]
    GeminiService[Gemini Service]
    GPTService[GPT Service]
    GroqService[Groq Service]
    GeminiImageService[Gemini Image Service]
    ThreadRenameService[Thread Rename Service]

    %% Models
    UserModel[User Model]
    ThreadModel[Thread Model]
    SharedThreadModel[Shared Thread Model]
    RoomModel[Room Model]
    FriendRelationshipModel[Friend Relationship Model]
    FriendChatModel[Friend Chat Model]
    GroupChatModel[Group Chat Model]
    FeatureRestrictionModel[Feature Restriction Model]
    RestrictedUserModel[Restricted User Model]
    ModelUsageModel[Model Usage Model]
    ServiceUsageModel[Service Usage Model]
    APILogModel[API Log Model]
    LoginHistoryModel[Login History Model]
    DailyStatsModel[Daily Stats Model]

    %% App connects to MongoDB
    App --> MongoDB
    App --> SocketIO

    %% App registers API blueprints
    App --> AuthAPI
    App --> ChatAPI
    App --> LiveAPI
    App --> FriendsAPI
    App --> ProfileAPI
    App --> AdminAPI
    App --> SpotifyAPI
    App --> UserAPI
    App --> StatisticsAPI
    App --> UploadAPI

    %% Socket.IO connections
    SocketIO --> LiveSocket
    SocketIO --> FriendsSocket

    %% Service connections
    ChatService --> GeminiService
    ChatService --> GPTService
    ChatService --> GroqService
    ChatService --> GeminiImageService
    ChatService --> ThreadRenameService

    %% API endpoints use services
    ChatAPI --> ChatService
    LiveAPI --> ChatService

    %% API endpoints use models
    AuthAPI --> UserModel
    AuthAPI --> LoginHistoryModel
    ChatAPI --> ThreadModel
    ChatAPI --> SharedThreadModel
    ChatAPI --> ModelUsageModel
    LiveAPI --> RoomModel
    LiveAPI --> ModelUsageModel
    FriendsAPI --> FriendRelationshipModel
    FriendsAPI --> FriendChatModel
    FriendsAPI --> GroupChatModel
    FriendsAPI --> UserModel
    ProfileAPI --> UserModel
    AdminAPI --> UserModel
    AdminAPI --> RestrictedUserModel
    AdminAPI --> FeatureRestrictionModel
    AdminAPI --> APILogModel
    AdminAPI --> DailyStatsModel
    StatisticsAPI --> ModelUsageModel
    StatisticsAPI --> ServiceUsageModel
    StatisticsAPI --> DailyStatsModel

    %% Socket handlers use models
    LiveSocket --> RoomModel
    LiveSocket --> ModelUsageModel
    FriendsSocket --> FriendChatModel
    FriendsSocket --> GroupChatModel
    FriendsSocket --> FriendRelationshipModel

    %% Detailed API Endpoints
    subgraph Auth_API_Endpoints
        AuthAPI_Register[/api/auth/register\nPOST - Register new user]
        AuthAPI_Login[/api/auth/login\nPOST - Login user]
        AuthAPI_Logout[/api/auth/logout\nPOST - Logout user]
        AuthAPI_Verify[/api/auth/send-verification\nPOST - Send verification code]
        AuthAPI_Reset[/api/auth/reset-password\nPOST - Reset password]
        AuthAPI_Check2FA[/api/auth/check-2fa\nGET - Check 2FA status]
        AuthAPI_Enable2FA[/api/auth/enable-2fa\nPOST - Enable 2FA]
        AuthAPI_Disable2FA[/api/auth/disable-2fa\nPOST - Disable 2FA]
    end

    subgraph Chat_API_Endpoints
        ChatAPI_Threads[/api/chat/threads\nGET - Get all threads]
        ChatAPI_Thread[/api/chat/thread/:thread_id\nGET - Get thread]
        ChatAPI_CreateThread[/api/chat/thread\nPOST - Create thread]
        ChatAPI_Send[/api/chat/send\nPOST - Send message]
        ChatAPI_DeleteThread[/api/chat/thread/:thread_id\nDELETE - Delete thread]
        ChatAPI_ShareThread[/api/chat/thread/:thread_id/share\nPOST - Share thread]
        ChatAPI_GetShared[/api/chat/shared/:share_id\nGET - Get shared thread]
        ChatAPI_CopyShared[/api/chat/shared/:share_id/copy\nPOST - Copy shared thread]
    end

    subgraph Live_API_Endpoints
        LiveAPI_Rooms[/api/live/rooms\nGET - Get all rooms]
        LiveAPI_Room[/api/live/room/:room_id\nGET - Get room]
        LiveAPI_CreateRoom[/api/live/room\nPOST - Create room]
        LiveAPI_Send[/api/live/send\nPOST - Send message]
        LiveAPI_Invite[/api/live/room/:room_id/invite\nPOST - Invite user]
        LiveAPI_DeleteRoom[/api/live/room/:room_id\nDELETE - Delete room]
        LiveAPI_LeaveRoom[/api/live/room/:room_id/leave\nPOST - Leave room]
    end

    subgraph Friends_API_Endpoints
        FriendsAPI_GetFriends[/api/friends/friends\nGET - Get all friends]
        FriendsAPI_Search[/api/friends/search\nGET - Search users]
        FriendsAPI_SendRequest[/api/friends/request\nPOST - Send friend request]
        FriendsAPI_AcceptRequest[/api/friends/accept\nPOST - Accept friend request]
        FriendsAPI_RejectRequest[/api/friends/reject\nPOST - Reject friend request]
        FriendsAPI_RemoveFriend[/api/friends/remove\nPOST - Remove friend]
        FriendsAPI_GetRequests[/api/friends/requests\nGET - Get friend requests]
        FriendsAPI_GetChat[/api/friends/chat/:chat_id\nGET - Get chat]
        FriendsAPI_CreateChat[/api/friends/chat\nPOST - Create chat]
        FriendsAPI_SendMessage[/api/friends/chat/:chat_id/message\nPOST - Send message]
        FriendsAPI_CreateGroup[/api/friends/group\nPOST - Create group chat]
        FriendsAPI_GetGroup[/api/friends/group/:chat_id\nGET - Get group chat]
        FriendsAPI_UpdateGroup[/api/friends/group/:chat_id\nPUT - Update group chat]
        FriendsAPI_AddToGroup[/api/friends/group/:chat_id/add\nPOST - Add to group]
        FriendsAPI_RemoveFromGroup[/api/friends/group/:chat_id/remove\nPOST - Remove from group]
        FriendsAPI_LeaveGroup[/api/friends/group/:chat_id/leave\nPOST - Leave group]
    end

    subgraph Admin_API_Endpoints
        AdminAPI_Check[/api/admin/check\nGET - Check admin status]
        AdminAPI_Users[/api/admin/users\nGET - Get all users]
        AdminAPI_Restrict[/api/admin/restrict\nPOST - Restrict user]
        AdminAPI_Unrestrict[/api/admin/unrestrict\nPOST - Unrestrict user]
        AdminAPI_FeatureRestrict[/api/admin/feature-restrict\nPOST - Set feature restrictions]
        AdminAPI_Stats[/api/admin/statistics\nGET - Get statistics]
        AdminAPI_Logs[/api/admin/logs\nGET - Get logs]
        AdminAPI_Updates[/api/admin/updates\nPOST - Create service update]
        AdminAPI_GetUpdates[/api/admin/updates\nGET - Get service updates]
    end

    %% Socket.IO Events
    subgraph SocketIO_Events
        %% Live Socket Events
        LiveSocket_Connect[connect\nAuthenticate user]
        LiveSocket_JoinRoom[join_room\nJoin a live room]
        LiveSocket_LeaveRoom[leave_room\nLeave a live room]
        LiveSocket_SendMessage[send_message\nSend message in room]
        LiveSocket_AITyping[ai_typing\nAI typing indicator]
        LiveSocket_AIResponseChunk[ai_response_chunk\nAI response chunk]
        LiveSocket_AIResponseComplete[ai_response_complete\nComplete AI response]

        %% Friends Socket Events
        FriendsSocket_Connect[connect\nAuthenticate user]
        FriendsSocket_JoinNamespace[join_friends_namespace\nJoin friends namespace]
        FriendsSocket_SendMessage[friend_send_message\nSend friend message]
        FriendsSocket_NewMessage[friend_new_message\nNew friend message]
        FriendsSocket_RequestSent[friend_request_sent\nFriend request sent]
        FriendsSocket_RequestReceived[friend_request_received\nFriend request received]
        FriendsSocket_RequestAccepted[friend_request_accepted\nFriend request accepted]
        FriendsSocket_FriendStatus[friend_status\nFriend online status]
    end

    %% Connect API blueprints to their endpoints
    AuthAPI --> Auth_API_Endpoints
    ChatAPI --> Chat_API_Endpoints
    LiveAPI --> Live_API_Endpoints
    FriendsAPI --> Friends_API_Endpoints
    AdminAPI --> Admin_API_Endpoints

    %% Connect Socket handlers to their events
    LiveSocket --> SocketIO_Events
    FriendsSocket --> SocketIO_Events

    %% Database Models Details
    subgraph Database_Models
        UserModel_Fields[User\n- username\n- email\n- password_hash\n- is_admin\n- is_super_admin\n- profile_picture\n- created_at\n- last_login]

        ThreadModel_Fields[Thread\n- user\n- title\n- messages\n- created_at\n- updated_at]

        RoomModel_Fields[Room\n- room_id\n- creator\n- participant\n- title\n- messages\n- created_at\n- is_active]

        FriendRelationshipModel_Fields[FriendRelationship\n- user\n- friend\n- created_at\n- is_accepted]

        FriendChatModel_Fields[FriendChat\n- chat_id\n- user1\n- user2\n- messages\n- created_at\n- updated_at\n- e2e_enabled]

        GroupChatModel_Fields[GroupChat\n- chat_id\n- name\n- creator\n- members\n- messages\n- created_at\n- profile_picture]
    end

    %% Connect models to their details
    UserModel --> UserModel_Fields
    ThreadModel --> ThreadModel_Fields
    RoomModel --> RoomModel_Fields
    FriendRelationshipModel --> FriendRelationshipModel_Fields
    FriendChatModel --> FriendChatModel_Fields
    GroupChatModel --> GroupChatModel_Fields
```

## AI Services and Data Flow

```mermaid
flowchart TB
    %% User Interactions
    User((User))

    %% Frontend Components
    ChatUI[Chat UI]
    LiveUI[Live Chat UI]
    FriendsUI[Friends UI]

    %% API Endpoints
    ChatAPI[/api/chat/send]
    LiveAPI[/api/live/send]
    FriendsAPI[/api/friends/chat/:id/message]

    %% WebSocket Connections
    LiveSocket[Live Socket\nsend_message]
    FriendsSocket[Friends Socket\nfriend_send_message]

    %% Services
    ChatService[Chat Service]

    %% AI Models
    GPT[GPT Service\ngpt-4o-mini]
    Gemini[Gemini Service\ngemini-2.0-flash]
    Groq[Groq Service\nqwen-qwq-32b]
    GeminiImage[Gemini Image Service]

    %% Database Models
    ThreadDB[(Thread Collection)]
    RoomDB[(Room Collection)]
    FriendChatDB[(FriendChat Collection)]
    GroupChatDB[(GroupChat Collection)]

    %% User Interactions
    User --> ChatUI
    User --> LiveUI
    User --> FriendsUI

    %% Frontend to Backend
    ChatUI --> ChatAPI
    LiveUI --> LiveAPI
    LiveUI --> LiveSocket
    FriendsUI --> FriendsAPI
    FriendsUI --> FriendsSocket

    %% API to Service
    ChatAPI --> ChatService
    LiveAPI --> ChatService
    LiveSocket --> ChatService

    %% Service to AI Models
    ChatService --> GPT
    ChatService --> Gemini
    ChatService --> Groq
    ChatService --> GeminiImage

    %% Service to Database
    ChatService --> ThreadDB
    ChatService --> RoomDB
    FriendsAPI --> FriendChatDB
    FriendsAPI --> GroupChatDB
    FriendsSocket --> FriendChatDB
    FriendsSocket --> GroupChatDB

    %% Data Flow Labels
    ChatUI -- "User message\n(text/images)" --> ChatAPI
    LiveUI -- "User message\n(text/images)" --> LiveSocket
    FriendsUI -- "User message\n(text/images)" --> FriendsSocket

    ChatService -- "AI response\n(streaming)" --> ChatUI
    ChatService -- "AI response\n(streaming)" --> LiveUI

    %% Subgraph for AI Model Selection Logic
    subgraph AI_Model_Selection
        ModelSelect{Has Images?}
        ModelSelect -- Yes --> GeminiImage
        ModelSelect -- No --> ModelType

        ModelType{Selected Model}
        ModelType -- "gpt-4o-mini" --> GPT
        ModelType -- "gemini-2.0-flash" --> Gemini
        ModelType -- "qwen-qwq-32b" --> Groq
    end

    ChatService --> AI_Model_Selection

    %% Encryption for Friend Messages
    subgraph E2E_Encryption
        Encrypt[Encrypt Message]
        Decrypt[Decrypt Message]

        FriendsUI -- "Plain text" --> Encrypt
        Encrypt -- "Encrypted" --> FriendChatDB
        FriendChatDB -- "Encrypted" --> Decrypt
        Decrypt -- "Plain text" --> FriendsUI
    end
```

## Database Schema

```mermaid
erDiagram
    User {
        string id PK
        string username
        string email
        string password_hash
        boolean is_admin
        boolean is_super_admin
        string profile_picture
        datetime created_at
        datetime last_login
        string display_name
        boolean two_factor_enabled
        string country_code
    }

    Thread {
        string id PK
        string user FK
        string title
        list messages
        datetime created_at
        datetime updated_at
    }

    SharedThread {
        string share_id PK
        string title
        list messages
        datetime created_at
    }

    Room {
        string room_id PK
        string creator FK
        string participant FK
        string title
        list messages
        datetime created_at
        datetime updated_at
        boolean is_active
    }

    FriendRelationship {
        string id PK
        string user FK
        string friend FK
        datetime created_at
        boolean is_accepted
    }

    FriendChat {
        string chat_id PK
        string user1 FK
        string user2 FK
        list messages
        datetime created_at
        datetime updated_at
        string e2e_enabled
        string user1_public_key
        string user2_public_key
    }

    GroupChat {
        string chat_id PK
        string name
        string creator FK
        list members FK
        list messages
        datetime created_at
        datetime updated_at
        string profile_picture
    }

    FeatureRestriction {
        string id PK
        string user FK
        int max_threads
        int max_conversation_sets
        int max_live_rooms
        int max_live_conversation_sets
        datetime created_at
        string created_by
    }

    RestrictedUser {
        string id PK
        string email
        list restricted_services
        datetime created_at
        string created_by
    }

    ModelUsage {
        string id PK
        string user FK
        string model_name
        string service
        int tokens_used
        datetime timestamp
    }

    ServiceUsage {
        string id PK
        string user FK
        string service
        string action
        datetime timestamp
    }

    APILog {
        string id PK
        string user FK
        string action
        string service
        datetime timestamp
        dict details
    }

    LoginHistory {
        string id PK
        string user FK
        datetime login_time
        string ip_address
        string user_agent
        dict device_info
        string location
    }

    DailyStats {
        string id PK
        datetime date
        int active_users
        int total_messages
        int avg_response_time
        dict service_usage
        dict model_usage
    }

    User ||--o{ Thread : creates
    User ||--o{ Room : creates
    User ||--o{ FriendRelationship : "has"
    User ||--o{ FriendChat : "participates in"
    User ||--o{ GroupChat : "participates in"
    User ||--o{ FeatureRestriction : "has"
    User ||--o{ ModelUsage : "generates"
    User ||--o{ ServiceUsage : "generates"
    User ||--o{ APILog : "generates"
    User ||--o{ LoginHistory : "has"

    FriendRelationship }|--|| User : "references"
    FriendChat }|--|| User : "references"
    GroupChat }|--|| User : "references"
    Room }|--|| User : "references"
```

## WebSocket Communication Flow

```mermaid
sequenceDiagram
    participant Client
    participant SocketIO as Socket.IO Server
    participant LiveSocket as Live Socket Handlers
    participant FriendsSocket as Friends Socket Handlers
    participant ChatService as Chat Service
    participant DB as MongoDB

    %% Connection Establishment
    Client->>SocketIO: connect()
    SocketIO->>LiveSocket: handle_connect()
    SocketIO->>FriendsSocket: handle_connect()
    FriendsSocket->>Client: join user_room (user_{user_id})

    %% Live Chat Room Joining
    Client->>SocketIO: emit('join_room', {room_id})
    SocketIO->>LiveSocket: handle_join_room(data)
    LiveSocket->>SocketIO: join_room(room_id)
    LiveSocket->>Client: emit('room_joined', {room_id})

    %% Live Chat Message Sending
    Client->>SocketIO: emit('send_message', {room_id, message, model, images})
    SocketIO->>LiveSocket: handle_send_message(data)
    LiveSocket->>DB: Room.objects(room_id=room_id).first()
    LiveSocket->>Client: emit('new_message', user_message, room=room_id)

    %% AI Response Generation
    LiveSocket->>ChatService: process_ai_response(room, user, model, images)
    LiveSocket->>Client: emit('ai_typing', {user_id, room_id}, room=room_id)

    %% AI Response Streaming
    ChatService-->>LiveSocket: yield chunks
    LiveSocket->>Client: emit('ai_response_chunk', {text, user_id, room_id}, room=room_id)

    %% AI Response Completion
    LiveSocket->>DB: room.add_ai_response(assistant_message, user_id)
    LiveSocket->>Client: emit('ai_response_complete', {message, room_id, user_id}, room=room_id)

    %% Friend Chat Message Sending
    Client->>SocketIO: emit('friend_send_message', {chat_id, message, images})
    SocketIO->>FriendsSocket: handle_friend_send_message(data)
    FriendsSocket->>DB: FriendChat.objects(chat_id=chat_id).first()

    %% Friend Chat Message Broadcasting
    FriendsSocket->>Client: emit('friend_message_sent', {success, chat_id, message})
    FriendsSocket->>Client: emit('friend_new_message', {chat_id, message}, room=chat_room)
    FriendsSocket->>Client: emit('friend_new_message_notification', notification, room=friend_room)
    FriendsSocket->>DB: chat.add_message(user, message, images)

    %% Friend Request Flow
    Client->>SocketIO: emit('friend_request_sent', {friend_id})
    SocketIO->>FriendsSocket: handle_friend_request_sent(data)
    FriendsSocket->>Client: emit('friend_request_received', {user_id, username}, room=friend_room)
```