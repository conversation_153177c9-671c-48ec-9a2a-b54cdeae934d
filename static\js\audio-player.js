/**
 * Audio Player for TTS responses
 */
class AudioPlayer {
    constructor() {
        this.audioElements = new Map(); // Map to store audio elements by ID
        this.currentlyPlaying = null;   // Currently playing audio element
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Global event delegation for audio player controls
        document.addEventListener('click', (event) => {
            // Play button
            if (event.target.closest('.play-audio-btn')) {
                const button = event.target.closest('.play-audio-btn');
                const audioId = button.getAttribute('data-audio-id');
                this.togglePlay(audioId);
            }
            
            // Save button
            if (event.target.closest('.save-audio-btn')) {
                const button = event.target.closest('.save-audio-btn');
                const audioId = button.getAttribute('data-audio-id');
                this.saveAudio(audioId);
            }
            
            // Waveform click for seeking
            if (event.target.closest('.waveform-container')) {
                const container = event.target.closest('.waveform-container');
                const audioId = container.getAttribute('data-audio-id');
                const rect = container.getBoundingClientRect();
                const clickPosition = (event.clientX - rect.left) / rect.width;
                this.seekAudio(audioId, clickPosition);
            }
        });
    }

    /**
     * Create a new audio player for a speech response
     * @param {Object} speechData - The speech data from the API
     * @param {string} messageId - The ID of the message containing this audio
     * @returns {HTMLElement} - The audio player element
     */
    createAudioPlayer(speechData, messageId) {
        // Generate a unique ID for this audio player
        const audioId = `audio-${messageId}-${Date.now()}`;
        
        // Create the audio element
        const audioElement = new Audio(`data:audio/${speechData.format};base64,${speechData.audio_data}`);
        audioElement.id = audioId;
        
        // Store the audio element for later use
        this.audioElements.set(audioId, {
            element: audioElement,
            duration: 0,
            isPlaying: false
        });
        
        // Create the waveform bars
        const numBars = 40; // Number of bars in the waveform
        let waveformBars = '';
        for (let i = 0; i < numBars; i++) {
            const height = Math.floor(Math.random() * 30) + 5; // Random height between 5 and 35px
            waveformBars += `<div class="waveform-bar" style="height: ${height}px; --bar-index: ${i}"></div>`;
        }
        
        // Create the audio player container
        const container = document.createElement('div');
        container.className = 'speech-result';
        container.innerHTML = `
            <div class="speech-result-title">Speech Result</div>
            <div class="audio-player-container">
                <div class="waveform-container" data-audio-id="${audioId}">
                    <div class="waveform-progress"></div>
                    <div class="waveform-playhead"></div>
                    <div class="waveform">
                        ${waveformBars}
                    </div>
                </div>
                <div class="audio-controls">
                    <div class="audio-buttons">
                        <button class="audio-button play-audio-btn" data-audio-id="${audioId}">
                            <i data-lucide="play" class="icon"></i>
                            <span>Play</span>
                        </button>
                        <button class="audio-button save-audio-btn" data-audio-id="${audioId}">
                            <i data-lucide="download" class="icon"></i>
                            <span>Save</span>
                        </button>
                    </div>
                    <div class="audio-metadata">
                        <div class="audio-duration">0.00 seconds</div>
                        <div class="audio-size">${speechData.size_kb}KB</div>
                    </div>
                </div>
            </div>
        `;
        
        // Initialize the audio element
        const audio = this.audioElements.get(audioId);
        
        // Update duration when metadata is loaded
        audioElement.addEventListener('loadedmetadata', () => {
            audio.duration = audioElement.duration;
            container.querySelector('.audio-duration').textContent = 
                `${audioElement.duration.toFixed(2)} seconds`;
        });
        
        // Update progress during playback
        audioElement.addEventListener('timeupdate', () => {
            const progress = audioElement.currentTime / audioElement.duration;
            container.querySelector('.waveform-progress').style.width = `${progress * 100}%`;
            container.querySelector('.waveform-playhead').style.left = `${progress * 100}%`;
        });
        
        // Handle audio ended
        audioElement.addEventListener('ended', () => {
            this.resetPlayButton(audioId);
            this.currentlyPlaying = null;
            audio.isPlaying = false;
            
            // Reset waveform animation
            const waveformBars = container.querySelectorAll('.waveform-bar');
            waveformBars.forEach(bar => {
                bar.classList.remove('playing');
            });
        });
        
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            setTimeout(() => {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    }
                });
            }, 0);
        }
        
        return container;
    }
    
    /**
     * Toggle play/pause for an audio element
     * @param {string} audioId - The ID of the audio element
     */
    togglePlay(audioId) {
        const audio = this.audioElements.get(audioId);
        if (!audio) return;
        
        const audioElement = audio.element;
        
        const containerElement = document.querySelector(`.waveform-container[data-audio-id="${audioId}"]`);
        if (!containerElement) return;
        
        const container = containerElement.closest('.speech-result');
        if (!container) return;
        
        const playButton = container.querySelector('.play-audio-btn');
        if (!playButton) return;
        
        const playText = playButton.querySelector('span');
        
        // If this audio is already playing, pause it
        if (audio.isPlaying) {
            audioElement.pause();
            audio.isPlaying = false;
            this.currentlyPlaying = null;
            
            // Replace button content with play icon and text
            playButton.innerHTML = '<i data-lucide="play" class="icon"></i><span>Play</span>';
            
            // Initialize the new icon
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    elements: playButton.querySelectorAll('i')
                });
            }
            
            // Reset waveform animation
            const waveformBars = container.querySelectorAll('.waveform-bar');
            waveformBars.forEach(bar => {
                bar.classList.remove('playing');
            });
        } 
        // If another audio is playing, stop it first
        else {
            if (this.currentlyPlaying) {
                this.resetPlayButton(this.currentlyPlaying);
                const currentAudio = this.audioElements.get(this.currentlyPlaying);
                if (currentAudio) {
                    currentAudio.element.pause();
                    currentAudio.isPlaying = false;
                }
            }
            
            // Play this audio
            audioElement.play();
            audio.isPlaying = true;
            this.currentlyPlaying = audioId;
            
            // Replace button content with pause icon and text
            playButton.innerHTML = '<i data-lucide="pause" class="icon"></i><span>Pause</span>';
            
            // Initialize the new icon
            if (typeof lucide !== 'undefined') {
                lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    elements: playButton.querySelectorAll('i')
                });
            }
            
            // Animate waveform
            const waveformBars = container.querySelectorAll('.waveform-bar');
            waveformBars.forEach((bar, index) => {
                bar.classList.add('playing');
            });
        }
    }
    
    /**
     * Reset a play button to its initial state
     * @param {string} audioId - The ID of the audio element
     */
    resetPlayButton(audioId) {
        const containerElement = document.querySelector(`.waveform-container[data-audio-id="${audioId}"]`);
        if (!containerElement) return;
        
        const container = containerElement.closest('.speech-result');
        if (!container) return;
        
        const playButton = container.querySelector('.play-audio-btn');
        if (!playButton) return;
        
        // Replace button content with play icon and text
        playButton.innerHTML = '<i data-lucide="play" class="icon"></i><span>Play</span>';
        
        // Refresh Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                elements: playButton.querySelectorAll('i')
            });
        }
    }
    
    /**
     * Seek to a position in the audio
     * @param {string} audioId - The ID of the audio element
     * @param {number} position - The position to seek to (0-1)
     */
    seekAudio(audioId, position) {
        const audio = this.audioElements.get(audioId);
        if (!audio) return;
        
        const audioElement = audio.element;
        audioElement.currentTime = position * audioElement.duration;
        
        // If not playing, update the progress visually
        if (!audio.isPlaying) {
            const container = document.querySelector(`.waveform-container[data-audio-id="${audioId}"]`);
            if (!container) return;
            
            const progressElement = container.querySelector('.waveform-progress');
            if (progressElement) {
                progressElement.style.width = `${position * 100}%`;
            }
            
            const playheadElement = container.querySelector('.waveform-playhead');
            if (playheadElement) {
                playheadElement.style.left = `${position * 100}%`;
            }
        }
    }
    
    /**
     * Save the audio file
     * @param {string} audioId - The ID of the audio element
     */
    saveAudio(audioId) {
        const audio = this.audioElements.get(audioId);
        if (!audio) return;
        
        // Create a download link
        const audioSrc = audio.element.src;
        const downloadLink = document.createElement('a');
        downloadLink.href = audioSrc;
        downloadLink.download = `speech-${Date.now()}.wav`;
        
        // Trigger download
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
}

// Initialize the audio player when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.audioPlayer) {
        window.audioPlayer = new AudioPlayer();
        console.log('Audio player initialized');
    }
});