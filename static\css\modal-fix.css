/**
 * modal-fix.css
 * Fixes for modal display issues
 */

/* Fix for the add credits modal - using the same approach as admin modals */
#addCreditsModal, #updateModelCostModal {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    z-index: 9999 !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
}

#addCreditsModal.hidden, #updateModelCostModal.hidden {
    display: none !important;
}

#addCreditsModal:not(.hidden), #updateModelCostModal:not(.hidden) {
    display: flex !important; /* Changed back to flex for proper centering */
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure modal content is visible and centered */
#addCreditsModal > div, #updateModelCostModal > div {
    background-color: #1e293b !important;
    border-radius: 0.5rem !important;
    border: 1px solid #334155 !important;
    width: 100% !important;
    max-width: 28rem !important;
    margin: 0 !important; /* Remove auto margin to allow flex centering to work */
    position: relative !important;
    overflow: visible !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Ensure buttons are visible and clickable */
#addCreditsModal button, #updateModelCostModal button {
    cursor: pointer !important;
    position: relative !important;
    z-index: 10000 !important;
}

/* Ensure form elements are visible */
#addCreditsModal input,
#addCreditsModal select,
#addCreditsModal textarea,
#updateModelCostModal input,
#updateModelCostModal select,
#updateModelCostModal textarea {
    background-color: #0f172a !important;
    color: #e2e8f0 !important;
    border: 1px solid #334155 !important;
}

/* Fix for modal animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

#addCreditsModal:not(.hidden), #updateModelCostModal:not(.hidden) {
    animation: fadeIn 0.3s ease-out forwards;
}