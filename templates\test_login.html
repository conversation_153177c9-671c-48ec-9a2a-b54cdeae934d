<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen flex items-center justify-center">
    <div class="bg-neutral-800 p-8 rounded-xl shadow-xl max-w-md w-full">
        <h1 class="text-2xl font-bold mb-6 text-center">Login Information</h1>
        <div class="space-y-4">
            <div class="bg-neutral-700 p-4 rounded-lg">
                <p class="text-sm text-gray-400">Username</p>
                <p class="text-lg" id="username"></p>
            </div>
            <div class="bg-neutral-700 p-4 rounded-lg">
                <p class="text-sm text-gray-400">Login Method</p>
                <p class="text-lg" id="loginMethod"></p>
            </div>
            <div class="bg-neutral-700 p-4 rounded-lg">
                <p class="text-sm text-gray-400">Email</p>
                <p class="text-lg" id="email"></p>
            </div>
        </div>
    </div>

    <script>
        fetch('/testlogin')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('username').textContent = data.username;
                    document.getElementById('loginMethod').textContent = data.login_method;
                    document.getElementById('email').textContent = data.email;
                } else {
                    document.body.innerHTML = '<p class="text-red-500">Error: ' + data.message + '</p>';
                }
            })
            .catch(error => {
                document.body.innerHTML = '<p class="text-red-500">Error: ' + error.message + '</p>';
            });
    </script>
</body>
</html>