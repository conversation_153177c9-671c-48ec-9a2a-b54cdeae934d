# Spotify API

This API provides endpoints for interacting with the Spotify Web API, allowing users to control playback, search for music, manage playlists, and more.

## Authentication

### GET /api/spotify/connect
- **Description**: Get Spotify authorization URL
- **Response**: Authorization URL

### GET /api/spotify/callback
- **Description**: Handle Spotify OAuth callback
- **Query Parameters**:
  - `code`: Authorization code
  - `state`: State parameter
- **Response**: User information and success status

### GET /api/spotify/logout
- **Description**: Clear Spotify tokens from session
- **Response**: Success message

### GET /api/spotify/token/refresh
- **Description**: Refresh Spotify access token
- **Response**: New access token and expiration time

### GET /api/spotify/status
- **Description**: Check if the user has Spotify credentials
- **Response**: Authentication status

## Playback Control

### GET /api/spotify/playback/current
- **Description**: Get current playback state
- **Authentication**: Spotify authentication required
- **Response**: Current playback object or null

### POST /api/spotify/playback/control/:action
- **Description**: Control playback (play, pause, skip, etc.)
- **Authentication**: Spotify authentication required
- **Path Parameters**:
  - `action`: One of 'play', 'pause', 'next', 'previous', 'shuffle', 'repeat', 'volume'
- **Request Body** (varies by action):
  ```json
  {
    "context_uri": "spotify:album:1234", // For 'play'
    "uris": ["spotify:track:1234"], // For 'play'
    "state": true, // For 'shuffle' and 'repeat'
    "volume": 50 // For 'volume'
  }
  ```
- **Response**: Updated playback state

### POST /api/spotify/play-track
- **Description**: Play a specific track
- **Authentication**: Spotify authentication required
- **Request Body**:
  ```json
  {
    "uri": "spotify:track:1234"
  }
  ```
- **Response**: Updated playback state

### POST /api/spotify/play-liked-songs
- **Description**: Play user's liked songs
- **Authentication**: Spotify authentication required
- **Response**: Updated playback state

## Search

### GET /api/spotify/search
- **Description**: Search for tracks, albums, artists, or playlists
- **Authentication**: Spotify authentication required
- **Query Parameters**:
  - `q`: Search query
  - `limit`: Maximum number of results (default: 5)
  - `type`: Search type (track, album, artist, playlist)
- **Response**: Search results

## Devices

### GET /api/spotify/devices
- **Description**: Get available Spotify devices
- **Authentication**: Spotify authentication required
- **Response**: List of devices

### POST /api/spotify/transfer-playback
- **Description**: Transfer playback to a different device
- **Authentication**: Spotify authentication required
- **Request Body**:
  ```json
  {
    "device_id": "device_id",
    "play": true
  }
  ```
- **Response**: Success message

## Playlists

### GET /api/spotify/playlists
- **Description**: Get user's playlists
- **Authentication**: Spotify authentication required
- **Response**: List of playlists

### GET /api/spotify/playlist/:playlist_id
- **Description**: Get a specific playlist
- **Authentication**: Spotify authentication required
- **Response**: Playlist object

### GET /api/spotify/playlist/:playlist_id/tracks
- **Description**: Get tracks in a playlist
- **Authentication**: Spotify authentication required
- **Query Parameters**:
  - `offset`: Offset for pagination (default: 0)
  - `limit`: Maximum number of tracks (default: 100)
- **Response**: List of tracks

### POST /api/spotify/play-playlist
- **Description**: Play a specific playlist
- **Authentication**: Spotify authentication required
- **Request Body**:
  ```json
  {
    "playlist_id": "playlist_id"
  }
  ```
- **Response**: Success message

## Queue

### GET /api/spotify/queue
- **Description**: Get current playback queue
- **Authentication**: Spotify authentication required
- **Response**: Queue object

### POST /api/spotify/add-to-queue
- **Description**: Add a track to the playback queue
- **Authentication**: Spotify authentication required
- **Request Body**:
  ```json
  {
    "uri": "spotify:track:1234"
  }
  ```
- **Response**: Success message

## Usage Example

```javascript
// Get current playback
const playbackResponse = await fetch('/api/spotify/playback/current', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const playback = await playbackResponse.json();

// Play a track
const playResponse = await fetch('/api/spotify/play-track', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    uri: 'spotify:track:1234'
  })
});

// Search for tracks
const searchResponse = await fetch('/api/spotify/search?q=artist:queen&limit=10&type=track', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const searchResults = await searchResponse.json();
```
