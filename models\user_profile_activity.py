from mongoengine import Document, <PERSON><PERSON>ield, <PERSON><PERSON>ield, DateTimeField, Dict<PERSON>ield
from models.user import User
from datetime import datetime, timezone

class UserProfileActivity(Document):
    user = ReferenceField(User, required=True)
    timestamp = DateTimeField(default=lambda: datetime.now(timezone.utc))
    activity_type = StringField(required=True)  # login, service_use, profile_update, etc.
    description = StringField(required=True)
    metadata = DictField()  # Additional data related to the activity
    
    meta = {
        'collection': 'user_profile_activities',
        'indexes': [
            'user',
            'timestamp',
            'activity_type'
        ]
    }
    
    @classmethod
    def log_activity(cls, user, activity_type, description, metadata=None):
        """Log a user activity"""
        activity = cls(
            user=user,
            activity_type=activity_type,
            description=description,
            metadata=metadata or {}
        )
        activity.save()
        return activity