/**
 * AdminLogs.js
 * Handles the admin logs functionality
 */
class AdminLogs {
    constructor() {
        this.logs = [];
        this.filters = {
            service: '',
            action: '',
            hours: 24
        };
        this.isLoading = false;
        this.liveTailEnabled = false;
        this.liveTailInterval = null;
        this.lastLogTimestamp = null;

        // Pagination properties
        this.currentPage = 0;
        this.logsPerPage = 20;
        this.hasMoreLogs = true;
        this.isLoadingMore = false;
        this.scrollHandler = null;
    }

    /**
     * Create a preview log element (simplified version)
     */
    createPreviewLogElement(log) {
        const logElement = document.createElement('div');
        logElement.className = 'bg-slate-800/50 border border-slate-700/50 rounded-md p-2 flex items-start mb-2 text-xs';

        // Determine log color based on service
        let logColor = 'text-blue-400';
        if (log.service === 'chat') {
            logColor = 'text-green-400';
        } else if (log.service === 'live') {
            logColor = 'text-purple-400';
        } else if (log.service === 'spotify') {
            logColor = 'text-green-400';
        }

        // Format timestamp
        const timestamp = new Date(log.timestamp);
        const formattedTime = timestamp.toLocaleTimeString();

        // Format action for display
        let actionDisplay = log.action.replace(/_/g, ' ');
        actionDisplay = actionDisplay.charAt(0).toUpperCase() + actionDisplay.slice(1);

        // Create profile picture or initials
        let profilePicture = '';
        // Check for profile picture in various locations
        const profilePic = log.profile_picture || log.user?.profile_picture;

        if (profilePic) {
            const username = log.username || log.user?.username || 'Unknown';
            profilePicture = `
                <img src="${profilePic}" alt="${username}" class="w-6 h-6 rounded-full object-cover">
            `;
        } else {
            // Handle case where username might be undefined
            const username = log.username || log.user?.username || 'UN';
            const initials = username.substring(0, 2).toUpperCase();
            profilePicture = `
                <div class="w-6 h-6 rounded-full bg-slate-700 flex items-center justify-center text-slate-300 text-xs font-medium">
                    ${initials}
                </div>
            `;
        }

        logElement.innerHTML = `
            <div class="mr-2 flex-shrink-0">
                ${profilePicture}
            </div>
            <div class="flex-grow min-w-0">
                <div class="flex items-center justify-between">
                    <div class="font-medium text-slate-200 truncate" title="${log.username || log.user?.username || 'Unknown'}">
                        ${log.username || log.user?.username || 'Unknown'}
                    </div>
                    <div class="text-slate-500 ml-2 flex-shrink-0">
                        ${formattedTime}
                    </div>
                </div>
                <div class="flex items-center mt-0.5">
                    <span class="${logColor} mr-1">${log.service}</span>
                    <span class="text-slate-300">${actionDisplay}</span>
                </div>
            </div>
        `;

        return logElement;
    }

    /**
     * Load logs preview for the dashboard
     */
    loadLogsPreview() {
        const logsPreview = document.getElementById('logsPreview');
        if (!logsPreview) return;

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            logsPreview.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center text-amber-400">
                        <i data-lucide="shield-alert" class="h-4 w-4 mr-2"></i>
                        <span class="text-sm">Admin access required to view logs</span>
                    </div>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
            return;
        }

        // Show loading
        logsPreview.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading recent logs...</span>
                </div>
            </div>
        `;
        if (window.lucide) lucide.createIcons();

        // Fetch recent logs (limit to 5)
        console.log('Fetching logs preview from: /api/admin/logs?limit=5');
        fetch('/api/admin/logs?limit=5')
            .then(response => {
                if (!response.ok) {
                    console.error(`Error response: ${response.status} ${response.statusText}`);
                    throw new Error('Failed to load logs');
                }
                return response.json();
            })
            .then(data => {
                console.log(`Received ${data.length} logs for preview`);
                if (data.length > 0) {
                    console.log(`First preview log: ${data[0].action} for ${data[0].service} by ${data[0].username}`);
                }

                if (data.length === 0) {
                    logsPreview.innerHTML = `
                        <div class="flex justify-center items-center py-4">
                            <div class="flex items-center text-slate-500">
                                <i data-lucide="info" class="h-4 w-4 mr-2"></i>
                                <span class="text-sm">No logs found.</span>
                            </div>
                        </div>
                    `;
                } else {
                    logsPreview.innerHTML = '';

                    // Add logs to preview
                    data.forEach(log => {
                        const logElement = this.createPreviewLogElement(log);
                        logsPreview.appendChild(logElement);
                    });
                }

                if (window.lucide) lucide.createIcons();
            })
            .catch(error => {
                console.error('Error loading logs preview:', error);
                logsPreview.innerHTML = `
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center text-red-400">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <span class="text-sm">Failed to load logs.</span>
                        </div>
                    </div>
                `;
                if (window.lucide) lucide.createIcons();
            });
    }

    /**
     * Initialize the logs functionality
     */
    async init() {
        console.log('Initializing AdminLogs...');

        // Initialize admin check if it doesn't exist
        if (!window.adminCheck) {
            console.log('AdminCheck not available, creating instance');
            window.adminCheck = new AdminCheck();
        }

        // Wait for admin check to initialize
        await window.adminCheck.init();

        // Set up event listeners
        this.setupEventListeners();

        // Load logs preview
        this.loadLogsPreview();
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Open logs modal
        const openLogsBtn = document.getElementById('openLogsBtn');
        if (openLogsBtn) {
            openLogsBtn.addEventListener('click', () => {
                this.showLogsModal();
            });
        }

        // Close logs modal
        const closeLogsModal = document.getElementById('closeLogsModal');
        if (closeLogsModal) {
            closeLogsModal.addEventListener('click', () => {
                this.hideLogsModal();
            });
        }

        // Open clear logs modal
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => {
                this.showClearLogsModal();
            });
        }

        // Close clear logs modal
        const closeClearLogsModal = document.getElementById('closeClearLogsModal');
        if (closeClearLogsModal) {
            closeClearLogsModal.addEventListener('click', () => {
                this.hideClearLogsModal();
            });
        }

        // Cancel clear logs
        const cancelClearLogs = document.getElementById('cancelClearLogs');
        if (cancelClearLogs) {
            cancelClearLogs.addEventListener('click', () => {
                this.hideClearLogsModal();
            });
        }

        // Confirm clear logs
        const confirmClearLogs = document.getElementById('confirmClearLogs');
        if (confirmClearLogs) {
            confirmClearLogs.addEventListener('click', () => {
                this.clearLogs();
            });
        }

        // Filter changes
        const serviceFilter = document.getElementById('logsServiceFilter');
        const actionFilter = document.getElementById('logsActionFilter');
        const timeFilter = document.getElementById('logsTimeFilter');

        if (serviceFilter) {
            serviceFilter.addEventListener('change', () => {
                this.filters.service = serviceFilter.value;
                this.loadLogs(true); // Reset pagination when filter changes
            });
        }

        if (actionFilter) {
            actionFilter.addEventListener('change', () => {
                this.filters.action = actionFilter.value;
                this.loadLogs(true); // Reset pagination when filter changes
            });
        }

        if (timeFilter) {
            timeFilter.addEventListener('change', () => {
                this.filters.hours = parseInt(timeFilter.value);
                this.loadLogs(true); // Reset pagination when filter changes
            });
        }

        // Refresh button
        const refreshLogs = document.getElementById('refreshLogs');
        if (refreshLogs) {
            refreshLogs.addEventListener('click', () => {
                this.loadLogs(true); // Reset pagination when refreshing
            });
        }

        // Live tail toggle
        const liveTailToggle = document.getElementById('liveTailToggle');
        if (liveTailToggle) {
            liveTailToggle.addEventListener('change', () => {
                this.liveTailEnabled = liveTailToggle.checked;
                if (this.liveTailEnabled) {
                    this.startLiveTail();
                } else {
                    this.stopLiveTail();
                }
            });
        }
    }

    /**
     * Show the logs modal
     */
    showLogsModal() {
        const logsModal = document.getElementById('logsModal');
        if (logsModal) {
            logsModal.classList.remove('hidden');
            this.loadLogs(); // Refresh logs when opening modal
        }
    }

    /**
     * Hide the logs modal
     */
    hideLogsModal() {
        const logsModal = document.getElementById('logsModal');
        if (logsModal) {
            logsModal.classList.add('hidden');

            // Remove scroll handler when modal is closed
            this.removeScrollHandler();
        }
    }

    /**
     * Set up scroll handler for lazy loading
     */
    setupScrollHandler() {
        // Remove any existing handler first
        this.removeScrollHandler();

        const logsContainerWrapper = document.querySelector('.overflow-y-auto');
        if (!logsContainerWrapper) return;

        // Create the scroll handler function
        this.scrollHandler = () => {
            // Don't do anything if already loading
            if (this.isLoading || this.isLoadingMore) return;

            // Check if we've scrolled near the bottom - use a more generous threshold (200px)
            const { scrollTop, scrollHeight, clientHeight } = logsContainerWrapper;
            const scrolledToBottom = scrollTop + clientHeight >= scrollHeight - 200; // 200px threshold

            console.log(`Scroll position: ${scrollTop + clientHeight}/${scrollHeight}, threshold: ${scrollHeight - 200}`);

            if (scrolledToBottom && this.hasMoreLogs) {
                console.log('Detected scroll to bottom, loading more logs...');
                this.loadMoreLogs();
            }
        };

        // Attach the scroll handler
        logsContainerWrapper.addEventListener('scroll', this.scrollHandler);

        // Also check immediately in case the content doesn't fill the container
        setTimeout(() => {
            this.scrollHandler();
        }, 100);
    }

    /**
     * Remove scroll handler
     */
    removeScrollHandler() {
        if (this.scrollHandler) {
            const logsContainerWrapper = document.querySelector('.overflow-y-auto');
            if (logsContainerWrapper) {
                logsContainerWrapper.removeEventListener('scroll', this.scrollHandler);
            }
            this.scrollHandler = null;
        }
    }

    /**
     * Load more logs when scrolling or clicking the load more button
     */
    loadMoreLogs() {
        if (this.isLoading || this.isLoadingMore || !this.hasMoreLogs) {
            console.log('Skipping loadMoreLogs - already loading or no more logs');
            return;
        }

        console.log('Loading more logs...');
        this.isLoadingMore = true;

        // Update loading indicator
        const loadingIndicator = document.getElementById('logsLoadingMore');
        if (loadingIndicator) {
            loadingIndicator.innerHTML = `
                <div class="flex items-center justify-center py-2">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                    <span class="text-slate-500 text-sm">Loading more logs...</span>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
        }

        try {
            // Load the next page of logs
            this.loadLogs(false);

            // Reset loading more flag after a delay
            setTimeout(() => {
                this.isLoadingMore = false;
                console.log('Reset isLoadingMore flag');
            }, 1000);
        } catch (error) {
            console.error('Error in loadMoreLogs:', error);

            // Show error in the loading indicator
            if (loadingIndicator) {
                loadingIndicator.innerHTML = `
                    <div class="flex flex-col items-center justify-center py-2">
                        <div class="flex items-center text-red-400 mb-2">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <span class="text-sm">Failed to load more logs</span>
                        </div>
                        <button id="retryLoadMoreBtn" class="bg-slate-700 hover:bg-slate-600 text-slate-300 rounded px-3 py-1 text-sm flex items-center">
                            <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                            Retry
                        </button>
                    </div>
                `;

                // Add event listener to retry button
                setTimeout(() => {
                    const retryBtn = document.getElementById('retryLoadMoreBtn');
                    if (retryBtn) {
                        retryBtn.addEventListener('click', () => {
                            this.loadMoreLogs();
                        });
                    }
                    if (window.lucide) lucide.createIcons();
                }, 0);
            }

            // Reset loading flag
            this.isLoadingMore = false;
        }
    }

    /**
     * Show the clear logs modal
     */
    showClearLogsModal() {
        const clearLogsModal = document.getElementById('clearLogsModal');
        if (clearLogsModal) {
            clearLogsModal.classList.remove('hidden');

            // Reset status
            const clearLogsStatus = document.getElementById('clearLogsStatus');
            if (clearLogsStatus) {
                clearLogsStatus.classList.add('hidden');
                clearLogsStatus.innerHTML = '';
            }
        }
    }

    /**
     * Hide the clear logs modal
     */
    hideClearLogsModal() {
        const clearLogsModal = document.getElementById('clearLogsModal');
        if (clearLogsModal) {
            clearLogsModal.classList.add('hidden');
        }
    }

    /**
     * Clear logs based on selected filters
     */
    clearLogs() {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping clearLogs - user is not an admin');

            // Show error message
            const clearLogsStatus = document.getElementById('clearLogsStatus');
            if (clearLogsStatus) {
                clearLogsStatus.classList.remove('hidden');
                clearLogsStatus.innerHTML = `
                    <div class="flex items-center justify-center py-2 text-red-400">
                        <i data-lucide="shield-alert" class="h-4 w-4 mr-2"></i>
                        <span>Admin access required to clear logs</span>
                    </div>
                `;
                if (window.lucide) lucide.createIcons();
            }
            return;
        }

        // Get selected filters
        const service = document.getElementById('clearLogsServiceFilter').value;
        const action = document.getElementById('clearLogsActionFilter').value;
        const hours = document.getElementById('clearLogsTimeFilter').value;

        // Show loading status
        const clearLogsStatus = document.getElementById('clearLogsStatus');
        if (clearLogsStatus) {
            clearLogsStatus.classList.remove('hidden');
            clearLogsStatus.innerHTML = `
                <div class="flex items-center justify-center py-2 text-slate-300">
                    <i data-lucide="loader" class="h-4 w-4 animate-spin mr-2"></i>
                    <span>Clearing logs...</span>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
        }

        // Disable confirm button
        const confirmClearLogs = document.getElementById('confirmClearLogs');
        if (confirmClearLogs) {
            confirmClearLogs.disabled = true;
            confirmClearLogs.classList.add('opacity-50');
        }

        // Prepare data for API call
        const data = {};
        if (service) data.service = service;
        if (action) data.action = action;
        if (hours) data.hours = parseInt(hours);

        console.log('Clearing logs with params:', data);

        // Call API to clear logs
        // Try the most likely endpoint first
        const clearLogsUrl = '/api/admin/clear-logs';
        console.log(`Calling clear logs API at: ${clearLogsUrl}`);

        fetch(clearLogsUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                console.error(`Error response from ${clearLogsUrl}: ${response.status} ${response.statusText}`);

                // Try alternative URL if the first one fails
                if (response.status === 404) {
                    console.log('First endpoint not found, trying alternative URL...');
                    const alternativeUrl = '/api/admin/logs/clear';
                    console.log(`Trying alternative URL: ${alternativeUrl}`);

                    return fetch(alternativeUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    }).then(altResponse => {
                        if (!altResponse.ok) {
                            console.error(`Error response from ${alternativeUrl}: ${altResponse.status} ${altResponse.statusText}`);
                            return altResponse.text().then(text => {
                                throw new Error(`Failed to clear logs: ${text}`);
                            });
                        }
                        return altResponse.json();
                    });
                }

                return response.text().then(text => {
                    throw new Error(`Failed to clear logs: ${text}`);
                });
            }
            return response.json();
        })
        .then(result => {
            console.log('Logs cleared:', result);

            // Show success message
            if (clearLogsStatus) {
                clearLogsStatus.innerHTML = `
                    <div class="flex items-center justify-center py-2 text-green-400">
                        <i data-lucide="check-circle" class="h-4 w-4 mr-2"></i>
                        <span>Successfully cleared ${result.deleted_count} logs.</span>
                    </div>
                `;
                if (window.lucide) lucide.createIcons();
            }

            // Re-enable confirm button
            if (confirmClearLogs) {
                confirmClearLogs.disabled = false;
                confirmClearLogs.classList.remove('opacity-50');
            }

            // Reload logs after a short delay
            setTimeout(() => {
                this.loadLogs();
                this.loadLogsPreview();
            }, 1500);
        })
        .catch(error => {
            console.error('Error clearing logs:', error);

            // Show error message
            if (clearLogsStatus) {
                clearLogsStatus.innerHTML = `
                    <div class="flex items-center justify-center py-2 text-red-400">
                        <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                        <span>Failed to clear logs. Please try again.</span>
                    </div>
                `;
                if (window.lucide) lucide.createIcons();
            }

            // Re-enable confirm button
            if (confirmClearLogs) {
                confirmClearLogs.disabled = false;
                confirmClearLogs.classList.remove('opacity-50');
            }
        });
    }

    /**
     * Start live tail
     */
    startLiveTail() {
        console.log('Starting live tail...');

        // Reset pagination when starting live tail
        this.currentPage = 0;
        this.hasMoreLogs = true;

        // Remove scroll handler when live tail is active
        this.removeScrollHandler();

        // Store the timestamp of the most recent log
        if (this.logs.length > 0) {
            this.lastLogTimestamp = new Date(this.logs[0].timestamp).getTime();
        } else {
            this.lastLogTimestamp = Date.now();
        }

        // Set up interval to fetch new logs every 5 seconds
        this.liveTailInterval = setInterval(() => {
            this.fetchNewLogs();
        }, 5000);

        // Update UI to show live tail is active
        const liveTailStatus = document.getElementById('liveTailStatus');
        if (liveTailStatus) {
            liveTailStatus.classList.remove('hidden');
            liveTailStatus.classList.add('flex');
        }
    }

    /**
     * Stop live tail
     */
    stopLiveTail() {
        console.log('Stopping live tail...');
        if (this.liveTailInterval) {
            clearInterval(this.liveTailInterval);
            this.liveTailInterval = null;
        }

        // Update UI to show live tail is inactive
        const liveTailStatus = document.getElementById('liveTailStatus');
        if (liveTailStatus) {
            liveTailStatus.classList.add('hidden');
            liveTailStatus.classList.remove('flex');
        }

        // Restore scroll handler if we have more logs
        if (this.hasMoreLogs) {
            this.setupScrollHandler();
        }
    }

    /**
     * Fetch new logs for live tail
     */
    fetchNewLogs() {
        if (this.isLoading) return;

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping fetchNewLogs - user is not an admin');
            this.stopLiveTail(); // Stop live tail for non-admin users
            return;
        }

        // Build query string
        const queryParams = new URLSearchParams();
        if (this.filters.service) queryParams.append('service', this.filters.service);
        if (this.filters.action) queryParams.append('action', this.filters.action);

        // Fetch logs since the last timestamp
        fetch(`/api/admin/logs?${queryParams.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch new logs');
                }
                return response.json();
            })
            .then(data => {
                // Filter logs that are newer than the last one we saw
                const newLogs = data.filter(log => {
                    const logTime = new Date(log.timestamp).getTime();
                    return logTime > this.lastLogTimestamp;
                });

                if (newLogs.length > 0) {
                    // Update the last log timestamp
                    this.lastLogTimestamp = new Date(newLogs[0].timestamp).getTime();

                    // Add new logs to the top of the list
                    this.logs = [...newLogs, ...this.logs];

                    // Re-render logs
                    this.renderLogs();

                    // Scroll to top to show new logs
                    const logsContainer = document.getElementById('logsContainer');
                    if (logsContainer) {
                        logsContainer.scrollTop = 0;
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching new logs:', error);
                // Don't show error in UI for background updates
            });
    }

    /**
     * Load logs from the server
     * @param {boolean} reset - Whether to reset pagination and load from the beginning
     */
    loadLogs(reset = true) {
        if (this.isLoading) return;

        this.isLoading = true;

        // If resetting, show loading indicator and reset pagination
        if (reset) {
            this.showLoading();
            this.currentPage = 0;
            this.logs = [];
            this.hasMoreLogs = true;

            // Remove existing scroll handler if any
            this.removeScrollHandler();
        }

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping loadLogs - user is not an admin');
            this.showError('Admin access required to view logs');
            this.isLoading = false;
            return;
        }

        // Build query string
        const queryParams = new URLSearchParams();
        if (this.filters.service) queryParams.append('service', this.filters.service);
        if (this.filters.action) queryParams.append('action', this.filters.action);
        if (this.filters.hours) queryParams.append('hours', this.filters.hours);

        // Add pagination parameters
        queryParams.append('limit', this.logsPerPage);
        queryParams.append('skip', this.currentPage * this.logsPerPage);

        const url = `/api/admin/logs?${queryParams.toString()}`;
        console.log(`Fetching logs from: ${url} (page ${this.currentPage + 1})`);

        // Fetch logs
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    console.error(`Error response: ${response.status} ${response.statusText}`);
                    throw new Error('Failed to load logs');
                }
                return response.json();
            })
            .then(data => {
                console.log(`Received ${data.length} logs for page ${this.currentPage + 1}`);
                if (data.length > 0) {
                    console.log(`First log: ${data[0].action} for ${data[0].service} by ${data[0].username || data[0].user?.username || 'Unknown'}`);
                }

                // Check if we have more logs to load
                this.hasMoreLogs = data.length === this.logsPerPage;

                // If this is the first page, replace logs array, otherwise append
                if (reset) {
                    this.logs = data;
                } else {
                    this.logs = [...this.logs, ...data];
                }

                // Increment page counter for next load
                this.currentPage++;

                // Render logs
                this.renderLogs(reset);
                this.isLoading = false;

                // Update last log timestamp for live tail
                if (data.length > 0) {
                    this.lastLogTimestamp = new Date(data[0].timestamp).getTime();
                }

                // If live tail was enabled, restart it with new filters
                if (this.liveTailEnabled) {
                    this.stopLiveTail();
                    this.startLiveTail();
                }

                // Set up scroll handler for lazy loading if we have more logs
                if (this.hasMoreLogs) {
                    this.setupScrollHandler();

                    // Check if we need to load more logs immediately
                    // This handles the case where the content doesn't fill the container
                    setTimeout(() => {
                        const logsContainerWrapper = document.querySelector('.overflow-y-auto');
                        if (logsContainerWrapper) {
                            const { scrollHeight, clientHeight } = logsContainerWrapper;
                            console.log(`Container size check: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}`);

                            // If the content is shorter than the container, load more logs
                            if (scrollHeight <= clientHeight && this.hasMoreLogs && !this.isLoadingMore) {
                                console.log('Content doesn\'t fill container, loading more logs...');
                                this.loadMoreLogs();
                            }
                        }
                    }, 200);
                }
            })
            .catch(error => {
                console.error('Error loading logs:', error);
                this.showError('Failed to load logs. Please try again.');
                this.isLoading = false;
            });
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        const logsContainer = document.getElementById('logsContainer');
        if (logsContainer) {
            logsContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400">Loading logs...</span>
                    </div>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const logsContainer = document.getElementById('logsContainer');
        if (logsContainer) {
            logsContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center text-red-400">
                        <i data-lucide="alert-circle" class="h-5 w-5 mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
        }
    }

    /**
     * Render logs in the container
     * @param {boolean} reset - Whether to clear the container before rendering
     */
    renderLogs(reset = true) {
        const logsContainer = document.getElementById('logsContainer');
        if (!logsContainer) return;

        // If no logs and we're resetting, show no logs message
        if (this.logs.length === 0 && reset) {
            logsContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center text-slate-400">
                        <i data-lucide="info" class="h-5 w-5 mr-2"></i>
                        <span>No logs found matching the current filters.</span>
                    </div>
                </div>
            `;
            if (window.lucide) lucide.createIcons();
            return;
        }

        // If resetting, clear container first
        if (reset) {
            logsContainer.innerHTML = '';
        } else {
            // Remove loading indicator if it exists
            const loadingIndicator = document.getElementById('logsLoadingMore');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }

        // Get logs to render (all if reset, or just the new ones if appending)
        const logsToRender = reset ? this.logs : this.logs.slice(-this.logsPerPage);

        // Add logs
        logsToRender.forEach(log => {
            const logElement = this.createLogElement(log);
            logsContainer.appendChild(logElement);
        });

        // Add logs counter and "load more" indicator/button if there are more logs
        const logsCounter = document.createElement('div');
        logsCounter.className = 'flex justify-center items-center py-2 mt-2 text-xs text-slate-500';
        logsCounter.innerHTML = `Showing ${this.logs.length} logs`;
        logsContainer.appendChild(logsCounter);

        if (this.hasMoreLogs) {
            const loadMoreIndicator = document.createElement('div');
            loadMoreIndicator.id = 'logsLoadingMore';
            loadMoreIndicator.className = 'flex flex-col justify-center items-center py-3 mt-1';
            loadMoreIndicator.innerHTML = `
                <div class="flex items-center text-slate-500 mb-2">
                    <span class="text-sm">Scroll down or click button to load more</span>
                </div>
                <button id="loadMoreLogsBtn" class="bg-slate-700 hover:bg-slate-600 text-slate-300 rounded px-4 py-2 text-sm flex items-center">
                    <i data-lucide="chevrons-down" class="h-3 w-3 mr-2"></i>
                    Load More Logs
                </button>
            `;
            logsContainer.appendChild(loadMoreIndicator);

            // Add event listener to the load more button
            setTimeout(() => {
                const loadMoreBtn = document.getElementById('loadMoreLogsBtn');
                if (loadMoreBtn) {
                    loadMoreBtn.addEventListener('click', () => {
                        this.loadMoreLogs();
                    });
                }
                if (window.lucide) lucide.createIcons();
            }, 0);
        }

        // Initialize icons
        if (window.lucide) lucide.createIcons();
    }

    /**
     * Create a log element
     */
    createLogElement(log) {
        const logElement = document.createElement('div');
        logElement.className = 'bg-slate-800/50 border border-slate-700/50 rounded-md p-3 flex items-start';

        // Determine log color based on service
        let logColor = 'text-blue-400';
        if (log.service === 'chat') {
            logColor = 'text-green-400';
        } else if (log.service === 'live') {
            logColor = 'text-purple-400';
        } else if (log.service === 'spotify') {
            logColor = 'text-green-400';
        }

        // Format timestamp
        const timestamp = new Date(log.timestamp);
        const formattedTime = timestamp.toLocaleString();

        // Format action for display
        let actionDisplay = log.action.replace(/_/g, ' ');
        actionDisplay = actionDisplay.charAt(0).toUpperCase() + actionDisplay.slice(1);

        // Create profile picture or initials
        let profilePicture = '';

        // Check if this is a join action with creator details
        const isJoinWithCreator = (log.action === 'live_join' &&
                                  log.details &&
                                  log.details.creator_profile_picture);

        if (isJoinWithCreator) {
            // Create overlapping profile pictures for join actions
            const creatorPic = log.details.creator_profile_picture;
            const userPic = log.profile_picture || log.user?.profile_picture || '';
            const username = log.username || log.user?.username || 'Unknown';

            if (userPic) {
                // Both user and creator have profile pictures
                profilePicture = `
                    <div class="relative">
                        <img src="${creatorPic}" alt="${log.details.creator_name || 'Creator'}"
                             class="w-10 h-10 rounded-full object-cover absolute z-10 border-2 border-slate-800">
                        <img src="${userPic}" alt="${username}"
                             class="w-10 h-10 rounded-full object-cover absolute left-4 top-4 z-20 border-2 border-slate-800">
                    </div>
                    <div class="w-10 h-10"></div> <!-- Spacer -->
                `;
            } else {
                // Creator has profile picture but user doesn't
                const initials = username.substring(0, 2).toUpperCase();
                profilePicture = `
                    <div class="relative">
                        <img src="${creatorPic}" alt="${log.details.creator_name || 'Creator'}"
                             class="w-10 h-10 rounded-full object-cover absolute z-10 border-2 border-slate-800">
                        <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center
                                  text-slate-300 font-medium absolute left-4 top-4 z-20 border-2 border-slate-800">
                            ${initials}
                        </div>
                    </div>
                    <div class="w-10 h-10"></div> <!-- Spacer -->
                `;
            }
        } else {
            // Regular profile picture display
            // Check for profile picture in various locations
            const profilePic = log.profile_picture || log.user?.profile_picture;
            const username = log.username || log.user?.username || 'Unknown';

            if (profilePic) {
                profilePicture = `
                    <img src="${profilePic}" alt="${username}" class="w-10 h-10 rounded-full object-cover">
                `;
            } else {
                // Handle case where username might be undefined
                const initials = username.substring(0, 2).toUpperCase();
                profilePicture = `
                    <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center text-slate-300 font-medium">
                        ${initials}
                    </div>
                `;
            }
        }

        // Build details string
        let detailsStr = '';
        if (log.details && Object.keys(log.details).length > 0) {
            detailsStr = Object.entries(log.details)
                .map(([key, value]) => `<span class="text-slate-500">${key}:</span> ${value}`)
                .join(' • ');
        }

        // Add creator name for join actions
        let creatorInfo = '';
        if (isJoinWithCreator && log.details.creator_name) {
            creatorInfo = `
                <div class="text-xs text-purple-400 mt-1">
                    .    Joined ${log.details.creator_name}'s room
                </div>
            `;
        }

        logElement.innerHTML = `
            <div class="mr-3 flex-shrink-0">
                ${profilePicture}
            </div>
            <div class="flex-grow min-w-0">
                <div class="flex items-center justify-between">
                    <div class="font-medium text-slate-200 truncate" title="${log.username || log.user?.username || 'Unknown'}">
                        ${log.username || log.user?.username || 'Unknown'}
                    </div>
                    <div class="text-xs text-slate-500 ml-2 flex-shrink-0">
                        ${formattedTime}
                    </div>
                </div>
                ${creatorInfo}
                <div class="flex items-center mt-1">
                    <span class="${logColor} text-sm mr-2">${log.service}</span>
                    <span class="text-slate-300 text-sm">${actionDisplay}</span>
                </div>
                ${detailsStr ? `<div class="text-xs text-slate-400 mt-1">${detailsStr}</div>` : ''}
                <div class="text-xs text-slate-500 mt-1 hover:text-slate-400 cursor-help" title="${log.email || log.user?.email || 'Unknown'}">
                    ${log.email || log.user?.email || 'Unknown'}
                </div>
            </div>
        `;

        return logElement;
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.adminLogs = new AdminLogs();
    window.adminLogs.init();
});
