import base64
import os
from google import genai
from google.genai import types
from PIL import Image
import io
import tempfile

class GeminiImageService:
    def __init__(self):
        self.client = genai.Client(
            api_key=os.getenv('GEMINI_API_KEY')
        )

    def _save_image_temp(self, image_data, mime_type):
        # Create a temporary file
        image_bytes = base64.b64decode(image_data)
        temp = tempfile.NamedTemporaryFile(delete=False, suffix=self._get_extension(mime_type))
        temp.write(image_bytes)
        temp.close()
        return temp.name

    def _get_extension(self, mime_type):
        # Map mime types to file extensions
        mime_map = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp'
        }
        return mime_map.get(mime_type, '.jpg')

    def generate_stream(self, message_data):
        images = message_data.get('images', [])
        prompt = message_data.get('prompt', '')

        if not images or not prompt:
            raise ValueError("Both images and prompt are required")

        try:
            # Save images to temporary files and upload them
            temp_files = []
            uploaded_files = []

            for img in images:
                temp_path = self._save_image_temp(img['data'], img['mime_type'])
                temp_files.append(temp_path)
                uploaded_file = self.client.files.upload(file=temp_path)
                uploaded_files.append(uploaded_file)

            # Create content parts
            parts = [
                types.Part.from_uri(
                    file_uri=file.uri,
                    mime_type=file.mime_type
                )
                for file in uploaded_files
            ]
            # Add the text prompt
            parts.append(types.Part.from_text(text=prompt))

            # Create content
            contents = [
                types.Content(
                    role="user",
                    parts=parts
                )
            ]

            # Generate response
            config = types.GenerateContentConfig(
                response_mime_type="text/plain"
            )

            # Stream the response
            response_stream = self.client.models.generate_content_stream(
                model="gemini-2.0-flash",
                contents=contents,
                config=config
            )

            # Check if response_stream is iterable
            if hasattr(response_stream, '__iter__'):
                for chunk in response_stream:
                    if chunk and hasattr(chunk, 'text') and chunk.text:
                        yield chunk.text
            else:
                # Handle case where response is not iterable
                import logging
                logging.error(f"Unexpected response type from Gemini API: {type(response_stream)}")
                if response_stream and hasattr(response_stream, 'text'):
                    yield response_stream.text
                else:
                    yield "Error: Received non-iterable response from Gemini API"

            # Cleanup temporary files
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except:
                    pass

        except Exception as e:
            yield f"Error processing images: {str(e)}"




