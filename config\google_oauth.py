from oauthlib.oauth2 import WebApplicationClient
import os
from flask import request, url_for

def get_google_provider_cfg():
    return {
        "authorization_endpoint": "https://accounts.google.com/o/oauth2/v2/auth",
        "token_endpoint": "https://oauth2.googleapis.com/token",
        "userinfo_endpoint": "https://www.googleapis.com/oauth2/v3/userinfo"
    }

def create_google_oauth_client():
    client_id = os.getenv("GOOGLE_CLIENT_ID")
    client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
    
    if not client_id or not client_secret:
        raise ValueError("GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET environment variable is not set")
    
    # Allow HTTP in development
    if os.getenv('FLASK_ENV') == 'development':
        os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
    
    return WebApplicationClient(client_id)

def get_callback_url():
    host = request.host
    
    # Always use HTTPS for onrender.com domain
    # For local development, allow HTTP
    if 'onrender.com' in host:
        scheme = 'https'
    elif os.getenv('FLASK_ENV') == 'development':
        scheme = 'http'
    else:
        scheme = 'https'
    
    callback_url = f"{scheme}://{host}/auth/google/callback"
    
    print("\nCallback URL Debug Info:")
    print(f"Host: {host}")
    print(f"Scheme: {scheme}")
    print(f"Final Callback URL: {callback_url}")
    print(f"Current Request URL: {request.url}")
    print(f"Request Headers: {dict(request.headers)}\n")
    
    return callback_url




