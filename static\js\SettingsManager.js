/**
 * SettingsManager.js
 * Manages user and system settings
 */

class SettingsManager {
    constructor() {
        this.userSettings = {
            appearance: {
                darkMode: true,
                accentColor: 'cyan',
                fontSize: 'medium'
            },
            notifications: {
                enabled: true,
                sound: true,
                desktop: true,
                updates: true,
                services: true
            },
            serviceVisibility: {},
            privacy: {
                showOnlineStatus: true,
                shareActivity: true,
                allowFriendRequests: true
            },
            account: {
                username: '',
                email: '',
                role: 'admin'
            },
            connectedServices: []
        };

        // Load settings from localStorage
        this.loadSettings();
    }

    /**
     * Load settings from localStorage
     */
    loadSettings() {
        const storedSettings = localStorage.getItem('userSettings');
        if (storedSettings) {
            try {
                const settings = JSON.parse(storedSettings);
                this.userSettings = { ...this.userSettings, ...settings };
                console.log('Settings loaded from localStorage');
            } catch (e) {
                console.warn('Failed to parse stored settings');
            }
        }
    }

    /**
     * Save settings to localStorage
     */
    saveSettings() {
        localStorage.setItem('userSettings', JSON.stringify(this.userSettings));
        console.log('Settings saved to localStorage');
    }

    /**
     * Get all user settings
     * @returns {Object} User settings
     */
    getAllSettings() {
        return this.userSettings;
    }

    /**
     * Get a specific setting
     * @param {string} category Setting category
     * @param {string} key Setting key
     * @returns {any} Setting value
     */
    getSetting(category, key) {
        if (this.userSettings[category] && key in this.userSettings[category]) {
            return this.userSettings[category][key];
        }
        return null;
    }

    /**
     * Update a specific setting
     * @param {string} category Setting category
     * @param {string} key Setting key
     * @param {any} value Setting value
     */
    updateSetting(category, key, value) {
        if (!this.userSettings[category]) {
            this.userSettings[category] = {};
        }

        this.userSettings[category][key] = value;
        this.saveSettings();

        // Dispatch event for setting change
        const event = new CustomEvent('settingChanged', {
            detail: { category, key, value }
        });
        window.dispatchEvent(event);
    }

    /**
     * Update multiple settings at once
     * @param {string} category Setting category
     * @param {Object} settings Settings object
     */
    updateSettings(category, settings) {
        if (!this.userSettings[category]) {
            this.userSettings[category] = {};
        }

        this.userSettings[category] = { ...this.userSettings[category], ...settings };
        this.saveSettings();

        // Dispatch event for settings change
        const event = new CustomEvent('settingsChanged', {
            detail: { category, settings }
        });
        window.dispatchEvent(event);
    }

    /**
     * Get service visibility
     * @param {string} serviceId Service ID
     * @returns {boolean} Whether the service is visible
     */
    getServiceVisibility(serviceId) {
        return this.userSettings.serviceVisibility[serviceId] !== false;
    }

    /**
     * Set service visibility
     * @param {string} serviceId Service ID
     * @param {boolean} visible Whether the service should be visible
     */
    setServiceVisibility(serviceId, visible) {
        this.userSettings.serviceVisibility[serviceId] = visible;
        this.saveSettings();

        // Dispatch event for service visibility change
        const event = new CustomEvent('serviceVisibilityChanged', {
            detail: { serviceId, visible }
        });
        window.dispatchEvent(event);
    }

    /**
     * Apply appearance settings
     */
    applyAppearanceSettings() {
        const { darkMode, accentColor, fontSize } = this.userSettings.appearance;

        // Apply dark mode
        if (darkMode) {
            document.documentElement.classList.add('dark-mode');
        } else {
            document.documentElement.classList.remove('dark-mode');
        }

        // Apply accent color
        document.documentElement.classList.remove('accent-cyan', 'accent-purple', 'accent-green', 'accent-amber', 'accent-red', 'accent-blue');
        document.documentElement.classList.add(`accent-${accentColor}`);

        // Apply font size
        document.documentElement.classList.remove('font-small', 'font-medium', 'font-large');
        document.documentElement.classList.add(`font-${fontSize}`);
    }

    /**
     * Apply notification settings
     */
    applyNotificationSettings() {
        const { enabled } = this.userSettings.notifications;

        if (enabled) {
            // Request notification permission if needed
            if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
                Notification.requestPermission();
            }
        }
    }

    /**
     * Show a notification
     * @param {string} title Notification title
     * @param {string} body Notification body
     * @param {string} icon Notification icon
     */
    showNotification(title, body, icon) {
        const { enabled, desktop } = this.userSettings.notifications;

        if (enabled && desktop && Notification.permission === 'granted') {
            new Notification(title, {
                body,
                icon
            });
        }
    }

    /**
     * Get connected external services
     * @returns {Promise<Array>} Array of connected services
     */
    async getConnectedServices() {
        try {
            const response = await fetch('/api/user/connected-services');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            // Update local storage with the latest data
            this.userSettings.connectedServices = data.services || [];
            this.saveSettings();

            return this.userSettings.connectedServices;
        } catch (error) {
            console.error('Error fetching connected services:', error);
            // Return cached data if available
            return this.userSettings.connectedServices || [];
        }
    }

    /**
     * Disconnect an external service
     * @param {string} serviceId Service ID to disconnect
     * @returns {Promise<boolean>} Whether the disconnection was successful
     */
    async disconnectService(serviceId) {
        try {
            const response = await fetch(`/api/user/disconnect-service/${serviceId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update local cache
                this.userSettings.connectedServices = this.userSettings.connectedServices.filter(
                    service => service.id !== serviceId
                );
                this.saveSettings();

                // Dispatch event for service disconnection
                const event = new CustomEvent('serviceDisconnected', {
                    detail: { serviceId }
                });
                window.dispatchEvent(event);

                return true;
            }

            return false;
        } catch (error) {
            console.error(`Error disconnecting service ${serviceId}:`, error);
            return false;
        }
    }

    /**
     * Reset settings to defaults
     */
    resetSettings() {
        this.userSettings = {
            appearance: {
                darkMode: true,
                accentColor: 'cyan',
                fontSize: 'medium'
            },
            notifications: {
                enabled: true,
                sound: true,
                desktop: true,
                updates: true,
                services: true
            },
            serviceVisibility: {},
            privacy: {
                showOnlineStatus: true,
                shareActivity: true,
                allowFriendRequests: true
            },
            account: {
                username: '',
                email: '',
                role: 'admin'
            },
            connectedServices: []
        };

        this.saveSettings();

        // Dispatch event for settings reset
        const event = new CustomEvent('settingsReset');
        window.dispatchEvent(event);
    }
}

// Create global settings manager instance
window.settingsManager = new SettingsManager();
