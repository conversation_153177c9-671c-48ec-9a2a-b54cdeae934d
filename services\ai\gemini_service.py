import os
from google import genai
from google.genai import types

class GeminiService:
    def __init__(self):
        self.client = genai.Client(
            api_key=os.getenv('GEMINI_API_KEY')
        )

    def generate_stream(self, messages, system_prompt_file='prompts/system-prompt.txt'):
        contents = self._convert_messages_to_gemini_format(messages)

        with open(system_prompt_file, 'r') as f:
            system_prompt = f.read()

        config = types.GenerateContentConfig(
            response_mime_type="text/plain",
            system_instruction=[
                types.Part.from_text(text=system_prompt),
            ],
        )

        for chunk in self.client.models.generate_content_stream(
            model="gemini-2.0-flash",
            contents=contents,
            config=config,
        ):
            if chunk.text:
                yield chunk.text

    def _convert_messages_to_gemini_format(self, messages):
        return [
            types.Content(
                role="user" if msg["role"] == "user" else "model",
                parts=[types.Part.from_text(text=msg["content"])]
            )
            for msg in messages
        ]
