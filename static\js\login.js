// Function to send verification code for registration
async function sendVerificationCode(type) {
    let email;
    if (type === 'login') {
        // This is now handled by the login flow
        return;
    } else {
        email = document.querySelector('#registerForm input[name="email"]').value;
    }

    if (!email) {
        showNotification('Email address not found', 'error');
        return;
    }

    try {
        const response = await fetch('/auth/send-verification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                type: type
            })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('Verification code sent! Please check your email.', 'success');
            startVerificationTimer();
        } else {
            showNotification(data.error || 'Failed to send verification code', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred while sending the verification code', 'error');
    }
}

// Function to start verification timer
function startVerificationTimer(elementId = null) {
    const timerElements = elementId ?
        [document.getElementById(elementId)] :
        document.querySelectorAll('.verification-timer');

    let timeLeft = 60; // 60 seconds cooldown

    timerElements.forEach(timerElement => {
        if (timerElement) {
            timerElement.textContent = `Resend code in ${timeLeft} seconds`;

            // Disable resend button if it exists
            const resendBtn = document.getElementById('resendCodeBtn');
            if (resendBtn) {
                resendBtn.disabled = true;
                resendBtn.classList.add('opacity-50');
            }
        }
    });

    const timerId = setInterval(() => {
        timeLeft--;

        timerElements.forEach(timerElement => {
            if (timerElement) {
                timerElement.textContent = `Resend code in ${timeLeft} seconds`;
            }
        });

        if (timeLeft <= 0) {
            clearInterval(timerId);
            timerElements.forEach(timerElement => {
                if (timerElement) {
                    timerElement.textContent = '';
                }
            });

            // Enable resend button if it exists
            const resendBtn = document.getElementById('resendCodeBtn');
            if (resendBtn) {
                resendBtn.disabled = false;
                resendBtn.classList.remove('opacity-50');
            }
        }
    }, 1000);
}

// Function to show notifications
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px',
        borderRadius: '5px',
        backgroundColor: type === 'success' ? '#4CAF50' : '#f44336',
        color: 'white',
        zIndex: '1000',
        transition: 'opacity 0.5s'
    });

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

// Function to toggle between login and register forms
function toggleForms() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const forgotPasswordContainer = document.getElementById('forgotPasswordContainer');
    // Use a more specific selector to target only the toggle button at the bottom
    const toggleButton = document.querySelector('button[onclick="toggleForms()"]');
    // Get the heading element
    const headingElement = document.querySelector('h2.font-medium.text-neutral-100.text-center');

    // Hide forgot password form if it's visible
    forgotPasswordContainer.classList.add('hidden');

    if (registerForm.classList.contains('hidden')) {
        // Switching to register form
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        toggleButton.textContent = 'Already have an account? Sign In';
        // Update heading for registration
        if (headingElement) {
            headingElement.textContent = 'Create a Kevko Systems Account';
        }
    } else {
        // Switching to login form
        registerForm.classList.add('hidden');
        loginForm.classList.remove('hidden');
        toggleButton.textContent = "Don't have an account? Sign Up";
        // Update heading for login
        if (headingElement) {
            headingElement.textContent = 'Sign In to Kevko Systems';
        }
    }
}

// Function to show forgot password form
function showForgotPassword() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const forgotPasswordContainer = document.getElementById('forgotPasswordContainer');
    // Get the heading element
    const headingElement = document.querySelector('h2.font-medium.text-neutral-100.text-center');

    // Show step 1 of password reset and hide other steps
    document.getElementById('resetStep1').classList.remove('hidden');
    document.getElementById('resetStep2').classList.add('hidden');
    document.getElementById('resetStep3').classList.add('hidden');

    // Hide login and register forms, show forgot password form
    loginForm.classList.add('hidden');
    registerForm.classList.add('hidden');
    forgotPasswordContainer.classList.remove('hidden');

    // Update heading for password reset
    if (headingElement) {
        headingElement.textContent = 'Reset Your Password';
    }
}

// Function to go back to login form
function backToLogin() {
    const loginForm = document.getElementById('loginForm');
    const forgotPasswordContainer = document.getElementById('forgotPasswordContainer');
    // Get the heading element
    const headingElement = document.querySelector('h2.font-medium.text-neutral-100.text-center');

    // Hide forgot password form, show login form
    forgotPasswordContainer.classList.add('hidden');
    loginForm.classList.remove('hidden');

    // Restore heading for login
    if (headingElement) {
        headingElement.textContent = 'Sign In to Kevko Systems';
    }
}

// Function to request password reset
async function requestPasswordReset() {
    const login = document.getElementById('resetLogin').value;

    if (!login) {
        showNotification('Please enter your username or email', 'error');
        return;
    }

    try {
        // Show loading state
        const resetButton = document.querySelector('#resetStep1 button');
        const originalButtonText = resetButton.innerHTML;
        resetButton.innerHTML = '<span class="animate-pulse">Sending...</span>';
        resetButton.disabled = true;

        const response = await fetch('/auth/request-password-reset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                login: login
            })
        });

        const data = await response.json();

        // Reset button state
        resetButton.innerHTML = originalButtonText;
        resetButton.disabled = false;

        if (response.ok) {
            // Store the email for later use
            window.resetEmail = data.email;

            // Move to step 2
            document.getElementById('resetStep1').classList.add('hidden');
            document.getElementById('resetStep2').classList.remove('hidden');

            showNotification('Verification code sent! Please check your email.', 'success');
        } else {
            showNotification(data.error || 'Failed to send verification code', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred while sending the verification code', 'error');

        // Reset button state
        const resetButton = document.querySelector('#resetStep1 button');
        resetButton.innerHTML = 'Send Verification Code';
        resetButton.disabled = false;
    }
}

// Function to reset password
async function resetPassword() {
    const verificationCode = document.getElementById('resetVerificationCode').value;
    const newPassword = document.getElementById('resetNewPassword').value;
    const confirmPassword = document.getElementById('resetConfirmPassword').value;

    if (!verificationCode) {
        showNotification('Please enter the verification code', 'error');
        return;
    }

    if (!newPassword) {
        showNotification('Please enter a new password', 'error');
        return;
    }

    if (newPassword !== confirmPassword) {
        showNotification('Passwords do not match', 'error');
        return;
    }

    try {
        // Show loading state
        const resetButton = document.querySelector('#resetStep2 button');
        const originalButtonText = resetButton.innerHTML;
        resetButton.innerHTML = '<span class="animate-pulse">Resetting...</span>';
        resetButton.disabled = true;

        const response = await fetch('/auth/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: window.resetEmail,
                verification_code: verificationCode,
                new_password: newPassword
            })
        });

        const data = await response.json();

        // Reset button state
        resetButton.innerHTML = originalButtonText;
        resetButton.disabled = false;

        if (response.ok) {
            // Move to step 3 (success)
            document.getElementById('resetStep2').classList.add('hidden');
            document.getElementById('resetStep3').classList.remove('hidden');
        } else {
            showNotification(data.error || 'Failed to reset password', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred while resetting the password', 'error');

        // Reset button state
        const resetButton = document.querySelector('#resetStep2 button');
        resetButton.innerHTML = 'Reset Password';
        resetButton.disabled = false;
    }
}

// Handle flash messages
document.addEventListener('DOMContentLoaded', () => {
    const messages = document.querySelectorAll('.flash-message');
    messages.forEach(message => {
        showNotification(message.textContent, message.classList.contains('success') ? 'success' : 'error');
        message.remove();
    });
})

// Variables to store login credentials during 2FA verification
let currentLoginAttempt = {
    login: '',
    password: '',
    next: ''
};

// Handle login form submission
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const loginInput = document.querySelector('#loginForm input[name="login"]').value;
    const passwordInput = document.querySelector('#loginForm input[name="password"]').value;
    const nextParam = new URLSearchParams(window.location.search).get('next') || '';

    // Store current login attempt
    currentLoginAttempt = {
        login: loginInput,
        password: passwordInput,
        next: nextParam
    };

    console.log('Login attempt:', {
        login: loginInput,
        passwordLength: passwordInput.length,
        next: nextParam
    });

    try {
        // First, check credentials and see if 2FA is required
        const response = await fetch('/auth/check-credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                login: loginInput,
                password: passwordInput
            })
        });

        const data = await response.json();

        if (!response.ok) {
            showNotification(data.error || 'Login failed', 'error');
            return;
        }

        if (data.two_factor_required) {
            // Show 2FA modal and start timer
            showTwoFactorModal();
            startVerificationTimer('verificationTimer');
        } else {
            // No 2FA required, proceed with login
            submitLoginForm();
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred during login', 'error');
    }
});

// Function to show the 2FA modal
function showTwoFactorModal() {
    const modal = document.getElementById('twoFactorModal');
    modal.classList.remove('hidden');
    document.getElementById('verificationCode').focus();

    // Set up event listeners for the modal
    document.getElementById('verifyCodeBtn').addEventListener('click', verifyCode);
    document.getElementById('resendCodeBtn').addEventListener('click', resendVerificationCode);

    // Also allow pressing Enter in the verification code input
    document.getElementById('verificationCode').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifyCode();
        }
    });
}

// Function to hide the 2FA modal
function hideTwoFactorModal() {
    const modal = document.getElementById('twoFactorModal');
    modal.classList.add('hidden');
}

// Function to verify the entered code
async function verifyCode() {
    const code = document.getElementById('verificationCode').value;

    if (!code) {
        showNotification('Please enter the verification code', 'error');
        return;
    }

    try {
        // Submit the login with the verification code
        submitLoginForm(code);
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred while verifying the code', 'error');
    }
}

// Function to resend the verification code
async function resendVerificationCode() {
    try {
        const response = await fetch('/auth/resend-verification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                login: currentLoginAttempt.login
            })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('Verification code resent! Please check your email.', 'success');
            startVerificationTimer('verificationTimer');
        } else {
            showNotification(data.error || 'Failed to resend verification code', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('An error occurred while resending the code', 'error');
    }
}

// Function to submit the login form
function submitLoginForm(verificationCode = '') {
    // Create a hidden form to submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/auth/login' + (currentLoginAttempt.next ? `?next=${encodeURIComponent(currentLoginAttempt.next)}` : '');
    form.style.display = 'none';

    // Add login field
    const loginField = document.createElement('input');
    loginField.type = 'text';
    loginField.name = 'login';
    loginField.value = currentLoginAttempt.login;
    form.appendChild(loginField);

    // Add password field
    const passwordField = document.createElement('input');
    passwordField.type = 'password';
    passwordField.name = 'password';
    passwordField.value = currentLoginAttempt.password;
    form.appendChild(passwordField);

    // Add verification code field if provided
    if (verificationCode) {
        const codeField = document.createElement('input');
        codeField.type = 'text';
        codeField.name = 'verification_code';
        codeField.value = verificationCode;
        form.appendChild(codeField);
    }

    // Add form to body and submit
    document.body.appendChild(form);
    form.submit();
}
