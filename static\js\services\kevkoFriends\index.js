/**
 * KevkoFriends Service
 * Provides friends and chat functionality
 */

// Service class definition
window.KevkoFriendsService = class KevkoFriendsService extends window.Service {
    constructor(config) {
        super(config);
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // No initialization needed for this service
    }

    /**
     * Render the service card
     * @returns {string} HTML for the service card
     */
    render() {
        try {
            console.log(`Rendering KevkoFriends service:`, {
                id: this.id,
                name: this.name,
                status: this.status,
                url: this.url,
                type: this.type
            });
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            // Define the action button - Go to Friends
            const actionButton = `
                <a href="/friends"
                   class="w-full bg-${this.color}-600 hover:bg-${this.color}-500 border border-${this.color}-500/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                    <span class="text-sm text-white">Go to ${this.name}</span>
                    <i data-lucide="arrow-right" class="h-4 w-4 ml-1 text-white"></i>
                </a>
            `;

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoFriends',
            name: 'Friends',
            description: 'Chat with friends and invite them to live sessions',
            icon: 'users-2',
            color: 'violet',
            category: 'Social',
            version: '1.0.0',
            author: 'Kevko',
            features: [
                'Chat with friends',
                'Friend management',
                'Invite friends to live sessions',
                'Real-time messaging'
            ]
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }
}


