/**
 * add-credits-modal-fix.js
 * A direct fix for the add credits modal functionality
 */

// Global function to show the add credits modal
window.openAddCreditsModal = function() {
    // Removed excessive logging

    // Get the modal element
    const modal = document.getElementById('addCreditsModal');
    if (!modal) {
        console.error('Add credits modal not found');
        return false;
    }

    // Reset form fields
    const userSearchInput = document.getElementById('creditUserSearch');
    if (userSearchInput) {
        userSearchInput.value = '';
    }

    const userSelect = document.getElementById('creditUser');
    if (userSelect) {
        userSelect.innerHTML = '<option value="">Select a user</option>';
    }

    const creditAmount = document.getElementById('creditAmount');
    if (creditAmount) {
        creditAmount.value = '10';
    }

    const creditReason = document.getElementById('creditReason');
    if (creditReason) {
        creditReason.value = 'Admin adjustment';
    }

    const searchResultsContainer = document.getElementById('userSearchResults');
    if (searchResultsContainer) {
        searchResultsContainer.innerHTML = '';
    }

    // Ensure the modal is a direct child of the body to avoid positioning issues
    if (modal.parentElement && modal.parentElement.id !== 'body') {
        document.body.appendChild(modal);
    }

    // Set proper positioning styles to ensure it's centered in the viewport
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.right = '0';
    modal.style.bottom = '0';
    modal.style.display = 'flex';
    modal.style.alignItems = 'center';
    modal.style.justifyContent = 'center';
    modal.style.zIndex = '9999';

    // Show the modal
    modal.classList.remove('hidden');

    // Initialize Lucide icons if available
    if (window.lucide) {
        lucide.createIcons({
            attrs: {
                class: ["h-5", "w-5"]
            },
            elements: [modal]
        });
    }

    return true;
};

// Global function to hide the add credits modal
window.closeAddCreditsModal = function() {
    // Removed excessive logging

    // Get the modal element
    const modal = document.getElementById('addCreditsModal');
    if (!modal) {
        console.error('Add credits modal not found');
        return false;
    }

    // Hide the modal
    modal.classList.add('hidden');
    modal.style.display = 'none';

    // Reset any inline styles that might interfere with future positioning
    modal.style.position = '';
    modal.style.top = '';
    modal.style.left = '';
    modal.style.right = '';
    modal.style.bottom = '';
    modal.style.alignItems = '';
    modal.style.justifyContent = '';

    return true;
};

// Update CreditManager to use our global function
document.addEventListener('DOMContentLoaded', function() {
    // Add the functions to CreditManager if it exists
    if (window.creditManager) {
        window.creditManager.openAddCreditsModal = window.openAddCreditsModal;
        window.creditManager.closeAddCreditsModal = window.closeAddCreditsModal;
    }

    // Update close buttons in the add credits modal
    const closeButtons = [
        document.getElementById('closeAddCreditsModal'),
        document.getElementById('cancelAddCredits')
    ];

    closeButtons.forEach(button => {
        if (button) {
            // Clone to remove existing listeners
            const newBtn = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newBtn, button);
            }

            // Add new event listener
            newBtn.addEventListener('click', function() {
                window.closeAddCreditsModal();
            });
        }
    });

    // Make sure the confirm button works
    const confirmBtn = document.getElementById('confirmAddCredits');
    if (confirmBtn) {
        // Clone to remove existing listeners
        const newBtn = confirmBtn.cloneNode(true);
        if (confirmBtn.parentNode) {
            confirmBtn.parentNode.replaceChild(newBtn, confirmBtn);
        }

        // Add new event listener
        newBtn.addEventListener('click', function() {
            if (window.creditManager && typeof window.creditManager.handleAddCredits === 'function') {
                window.creditManager.handleAddCredits();
            } else {
                alert('Credit manager not available. Please try again later.');
            }
        });
    }

    // Update the CreditNotification.js button to use our function
    const addCreditsBtn = document.getElementById('addCreditsBtn');
    if (addCreditsBtn) {
        // Clone to remove existing listeners
        const newBtn = addCreditsBtn.cloneNode(true);
        if (addCreditsBtn.parentNode) {
            addCreditsBtn.parentNode.replaceChild(newBtn, addCreditsBtn);
        }

        // Add new event listener
        newBtn.addEventListener('click', function() {
            // Hide the notification modal if it exists
            if (window.creditNotification) {
                window.creditNotification.hideModal();
            }

            // Show the add credits modal
            window.openAddCreditsModal();
        });
    }
});