"""
Template API Routes

This file serves as a template for creating new API endpoints.
It demonstrates best practices for API organization, error handling, and performance.
"""
from flask import request, jsonify
from flask_login import login_required, current_user
from utils.api_utils import create_api_blueprint, api_response, validate_json_payload, cache_control
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
import logging

# Create a blueprint for this API module
template_api = create_api_blueprint('template', __name__)

@template_api.route('/example', methods=['GET'])
@login_required
@check_service_access('template')
@api_response
@cache_control(max_age=60)  # Cache for 60 seconds
def get_example():
    """
    Example GET endpoint that returns a simple response.
    
    Returns:
        dict: A dictionary with example data
    """
    return {
        'message': 'This is an example API endpoint',
        'user': current_user.username
    }

@template_api.route('/example', methods=['POST'])
@login_required
@check_service_access('template')
@log_api_request('example_post', 'template')
@validate_json_payload('name', 'value')
@api_response
def post_example():
    """
    Example POST endpoint that processes a JSON payload.
    
    JSON Payload:
        name (str): A name parameter
        value (str): A value parameter
    
    Returns:
        dict: A dictionary with the processed data
    """
    data = request.json
    name = data.get('name')
    value = data.get('value')
    
    # Log the request
    logging.info(f"Processing example request: name={name}, value={value}")
    
    # Process the data (this is just an example)
    processed_value = value.upper()
    
    return {
        'message': 'Data processed successfully',
        'name': name,
        'processed_value': processed_value
    }

@template_api.route('/example/<item_id>', methods=['PUT'])
@login_required
@check_service_access('template')
@log_api_request('example_update', 'template')
@validate_json_payload('value')
@api_response
def update_example(item_id):
    """
    Example PUT endpoint that updates an item.
    
    Args:
        item_id (str): The ID of the item to update
    
    JSON Payload:
        value (str): The new value for the item
    
    Returns:
        dict: A dictionary with the updated item
    """
    data = request.json
    value = data.get('value')
    
    # Log the request
    logging.info(f"Updating example item {item_id} with value: {value}")
    
    # Update the item (this is just an example)
    return {
        'message': 'Item updated successfully',
        'item_id': item_id,
        'new_value': value
    }

@template_api.route('/example/<item_id>', methods=['DELETE'])
@login_required
@check_service_access('template')
@log_api_request('example_delete', 'template')
@api_response
def delete_example(item_id):
    """
    Example DELETE endpoint that deletes an item.
    
    Args:
        item_id (str): The ID of the item to delete
    
    Returns:
        dict: A dictionary with the result of the deletion
    """
    # Log the request
    logging.info(f"Deleting example item {item_id}")
    
    # Delete the item (this is just an example)
    return {
        'message': 'Item deleted successfully',
        'item_id': item_id
    }

# Error handling example
@template_api.errorhandler(404)
def not_found(error):
    """Handle 404 Not Found errors for this blueprint"""
    return jsonify({
        'success': False,
        'error': 'Not Found',
        'message': 'The requested resource was not found'
    }), 404