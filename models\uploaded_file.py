from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, BinaryField, Reference<PERSON>ield
from models.user import User
from datetime import datetime, timezone
import uuid
import base64
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ANSI color codes for terminal output
class TermColors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    RESET = '\033[0m'

class UploadedFile(Document):
    """Model for storing uploaded files"""
    file_id = StringField(required=True, unique=True, default=lambda: str(uuid.uuid4()))
    filename = StringField(required=True)
    content_type = StringField(required=True)
    data = BinaryField(required=True)  # Binary file data
    uploaded_by = ReferenceField(User, required=True)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))

    meta = {
        'collection': 'uploaded_files',
        'indexes': [
            'file_id',
            'uploaded_by',
            'created_at'
        ]
    }

    def to_dict(self):
        """Convert the model to a dictionary"""
        return {
            'file_id': self.file_id,
            'filename': self.filename,
            'content_type': self.content_type,
            'uploaded_by': str(self.uploaded_by.id) if self.uploaded_by else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'url': f"/api/upload/{self.file_id}"
        }

    def get_data_url(self):
        """Get the file data as a base64 data URL"""
        if not self.data:
            return None

        # Convert binary data to base64
        encoded_data = base64.b64encode(self.data).decode('utf-8')

        # Create data URL
        return f"data:{self.content_type};base64,{encoded_data}"

    @classmethod
    def delete_by_url(cls, url):
        """Delete a file by its URL

        Args:
            url (str): The URL of the file to delete (e.g., /api/upload/file_id)

        Returns:
            bool: True if the file was deleted, False otherwise
        """
        if not url:
            logger.warning("Attempted to delete file with empty URL")
            return False

        try:
            # Extract the file_id from the URL
            # URL format is typically /api/upload/file_id or /upload/file_id
            url = url.strip()
            print(f"{TermColors.RED}[FILE DELETION] Processing URL: {url}{TermColors.RESET}")
            
            # Handle different URL formats
            if '/api/upload/' in url:
                file_id = url.split('/api/upload/')[1]
            elif '/upload/' in url:
                file_id = url.split('/upload/')[1]
            else:
                parts = url.split('/')
                if len(parts) < 3:
                    print(f"{TermColors.RED}[FILE DELETION] Invalid file URL format: {url}{TermColors.RESET}")
                    logger.warning(f"Invalid file URL format: {url}")
                    return False
                file_id = parts[-1]  # Get the last part of the URL

            # Check if there are query parameters and remove them
            if '?' in file_id:
                file_id = file_id.split('?')[0]
                
            print(f"{TermColors.RED}[FILE DELETION] Extracted file ID: {file_id} from URL: {url}{TermColors.RESET}")
            logger.info(f"Attempting to delete file with ID: {file_id} from URL: {url}")

            # Find and delete the file
            file_obj = cls.objects(file_id=file_id).first()
            if file_obj:
                # Log file details before deletion
                logger.info(f"Found file to delete: {file_id}, filename: {file_obj.filename}, uploaded by: {file_obj.uploaded_by.username if file_obj.uploaded_by else 'unknown'}")
                
                # Delete the file
                file_obj.delete()
                # Use red color for the deletion log
                print(f"{TermColors.RED}[FILE DELETION] Successfully deleted file with ID: {file_id}{TermColors.RESET}")
                logger.info(f"Successfully deleted file with ID: {file_id}")
                return True
            else:
                print(f"{TermColors.RED}[FILE DELETION] File not found with ID: {file_id} from URL: {url}{TermColors.RESET}")
                logger.warning(f"File not found with ID: {file_id} from URL: {url}")
                return False
        except Exception as e:
            logger.error(f"Error deleting file by URL {url}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
