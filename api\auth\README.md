# Authentication API

This API provides endpoints for user authentication, registration, and password management.

## Endpoints

### POST /api/auth/register
- **Description**: Register a new user
- **Request Body**:
  ```json
  {
    "username": "username",
    "email": "<EMAIL>",
    "password": "password",
    "verification_code": "123456"
  }
  ```
- **Response**: User object with success message

### POST /api/auth/login
- **Description**: Log in a user
- **Request Body**:
  ```json
  {
    "login": "username_or_email",
    "password": "password",
    "verification_code": "123456" // Optional, required if 2FA is enabled
  }
  ```
- **Response**: User object with success message or 2FA requirement

### POST /api/auth/logout
- **Description**: Log out the current user
- **Authentication**: Required
- **Response**: Success message

### POST /api/auth/send-verification
- **Description**: Send verification code for registration or password reset
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "purpose": "register" // or "reset"
  }
  ```
- **Response**: Success message

### POST /api/auth/reset-password
- **Description**: Reset user password
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "verification_code": "123456",
    "new_password": "new_password"
  }
  ```
- **Response**: Success message

### GET /api/auth/check-auth
- **Description**: Check if user is authenticated
- **Response**: Authentication status and user object if authenticated

## Models

### User
- `id`: User ID
- `username`: Username
- `email`: Email address
- `password_hash`: Hashed password
- `is_admin`: Boolean indicating if the user is an admin
- `two_factor_enabled`: Boolean indicating if two-factor authentication is enabled
- `created_at`: Account creation timestamp

## Usage Example

```javascript
// Register a new user
const registerResponse = await fetch('/api/auth/send-verification', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    purpose: 'register'
  })
});

// After receiving verification code via email
const registerUserResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'username',
    email: '<EMAIL>',
    password: 'password',
    verification_code: '123456'
  })
});

// Login
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    login: 'username',
    password: 'password'
  })
});
const loginResult = await loginResponse.json();

// If 2FA is required
if (loginResult.two_factor_required) {
  // After receiving verification code via email
  const twoFactorResponse = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      login: 'username',
      password: 'password',
      verification_code: '123456'
    })
  });
}
```
