/**
 * AccessControl.js
 * Handles the UI and API interactions for service access control
 */
class AccessControl {
    constructor() {
        // Access Control Modal Elements - Step 1
        this.modal = document.getElementById('accessControlModal');
        this.searchInput = document.getElementById('accessControlUserSearch');
        this.searchResults = document.getElementById('accessControlSearchResults');
        this.cancelStep1Button = document.getElementById('cancelStep1Button');
        this.step1Content = document.getElementById('step1Content');
        this.step1Indicator = document.getElementById('step1Indicator');

        // Access Control Modal Elements - Step 2
        this.step2Content = document.getElementById('step2Content');
        this.step2Indicator = document.getElementById('step2Indicator');
        this.selectedUserAvatar = document.getElementById('selectedUserAvatar');
        this.selectedUserName = document.getElementById('selectedUserName');
        this.selectedUserEmail = document.getElementById('selectedUserEmail');
        this.serviceCheckboxes = document.querySelectorAll('.service-checkbox');
        this.serviceOptions = document.querySelectorAll('.service-option');
        this.backToStep1Button = document.getElementById('backToStep1Button');
        this.continueToStep3Button = document.getElementById('continueToStep3Button');

        // Access Control Modal Elements - Step 3
        this.step3Content = document.getElementById('step3Content');
        this.step3Indicator = document.getElementById('step3Indicator');
        this.confirmUserAvatar = document.getElementById('confirmUserAvatar');
        this.confirmUserName = document.getElementById('confirmUserName');
        this.confirmUserEmail = document.getElementById('confirmUserEmail');
        this.confirmServicesList = document.getElementById('confirmServicesList');
        this.backToStep2Button = document.getElementById('backToStep2Button');
        this.saveRestrictionButton = document.getElementById('saveRestrictionButton');

        // Common Modal Elements
        this.closeButton = document.getElementById('closeAccessControlModal');

        // Access Control Card Elements
        this.container = document.getElementById('accessControlContainer');
        this.addButton = document.getElementById('addAccessControlButton');
        this.viewAllButton = document.getElementById('viewAllAccessControlButton');

        // List Modal Elements
        this.listModal = document.getElementById('accessControlListModal');
        this.listContainer = document.getElementById('accessControlListContainer');
        this.closeListButton = document.getElementById('closeAccessControlListModal');

        // Selected user data
        this.selectedUser = null;

        // Current step
        this.currentStep = 1;
    }

    /**
     * Initialize the access control manager
     */
    async init() {
        // Initialize admin check if it doesn't exist
        if (!window.adminCheck) {
            console.log('AdminCheck not available in AccessControl, creating instance');
            window.adminCheck = new AdminCheck();
        }

        // Wait for admin check to initialize
        await window.adminCheck.init();

        // Add event listeners for add restriction modal
        if (this.addButton) {
            this.addButton.addEventListener('click', () => {
                // Check if user is an admin before showing modal
                if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
                    alert('Admin access required to manage service restrictions');
                    return;
                }
                this.showModal();
            });
        }

        // Common modal events
        if (this.closeButton) {
            this.closeButton.addEventListener('click', () => this.hideModal());
        }

        // Step 1 events
        if (this.searchInput) {
            this.searchInput.addEventListener('input', () => this.handleSearch());
        }

        if (this.cancelStep1Button) {
            this.cancelStep1Button.addEventListener('click', () => this.hideModal());
        }

        // Step 2 events
        if (this.backToStep1Button) {
            this.backToStep1Button.addEventListener('click', () => this.goToStep(1));
        }

        if (this.continueToStep3Button) {
            this.continueToStep3Button.addEventListener('click', () => this.goToStep(3));
        }

        // Add click events to service option cards
        if (this.serviceOptions) {
            this.serviceOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const service = option.getAttribute('data-service');
                    const checkbox = option.querySelector(`#${service}ServiceCheckbox`);
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;

                        // Update card styling
                        if (checkbox.checked) {
                            option.classList.add('border-cyan-500/70', 'bg-slate-600/70');
                            option.classList.remove('border-slate-600/30', 'bg-slate-700/50');
                        } else {
                            option.classList.remove('border-cyan-500/70', 'bg-slate-600/70');
                            option.classList.add('border-slate-600/30', 'bg-slate-700/50');
                        }
                    }
                });
            });
        }

        // Step 3 events
        if (this.backToStep2Button) {
            this.backToStep2Button.addEventListener('click', () => this.goToStep(2));
        }

        if (this.saveRestrictionButton) {
            this.saveRestrictionButton.addEventListener('click', () => this.saveRestriction());
        }

        // Add event listeners for list modal
        if (this.viewAllButton) {
            this.viewAllButton.addEventListener('click', () => {
                // Check if user is an admin before showing list modal
                if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
                    alert('Admin access required to view service restrictions');
                    return;
                }
                this.showListModal();
            });
        }

        if (this.closeListButton) {
            this.closeListButton.addEventListener('click', () => this.hideListModal());
        }

        // Load existing restrictions
        this.loadRestrictions();
    }

    /**
     * Show the access control modal
     */
    showModal() {
        if (!this.modal) return;

        // Reset form
        this.resetForm();

        // Show modal
        this.modal.classList.remove('hidden');

        // Debug DOM elements
        this.debugDOMElements();

        // Go to step 1
        this.goToStep(1);
    }

    /**
     * Debug DOM elements
     */
    debugDOMElements() {
        console.log('Debugging DOM elements:');
        console.log('Step 1 elements:');
        console.log('- modal:', this.modal);
        console.log('- searchInput:', this.searchInput);
        console.log('- searchResults:', this.searchResults);
        console.log('- step1Content:', this.step1Content);

        console.log('Step 2 elements:');
        console.log('- step2Content:', this.step2Content);
        console.log('- selectedUserAvatar:', this.selectedUserAvatar);
        console.log('- selectedUserName:', this.selectedUserName);
        console.log('- selectedUserEmail:', this.selectedUserEmail);

        // Re-initialize elements if they're null
        if (!this.selectedUserAvatar) {
            console.log('Re-initializing selectedUserAvatar');
            this.selectedUserAvatar = document.getElementById('selectedUserAvatar');
            console.log('After re-init:', this.selectedUserAvatar);
        }

        if (!this.selectedUserName) {
            console.log('Re-initializing selectedUserName');
            this.selectedUserName = document.getElementById('selectedUserName');
            console.log('After re-init:', this.selectedUserName);
        }

        if (!this.selectedUserEmail) {
            console.log('Re-initializing selectedUserEmail');
            this.selectedUserEmail = document.getElementById('selectedUserEmail');
            console.log('After re-init:', this.selectedUserEmail);
        }
    }

    /**
     * Hide the access control modal
     */
    hideModal() {
        if (!this.modal) return;
        this.modal.classList.add('hidden');
        this.resetForm();
    }

    /**
     * Reset the form
     */
    resetForm() {
        if (this.searchInput) this.searchInput.value = '';
        if (this.searchResults) this.searchResults.innerHTML = '';

        // Uncheck all service checkboxes
        this.serviceCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Reset service option cards
        this.serviceOptions.forEach(option => {
            option.classList.remove('border-cyan-500/70', 'bg-slate-600/70');
            option.classList.add('border-slate-600/30', 'bg-slate-700/50');
        });

        // Clear confirmation services list
        if (this.confirmServicesList) {
            this.confirmServicesList.innerHTML = '';
        }

        this.selectedUser = null;
        this.currentStep = 1;
    }

    /**
     * Go to a specific step in the modal
     * @param {number} step Step number (1, 2, or 3)
     */
    goToStep(step) {
        if (step < 1 || step > 3) return;

        // Hide all steps
        if (this.step1Content) this.step1Content.classList.add('hidden');
        if (this.step2Content) this.step2Content.classList.add('hidden');
        if (this.step3Content) this.step3Content.classList.add('hidden');

        // Reset step indicators
        if (this.step1Indicator) {
            this.step1Indicator.querySelector('div').classList.remove('bg-cyan-600');
            this.step1Indicator.querySelector('div').classList.add('bg-slate-700');
            this.step1Indicator.querySelector('span').classList.remove('text-slate-300');
            this.step1Indicator.querySelector('span').classList.add('text-slate-400');
        }

        if (this.step2Indicator) {
            this.step2Indicator.querySelector('div').classList.remove('bg-cyan-600');
            this.step2Indicator.querySelector('div').classList.add('bg-slate-700');
            this.step2Indicator.querySelector('span').classList.remove('text-slate-300');
            this.step2Indicator.querySelector('span').classList.add('text-slate-400');
        }

        if (this.step3Indicator) {
            this.step3Indicator.querySelector('div').classList.remove('bg-cyan-600');
            this.step3Indicator.querySelector('div').classList.add('bg-slate-700');
            this.step3Indicator.querySelector('span').classList.remove('text-slate-300');
            this.step3Indicator.querySelector('span').classList.add('text-slate-400');
        }

        // Show the selected step
        if (step === 1) {
            if (this.step1Content) this.step1Content.classList.remove('hidden');
            if (this.step1Indicator) {
                this.step1Indicator.querySelector('div').classList.remove('bg-slate-700');
                this.step1Indicator.querySelector('div').classList.add('bg-cyan-600');
                this.step1Indicator.querySelector('span').classList.remove('text-slate-400');
                this.step1Indicator.querySelector('span').classList.add('text-slate-300');
            }
        } else if (step === 2) {
            if (this.step2Content) this.step2Content.classList.remove('hidden');
            if (this.step2Indicator) {
                this.step2Indicator.querySelector('div').classList.remove('bg-slate-700');
                this.step2Indicator.querySelector('div').classList.add('bg-cyan-600');
                this.step2Indicator.querySelector('span').classList.remove('text-slate-400');
                this.step2Indicator.querySelector('span').classList.add('text-slate-300');
            }

            // Make sure user info is displayed if a user is selected
            if (this.selectedUser) {
                console.log('Updating user info in step 2 for:', this.selectedUser.username);

                // Re-initialize elements if needed
                if (!this.selectedUserAvatar) this.selectedUserAvatar = document.getElementById('selectedUserAvatar');
                if (!this.selectedUserName) this.selectedUserName = document.getElementById('selectedUserName');
                if (!this.selectedUserEmail) this.selectedUserEmail = document.getElementById('selectedUserEmail');

                // Update user info
                if (this.selectedUserAvatar) {
                    if (this.selectedUser.profile_picture) {
                        this.selectedUserAvatar.innerHTML = '';
                        const profileImg = document.createElement('img');
                        profileImg.src = this.selectedUser.profile_picture;
                        profileImg.alt = this.selectedUser.username;
                        profileImg.className = 'w-full h-full rounded-full object-cover';
                        this.selectedUserAvatar.appendChild(profileImg);
                    } else {
                        this.selectedUserAvatar.textContent = this.selectedUser.username ? this.selectedUser.username.substring(0, 2).toUpperCase() : 'U';
                    }
                }

                if (this.selectedUserName) {
                    this.selectedUserName.textContent = this.selectedUser.username;
                }

                if (this.selectedUserEmail) {
                    this.selectedUserEmail.textContent = this.selectedUser.email;
                }
            }
        } else if (step === 3) {
            // Prepare confirmation step
            this.prepareConfirmationStep();

            if (this.step3Content) this.step3Content.classList.remove('hidden');
            if (this.step3Indicator) {
                this.step3Indicator.querySelector('div').classList.remove('bg-slate-700');
                this.step3Indicator.querySelector('div').classList.add('bg-cyan-600');
                this.step3Indicator.querySelector('span').classList.remove('text-slate-400');
                this.step3Indicator.querySelector('span').classList.add('text-slate-300');
            }
        }

        this.currentStep = step;
    }

    /**
     * Prepare the confirmation step
     */
    prepareConfirmationStep() {
        if (!this.selectedUser) return;

        // Update user info
        if (this.confirmUserName) {
            this.confirmUserName.textContent = this.selectedUser.username;
        }

        if (this.confirmUserEmail) {
            this.confirmUserEmail.textContent = this.selectedUser.email;
        }

        if (this.confirmUserAvatar) {
            if (this.selectedUser.profile_picture) {
                // Clear any existing content
                this.confirmUserAvatar.textContent = '';

                // Create and add profile image
                const profileImg = document.createElement('img');
                profileImg.src = this.selectedUser.profile_picture;
                profileImg.alt = this.selectedUser.username;
                profileImg.className = 'w-full h-full rounded-full object-cover';
                this.confirmUserAvatar.appendChild(profileImg);
            } else {
                // Use initials
                this.confirmUserAvatar.textContent = this.selectedUser.username ? this.selectedUser.username.substring(0, 2).toUpperCase() : 'U';
            }
        }

        // Update services list
        if (this.confirmServicesList) {
            this.confirmServicesList.innerHTML = '';

            // Get selected services
            const selectedServices = [];
            this.serviceCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedServices.push(checkbox.value);
                }
            });

            if (selectedServices.length === 0) {
                this.confirmServicesList.innerHTML = `
                    <div class="text-xs text-slate-400 py-2">No services selected</div>
                `;
            } else {
                // Format services list
                const serviceNames = {
                    'chat': 'Chat',
                    'live': 'Live',
                    'spotify': 'KevkoFy',
                    'friends': 'Friends'
                };

                const serviceIcons = {
                    'chat': 'message-circle',
                    'live': 'video',
                    'spotify': 'music',
                    'friends': 'users'
                };

                const serviceColors = {
                    'chat': 'text-cyan-400',
                    'live': 'text-green-400',
                    'spotify': 'text-purple-400',
                    'friends': 'text-blue-400'
                };

                selectedServices.forEach(service => {
                    const serviceTag = document.createElement('div');
                    serviceTag.className = 'inline-flex items-center bg-slate-700/70 text-slate-300 text-xs px-2 py-1 rounded mr-1 mb-1';
                    serviceTag.innerHTML = `
                        <i data-lucide="${serviceIcons[service] || 'circle'}" class="h-3 w-3 ${serviceColors[service] || ''} mr-1"></i>
                        <span>${serviceNames[service] || service}</span>
                    `;

                    this.confirmServicesList.appendChild(serviceTag);
                });

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-3", "w-3"]
                        },
                        elements: [this.confirmServicesList]
                    });
                }
            }
        }
    }

    /**
     * Handle user search
     */
    handleSearch() {
        const query = this.searchInput.value.trim();

        if (query.length < 3) {
            this.searchResults.innerHTML = '';
            if (query.length > 0) {
                this.searchResults.innerHTML = `
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center text-slate-400">
                            <i data-lucide="info" class="h-4 w-4 mr-2"></i>
                            <span>Please enter at least 3 characters</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [this.searchResults]
                    });
                }
            }
            return;
        }

        // Show loading state
        this.searchResults.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400 text-sm">Searching...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-4", "w-4"]
                },
                elements: [this.searchResults]
            });
        }

        // Search users
        fetch(`/api/admin/search-users?query=${encodeURIComponent(query)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(users => {
                if (users.length === 0) {
                    this.searchResults.innerHTML = `
                        <div class="flex justify-center items-center py-4">
                            <div class="flex items-center text-slate-400">
                                <i data-lucide="user-x" class="h-4 w-4 mr-2"></i>
                                <span>No users found</span>
                            </div>
                        </div>
                    `;
                } else {
                    this.searchResults.innerHTML = '';

                    // Add each user to the results
                    users.forEach(user => {
                        const userItem = document.createElement('div');
                        userItem.className = 'flex items-center justify-between p-2 hover:bg-slate-700/50 rounded cursor-pointer';

                        // Determine avatar content - profile picture or initials
                        let avatarContent;
                        if (user.profile_picture) {
                            avatarContent = `<img src="${user.profile_picture}" alt="${user.username}" class="w-8 h-8 rounded-full object-cover">`;
                        } else {
                            avatarContent = `<div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center text-white text-sm font-medium">
                                ${user.username ? user.username.substring(0, 2).toUpperCase() : 'U'}
                            </div>`;
                        }

                        userItem.innerHTML = `
                            <div class="flex items-center">
                                ${avatarContent}
                                <div class="ml-2">
                                    <div class="text-sm font-medium text-slate-200">${user.username}</div>
                                    <div class="text-xs text-slate-400">${user.email}</div>
                                </div>
                            </div>
                            <button class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded">
                                Select
                            </button>
                        `;

                        // Add click event to select user
                        userItem.addEventListener('click', () => {
                            this.selectUser(user);
                        });

                        this.searchResults.appendChild(userItem);
                    });
                }

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [this.searchResults]
                    });
                }
            })
            .catch(error => {
                console.error('Error searching users:', error);
                this.searchResults.innerHTML = `
                    <div class="flex justify-center items-center py-4">
                        <div class="flex items-center text-red-400">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2"></i>
                            <span>Error searching users</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [this.searchResults]
                    });
                }
            });
    }

    /**
     * Select a user
     * @param {Object} user User object
     */
    selectUser(user) {
        console.log('Selected user:', user);
        this.selectedUser = user;

        // Update selected user info
        if (this.selectedUserAvatar) {
            console.log('Updating avatar for user:', user.username);
            if (user.profile_picture) {
                // Clear any existing content
                this.selectedUserAvatar.textContent = '';

                // Create and add profile image
                const profileImg = document.createElement('img');
                profileImg.src = user.profile_picture;
                profileImg.alt = user.username;
                profileImg.className = 'w-full h-full rounded-full object-cover';
                this.selectedUserAvatar.appendChild(profileImg);
            } else {
                // Use initials
                this.selectedUserAvatar.textContent = user.username ? user.username.substring(0, 2).toUpperCase() : 'U';
            }
        } else {
            console.error('selectedUserAvatar element not found');
        }

        if (this.selectedUserName) {
            console.log('Setting username:', user.username);
            this.selectedUserName.textContent = user.username;
        } else {
            console.error('selectedUserName element not found');
        }

        if (this.selectedUserEmail) {
            console.log('Setting email:', user.email);
            this.selectedUserEmail.textContent = user.email;
        } else {
            console.error('selectedUserEmail element not found');
        }

        // Clear search results
        if (this.searchResults) {
            this.searchResults.innerHTML = '';
        }

        // Clear search input
        if (this.searchInput) {
            this.searchInput.value = '';
        }

        // Check if user already has restrictions
        this.checkExistingRestrictions(user.email);

        // Go to step 2
        this.goToStep(2);

        // Force update the user info after a short delay
        setTimeout(() => {
            if (this.selectedUserAvatar && this.selectedUserName && this.selectedUserEmail) {
                console.log('Forcing update of user info after delay');

                // Update avatar again
                if (user.profile_picture) {
                    this.selectedUserAvatar.innerHTML = '';
                    const profileImg = document.createElement('img');
                    profileImg.src = user.profile_picture;
                    profileImg.alt = user.username;
                    profileImg.className = 'w-full h-full rounded-full object-cover';
                    this.selectedUserAvatar.appendChild(profileImg);
                } else {
                    this.selectedUserAvatar.textContent = user.username ? user.username.substring(0, 2).toUpperCase() : 'U';
                }

                // Update name and email again
                this.selectedUserName.textContent = user.username;
                this.selectedUserEmail.textContent = user.email;
            }
        }, 100);
    }

    /**
     * Check if user already has restrictions
     * @param {string} email User email
     */
    checkExistingRestrictions(email) {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping checkExistingRestrictions - user is not an admin');
            return;
        }

        // Reset all checkboxes first
        this.serviceCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        fetch(`/api/admin/restricted-users`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                // Find the user in the restrictions list
                const userRestriction = restrictions.find(restriction => restriction.email === email);

                if (userRestriction) {
                    // Update checkboxes based on existing restrictions
                    this.serviceCheckboxes.forEach(checkbox => {
                        checkbox.checked = userRestriction.restricted_services.includes(checkbox.value);
                    });
                }
            })
            .catch(error => {
                console.error('Error checking existing restrictions:', error);
            });
    }

    /**
     * Save the restriction
     */
    saveRestriction() {
        if (!this.selectedUser) {
            alert('Please select a user first');
            return;
        }

        // Get selected services
        const selectedServices = [];
        this.serviceCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedServices.push(checkbox.value);
            }
        });

        if (selectedServices.length === 0) {
            alert('Please select at least one service to restrict');
            return;
        }

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            alert('Admin access required to manage service restrictions');
            return;
        }

        // Format service names for success message
        const serviceNames = selectedServices.map(service => {
            const names = {
                'chat': 'Chat',
                'live': 'Live',
                'spotify': 'KevkoFy',
                'friends': 'Friends'
            };
            return names[service] || service;
        }).join(', ');

        // Show loading state on the button
        if (this.saveRestrictionButton) {
            this.saveRestrictionButton.innerHTML = `
                <i data-lucide="loader" class="h-4 w-4 animate-spin mr-1"></i>
                Applying...
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [this.saveRestrictionButton]
                });
            }

            // Disable the button
            this.saveRestrictionButton.disabled = true;
        }

        // Save restriction
        fetch('/api/admin/restrict-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: this.selectedUser.email,
                services: selectedServices
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Restriction saved:', data);

                // Show success message with toast notification
                const successToast = document.createElement('div');
                successToast.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center z-50';
                successToast.innerHTML = `
                    <i data-lucide="check-circle" class="h-5 w-5 mr-2"></i>
                    <div>
                        <div class="font-medium">Success!</div>
                        <div class="text-sm">${this.selectedUser.username} has been restricted from accessing: ${serviceNames}</div>
                    </div>
                `;

                document.body.appendChild(successToast);

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-5", "w-5"]
                        },
                        elements: [successToast]
                    });
                }

                // Remove the toast after 5 seconds
                setTimeout(() => {
                    successToast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                    setTimeout(() => {
                        document.body.removeChild(successToast);
                    }, 500);
                }, 5000);

                // Hide modal
                this.hideModal();

                // Reload restrictions
                this.loadRestrictions();
            })
            .catch(error => {
                console.error('Error saving restriction:', error);

                // Reset button
                if (this.saveRestrictionButton) {
                    this.saveRestrictionButton.innerHTML = 'Apply Restrictions';
                    this.saveRestrictionButton.disabled = false;
                }

                // Show error message
                alert('Error saving restriction. Please try again.');
            });
    }

    /**
     * Show the list modal
     */
    showListModal() {
        if (!this.listModal) return;

        // Load restrictions for the list
        this.loadRestrictionsForList();

        // Show modal
        this.listModal.classList.remove('hidden');
    }

    /**
     * Hide the list modal
     */
    hideListModal() {
        if (!this.listModal) return;
        this.listModal.classList.add('hidden');
    }

    /**
     * Load restrictions for the list modal
     */
    loadRestrictionsForList() {
        if (!this.listContainer) return;

        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            this.listContainer.innerHTML = `
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center text-amber-400">
                        <i data-lucide="shield-alert" class="h-5 w-5 mr-2"></i>
                        <span>Admin access required to view service restrictions</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-5", "w-5"]
                    },
                    elements: [this.listContainer]
                });
            }
            return;
        }

        // Show loading state
        this.listContainer.innerHTML = `
            <div class="flex justify-center items-center py-8">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400">Loading restrictions...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-5", "w-5"]
                },
                elements: [this.listContainer]
            });
        }

        // Load restrictions
        fetch('/api/admin/restricted-users')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                if (restrictions.length === 0) {
                    this.listContainer.innerHTML = `
                        <div class="flex justify-center items-center py-8">
                            <div class="flex items-center text-slate-400">
                                <i data-lucide="info" class="h-5 w-5 mr-2"></i>
                                <span>No service restrictions found</span>
                            </div>
                        </div>
                    `;
                } else {
                    this.listContainer.innerHTML = '';

                    // Add each restriction to the list
                    restrictions.forEach(restriction => {
                        const restrictionItem = document.createElement('div');
                        restrictionItem.className = 'bg-slate-800/50 p-4 rounded-lg border border-slate-700/50 mb-3';

                        // Format services list
                        const servicesList = restriction.restricted_services.map(service => {
                            const serviceNames = {
                                'chat': 'Chat',
                                'live': 'Live',
                                'spotify': 'KevkoFy',
                                'friends': 'Friends'
                            };

                            return `<span class="inline-block bg-slate-700/70 text-slate-300 text-xs px-2 py-1 rounded mr-1 mb-1">${serviceNames[service] || service}</span>`;
                        }).join('');

                        // Determine avatar content - profile picture or initials
                        let avatarContent;
                        if (restriction.profile_picture) {
                            avatarContent = `<img src="${restriction.profile_picture}" alt="${restriction.username}" class="w-10 h-10 rounded-full object-cover">`;
                        } else {
                            avatarContent = `<div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center text-white text-sm font-medium">
                                ${restriction.username ? restriction.username.substring(0, 2).toUpperCase() : 'U'}
                            </div>`;
                        }

                        restrictionItem.innerHTML = `
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="mr-3">
                                        ${avatarContent}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-slate-200">${restriction.username}</div>
                                        <div class="text-xs text-slate-400">${restriction.email}</div>
                                    </div>
                                </div>
                                <button class="remove-restriction-btn text-xs bg-slate-700 hover:bg-red-900/50 text-slate-300 hover:text-red-300 px-2 py-1 rounded" data-email="${restriction.email}">
                                    <i data-lucide="trash-2" class="h-3 w-3"></i>
                                </button>
                            </div>
                            <div class="text-xs text-slate-400 mb-2">Restricted from:</div>
                            <div class="flex flex-wrap">
                                ${servicesList}
                            </div>
                        `;

                        // Add click event to remove button
                        const removeButton = restrictionItem.querySelector('.remove-restriction-btn');
                        if (removeButton) {
                            removeButton.addEventListener('click', (e) => {
                                e.stopPropagation();
                                this.removeRestriction(restriction.email);
                            });
                        }

                        this.listContainer.appendChild(restrictionItem);
                    });
                }

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-3", "w-3"]
                        },
                        elements: [this.listContainer]
                    });
                }
            })
            .catch(error => {
                console.error('Error loading restrictions:', error);
                this.listContainer.innerHTML = `
                    <div class="flex justify-center items-center py-8">
                        <div class="flex items-center text-red-400">
                            <i data-lucide="alert-circle" class="h-5 w-5 mr-2"></i>
                            <span>Error loading restrictions</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-5", "w-5"]
                        },
                        elements: [this.listContainer]
                    });
                }
            });
    }

    /**
     * Remove a restriction
     * @param {string} email User email
     */
    removeRestriction(email) {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            alert('Admin access required to remove service restrictions');
            return;
        }

        if (!confirm('Are you sure you want to remove all service restrictions for this user?')) {
            return;
        }

        // Remove restriction
        fetch(`/api/admin/restricted-users/${encodeURIComponent(email)}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Restriction removed:', data);

                // Reload restrictions
                this.loadRestrictionsForList();
                this.loadRestrictions();
            })
            .catch(error => {
                console.error('Error removing restriction:', error);
                alert('Error removing restriction. Please try again.');
            });
    }

    /**
     * Load existing service restrictions
     */
    loadRestrictions() {
        // Check if user is an admin before making the API request
        if (window.adminCheck && !window.adminCheck.isUserAdmin()) {
            console.log('Skipping loadRestrictions - user is not an admin');

            // Clear container except for the add button
            const addButton = this.addButton;
            if (this.container) {
                this.container.innerHTML = '';

                // Add a message for non-admin users
                const messageDiv = document.createElement('div');
                messageDiv.className = 'text-center text-amber-400 text-xs py-2';
                messageDiv.innerHTML = 'Admin access required to view service restrictions';
                this.container.appendChild(messageDiv);

                // Add the button at the end
                if (addButton) {
                    this.container.appendChild(addButton);
                }
            }
            return;
        }

        if (!this.container) return;

        // Show loading state
        this.container.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400 text-sm">Loading restrictions...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-4", "w-4"]
                },
                elements: [this.container]
            });
        }

        // Load restrictions
        fetch('/api/admin/restricted-users')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(restrictions => {
                // Clear container
                this.container.innerHTML = '';

                // Add view all button
                if (this.viewAllButton) {
                    this.container.appendChild(this.viewAllButton);
                }

                // Add add button
                if (this.addButton) {
                    this.container.appendChild(this.addButton);
                }

                if (restrictions.length === 0) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'text-center text-slate-400 text-xs py-2 mt-2';
                    messageDiv.innerHTML = 'No service restrictions found';
                    this.container.appendChild(messageDiv);
                } else {
                    // Add each restriction to the container
                    restrictions.forEach(restriction => {
                        const restrictionItem = document.createElement('div');
                        restrictionItem.className = 'flex items-center justify-between bg-slate-800/50 p-2 rounded mb-2 mt-2';

                        // Format services list
                        const servicesList = restriction.restricted_services.map(service => {
                            const serviceNames = {
                                'chat': 'Chat',
                                'live': 'Live',
                                'spotify': 'KevkoFy',
                                'friends': 'Friends'
                            };

                            return serviceNames[service] || service;
                        }).join(', ');

                        // Determine avatar content - profile picture or initials
                        let avatarContent;
                        if (restriction.profile_picture) {
                            avatarContent = `<img src="${restriction.profile_picture}" alt="${restriction.username}" class="w-6 h-6 rounded-full object-cover mr-2">`;
                        } else {
                            avatarContent = `<div class="w-6 h-6 rounded-full bg-slate-700 flex items-center justify-center text-white text-xs font-medium mr-2">
                                ${restriction.username ? restriction.username.substring(0, 2).toUpperCase() : 'U'}
                            </div>`;
                        }

                        restrictionItem.innerHTML = `
                            <div class="flex items-center">
                                ${avatarContent}
                                <div>
                                    <div class="text-xs text-slate-300">${restriction.username}</div>
                                    <div class="text-xs text-slate-400">${restriction.email}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-slate-300">Restricted from: ${servicesList}</div>
                            </div>
                        `;

                        this.container.appendChild(restrictionItem);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading restrictions:', error);

                // Clear container
                this.container.innerHTML = '';

                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-center text-red-400 text-xs py-2';
                errorDiv.innerHTML = 'Error loading service restrictions';
                this.container.appendChild(errorDiv);

                // Add add button
                if (this.addButton) {
                    this.container.appendChild(this.addButton);
                }
            });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Create and initialize access control manager
    window.accessControl = new AccessControl();
    window.accessControl.init();
});
