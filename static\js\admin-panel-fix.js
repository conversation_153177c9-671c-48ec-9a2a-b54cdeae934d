/**
 * Fix for the admin panel settings issue
 * This script ensures that when returning from the admin panel to the dashboard,
 * the settings tabs are properly reset and only the active tab's content is shown.
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel settings fix script loaded');
    
    // Function to fix settings tabs when returning from admin panel
    function fixSettingsTabs() {
        console.log('Fixing settings tabs after admin panel navigation');
        
        // Wait for dashboard to be initialized
        if (window.dashboard) {
            // Reset settings tabs
            window.dashboard.resetSettingsTabs();
            
            // Fix settings sections visibility
            window.dashboard.fixSettingsSections();
        }
    }
    
    // Check if we're returning from admin panel
    // This can be detected by checking the referrer URL
    const referrer = document.referrer;
    if (referrer && referrer.includes('/admin')) {
        console.log('Detected return from admin panel');
        
        // Wait a bit for the dashboard to initialize
        setTimeout(fixSettingsTabs, 500);
    }
    
    // Also set up a MutationObserver to detect when the settings view becomes visible
    const settingsView = document.getElementById('settingsView');
    if (settingsView) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'class' && 
                    !settingsView.classList.contains('hidden')) {
                    
                    console.log('Settings view became visible, fixing tabs');
                    fixSettingsTabs();
                }
            });
        });
        
        observer.observe(settingsView, { attributes: true });
    }
});
