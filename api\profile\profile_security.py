from flask import jsonify, request
from flask_login import login_required, current_user
from models.login_history import LoginHistory
import logging
from . import profile_api

@profile_api.route('/enable-two-factor', methods=['POST'])
@login_required
def enable_two_factor():
    """Enable two-factor authentication for the current user"""
    try:
        current_user.two_factor_enabled = True
        current_user.save()

        return jsonify({
            'message': 'Two-factor authentication enabled',
            'two_factor_enabled': True
        })
    except Exception as e:
        logging.error(f"Error enabling two-factor: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/disable-two-factor', methods=['POST'])
@login_required
def disable_two_factor():
    """Disable two-factor authentication for the current user"""
    try:
        current_user.two_factor_enabled = False
        current_user.save()

        return jsonify({
            'message': 'Two-factor authentication disabled',
            'two_factor_enabled': False
        })
    except Exception as e:
        logging.error(f"Error disabling two-factor: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/toggle-two-factor', methods=['POST'])
@login_required
def toggle_two_factor():
    """Toggle two-factor authentication for the current user"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', False)

        # Update two-factor setting
        current_user.two_factor_enabled = enabled
        current_user.save()

        return jsonify({
            'message': f'Two-factor authentication {"enabled" if enabled else "disabled"}',
            'two_factor_enabled': enabled
        })
    except Exception as e:
        logging.error(f"Error toggling two-factor: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/login-history', methods=['GET'])
@login_required
def get_login_history():
    """Get login history for the current user"""
    try:
        # Get limit parameter (default: 10)
        limit = request.args.get('limit', 10, type=int)

        # Get login history
        login_history = LoginHistory.objects(user=current_user.id).order_by('-login_time').limit(limit)

        # Convert to list of dictionaries
        history_list = []
        for entry in login_history:
            history_dict = entry.to_dict()

            # Mark current session (first entry is the most recent)
            if history_list == []:  # First entry is the most recent (current session)
                history_dict['is_current'] = True

            history_list.append(history_dict)

        return jsonify({
            'success': True,
            'login_history': history_list
        })
    except Exception as e:
        logging.error(f"Error getting login history: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500