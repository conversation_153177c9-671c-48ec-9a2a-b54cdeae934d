from flask import jsonify, request
from . import spotify_api
from .services.spotify_client import get_spotify_client
from .decorators import require_auth
import logging

@spotify_api.route('/queue')
@require_auth
def get_queue():
    """Get current playback queue"""
    try:
        sp = get_spotify_client()
        queue = sp.queue()
        return jsonify(queue)
    except Exception as e:
        logging.error(f"Error getting queue: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/add-to-queue', methods=['POST'])
@require_auth
def add_to_queue():
    """Add a track to the playback queue"""
    try:
        sp = get_spotify_client()
        data = request.json
        uri = data.get('uri')

        if not uri:
            return jsonify({'error': 'uri is required'}), 400

        sp.add_to_queue(uri=uri)
        return jsonify({'success': True})
    except Exception as e:
        logging.error(f"Error adding to queue: {str(e)}")
        return jsonify({'error': str(e)}), 500
