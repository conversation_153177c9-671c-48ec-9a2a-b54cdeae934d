<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Preview</title>
    <style>
        :root {
            --profile-bg-color: {{ temp_profile.background_color }};
            --profile-text-color: {{ temp_profile.text_color }};
            --profile-accent-color: {{ temp_profile.accent_color }};
            --profile-container-color: {{ temp_profile.profile_container_color }};
            --profile-header-color: {{ temp_profile.profile_header_color }};
            --profile-about-color: {{ temp_profile.profile_about_color }};
            --profile-content-color: {{ temp_profile.profile_content_color }};
            --profile-friends-color: {{ temp_profile.profile_friends_color }};
            --profile-font: {{ temp_profile.font_family }};
            --profile-heading-font: {{ temp_profile.heading_font }};
        }
        
        body {
            font-family: var(--profile-font);
            color: var(--profile-text-color);
            background-color: var(--profile-bg-color);
            margin: 0;
            padding: 0;
        }
        
        {% if temp_profile.background_type == 'gradient' %}
        body {
            background: {{ temp_profile.background_gradient }};
        }
        {% elif temp_profile.background_type == 'image' %}
        body {
            background-image: url('{{ temp_profile.background_image }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }
        {% elif temp_profile.background_type == 'pattern' %}
        body {
            background-image: url('{{ temp_profile.background_pattern }}');
            background-repeat: repeat;
        }
        {% endif %}
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--profile-heading-font);
            color: var(--profile-accent-color);
        }
        
        .profile-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 12px;
            background-color: var(--profile-header-color, rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(10px);
        }
        
        .profile-picture {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--profile-accent-color);
        }
        
        .profile-info {
            margin-left: 1rem;
        }
        
        .profile-username {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .profile-display-name {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0.25rem 0;
        }
        
        .profile-joined {
            font-size: 0.8rem;
            opacity: 0.6;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            background-color: var(--profile-content-color, transparent);
            padding: 1rem;
            border-radius: 12px;
        }
        
        .profile-section {
            background-color: var(--profile-about-color, rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .profile-section.friends-section {
            background-color: var(--profile-friends-color, rgba(255, 255, 255, 0.1));
        }
        
        .profile-section-title {
            font-size: 1.2rem;
            margin-top: 0;
            margin-bottom: 0.5rem;
            border-bottom: 2px solid var(--profile-accent-color);
            padding-bottom: 0.25rem;
        }
        
        /* Layout specific styles */
        {% if temp_profile.layout_type == 'compact' %}
        .profile-content {
            grid-template-columns: 1fr;
        }
        {% elif temp_profile.layout_type == 'expanded' %}
        .profile-content {
            grid-template-columns: repeat(3, 1fr);
        }
        {% elif temp_profile.layout_type == 'minimal' %}
        .profile-section {
            background-color: transparent;
            backdrop-filter: none;
            padding: 0;
        }
        {% endif %}
    </style>
</head>
<body>
    <div class="profile-container">
        <div class="profile-header">
            <img src="{{ user.profile_picture or '/static/img/default-avatar.png' }}" alt="{{ user.username }}" class="profile-picture">
            <div class="profile-info">
                <h1 class="profile-username">{{ user.display_name or user.username }}</h1>
                {% if user.display_name %}
                <p class="profile-display-name">@{{ user.username }}</p>
                {% endif %}
                <p class="profile-joined">Joined {{ user.created_at.strftime('%B %Y') }}</p>
            </div>
        </div>
        
        <div class="profile-content" style="background-color: var(--profile-content-color, transparent)">
            <div class="profile-section">
                <h2 class="profile-section-title">About</h2>
                <p>Sample about content for preview</p>
            </div>
            
            <div class="profile-section">
                <h2 class="profile-section-title">Stats</h2>
                <p>Sample stats for preview</p>
            </div>
            
            <div class="profile-section">
                <h2 class="profile-section-title">Friends</h2>
                <p>Sample friends for preview</p>
            </div>
            
            <div class="profile-section">
                <h2 class="profile-section-title">Recent Activity</h2>
                <p>Sample activity for preview</p>
            </div>
        </div>
    </div>
</body>
</html>