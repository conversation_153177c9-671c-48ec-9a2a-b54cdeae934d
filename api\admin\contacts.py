from flask import jsonify, request
from flask_login import login_required
from models.user import User
from . import admin_api
from .routes import admin_required, super_admin_required
import logging

@admin_api.route('/contacts', methods=['GET'])
def admin_contacts():
    """Get admin contact information for the contact page"""
    try:
        # Only get admins that should be shown on the contacts page
        admins = User.objects(is_admin=True, show_on_contacts=True).only('email', 'username', 'profile_picture', 'is_super_admin')
        primary_admins = ['<EMAIL>', '<EMAIL>']

        # Sort admins to put primary admins first
        sorted_admins = sorted(admins, key=lambda admin: 0 if admin.email in primary_admins else 1)

        return jsonify([{
            'email': admin.email,
            'username': admin.username,
            'name': admin.username.split('@')[0] if '@' in admin.username else admin.username,
            'is_primary': admin.email in primary_admins,
            'is_super_admin': admin.is_super_admin,
            'profile_picture': admin.profile_picture if hasattr(admin, 'profile_picture') else None
        } for admin in sorted_admins])
    except Exception as e:
        logging.error(f"Error in admin_contacts: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/toggle-contact-visibility', methods=['POST'])
@super_admin_required
def toggle_contact_visibility():
    """Toggle whether an admin appears on the contact page"""
    try:
        data = request.get_json()
        admin_email = data.get('email')
        show_on_contacts = data.get('show_on_contacts')

        if not admin_email:
            return jsonify({'error': 'Admin email is required'}), 400

        if show_on_contacts is None:
            return jsonify({'error': 'Show on contacts value is required'}), 400

        # Find the admin user
        admin = User.objects(email=admin_email, is_admin=True).first()
        if not admin:
            return jsonify({'error': 'Admin not found'}), 404

        # Update the visibility
        admin.show_on_contacts = show_on_contacts
        admin.save()

        return jsonify({
            'message': f"Admin {admin_email} {'will' if show_on_contacts else 'will not'} be shown on contacts page",
            'email': admin.email,
            'show_on_contacts': admin.show_on_contacts
        })
    except Exception as e:
        logging.error(f"Error in toggle_contact_visibility: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
