from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from models.user import User
from datetime import datetime, timezone

class LoginHistory(Document):
    """Model for storing user login history"""
    user = ReferenceField(User, required=True)
    login_time = DateTimeField(default=lambda: datetime.now(timezone.utc))
    ip_address = StringField()
    user_agent = StringField()
    device_info = DictField()  # Store parsed user agent info (browser, os, device type)
    location = StringField()   # Optional: general location based on IP (country/city)
    
    meta = {
        'collection': 'login_history',
        'indexes': [
            'user',
            'login_time'
        ],
        'ordering': ['-login_time']  # Default ordering by login time descending
    }
    
    @classmethod
    def log_login(cls, user, request):
        """
        Log a new login event
        
        Args:
            user: User object
            request: Flask request object
        
        Returns:
            LoginHistory: The created login history entry
        """
        try:
            # Extract IP address
            ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)
            if ip_address:
                # X-Forwarded-For can contain multiple IPs, take the first one
                ip_address = ip_address.split(',')[0].strip()
            
            # Extract user agent
            user_agent = request.headers.get('User-Agent', '')
            
            # Parse user agent to get device info
            device_info = cls.parse_user_agent(user_agent)
            
            # Create and save login history
            login_history = cls(
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                device_info=device_info
            )
            login_history.save()
            
            # Update user's last login time
            user.last_login = datetime.now(timezone.utc)
            user.save()
            
            return login_history
        except Exception as e:
            print(f"Error logging login history: {str(e)}")
            return None
    
    @classmethod
    def parse_user_agent(cls, user_agent_string):
        """
        Parse user agent string to extract device information
        
        Args:
            user_agent_string: Browser user agent string
            
        Returns:
            dict: Dictionary containing device information
        """
        device_info = {
            'browser': 'Unknown',
            'browser_version': '',
            'os': 'Unknown',
            'os_version': '',
            'device_type': 'Unknown'
        }
        
        if not user_agent_string:
            return device_info
        
        # Simple parsing logic - can be enhanced with a proper user-agent parsing library
        user_agent = user_agent_string.lower()
        
        # Detect browser
        if 'firefox' in user_agent:
            device_info['browser'] = 'Firefox'
        elif 'chrome' in user_agent and 'edg' not in user_agent and 'opr' not in user_agent:
            device_info['browser'] = 'Chrome'
        elif 'safari' in user_agent and 'chrome' not in user_agent:
            device_info['browser'] = 'Safari'
        elif 'edg' in user_agent:
            device_info['browser'] = 'Edge'
        elif 'opr' in user_agent or 'opera' in user_agent:
            device_info['browser'] = 'Opera'
        elif 'msie' in user_agent or 'trident' in user_agent:
            device_info['browser'] = 'Internet Explorer'
        
        # Detect OS
        if 'windows' in user_agent:
            device_info['os'] = 'Windows'
            if 'windows nt 10' in user_agent:
                device_info['os_version'] = '10'
            elif 'windows nt 6.3' in user_agent:
                device_info['os_version'] = '8.1'
            elif 'windows nt 6.2' in user_agent:
                device_info['os_version'] = '8'
            elif 'windows nt 6.1' in user_agent:
                device_info['os_version'] = '7'
        elif 'macintosh' in user_agent or 'mac os' in user_agent:
            device_info['os'] = 'macOS'
        elif 'android' in user_agent:
            device_info['os'] = 'Android'
        elif 'ios' in user_agent or 'iphone' in user_agent or 'ipad' in user_agent:
            device_info['os'] = 'iOS'
        elif 'linux' in user_agent:
            device_info['os'] = 'Linux'
        
        # Detect device type
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            device_info['device_type'] = 'Mobile'
        elif 'ipad' in user_agent or 'tablet' in user_agent:
            device_info['device_type'] = 'Tablet'
        else:
            device_info['device_type'] = 'Desktop'
        
        return device_info
    
    def to_dict(self):
        """Convert login history to dictionary"""
        return {
            'id': str(self.id),
            'login_time': self.login_time.isoformat(),
            'ip_address': self.ip_address,
            'device_info': self.device_info,
            'location': self.location,
            'is_current': False  # Will be set to True for current session in the API
        }
