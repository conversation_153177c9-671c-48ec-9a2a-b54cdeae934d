/**
 * KevkoFyService.js
 * Custom implementation of the KevkoFy service
 */
window.KevkoFyService = class KevkoFyService extends window.Service {
    constructor(config) {
        super(config);
        this.playlists = [];
        this.currentTrack = null;
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // In a real implementation, this might fetch data from an API
        this.playlists = [
            { id: 1, name: 'Chill Vibes', tracks: 12 },
            { id: 2, name: 'Workout Mix', tracks: 15 },
            { id: 3, name: 'Focus Mode', tracks: 8 }
        ];
    }

    /**
     * Render the service card HTML with additional features
     * @returns {string} HTML string
     */
    render() {
        console.log(`Rendering KevkoFyService:`, {
            id: this.id,
            name: this.name,
            status: this.status,
            url: this.url,
            type: this.type
        });
        const statusClass = this.status === 'offline' ? 'offline' : '';
        const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
        const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

        // Define the action button based on status
        let actionButton;

        // Always use the Go to Spotify button regardless of status
        actionButton = `
            <a href="/spotify"
               class="w-full bg-${this.color}-600 hover:bg-${this.color}-500 border border-${this.color}-500/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                <span class="text-sm text-white">Go to ${this.name}</span>
                <i data-lucide="arrow-right" class="h-4 w-4 ml-1 text-white"></i>
            </a>
        `;

        return `
            <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                <div class="flex flex-col h-full">
                    <div class="flex items-start justify-between mb-4">
                        <div class="p-2 rounded-lg bg-${this.color}-500/10">
                            <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                        </div>
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                            <span class="text-xs text-slate-400">${statusText}</span>
                        </div>
                    </div>
                    <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                    <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                    ${actionButton}
                    <div id="playlists-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                        <h4 class="text-xs font-medium text-slate-300 mb-2">Your Playlists</h4>
                        <div class="space-y-1 max-h-32 overflow-y-auto">
                            <!-- Playlists will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize playlists button
            const showPlaylistsBtn = element.querySelector('#showPlaylists');
            if (showPlaylistsBtn) {
                console.log(`Found playlists button for service: ${this.id}`);
                showPlaylistsBtn.addEventListener('click', () => {
                    this.togglePlaylists(element);
                });
            } else {
                console.log(`No playlists button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoFyService ${this.id}:`, error);
        }
    }

    /**
     * Toggle playlists visibility
     * @param {HTMLElement} element The service element
     */
    async togglePlaylists(element) {
        const playlistsContainer = element.querySelector(`#playlists-${this.id}`);
        if (!playlistsContainer) return;

        const isHidden = playlistsContainer.classList.contains('hidden');

        if (isHidden) {
            // Initialize playlists if needed
            if (this.playlists.length === 0) {
                await this.init();
            }

            // Populate playlists
            const playlistsContent = playlistsContainer.querySelector('.space-y-1');
            if (playlistsContent) {
                playlistsContent.innerHTML = this.playlists.map(playlist => `
                    <div class="flex items-center justify-between bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors">
                        <span class="text-xs text-slate-300">${playlist.name}</span>
                        <span class="text-xs text-slate-500">${playlist.tracks} tracks</span>
                    </div>
                `).join('');
            }

            // Show playlists
            playlistsContainer.classList.remove('hidden');
        } else {
            // Hide playlists
            playlistsContainer.classList.add('hidden');
        }
    }
}
