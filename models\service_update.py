from mongoengine import Document, <PERSON><PERSON><PERSON>, DateTimeField, <PERSON><PERSON><PERSON>
from datetime import datetime, timezone
from models.user import User

class ServiceUpdate(Document):
    """
    Model for storing service updates published by admins
    """
    service_id = StringField(required=True)  # ID of the service (e.g., 'kevkoAI', 'kevkoFy')
    title = StringField(required=True)  # Title of the update
    description = StringField(required=True)  # Detailed description of the update
    version = StringField()  # Optional version number
    published_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    published_by = ReferenceField(User, required=True)  # Admin who published the update

    meta = {
        'collection': 'service_updates',
        'indexes': [
            'service_id',
            'published_at'
        ],
        'ordering': ['-published_at']  # Default ordering by publication date descending
    }

    def to_dict(self):
        """
        Convert the document to a dictionary
        """
        return {
            'id': str(self.id),
            'service_id': self.service_id,
            'title': self.title,
            'description': self.description,
            'version': self.version,
            'published_at': self.published_at.isoformat(),
            'published_by': self.published_by.username if self.published_by else None
        }
