from flask import jsonify, request
from flask_login import login_required, current_user
from models.user import User
import logging
from . import profile_api

@profile_api.route('/update-username', methods=['POST'])
@login_required
def update_username():
    """Update the current user's username"""
    try:
        data = request.get_json()
        new_username = data.get('username')

        if not new_username:
            return jsonify({'error': 'Username is required'}), 400

        # Check if username is already taken
        existing_user = User.objects(username=new_username).first()
        if existing_user and str(existing_user.id) != str(current_user.id):
            return jsonify({'error': 'Username already taken'}), 400

        # Update username
        current_user.set_username(new_username)
        current_user.save()

        return jsonify({
            'message': 'Username updated successfully',
            'username': current_user.username
        })
    except Exception as e:
        logging.error(f"Error updating username: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500



@profile_api.route('/update-profile-picture', methods=['POST'])
@login_required
def update_profile_picture():
    """Update the current user's profile picture URL"""
    try:
        data = request.get_json()
        profile_picture = data.get('profile_picture')

        # Update profile picture URL
        current_user.profile_picture = profile_picture
        current_user.save()

        return jsonify({
            'message': 'Profile picture updated successfully',
            'profile_picture': current_user.profile_picture
        })
    except Exception as e:
        logging.error(f"Error updating profile picture: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/update-timezone', methods=['POST'])
@login_required
def update_timezone():
    """Update the current user's timezone"""
    try:
        data = request.get_json()
        timezone = data.get('timezone')

        if not timezone:
            return jsonify({'error': 'Timezone is required'}), 400

        # Update timezone
        current_user.update_timezone(timezone)

        return jsonify({
            'message': 'Timezone updated successfully',
            'timezone': current_user.timezone
        })
    except Exception as e:
        logging.error(f"Error updating timezone: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/info', methods=['GET'])
@login_required
def get_profile_info():
    """Get the current user's profile information"""
    try:
        return jsonify({
            'id': str(current_user.id),
            'username': current_user.username,
            'email': current_user.email,
            'profile_picture': current_user.profile_picture,
            'is_admin': current_user.is_admin,
            'timezone': current_user.timezone,
            'created_at': current_user.created_at.isoformat() if hasattr(current_user, 'created_at') else None
        })
    except Exception as e:
        logging.error(f"Error getting profile info: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@profile_api.route('/user/<username>', methods=['GET'])
@login_required
def get_user_basic_info(username):
    """Get basic user information by username for profile popup"""
    try:
        # Find the user by username
        user = User.objects(username=username).first()
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Check if the profile is private and not the current user
        from models.profile_customization import ProfileCustomization
        profile = ProfileCustomization.get_or_create(user)

        if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
            return jsonify({'success': False, 'error': 'Profile is private'}), 403

        return jsonify({
            'success': True,
            'user': {
                'id': str(user.id),
                'username': user.username,
                'display_name': user.display_name,
                'profile_picture': user.profile_picture
            }
        })
    except Exception as e:
        logging.error(f"Error getting user basic info for {username}: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500