/* Statistics Redesign - Modern, Compact, Vibrant */

/* Base styles for statistics panel */
#statisticsView {
  --primary-gradient: linear-gradient(135deg, #06b6d4, #3b82f6);
  --secondary-gradient: linear-gradient(135deg, #8b5cf6, #ec4899);
  --accent-gradient: linear-gradient(135deg, #f59e0b, #ef4444);
  --card-bg: rgba(15, 23, 42, 0.7);
  --card-border: rgba(51, 65, 85, 0.5);
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --chart-height: 200px;
}

/* Card styling */
.stats-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Card header */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  background-color: rgba(30, 41, 59, 0.5);
}

.stats-header h3 {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.stats-header h3 i {
  margin-right: 0.5rem;
}

/* Grid layout */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 1rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.stats-grid-full {
  grid-column: span 2;
  width: 100%;
}

.stats-grid-half {
  grid-column: span 1;
  width: 100%;
}

/* Metrics grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1.25rem;
  width: 100%;
}

.metric-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.metric-tile:hover {
  transform: translateY(-2px);
  background: rgba(30, 41, 59, 0.6);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Chart container */
.chart-wrapper {
  padding: 1.25rem;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.chart-container {
  height: var(--chart-height);
  position: relative;
  width: 100%;
  max-width: 100%;
}

/* Legend styling */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: rgba(30, 41, 59, 0.4);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}

.legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.legend-label {
  margin-right: 0.5rem;
}

.legend-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Period selector buttons */
.period-selector {
  display: flex;
  gap: 0.25rem;
}

.period-btn {
  background: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(51, 65, 85, 0.3);
  border-radius: 0.25rem;
  color: var(--text-secondary);
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn:hover {
  background: rgba(30, 41, 59, 0.6);
  color: var(--text-primary);
}

.period-btn.active {
  background: var(--primary-gradient);
  border-color: transparent;
  color: white;
}

/* Chart clickable styles */
.chart-clickable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.chart-clickable::after {
  content: '';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(148, 163, 184, 0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15 3 21 3 21 9'%3E%3C/polyline%3E%3Cpath d='M21 3L14 10'%3E%3C/path%3E%3Cpath d='M10 14L3 21'%3E%3C/path%3E%3Cpolyline points='3 15 3 21 9 21'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 5;
}

.chart-clickable:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-clickable:hover::after {
  opacity: 1;
}

/* Chart modal */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.chart-modal.visible {
  opacity: 1;
  visibility: visible;
}

.chart-modal.hidden {
  display: none;
}

.chart-modal:not(.hidden) {
  display: flex;
}

.chart-modal-content {
  background-color: rgba(15, 23, 42, 0.95);
  border: 1px solid var(--card-border);
  border-radius: 0.75rem;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.chart-modal.visible .chart-modal-content {
  transform: translateY(0);
}

.chart-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  background-color: rgba(30, 41, 59, 0.5);
}

.chart-modal-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-modal-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-modal-btn:hover {
  background: rgba(51, 65, 85, 0.3);
  color: var(--text-primary);
}

.chart-modal-body {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

.chart-modal-container {
  height: 60vh;
  position: relative;
}

/* Users table */
.top-users-container {
  height: 240px; /* Height for exactly 4 rows */
  overflow-y: auto;
  border-radius: 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
}

.top-users-container::-webkit-scrollbar {
  width: 4px;
}

.top-users-container::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 4px;
}

.top-users-container::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.top-users-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.5);
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  text-align: left;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  position: sticky;
  top: 0;
  background-color: rgba(30, 41, 59, 0.8);
  z-index: 1;
}

.users-table td {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(51, 65, 85, 0.1);
}

.user-table-row {
  height: 60px; /* Fixed height for each row to ensure exactly 4 rows are visible */
}

/* Loading and error states */
.stats-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(4px);
}

.stats-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

/* Tooltip */
.stats-tooltip {
  position: absolute;
  background: rgba(15, 23, 42, 0.9);
  color: var(--text-primary);
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 100;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(51, 65, 85, 0.5);
  max-width: 200px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-card {
  animation: fadeIn 0.5s ease forwards;
}

.stats-grid-half:nth-child(2) .stats-card {
  animation-delay: 0.1s;
}

.stats-grid-half:nth-child(3) .stats-card {
  animation-delay: 0.2s;
}

.stats-grid-half:nth-child(4) .stats-card {
  animation-delay: 0.3s;
}

.stats-grid-full:last-child .stats-card {
  animation-delay: 0.4s;
}

/* World Map Styles */
.world-map-container {
  height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: rgba(30, 41, 59, 0.4);
  animation: mapFadeIn 0.8s ease forwards;
  opacity: 1;
}

/* D3 Map Styles */
.country {
  fill: rgba(30, 41, 59, 0.8);
  stroke: rgba(51, 65, 85, 0.5);
  stroke-width: 0.5px;
  transition: fill 0.3s ease;
}

.country:hover {
  fill: rgba(6, 182, 212, 0.7);
  cursor: pointer;
  stroke: rgba(255, 255, 255, 0.8);
  stroke-width: 1px;
}

.country-with-users {
  stroke: rgba(255, 255, 255, 0.5);
  stroke-width: 0.8px;
  filter: drop-shadow(0 0 2px rgba(6, 182, 212, 0.3));
}

/* Map tooltip */
.map-tooltip {
  position: fixed; /* Fixed positioning for better placement */
  display: block; /* Always visible when opacity > 0 */
  background: rgba(15, 23, 42, 0.95);
  color: white;
  font-size: 13px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(6, 182, 212, 0.5);
  pointer-events: none;
  z-index: 1000; /* High z-index to appear above other elements */
  max-width: 200px;
  min-width: 120px;
  text-align: center;
  backdrop-filter: blur(4px);
  transform: translateY(-5px);
  transition: opacity 0.2s;
}

/* Tooltip country name */
.tooltip-country {
  color: #e2e8f0;
  font-size: 14px;
  margin-bottom: 4px;
  text-align: center;
}

/* Tooltip user count - highlighted */
.tooltip-users {
  color: #06b6d4;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  margin-top: 2px;
  padding: 2px 0;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}

/* Map Legend */
.map-legend {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 4px;
  padding: 8px;
  border: 1px solid rgba(51, 65, 85, 0.5);
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 11px;
  color: var(--text-secondary);
  backdrop-filter: blur(4px);
  z-index: 100;
}

.map-legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.map-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Map animation */
@keyframes mapFadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Map controls */
.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 100;
}

.map-control-btn {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border: none;
  font-size: 16px;
  transition: transform 0.2s ease;
}

.map-control-btn:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .stats-grid-half {
    grid-column: span 1;
    width: 100%;
  }

  .stats-grid-full {
    grid-column: span 1;
    width: 100%;
  }

  .metrics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    padding: 1rem;
  }

  .chart-container {
    height: 180px;
  }

  .world-map-container {
    height: 300px;
  }

  .stats-card {
    overflow: visible;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .metric-tile {
    padding: 0.75rem 0.5rem;
  }

  .metric-value {
    font-size: 1.25rem;
  }

  .metric-label {
    font-size: 0.7rem;
  }

  .chart-container {
    height: 160px;
  }

  .world-map-container {
    height: 250px;
  }

  /* Period selector responsive styles */
  .period-selector {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 0.5rem;
  }

  .stats-header {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .chart-wrapper {
    padding: 0.75rem;
  }

  .stats-card {
    margin-bottom: 0;
  }
}
