/* Friends-specific styles */

/* Main container */
.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px); /* Adjust for top nav */
  width: 100%;
  overflow: hidden;
}

/* Chat Content Area */
#chatContent {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  position: relative;
  height: calc(100% - 60px); /* Adjust for input area */
}

/* Chat container */
.chat-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

/* Friend list */
.friend-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
  padding: 0.5rem;
  flex: 1;
}

.friend-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: rgba(45, 45, 45, 0.5);
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.friend-item:hover {
  background-color: rgba(60, 60, 60, 0.7);
}

.friend-item.active {
  background-color: rgba(14, 165, 233, 0.2);
  border-left: 3px solid #0ea5e9;
}

.friend-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3c3c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
  overflow: hidden;
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.friend-avatar .avatar-placeholder {
  color: #fff;
  font-size: 1.2rem;
  font-weight: bold;
}

.friend-info {
  flex: 1;
  overflow: hidden;
}

.friend-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.friend-status {
  font-size: 0.8rem;
  color: #969696;
}

.friend-badge {
  background-color: #0ea5e9;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0 4px;
}

/* Sidebar buttons */
.sidebar-buttons {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  margin-bottom: 0.5rem;
}

.sidebar-buttons button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: rgba(14, 165, 233, 0.2);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.sidebar-buttons button:hover {
  background-color: rgba(14, 165, 233, 0.3);
}

.sidebar-buttons button i {
  color: #0ea5e9;
}

/* Friend search */
.friend-search {
  padding: 0.5rem 1rem;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: rgba(45, 45, 45, 0.8);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
}

.search-container i {
  color: #969696;
  margin-right: 0.5rem;
}

.search-container input {
  background: transparent;
  border: none;
  color: white;
  flex: 1;
  outline: none;
}

.search-container button {
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
}

/* Active friend info in header */
.active-friend-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.active-friend-info .avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3c3c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.active-friend-info .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.friend-name-container {
  display: flex;
  flex-direction: column;
}

/* Encryption status indicator */
.encryption-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #10b981;
  margin-top: 0.25rem;
}

.encryption-status.hidden {
  display: none;
}

.encryption-status .encryption-icon {
  width: 14px;
  height: 14px;
  color: #10b981;
}

/* Welcome message */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #969696;
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #0ea5e9;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(14, 165, 233, 0.1);
  border-radius: 50%;
}

.welcome-message h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.welcome-message p {
  max-width: 400px;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 0.5rem;
  max-width: 400px;
}

.error-message i {
  color: #ff4d4d;
  margin-bottom: 0.5rem;
  width: 24px;
  height: 24px;
}

.error-message p {
  color: #ff4d4d;
  margin: 0;
  text-align: center;
}

/* Chat messages */
.message-container {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  height: calc(100% - 60px);
  overflow-y: auto;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  margin-top: 0.5rem;
}

.typing-indicator.hidden {
  display: none;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #aaa;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.typing-text {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #aaa;
}

@keyframes typing-dot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.message {
  display: flex;
  max-width: 80%;
}

.message.message-incoming {
  align-self: flex-start;
}

.message.outgoing, .message.message-outgoing {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3c3c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.75rem;
  flex-shrink: 0;
  overflow: hidden;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  background-color: #2d2d2d;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  position: relative;
}

.message.outgoing .message-content, .message.message-outgoing .message-content {
  background-color: #0369a1;
  color: white;
}

/* System message styles */
.message.message-system {
  align-self: center;
  max-width: 90%;
  margin: 0.5rem 0;
}

.message.message-system .system-message-content {
  background-color: rgba(45, 45, 45, 0.7);
  border: 1px solid rgba(60, 60, 60, 0.7);
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  color: #e0e0e0;
}

.message.message-system-info .system-message-content {
  background-color: rgba(14, 165, 233, 0.15);
  border: 1px solid rgba(14, 165, 233, 0.3);
}

.message.message-system-success .system-message-content {
  background-color: rgba(16, 185, 129, 0.15);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.message.message-system-warning .system-message-content {
  background-color: rgba(245, 158, 11, 0.15);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.message.message-system-error .system-message-content {
  background-color: rgba(239, 68, 68, 0.15);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.message.message-system .message-sender {
  color: #0ea5e9;
  font-weight: 600;
}

.message.message-system .message-text a {
  color: #0ea5e9;
  text-decoration: underline;
  font-weight: 500;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.message-sender {
  font-weight: 600;
  font-size: 0.875rem;
}

.message-text {
  word-break: break-word;
}

/* Message status indicators */
.message-status {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.25rem;
  font-size: 0.75rem;
}

.message-status i {
  width: 14px;
  height: 14px;
  color: #aaa;
}

.message-status.sending i {
  animation: spin 1s linear infinite;
}

.message-status.sent i {
  color: #aaa;
}

.message-status.delivered i {
  color: #0369a1;
}

.message-status.read i {
  color: #0369a1;
}

.message-status.error i {
  color: #ef4444;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Message info (time and status) */
.message-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.message-time {
  font-size: 0.7rem;
  color: #969696;
}

.message.outgoing .message-time, .message.message-outgoing .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* Message status indicators */
.message-status {
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-status i {
  width: 14px;
  height: 14px;
  color: rgba(255, 255, 255, 0.5);
  transition: color 0.3s ease, opacity 0.3s ease;
}

.message-status.sending i {
  color: rgba(255, 255, 255, 0.3);
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.3; }
  50% { opacity: 0.7; }
  100% { opacity: 0.3; }
}

.message-status.sent i {
  color: rgba(255, 255, 255, 0.5);
}

.message-status.delivered i {
  color: rgba(255, 255, 255, 0.7);
}

.message-status.read i {
  color: #0ea5e9;
}

.message-status.error i {
  color: #ef4444;
}

/* Date separator */
.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  color: #969696;
  font-size: 0.8rem;
}

.date-separator::before,
.date-separator::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: #3c3c3c;
  margin: 0 1rem;
}

/* Search results */
.search-results {
  margin-top: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: rgba(45, 45, 45, 0.5);
  margin-bottom: 0.5rem;
}

/* Add Friend hover button */
.add-friend-hover-btn {
  transition: opacity 0.2s ease-in-out;
}

/* Ensure the button is visible on touch devices */
@media (hover: none) {
  .add-friend-hover-btn {
    opacity: 1 !important;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3c3c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
  overflow: hidden;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
}

.user-action {
  margin-left: 0.5rem;
}

/* Add hover effect for clickable user items in search results */
#searchResults > div {
  transition: all 0.2s ease;
}

#searchResults > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.user-action button {
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  transition: background-color 0.2s ease, opacity 0.2s ease;
  font-size: 0.875rem;
}

.user-action button:hover {
  background-color: #0284c7;
}

.user-action button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.user-action button.pending {
  background-color: #4b5563;
  cursor: default;
}

.user-action button.friend {
  background-color: #3c3c3c;
}

.user-action button.accept {
  background-color: #10b981;
}

.user-action button.accept:hover {
  background-color: #059669;
}

.user-action button.reject {
  background-color: #ef4444;
}

.user-action button.reject:hover {
  background-color: #dc2626;
}

/* Friend requests */
.request-list {
  margin-top: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.request-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: rgba(45, 45, 45, 0.5);
  margin-bottom: 0.5rem;
}

.request-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #3c3c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
  overflow: hidden;
}

.request-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.request-avatar .avatar-placeholder {
  color: #fff;
  font-size: 1.2rem;
  font-weight: bold;
}

.request-info {
  flex: 1;
  overflow: hidden;
  margin-right: 0.75rem;
}

.request-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
}

.request-username {
  font-size: 0.8rem;
  color: #969696;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.request-actions button {
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.request-actions .accept-btn {
  background-color: #10b981;
  color: white;
}

.request-actions .accept-btn:hover {
  background-color: #059669;
}

.request-actions .reject-btn {
  background-color: #ef4444;
  color: white;
}

.request-actions .reject-btn:hover {
  background-color: #dc2626;
}

.request-actions .cancel-btn {
  background-color: #4b5563;
  color: white;
}

.request-actions .cancel-btn:hover {
  background-color: #374151;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid #3c3c3c;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
  padding: 0 0.5rem;
}

.tab {
  padding: 0.75rem 1.25rem;
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  position: relative;
  font-weight: 500;
  transition: color 0.2s, background-color 0.2s;
  border-radius: 0.5rem 0.5rem 0 0;
}

.tab:hover {
  background-color: rgba(60, 60, 60, 0.3);
  color: #e0e0e0;
}

.tab.active {
  color: white;
  background-color: rgba(14, 165, 233, 0.1);
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #0ea5e9;
}

.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Badge */
.badge {
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  padding: 0 4px;
  margin-left: 0.5rem;
}

/* Modal styles */
body.modal-open {
  overflow: hidden;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.active,
.modal[style*="display: flex"] {
  display: flex;
  opacity: 1;
}

.modal-content {
  background-color: rgb(30, 30, 30);
  border-radius: 0.5rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.active .modal-content,
.modal[style*="display: flex"] .modal-content {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgb(73, 73, 73);
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-header button {
  background: none;
  border: none;
  color: rgb(156, 163, 175);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.modal-header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 1rem;
}

/* Modal actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn-primary {
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.btn-secondary {
  background-color: #3c3c3c;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #969696;
}

.empty-state-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #0ea5e9;
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #969696;
}

.spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #0ea5e9;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Notifications */
.notification {
  background-color: #2d2d2d;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: slide-in 0.3s ease-out;
  max-width: 350px;
}

.notification.success {
  border-left: 4px solid #10b981;
}

.notification.error {
  border-left: 4px solid #ef4444;
}

.notification.info {
  border-left: 4px solid #0ea5e9;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification i {
  color: #10b981;
}

.notification.error i {
  color: #ef4444;
}

.notification.info i {
  color: #0ea5e9;
}

.close-notification {
  background: none;
  border: none;
  color: #969696;
  cursor: pointer;
  padding: 0.25rem;
}

.notification.fade-out {
  opacity: 0;
  transform: translateX(10px);
  transition: opacity 0.3s, transform 0.3s;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  margin: 0.5rem;
  background-color: rgba(45, 45, 45, 0.5);
  border-radius: 1rem;
  max-width: 200px;
  animation: fade-in 0.3s ease-out;
}

.typing-dots {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #969696;
  margin: 0 2px;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  font-size: 0.8rem;
  color: #969696;
}

@keyframes typing-dot {
  0%, 80%, 100% { transform: scale(0.7); }
  40% { transform: scale(1); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Load more messages button and container */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 0.75rem 0;
  margin-bottom: 0.5rem;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(45, 45, 45, 0.7);
  color: #e0e0e0;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.load-more-btn:hover {
  background-color: rgba(60, 60, 60, 0.9);
}

.load-more-btn:active {
  background-color: rgba(75, 75, 75, 1);
}

.load-more-btn i {
  margin-right: 0.25rem;
}

/* Loading more indicator */
.loading-more-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 0;
  margin-bottom: 0.5rem;
  color: #969696;
  font-size: 0.875rem;
}

.loading-more-indicator i {
  margin-right: 0.5rem;
}

/* Image preview container and upload indicators */
#imagePreviewContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(45, 45, 45, 0.5);
  border-radius: 0.5rem;
  position: relative;
}

#imagePreviewContainer.hidden {
  display: none;
}

.image-preview-container {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #2d2d2d;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.remove-image-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.global-remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.global-remove-image-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.image-upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  z-index: 10;
}

.image-upload-loading .spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
  margin: 0;
}

.image-upload-success {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #10b981;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: fade-in 0.3s ease-out;
}

.image-upload-error {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: fade-in 0.3s ease-out;
}

.message-sending-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(45, 45, 45, 0.7);
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  animation: fade-in 0.3s ease-out;
}

.message-sending-loading .spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

.message-sending-loading span {
  font-size: 0.875rem;
  color: #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    display: flex;
    gap: 0.5rem;
  }

  .header-actions span {
    display: none;
  }

  .message {
    max-width: 90%;
  }

  /* Mobile sidebar */
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    transform: translateX(-100%);
    z-index: 1000;
    width: 85%;
    max-width: 300px;
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  }

  /* Add overlay when sidebar is open */
  .sidebar.open::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}


