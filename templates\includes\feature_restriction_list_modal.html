<div id="featureRestrictionListModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg p-6 w-[500px] max-w-full max-h-[80vh] flex flex-col">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Feature Restricted Users</h3>
            <button id="closeFeatureRestrictionListModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="space-y-2 overflow-y-auto flex-grow pr-1">
            <!-- User list will be populated by JS -->
            <div id="featureRestrictionListContainer" class="space-y-2">
                <div class="flex justify-center items-center py-8">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400">Loading restricted users...</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pt-4 mt-2 border-t border-slate-700">
            <button id="closeFeatureRestrictionListButton" class="w-full bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md px-4 py-2">Close</button>
        </div>
    </div>
</div>
