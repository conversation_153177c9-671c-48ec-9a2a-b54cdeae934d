from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, ReferenceField
from datetime import datetime, timezone
from models.user import User

class ServiceUsage(Document):
    """
    Model for tracking service usage
    """
    user = ReferenceField(User, required=True)
    service = StringField(required=True)  # 'chat', 'live', 'spotify', etc.
    action = StringField(required=True)  # Specific action within the service
    timestamp = DateTimeField(default=lambda: datetime.now(timezone.utc))

    meta = {
        'collection': 'service_usage',
        'indexes': [
            'user',
            'service',
            'timestamp'
        ]
    }

    @classmethod
    def log_usage(cls, user_id, service, action):
        """
        Log service usage
        """
        try:
            usage = cls(
                user=user_id,
                service=service,
                action=action
            )
            usage.save()
            return usage
        except Exception as e:
            print(f"Error logging service usage: {str(e)}")
            return None
