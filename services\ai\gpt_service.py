import os
from openai import OpenAI

class GPTService:
    def __init__(self):
        self.client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

    def generate_stream(self, messages, system_prompt_file='prompts/system-prompt.txt'):
        # Read the system prompt from the specified file
        with open(system_prompt_file, 'r') as f:
            system_prompt = f.read()

        # Limit user messages to 1500 characters
        limited_messages = [
            {
                "role": msg["role"],
                "content": msg["content"][:1500] if msg["role"] == "user" else msg["content"]
            }
            for msg in messages
        ]

        # Add system message at the beginning of the conversation
        full_messages = [
            {"role": "system", "content": system_prompt}
        ] + limited_messages

        response = self.client.chat.completions.create(
            model="gpt-4o-mini",
            messages=full_messages,
            stream=True
        )

        for chunk in response:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
