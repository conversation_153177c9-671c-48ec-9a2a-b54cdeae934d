"""
Socket.IO event handlers for the friends API.
This file is kept for backward compatibility and imports all handlers from the socket package.
All socket handler implementations have been moved to the socket/ directory.
"""
# Import directly from the socket package
from api.friends.socket.init_socket import init_socketio as init_socketio_from_package

def init_socketio(socketio):
    """Initialize Socket.IO event handlers for friends"""
    # Delegate to the modular implementation
    init_socketio_from_package(socketio)