from mongoengine import Document, <PERSON><PERSON><PERSON>, IntField, DateT<PERSON><PERSON><PERSON>, <PERSON><PERSON>ield
from models.user import User
from datetime import datetime

class FeatureRestriction(Document):
    """Model for storing feature restrictions for users"""
    user = ReferenceField(User, required=True)
    max_threads = IntField(default=5)  # Maximum number of threads a user can create
    max_conversation_sets = IntField(default=5)  # Maximum number of conversation sets per thread
    max_live_rooms = IntField(default=7)  # Maximum number of live rooms a user can create
    max_live_conversation_sets = IntField(default=15)  # Maximum number of conversation sets per live room
    created_at = DateTimeField(default=datetime.utcnow)
    created_by = StringField()  # Admin email who added the restriction

    meta = {
        'collection': 'feature_restrictions',
        'indexes': [
            'user',
        ]
    }

    @classmethod
    def get_user_restrictions(cls, user_id):
        """Get restrictions for a user"""
        return cls.objects(user=user_id).first()

    @classmethod
    def set_user_restrictions(cls, user_id, max_threads, max_conversation_sets, admin_email, max_live_rooms=7, max_live_conversation_sets=15):
        """Set restrictions for a user"""
        restriction = cls.objects(user=user_id).first()
        if restriction:
            # Update existing restriction
            restriction.max_threads = max_threads
            restriction.max_conversation_sets = max_conversation_sets
            restriction.max_live_rooms = max_live_rooms
            restriction.max_live_conversation_sets = max_live_conversation_sets
            restriction.save()
            return restriction
        else:
            # Create new restriction
            restriction = cls(
                user=user_id,
                max_threads=max_threads,
                max_conversation_sets=max_conversation_sets,
                max_live_rooms=max_live_rooms,
                max_live_conversation_sets=max_live_conversation_sets,
                created_by=admin_email
            )
            restriction.save()
            return restriction

    @classmethod
    def remove_user_restrictions(cls, user_id):
        """Remove restrictions for a user"""
        restriction = cls.objects(user=user_id).first()
        if restriction:
            restriction.delete()
            return True
        return False

    @classmethod
    def get_all_restrictions(cls):
        """Get all feature restrictions"""
        return cls.objects.all()

    @classmethod
    def can_create_thread(cls, user_id):
        """Check if a user can create a new thread"""
        from models.thread import Thread

        restriction = cls.objects(user=user_id).first()
        if not restriction:
            return True  # No restrictions

        # Count user's threads
        thread_count = Thread.objects(user=user_id).count()
        return thread_count < restriction.max_threads

    @classmethod
    def can_add_message(cls, user_id, thread_id):
        """Check if a user can add a new message to a thread"""
        from models.thread import Thread

        restriction = cls.objects(user=user_id).first()
        if not restriction:
            return True  # No restrictions

        # Get thread and count messages
        thread = Thread.objects(id=thread_id, user=user_id).first()
        if not thread:
            return False  # Thread not found

        # Count conversation sets (user + assistant messages)
        # Each set consists of a user message followed by an assistant message
        message_count = len(thread.messages)
        conversation_sets = message_count // 2  # Integer division

        return conversation_sets < restriction.max_conversation_sets

    @classmethod
    def can_create_live_room(cls, user_id):
        """Check if a user can create a new live room"""
        from models.room import Room

        restriction = cls.objects(user=user_id).first()
        if not restriction:
            return True  # No restrictions

        # Count user's rooms where they are the creator
        room_count = Room.objects(creator=user_id).count()
        return room_count < restriction.max_live_rooms

    @classmethod
    def can_add_live_message(cls, user_id, room_id):
        """Check if a user can add a new message to a live room"""
        from models.room import Room

        restriction = cls.objects(user=user_id).first()
        if not restriction:
            return True  # No restrictions

        # Get room
        room = Room.objects(room_id=room_id).first()
        if not room:
            return False  # Room not found

        # Count conversation sets (user + assistant messages)
        # Each set consists of a user message followed by an assistant message
        message_count = len(room.messages)
        conversation_sets = message_count // 2  # Integer division

        return conversation_sets < restriction.max_live_conversation_sets
