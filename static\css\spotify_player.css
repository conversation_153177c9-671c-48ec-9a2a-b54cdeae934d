/* Reset progress bar styles */
.progress-section {
    width: 100%;
    max-width: 500px;
    margin: 20px auto;
    padding: 0 20px;
}

.progress-container {
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
    position: relative;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #4d4d4d;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

#progress {
    width: 0%;
    height: 100%;
    background-color: #1db954;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 0;
}

.hover-time {
    position: absolute;
    display: none;
    background-color: #282828;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transform: translateX(-50%);
    top: -25px;
    pointer-events: none;
}

.time-info {
    width: 100%;
    display: flex;
    justify-content: space-between;
    color: #b3b3b3;
    font-size: 12px;
    margin-top: 8px;
}

.progress-bar:hover {
    height: 6px;
}

.progress-bar:hover #progress {
    background-color: #1ed760;
}

/* Add hover effect for progress bar */
.progress-bar:hover::after {
    content: '';
    position: absolute;
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 140px;
    padding: 0 10px;
}

.volume-icon {
    cursor: pointer;
    color: var(--spotify-light-gray);
    transition: color 0.2s ease;
}

.volume-icon:hover {
    color: white;
}

.volume-slider {
    -webkit-appearance: none;
    width: 100px;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    cursor: pointer;
    transition: background 0.2s ease;
    margin: 0;
    padding: 0;
}

/* Remove the static background gradient */
/* .volume-slider {
    background: linear-gradient(...);
} */

/* Chrome/Safari/Edge */
.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
    margin-top: -4px; /* Centers the thumb: (thumb height - track height) / -2 */
}

/* Firefox */
.volume-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    border: none;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.volume-slider:hover::-webkit-slider-thumb,
.volume-slider:hover::-moz-range-thumb {
    opacity: 1;
}

.volume-slider::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 2px;
}

.volume-slider::-moz-range-track {
    height: 4px;
    border-radius: 2px;
}

/* Add this to show the filled part of the slider */
.volume-slider {
    background: linear-gradient(to right, var(--spotify-green) 0%, var(--spotify-green) 50%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 100%);
}

.user-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.logout-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background-color: rgba(29, 185, 84, 0.1);
    color: #1DB954;
    padding: 8px 16px;
    border-radius: 500px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.logout-button:hover {
    background-color: rgba(29, 185, 84, 0.2);
    transform: scale(1.05);
}

.logout-button i {
    font-size: 1rem;
}

.device-control {
    position: relative;
    margin: 15px 0;
    width: 100%;
    z-index: 1000;
}

.device-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 8px 12px;
    color: #fff;
    width: 100%;
    cursor: pointer;
    transition: background-color 0.2s;
}

.device-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.device-button .device-name {
    flex-grow: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.device-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #282828;
    border-radius: 4px;
    margin-top: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.device-dropdown.show {
    display: block;
}

.device-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.device-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.device-option.active {
    background: rgba(29, 185, 84, 0.3);
}

.device-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.device-option i.fa-check {
    margin-left: auto;
    color: #1DB954;
}

/* Scrollbar styling for the device dropdown */
.device-dropdown::-webkit-scrollbar {
    width: 8px;
}

.device-dropdown::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.device-dropdown::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.device-dropdown::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
}

.player-container.loading {
    position: relative;
}

.player-container.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.player-container.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #1DB954;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1001;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
