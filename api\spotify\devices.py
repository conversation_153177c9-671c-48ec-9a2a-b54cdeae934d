from flask import jsonify, request
from . import spotify_api
from .services.spotify_client import get_spotify_client
from .decorators import require_auth
import logging

@spotify_api.route('/devices')
@require_auth
def get_devices():
    """Get available Spotify devices"""
    try:
        sp = get_spotify_client()
        devices = sp.devices()
        return jsonify(devices)
    except Exception as e:
        logging.error(f"Error getting devices: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/transfer-playback', methods=['POST'])
@require_auth
def transfer_playback():
    """Transfer playback to a different device"""
    try:
        sp = get_spotify_client()
        data = request.json
        device_id = data.get('device_id')
        play = data.get('play', True)

        if not device_id:
            return jsonify({'error': 'device_id is required'}), 400

        sp.transfer_playback(device_id=device_id, force_play=play)
        return jsonify({'success': True})
    except Exception as e:
        logging.error(f"Error transferring playback: {str(e)}")
        return jsonify({'error': str(e)}), 500
