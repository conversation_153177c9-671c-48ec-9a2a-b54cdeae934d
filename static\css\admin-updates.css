/* Admin Updates Section Styles */
.admin-updates-section {
    background-color: #1e293b;
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(148, 163, 184, 0.2);
}

.admin-updates-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    background-color: rgba(30, 41, 59, 0.8);
}

.admin-updates-title {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
    color: #f8fafc;
    letter-spacing: 0.025em;
}

.admin-updates-title i {
    color: #38bdf8;
    margin-right: 0.75rem;
}

.admin-updates-actions {
    display: flex;
    gap: 0.75rem;
}

.admin-updates-content {
    padding: 1.25rem;
    max-height: 600px;
    overflow-y: auto;
}

.admin-updates-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.update-card {
    background-color: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 0.5rem;
    padding: 1.25rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.update-card:hover {
    background-color: rgba(30, 41, 59, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.update-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.update-card-service {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9375rem;
    font-weight: 600;
    color: #f1f5f9;
}

.update-card-service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.update-card-date {
    font-size: 0.8125rem;
    color: #94a3b8;
    font-weight: 500;
}

.update-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #f8fafc;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.update-card-description {
    font-size: 0.875rem;
    color: #cbd5e1;
    margin-bottom: 0.75rem;
    white-space: pre-line;
    line-height: 1.5;
}

.update-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8125rem;
    color: #94a3b8;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.update-card-version {
    background-color: rgba(148, 163, 184, 0.15);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    margin-right: 0.75rem;
}

.update-card-author {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.update-card-actions {
    display: flex;
    gap: 0.75rem;
}

.update-card-action {
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 0.375rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.update-card-action:hover {
    color: #f1f5f9;
    background-color: rgba(148, 163, 184, 0.15);
}

.update-card-action.delete:hover {
    color: #f87171;
    background-color: rgba(239, 68, 68, 0.15);
}

/* Empty state */
.admin-updates-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: #94a3b8;
    background-color: rgba(30, 41, 59, 0.3);
    border-radius: 0.5rem;
    border: 1px dashed rgba(148, 163, 184, 0.2);
}

.admin-updates-empty i {
    font-size: 2.5rem;
    margin-bottom: 1.25rem;
    opacity: 0.7;
}

.admin-updates-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #cbd5e1;
}

.admin-updates-empty-text {
    font-size: 0.9375rem;
    line-height: 1.5;
    max-width: 24rem;
    margin: 0 auto;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw; /* Full viewport width */
    height: 100vh; /* Full viewport height */
    z-index: 9999; /* Higher z-index to ensure it's above everything */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    transition: visibility 0s linear 0.3s, opacity 0.3s;
}

.modal.hidden {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    transition: visibility 0s, opacity 0s; /* Immediate transition */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal.hidden .modal-overlay {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal.closing {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal.closing .modal-container {
    animation: modalSlideOut 0.2s ease forwards;
}

/* No animation for overlay when closing - it disappears immediately */
.modal.closing .modal-overlay {
    opacity: 0;
    transition: opacity 0s; /* Immediate transition */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw; /* Full viewport width */
    height: 100vh; /* Full viewport height */
    background-color: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.3s ease;
    z-index: -1; /* Ensures it stays behind the modal content */
}

.modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Center in the viewport regardless of scroll position */
    background-color: #1e293b;
    border-radius: 0.75rem;
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    max-width: 550px;
    width: calc(100% - 2rem); /* Account for padding */
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 10000; /* Increased z-index to ensure it's above everything */
}

.modal-header {
    padding: 1.25rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(30, 41, 59, 0.9);
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f8fafc;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-title i {
    color: #38bdf8;
}

.modal-close {
    background: none;
    border: none;
    color: #94a3b8;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    line-height: 1;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    width: 2rem;
    height: 2rem;
}

.modal-close:hover {
    color: #f1f5f9;
    background-color: rgba(148, 163, 184, 0.1);
}

.modal-body {
    padding: 1.5rem;
    flex: 1;
}

.modal-footer {
    padding: 1.25rem;
    border-top: 1px solid rgba(148, 163, 184, 0.2);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    background-color: rgba(30, 41, 59, 0.9);
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
    position: sticky;
    bottom: 0;
    z-index: 10;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .modal-container {
        max-width: 95%;
        max-height: calc(100vh - 2rem);
    }

    .modal-header, .modal-body, .modal-footer {
        padding: 1rem;
    }

    .modal-title {
        font-size: 1.125rem;
    }
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9375rem;
    font-weight: 600;
    color: #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.form-group label i {
    color: #38bdf8;
    font-size: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    background-color: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 0.5rem;
    color: #f1f5f9;
    font-size: 0.9375rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-control:hover {
    border-color: rgba(148, 163, 184, 0.4);
}

.form-control:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 0 3px rgba(56, 189, 248, 0.25);
    background-color: rgba(15, 23, 42, 0.7);
}

.form-control::placeholder {
    color: #64748b;
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2394a3b8' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
    line-height: 1.5;
}

.form-help {
    font-size: 0.8125rem;
    color: #94a3b8;
    margin-top: 0.5rem;
    line-height: 1.4;
    display: flex;
    align-items: flex-start;
    gap: 0.375rem;
}

.form-help i {
    color: #64748b;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

/* Required field indicator */
.form-group.required label::after {
    content: '*';
    color: #f87171;
    margin-left: 0.25rem;
}

/* Button styles */
.btn-primary {
    background-color: #0ea5e9;
    color: #f8fafc;
    border: none;
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9375rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    background-color: #0284c7;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary i {
    font-size: 1rem;
}

.btn-secondary {
    background-color: rgba(148, 163, 184, 0.1);
    color: #e2e8f0;
    border: 1px solid rgba(148, 163, 184, 0.2);
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9375rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: rgba(148, 163, 184, 0.2);
    border-color: rgba(148, 163, 184, 0.3);
    transform: translateY(-1px);
}

.btn-secondary:active {
    transform: translateY(0);
}

.btn-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #f87171;
    border: 1px solid rgba(239, 68, 68, 0.2);
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9375rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background-color: #ef4444;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}
