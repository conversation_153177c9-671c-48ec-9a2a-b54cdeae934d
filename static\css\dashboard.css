/* Dashboard.css - All styles for the dashboard */

/* Highlight pulse animation for search results */
@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
    }
}

.highlight-pulse {
    animation: highlight-pulse 2s ease-out 1;
}

/* Base styles */
body {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

body::-webkit-scrollbar {
    display: none;
}

/* Custom scrollbar */
.custom-scrollbar {
    position: fixed;
    top: 0;
    right: 0;
    width: 10px;
    height: 100vh;
    z-index: 100;
    background: transparent;
}

.custom-scrollbar-thumb {
    position: absolute;
    right: 2px;
    width: 6px;
    border-radius: 3px;
    background: linear-gradient(to bottom, rgba(56, 189, 248, 0.5), rgba(168, 85, 247, 0.5));
    opacity: 0.7;
    transition: opacity 0.2s, width 0.2s;
}

.custom-scrollbar-thumb:hover {
    opacity: 0.9;
    width: 8px;
}

/* Card and navigation styles */
.card {
    background-color: rgba(15, 23, 42, 0.5);
    border-color: rgba(51, 65, 85, 0.5);
    backdrop-filter: blur(8px);
    border-radius: 0.5rem;
    border-width: 1px;
}

/* Override for sidebar card to prevent blur */
@media (max-width: 768px) {
    .sidebar-container .card {
        background-color: rgb(15, 23, 42); /* Solid background color */
        backdrop-filter: none; /* Remove blur effect */
        border-color: rgba(51, 65, 85, 0.8); /* Slightly more visible border */
    }
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    color: rgb(148, 163, 184);
    transition: all 0.2s;
    cursor: pointer;
}

.nav-item:hover {
    background-color: rgba(30, 41, 59, 0.5);
    color: rgb(241, 245, 249);
}

.nav-item.active {
    background-color: rgba(30, 41, 59, 0.7);
    color: rgb(34, 211, 238);
}

/* Ensure nav items are clickable on mobile */
@media (max-width: 768px) {
    .nav-item {
        padding: 0.75rem 1rem; /* Larger touch target on mobile */
        backdrop-filter: none; /* Ensure no blur effect */
        -webkit-backdrop-filter: none; /* For Safari */
    }

    /* Ensure all sidebar content is clear and crisp */
    .sidebar-container * {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }
}

/* Service card styles */
.service-card {
    background-color: rgba(30, 41, 59, 0.5);
    border-width: 1px;
    transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s;
    height: 280px; /* Fixed height for all cards */
    display: flex;
    flex-direction: column;
}

.service-card:hover {
    background-color: rgba(30, 41, 59, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-card.offline {
    cursor: not-allowed;
}

.service-card.hidden {
    display: none;
}

/* Service card content area */
.service-card > div:first-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Service card description */
.service-card p {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* Service card action area */
.service-card > div:last-child {
    margin-top: auto;
}

/* Updates section styles */
.update-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    background-color: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(51, 65, 85, 0.5);
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.update-item .icon-container {
    flex-shrink: 0;
    width: 2.5rem; /* w-10 */
    height: 2.5rem; /* h-10 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.update-item .icon-container i {
    width: 1.25rem; /* w-5 */
    height: 1.25rem; /* h-5 */
}

.update-item:hover {
    background-color: rgba(30, 41, 59, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.update-item .flex-1 h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #f1f5f9;
}

.update-item .flex-1 p {
    line-height: 1.4;
}

/* Background gradient */
.bg-gradient {
    background: linear-gradient(to bottom right, #000000, rgb(15, 23, 42));
    color: rgb(241, 245, 249);
}

/* User menu styles */
.user-menu {
    position: absolute;
    right: 0;
    margin-top: 0.5rem;
    width: 16rem; /* Increased from 12rem to accommodate longer text */
    min-width: 240px; /* Ensure minimum width for content */
    background-color: rgb(30, 41, 59);
    border: 1px solid rgb(51, 65, 85);
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 50;
    transition: opacity 0.2s ease, transform 0.2s ease;
    transform-origin: top right;
    overflow: hidden; /* Prevent content from spilling out */
}

/* View content styles */
.view-content {
    transition: opacity 0.3s ease-in-out;
    animation: fadeIn 0.3s ease-in-out;
}

.view-content.hidden {
    display: none;
}

/* Download button styles */
.download-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Notification toggle styles */
.notification-toggle {
    transition: background-color 0.3s ease;
}

.notification-toggle span {
    transition: transform 0.3s ease;
}

/* Toggle thumb styles */
.toggle-thumb {
    transition: transform 0.3s ease;
}

.toggle-thumb.active {
    transform: translateX(24px);
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Playlists container styles */
.playlists-container {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
}

/* Webkit scrollbar styles for playlists */
.playlists-container::-webkit-scrollbar {
    width: 6px;
}

.playlists-container::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 3px;
}

.playlists-container::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
}

.playlists-container::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.4);
}

/* Updates section styles */
.updates-container {
    max-height: 500px;
    overflow-y: auto;
}

.updates-container::-webkit-scrollbar {
    width: 6px;
}

.updates-container::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 3px;
}

.updates-container::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
}

.updates-container::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.4);
}

/* Service status indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: rgb(34, 197, 94);
}

.status-indicator.offline {
    background-color: rgb(239, 68, 68);
}

/* Loading animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
}

/* Modal styles */
#addServiceModal, .addServiceModal, #serviceEditorModal, .serviceEditorModal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

/* Code editor styles */
.code-tab {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s;
}

.code-tab.active {
    background-color: rgb(30, 41, 59);
    color: rgb(226, 232, 240);
    border-bottom: 2px solid rgb(56, 189, 248);
}

/* Settings tab styles */
.settings-tab {
    position: relative;
    transition: color 0.2s;
}

.settings-tab.active {
    color: rgb(226, 232, 240);
}

.settings-tab span.absolute {
    transition: transform 0.2s;
}

.settings-tab.active span.absolute {
    transform: scaleX(1);
}

.settings-tab:not(.active) span.absolute {
    transform: scaleX(0);
}

/* Accent color classes */
.accent-cyan {
    --accent-color: rgb(6, 182, 212);
}

.accent-purple {
    --accent-color: rgb(147, 51, 234);
}

.accent-green {
    --accent-color: rgb(22, 163, 74);
}

.accent-amber {
    --accent-color: rgb(245, 158, 11);
}

.accent-red {
    --accent-color: rgb(220, 38, 38);
}

.accent-blue {
    --accent-color: rgb(37, 99, 235);
}

/* Font size classes */
.font-small {
    --font-size: 0.875rem;
    font-size: 0.875rem;
}

.font-medium {
    --font-size: 1rem;
    font-size: 1rem;
}

.font-large {
    --font-size: 1.125rem;
    font-size: 1.125rem;
}

/* Service Editor styles */
.editor-tab {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s;
}

.editor-tab.active {
    background-color: rgb(30, 41, 59);
    color: rgb(226, 232, 240);
    border-bottom: 2px solid rgb(56, 189, 248);
}

/* Service content styles */
.service-content {
    padding: 1rem;
    background-color: #1e293b;
    border-radius: 0.5rem;
    color: #e2e8f0;
}

.service-content h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #f8fafc;
}

.service-controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.primary-button {
    background-color: #0ea5e9;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.primary-button:hover {
    background-color: #0284c7;
}

.secondary-button {
    background-color: #334155;
    color: #e2e8f0;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.secondary-button:hover {
    background-color: #475569;
}

/* Friends panel styles */
.friend-item.has-new-messages {
    position: relative;
    border-color: rgba(6, 182, 212, 0.5);
    background-color: rgba(6, 182, 212, 0.1);
}

.friend-item.has-new-messages::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background-color: rgb(6, 182, 212);
    border-radius: 50%;
    animation: pulse 1.5s infinite ease-in-out;
}

/* Online/offline indicators for friends */
.online-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background-color: rgb(34, 197, 94);
    border-radius: 50%;
    border: 2px solid rgb(30, 41, 59);
}

.offline-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background-color: rgb(148, 163, 184);
    border-radius: 50%;
    border: 2px solid rgb(30, 41, 59);
}

/* System messages in chat */
.message.system {
    justify-content: center;
    margin: 1rem 0;
}

.message.system .message-content {
    background-color: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.5rem;
    padding: 0.75rem;
    max-width: 80%;
}

.message.system.invite .message-content {
    background-color: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.3);
}

/* Invitation message styles */
.system-message.invite-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background-color: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.3);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.invite-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(6, 182, 212, 0.2);
    border-radius: 50%;
    color: rgb(6, 182, 212);
}

.invite-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.invite-text {
    font-size: 0.875rem;
    color: rgb(226, 232, 240);
}

.invite-button {
    display: inline-flex;
    align-items: center;
    background-color: rgb(6, 182, 212);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    border: none;
}

.invite-button:hover {
    background-color: rgb(8, 145, 178);
}

/* Responsive styles */
/* Mobile devices (portrait) */
@media (max-width: 640px) {
    .max-w-1800px {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    header {
        padding: 0.75rem 0;
    }

    .service-card {
        height: auto;
        min-height: 220px;
    }

    .service-card h3 {
        font-size: 1rem;
    }

    .service-card p {
        font-size: 0.75rem;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }

    .update-item {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .update-item .icon-container {
        margin: 0 auto;
    }

    .user-menu {
        width: 90%; /* Slightly increased from 85% to better fit content */
        min-width: 240px; /* Ensure minimum width for content */
        max-width: 320px; /* Increased max-width to accommodate longer text */
        right: 8px; /* Position it slightly from the right edge */
        left: auto; /* Remove left:0 to prevent stretching */
        border-radius: 8px; /* Add rounded corners */
        margin-top: 8px; /* Add some space from the top */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Add shadow for better visibility */
        z-index: 100; /* Ensure it appears above other elements */
        position: absolute; /* Ensure absolute positioning */
        top: 100%; /* Position below the trigger */
    }

    /* Improve user menu items for mobile */
    .user-menu a,
    .user-menu #adminModeToggle {
        padding: 10px 12px; /* Increase padding for better touch targets */
        margin-bottom: 4px; /* Add spacing between items */
        white-space: nowrap; /* Prevent text from wrapping */
        overflow: hidden; /* Hide overflow */
        text-overflow: ellipsis; /* Add ellipsis for overflow text */
        display: block; /* Ensure block display for proper text handling */
    }

    .user-menu .p-3 {
        padding: 12px; /* Increase padding for the header section */
    }

    .user-menu .p-2 {
        padding: 8px; /* Adjust padding for the menu items container */
    }

    /* Fix text overflow in user menu */
    .user-menu p {
        white-space: nowrap; /* Prevent text from wrapping */
        overflow: hidden; /* Hide overflow */
        text-overflow: ellipsis; /* Add ellipsis for overflow text */
        max-width: 100%; /* Ensure text doesn't exceed container width */
    }

    /* Admin link styling */
    #adminLink {
        display: block; /* Ensure block display */
        width: 100%; /* Full width */
        overflow: hidden; /* Hide overflow */
        text-overflow: ellipsis; /* Add ellipsis for overflow text */
    }

    /* Adjust modal sizing for mobile */
    #addServiceModal .bg-slate-800,
    .addServiceModal .bg-slate-800,
    #serviceEditorModal .bg-slate-800,
    .serviceEditorModal .bg-slate-800 {
        width: 95% !important;
        max-height: 90vh;
        overflow-y: auto;
    }

    /* Ensure settings panels don't overflow */
    .settings-section {
        overflow-x: hidden;
        max-width: 100%;
    }

    /* Make settings tabs scrollable on mobile */
    .settings-tabs {
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        padding-bottom: 5px;
    }

    .settings-tabs::-webkit-scrollbar {
        display: none;
    }

    /* Adjust admin panels for mobile */
    .admin-panel {
        overflow-x: auto;
    }

    /* Make sure tables are responsive */
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    /* Ensure modal content is properly sized */
    .modal-content {
        width: 95% !important;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* Adjust friends panel for mobile */
    #friendListPanel, #friendChatPanel {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Fix button layouts on mobile */
    .service-controls {
        flex-direction: column;
    }

    /* Ensure form inputs fit on mobile */
    input, select, textarea {
        max-width: 100%;
    }
}

/* Small tablets and large phones */
@media (min-width: 641px) and (max-width: 768px) {
    .max-w-1800px {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .service-card {
        height: 250px;
    }

    .service-card h3 {
        font-size: 1.1rem;
    }

    /* Ensure settings panels fit properly */
    .settings-section {
        overflow-x: hidden;
    }

    /* Adjust modal sizing for tablets */
    .modal-content {
        width: 90% !important;
        max-height: 85vh;
    }

    /* Make sure tables are responsive */
    table {
        display: block;
        overflow-x: auto;
    }
}

/* Tablets and small laptops */
@media (min-width: 769px) and (max-width: 1024px) {
    .max-w-1800px {
        padding-left: 1.5rem !important;
        padding-right: 1.5rem !important;
    }

    .service-card {
        height: 260px;
    }

    /* Ensure settings panels fit properly */
    .settings-section {
        overflow-x: hidden;
    }
}

/* Laptops and desktops */
@media (min-width: 1025px) and (max-width: 1280px) {
    .service-card {
        height: 270px;
    }
}

/* Ensure proper spacing on mobile */
@media (max-width: 768px) {
    /* Adjust sidebar for mobile */
    .sidebar-container {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        width: 70%; /* Reduced from 80% to make sidebar less wide */
        max-width: 250px; /* Reduced from 300px to make sidebar less wide */
        z-index: 60; /* Higher z-index than the overlay */
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        background-color: rgb(15, 23, 42); /* Solid background color instead of transparent */
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        display: block !important; /* Ensure sidebar is always displayed in the DOM on mobile */
        /* Remove backdrop-filter to prevent blur */
    }

    .sidebar-container.open {
        transform: translateX(0);
    }

    /* Overlay for sidebar - completely hidden and disabled */
    .sidebar-overlay {
        display: none !important; /* Always hide the overlay */
        position: fixed;
        inset: 0;
        background-color: transparent;
        z-index: -1; /* Put it behind everything */
        opacity: 0;
        pointer-events: none; /* This allows clicks to pass through the overlay */
    }

    .sidebar-overlay.active {
        display: none !important; /* Ensure it's always hidden */
        opacity: 0;
    }

    /* Adjust content spacing */
    .view-content {
        padding: 0.5rem;
    }

    /* Make panels scrollable */
    .panel-content {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Fix button layouts */
    .button-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Ensure modals are properly sized and scrollable */
    .modal {
        padding: 0.5rem;
    }

    .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }

    /* Fix friends panel layout */
    #messagesContainer {
        max-height: 50vh;
    }

    /* Ensure admin panels are scrollable */
    .admin-section {
        overflow-x: auto;
    }

    /* Fix form layouts */
    .form-group {
        flex-direction: column;
    }
}

/* Fix for extra small devices */
@media (max-width: 360px) {
    .service-card {
        min-height: 200px;
    }

    h1, h2 {
        font-size: 1.25rem !important;
    }

    .nav-item {
        padding: 0.4rem 0.6rem;
        font-size: 0.9rem;
    }

    /* Ensure buttons are properly sized */
    button {
        padding: 0.4rem 0.8rem !important;
        font-size: 0.8rem !important;
    }

    /* Fix modal content */
    .modal-content {
        padding: 0.75rem !important;
    }
}

/* Ensure content is visible on landscape orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .service-card {
        height: auto;
        min-height: 180px;
    }

    .modal-content {
        max-height: 90vh;
    }

    .settings-section, .admin-section, .panel-content {
        max-height: 80vh;
    }

    /* Adjust sidebar height */
    .sidebar-container {
        max-height: 100vh;
        overflow-y: auto;
    }
}