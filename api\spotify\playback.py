from flask import jsonify, request
from . import spotify_api
from .services.spotify_client import get_spotify_client
from .decorators import require_auth
from utils.api_logger import log_api_request
import time
import logging
from spotipy.exceptions import SpotifyException

@spotify_api.route('/playback/current')
@require_auth
def get_current_playback():
    """Get current playback state"""
    try:
        sp = get_spotify_client()
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            try:
                current_playback = sp.current_playback()

                # If no active playback, return null instead of error
                if current_playback is None:
                    return jsonify(None)

                return jsonify(current_playback)

            except Exception as e:
                logging.error(f"Attempt {attempt + 1} failed: {str(e)}")
                attempt += 1
                if attempt < max_attempts:
                    time.sleep(0.5)  # Wait briefly before retry

        # If all attempts fail, return null instead of error
        return jsonify(None)
    except Exception as e:
        logging.error(f"Error getting current playback: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/playback/control/<action>', methods=['POST'])
@require_auth
@log_api_request('playback_control', 'spotify')
def control_playback(action):
    """Control playback (play, pause, skip, etc.)"""
    try:
        sp = get_spotify_client()
        logging.info(f"Executing Spotify playback control: {action}")

        if action == 'play':
            # Get optional parameters
            context_uri = request.json.get('context_uri') if request.json else None
            uris = request.json.get('uris') if request.json else None
            offset = request.json.get('offset') if request.json else None
            position_ms = request.json.get('position_ms') if request.json else None

            # Get current playback state first
            playback = sp.current_playback()
            if not playback or not playback.get('device'):
                devices = sp.devices()
                if not devices['devices']:
                    return jsonify({'error': 'No available devices'}), 400
                # Use first available device
                sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)
                time.sleep(1)  # Wait for transfer

            # Start playback with provided parameters
            sp.start_playback(
                context_uri=context_uri,
                uris=uris,
                offset=offset,
                position_ms=position_ms
            )

        elif action == 'pause':
            # Get current device info before pausing
            current_playback = sp.current_playback()
            current_device = current_playback.get('device', {}) if current_playback else {}

            # Execute pause command
            sp.pause_playback()

            # Add extra delay for external devices like TV
            if current_device.get('type') in ['TV', 'Speaker']:
                time.sleep(1.5)  # Longer delay for external devices

        elif action == 'next':
            # Get current playback state first
            playback = sp.current_playback()
            if not playback:
                return jsonify({'error': 'No active playback session'}), 404

            # Ensure there's an active device
            if not playback.get('device'):
                devices = sp.devices()
                if not devices['devices']:
                    return jsonify({'error': 'No available devices'}), 400
                # Use first available device
                sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=False)
                time.sleep(1)  # Wait for transfer

            # Skip to next track
            sp.next_track()

        elif action == 'previous':
            # Get current playback state first
            playback = sp.current_playback()
            if not playback:
                return jsonify({'error': 'No active playback session'}), 404

            # Ensure there's an active device
            if not playback.get('device'):
                devices = sp.devices()
                if not devices['devices']:
                    return jsonify({'error': 'No available devices'}), 400
                # Use first available device
                sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=False)
                time.sleep(1)  # Wait for transfer

            # Skip to previous track
            sp.previous_track()

        elif action == 'shuffle':
            state = request.json.get('state', True) if request.json else True
            sp.shuffle(state)

        elif action == 'repeat':
            state = request.json.get('state', 'context') if request.json else 'context'
            sp.repeat(state)

        elif action == 'volume':
            volume = request.json.get('volume', 50) if request.json else 50
            sp.volume(volume)

        else:
            return jsonify({'error': 'Invalid action'}), 400

        # Wait for the change to take effect
        # Use a longer delay for pause actions to ensure device status is updated correctly
        if action == 'pause':
            time.sleep(1.0)  # Longer delay for pause to ensure device status is updated
        else:
            time.sleep(0.5)  # Standard delay for other actions

        # Get updated playback state with multiple attempts to ensure accuracy
        max_attempts = 2
        attempt = 0
        updated_playback = None

        while attempt < max_attempts and (updated_playback is None or
                                         (action == 'pause' and updated_playback.get('is_playing'))):
            updated_playback = sp.current_playback()
            attempt += 1
            if attempt < max_attempts and (updated_playback is None or
                                          (action == 'pause' and updated_playback.get('is_playing'))):
                time.sleep(0.5)  # Wait before retry

        return jsonify({
            'success': True,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'item': updated_playback.get('item', None) if updated_playback else None,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })

    except SpotifyException as e:
        logging.error(f"Spotify API error: {str(e)}")
        return jsonify({'error': str(e)}), e.http_status if hasattr(e, 'http_status') else 500
    except Exception as e:
        logging.error(f"Error controlling playback: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/play-track', methods=['POST'])
@require_auth
@log_api_request('playback_control', 'spotify')
def play_track():
    """Play a specific track"""
    try:
        sp = get_spotify_client()
        data = request.json
        track_uri = data.get('uri')

        if not track_uri:
            return jsonify({'error': 'Track URI is required'}), 400

        # Get current playback state first
        playback = sp.current_playback()
        if not playback or not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)
            time.sleep(1)  # Wait for transfer

        # Start playback with the track
        sp.start_playback(uris=[track_uri])

        # Wait briefly for the change to take effect
        time.sleep(0.5)

        # Get updated playback state
        updated_playback = sp.current_playback()

        return jsonify({
            'success': True,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'item': updated_playback.get('item', None) if updated_playback else None,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })
    except Exception as e:
        logging.error(f"Error playing track: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/play', methods=['PUT'])
@require_auth
@log_api_request('playback_control', 'spotify')
def play():
    """Play tracks or context with optional parameters"""
    try:
        sp = get_spotify_client()
        data = request.json or {}

        # Extract parameters
        context_uri = data.get('context_uri')
        uris = data.get('uris')
        offset = data.get('offset')
        position_ms = data.get('position_ms')
        force_device = data.get('force_device', False)

        # Handle special case for liked songs
        if context_uri == 'saved':
            # Get user's saved tracks
            results = sp.current_user_saved_tracks(limit=50)
            if not results['items']:
                return jsonify({'error': 'No saved tracks found'}), 404

            # Extract track URIs
            uris = [item['track']['uri'] for item in results['items']]
            context_uri = None  # Clear context_uri to use uris instead

        # Get current playback state first
        playback = sp.current_playback()
        if force_device or not playback or not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)
            time.sleep(1)  # Wait for transfer

        # Start playback with provided parameters
        sp.start_playback(
            context_uri=context_uri,
            uris=uris,
            offset=offset,
            position_ms=position_ms
        )

        # Wait briefly for the change to take effect
        time.sleep(0.5)

        # Get updated playback state
        updated_playback = sp.current_playback()

        return jsonify({
            'success': True,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'item': updated_playback.get('item', None) if updated_playback else None,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })
    except Exception as e:
        logging.error(f"Error playing content: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/playback/seek', methods=['POST'])
@require_auth
@log_api_request('playback_control', 'spotify')
def seek_playback():
    """Seek to position in currently playing track"""
    try:
        sp = get_spotify_client()
        data = request.json or {}
        position_ms = data.get('position_ms')

        if position_ms is None:
            return jsonify({'error': 'position_ms is required'}), 400

        # Get current playback state first
        playback = sp.current_playback()
        if not playback:
            return jsonify({'error': 'No active playback session'}), 404

        # Ensure there's an active device
        if not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=False)
            time.sleep(1)  # Wait for transfer

        # Seek to position
        sp.seek_track(position_ms=position_ms)

        # Wait briefly for the change to take effect
        time.sleep(0.2)

        # Get updated playback state
        updated_playback = sp.current_playback()

        return jsonify({
            'success': True,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })
    except Exception as e:
        logging.error(f"Error seeking playback: {str(e)}")
        return jsonify({'error': str(e)}), 500

@spotify_api.route('/play-liked-songs', methods=['POST'])
@require_auth
@log_api_request('playback_control', 'spotify')
def play_liked_songs():
    """Play user's liked songs"""
    try:
        logging.info("Executing Spotify play liked songs")
        sp = get_spotify_client()

        # Get user's saved tracks
        results = sp.current_user_saved_tracks(limit=50)
        if not results['items']:
            return jsonify({'error': 'No saved tracks found'}), 404

        # Extract track URIs
        track_uris = [item['track']['uri'] for item in results['items']]

        # Get current playback state first
        playback = sp.current_playback()
        if not playback or not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)
            time.sleep(1)  # Wait for transfer

        # Start playback with the tracks
        sp.start_playback(uris=track_uris)

        # Wait briefly for the change to take effect
        time.sleep(0.5)

        # Get updated playback state
        updated_playback = sp.current_playback()

        return jsonify({
            'success': True,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'item': updated_playback.get('item', None) if updated_playback else None,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })
    except Exception as e:
        logging.error(f"Error playing liked songs: {str(e)}")
        return jsonify({'error': str(e)}), 500
