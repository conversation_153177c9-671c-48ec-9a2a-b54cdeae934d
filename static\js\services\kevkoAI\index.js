/**
 * KevkoAI Service
 * AI-powered chat assistant
 */

// Service class definition
window.KevkoAIService = class KevkoAIService extends window.Service {
    constructor(config) {
        super(config);
        this.openInNewTab = true;

        // Ensure the icon and color are set correctly
        this.icon = 'bot';
        this.color = 'purple';
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // No initialization needed for this service
        console.log('Initializing KevkoAI service');
    }

    /**
     * Render the service card HTML with additional features
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering KevkoAI service:`, {
                id: this.id,
                name: this.name,
                status: this.status,
                url: this.url,
                type: this.type
            });
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            // Define the action button - Go to Chat only
            const actionButtons = `
                <div class="flex flex-col gap-2">
                    <a href="/chat" target="_blank"
                       class="w-full bg-${this.color}-600 hover:bg-${this.color}-500 border border-${this.color}-500/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                        <span class="text-sm text-white">Go to Chat</span>
                        <i data-lucide="message-circle" class="h-4 w-4 ml-1 text-white"></i>
                    </a>
                </div>
            `;

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButtons}
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoAI',
            name: 'KevkoAI',
            description: 'AI-powered chat assistant',
            icon: 'bot',
            color: 'purple',
            category: 'Productivity',
            version: '1.0.0',
            author: 'Kevko',
            openInNewTab: true,
            features: [
                'AI chat',
                'Multiple models',
                'Conversation history',
                'File uploads'
            ],
            longDescription: 'KevkoAI is a state-of-the-art conversational assistant powered by advanced language models. It can help with a wide range of tasks from answering questions and providing information to assisting with creative writing and code development. The assistant maintains context throughout conversations, allowing for natural back-and-forth exchanges. KevkoAI opens in a new tab for a dedicated chat experience with minimal distractions.'
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }
};
