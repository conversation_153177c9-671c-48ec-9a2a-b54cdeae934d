/* Friends Panel CSS - Modern styling for the dashboard friends panel */

/* Theme Settings Styling */
.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 8px;
    border-radius: 8px;
    border: 2px solid transparent;
}

.theme-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.theme-option.active {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-color {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-bottom: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.theme-option:hover .theme-color {
    transform: scale(1.1);
}

.theme-option.active .theme-color {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.theme-name {
    font-size: 0.75rem;
    color: #e2e8f0;
    margin-top: 2px;
}

.theme-preview {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-preview {
    transition: all 0.3s ease;
}

.message-preview.self {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    border-top-right-radius: 4px;
}

/* Theme Colors */
.theme-blue .message-preview.self {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.theme-purple .message-preview.self {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.theme-green .message-preview.self {
    background: linear-gradient(135deg, #10b981, #059669);
}

.theme-red .message-preview.self {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.theme-orange .message-preview.self {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

.theme-teal .message-preview.self {
    background: linear-gradient(135deg, #14b8a6, #0d9488);
}

.theme-pink .message-preview.self {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.theme-indigo .message-preview.self {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.theme-amber .message-preview.self {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Message Theme Colors - Using !important to override default styles */
.message.self.theme-blue .message-content {
    background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
}

.message.self.theme-purple .message-content {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
}

.message.self.theme-green .message-content {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}

.message.self.theme-red .message-content {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

.message.self.theme-orange .message-content {
    background: linear-gradient(135deg, #f97316, #ea580c) !important;
}

.message.self.theme-teal .message-content {
    background: linear-gradient(135deg, #14b8a6, #0d9488) !important;
}

.message.self.theme-pink .message-content {
    background: linear-gradient(135deg, #ec4899, #db2777) !important;
}

.message.self.theme-indigo .message-content {
    background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
}

.message.self.theme-amber .message-content {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

/* Friend Message Theme Colors - Using !important to override default styles */
.message.friend.theme-blue .message-content {
    background: rgba(37, 99, 235, 0.2) !important;
    border: 1px solid rgba(37, 99, 235, 0.3) !important;
}

.message.friend.theme-purple .message-content {
    background: rgba(124, 58, 237, 0.2) !important;
    border: 1px solid rgba(124, 58, 237, 0.3) !important;
}

.message.friend.theme-green .message-content {
    background: rgba(5, 150, 105, 0.2) !important;
    border: 1px solid rgba(5, 150, 105, 0.3) !important;
}

.message.friend.theme-red .message-content {
    background: rgba(220, 38, 38, 0.2) !important;
    border: 1px solid rgba(220, 38, 38, 0.3) !important;
}

.message.friend.theme-orange .message-content {
    background: rgba(234, 88, 12, 0.2) !important;
    border: 1px solid rgba(234, 88, 12, 0.3) !important;
}

.message.friend.theme-teal .message-content {
    background: rgba(13, 148, 136, 0.2) !important;
    border: 1px solid rgba(13, 148, 136, 0.3) !important;
}

.message.friend.theme-pink .message-content {
    background: rgba(219, 39, 119, 0.2) !important;
    border: 1px solid rgba(219, 39, 119, 0.3) !important;
}

.message.friend.theme-indigo .message-content {
    background: rgba(79, 70, 229, 0.2) !important;
    border: 1px solid rgba(79, 70, 229, 0.3) !important;
}

.message.friend.theme-amber .message-content {
    background: rgba(217, 119, 6, 0.2) !important;
    border: 1px solid rgba(217, 119, 6, 0.3) !important;
}

/* Friend List Styling */
#friendList {
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
}

#friendList::-webkit-scrollbar {
    width: 4px;
}

#friendList::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 4px;
}

#friendList::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 4px;
}

/* Friend Item Styling */
#friendList .friend-item {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

#friendList .friend-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, rgba(14, 165, 233, 0.2), transparent);
    transition: width 0.3s ease;
}

#friendList .friend-item:hover::before {
    width: 100%;
}

#friendList .friend-item.active::before {
    width: 100%;
    background: linear-gradient(90deg, rgba(14, 165, 233, 0.3), rgba(14, 165, 233, 0.1));
}

#friendList .friend-item.active {
    border-color: rgba(14, 165, 233, 0.5);
}

/* Friend Avatar Styling */
.friend-avatar, .user-avatar {
    position: relative;
    overflow: hidden;
}

.friend-avatar img, .user-avatar img {
    transition: transform 0.3s ease;
}

.friend-avatar:hover img, .user-avatar:hover img {
    transform: scale(1.05);
}

.online-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #10b981;
    border: 2px solid #1e293b;
    z-index: 1;
}

.offline-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #6b7280;
    border: 2px solid #1e293b;
    z-index: 1;
}

/* Messages Container Styling */
#messagesContainer {
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
    padding-bottom: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
}

#messagesContainer::-webkit-scrollbar {
    width: 4px;
}

#messagesContainer::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 4px;
}

#messagesContainer::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 4px;
}

/* Mobile styles for messages container */
@media (max-width: 768px) {
    #messagesContainer {
        padding-bottom: 24px;
        padding-left: 8px;
        padding-right: 8px;
    }
}

/* Message Styling */
.message {
    margin-bottom: 12px;
    display: flex;
    animation: fadeIn 0.3s ease;
    position: relative;
    width: 100%;
}

@media (max-width: 768px) {
    .message {
        margin-bottom: 8px;
        width: 100%;
    }
}

.message.self {
    justify-content: flex-end;
}

.message.friend {
    justify-content: flex-start;
}

/* System message styling */
.message.system {
    justify-content: center;
    margin: 16px 0;
}

.message.system .system-content {
    max-width: 90%;
    background-color: transparent;
    box-shadow: none;
    padding: 0;
}

.message.system .system-time {
    text-align: center;
    margin-top: 4px;
    opacity: 0.7;
    font-size: 0.7rem;
}

/* System message types */
.system-message {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(51, 65, 85, 0.5);
    font-size: 0.85rem;
}

.system-message.info-message {
    background-color: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.3);
    color: #38bdf8;
}

.system-message.warning-message {
    background-color: rgba(234, 179, 8, 0.1);
    border-color: rgba(234, 179, 8, 0.3);
    color: #facc15;
}

.system-message.error-message {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #f87171;
}

/* Invitation message styling */
.system-message.invite-message {
    display: flex;
    padding: 0;
    overflow: hidden;
    background-color: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.3);
}

.invite-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(14, 165, 233, 0.2);
    padding: 12px;
    color: #38bdf8;
}

.invite-content {
    padding: 12px;
    flex-grow: 1;
}

.invite-text {
    margin-bottom: 8px;
    color: #e2e8f0;
}

.invite-button {
    display: inline-flex;
    align-items: center;
    background-color: #0ea5e9;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    outline: none;
}

.invite-button:hover {
    background-color: #0284c7;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invite-button:focus {
    outline: 2px solid rgba(14, 165, 233, 0.5);
    outline-offset: 2px;
}

/* Message time gap styling */
.message + .message {
    margin-top: 4px;
}

/* Add more space between messages with significant time gap */
.date-separator + .message {
    margin-top: 16px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-content {
    max-width: 75%;
    padding: 10px 14px;
    border-radius: 18px;
    position: relative;
    overflow: hidden;
}

@media (max-width: 768px) {
    .message-content {
        max-width: 85%;
        padding: 8px 12px;
    }
}

.message-text {
    white-space: pre-wrap; /* Preserve line breaks */
    word-wrap: break-word; /* Break long words */
}

/* Message Images */
.message-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    max-width: 100%;
}

.message-image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-image-preview:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Default styles that will be overridden by theme-specific styles */
.message.self:not([class*="theme-"]) .message-content {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    color: white;
    border-top-right-radius: 4px;
}

.message.friend:not([class*="theme-"]) .message-content {
    background: rgba(51, 65, 85, 0.8);
    color: #e2e8f0;
    border-top-left-radius: 4px;
}

/* Ensure all self messages have white text and the right border radius */
.message.self .message-content {
    color: white !important;
    border-top-right-radius: 4px !important;
}

/* Ensure all friend messages have the right text color and border radius */
.message.friend .message-content {
    color: #e2e8f0 !important;
    border-top-left-radius: 4px !important;
}

.message-time {
    font-size: 0.7rem;
    margin-top: 4px;
    opacity: 0.8;
    text-align: right;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 8px;
    align-self: flex-end;
}

.message.self .message-avatar {
    margin-right: 0;
    margin-left: 8px;
}

/* Date Separator */
.date-separator {
    display: flex;
    align-items: center;
    margin: 20px 0 8px 0;
    color: #94a3b8;
    font-size: 0.7rem;
    opacity: 0.7;
}

.date-separator::before,
.date-separator::after {
    content: "";
    flex: 1;
    height: 1px;
    background-color: rgba(148, 163, 184, 0.2);
}

.date-separator::before {
    margin-right: 12px;
}

.date-separator::after {
    margin-left: 12px;
}

/* Add a small gap between messages with time difference */
.message + .message {
    position: relative;
}

/* Notification Container Styling */
#notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    pointer-events: none;
}

.notification {
    pointer-events: auto;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    border-radius: 6px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

/* Friends Menu Modal Styling */
#friendsMenuModal .modal-content {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
}

#friendsMenuModal .group {
    position: relative;
    overflow: hidden;
}

#friendsMenuModal .group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 65%, rgba(255, 255, 255, 0.08) 100%);
    z-index: 1;
    transition: all 0.3s ease;
}

#friendsMenuModal .group:hover::before {
    background: linear-gradient(45deg, transparent 50%, rgba(255, 255, 255, 0.12) 100%);
}

/* Chat Theme Modal Styling */
#chatThemeModal .modal-content {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 20px 50px -10px rgba(0, 0, 0, 0.5), 0 10px 20px -10px rgba(0, 0, 0, 0.3);
}

#chatThemeModal .theme-option {
    position: relative;
    transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
}

#chatThemeModal .theme-color {
    position: relative;
    transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
}

#chatThemeModal .theme-option.selected .theme-color {
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

#chatThemeModal .theme-selected-indicator {
    transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
}

#previewSelfMessage {
    transition: background 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Theme color preview in the header */
#selectedThemeColor {
    transition: background 0.3s ease;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

/* Apply button pulse animation */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

#applyThemeBtn {
    animation: pulse 2s infinite;
}

.message + .message.time-gap {
    margin-top: 16px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 16px;
    color: #94a3b8;
    font-size: 0.75rem;
}

.typing-dots {
    display: flex;
    margin-left: 8px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #94a3b8;
    margin-right: 3px;
    animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
    margin-right: 0;
}

@keyframes typingAnimation {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-4px);
    }
}

/* Welcome Message Styling */
#welcomeMessage {
    background: radial-gradient(circle at center, rgba(14, 165, 233, 0.1), transparent 70%);
    border-radius: 16px;
    transition: all 0.3s ease;
}

#welcomeMessage .welcome-icon {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    box-shadow: 0 8px 16px rgba(14, 165, 233, 0.2);
    transition: all 0.3s ease;
}

#welcomeMessage:hover .welcome-icon {
    transform: scale(1.05);
}

/* Message Input Styling */
.message-input-container {
    position: relative;
    transition: all 0.2s ease;
    display: flex;
    align-items: center; /* Center items vertically */
    width: 100%; /* Ensure full width */
}

#messageInput {
    resize: none;
    transition: all 0.2s ease;
    min-height: 42px;
    max-height: 120px;
    padding-right: 40px; /* Padding for send button */
    padding-left: 40px; /* Padding for image button */
    overflow-y: auto; /* Allow scrolling for longer messages */
    line-height: 1.5;
    white-space: pre-wrap; /* Preserve line breaks */
    word-wrap: break-word; /* Break long words */
    scrollbar-width: none; /* Hide scrollbar in Firefox */
    text-align: left; /* Left-align the text */
    width: 100%; /* Ensure full width */
}

#messageInput::-webkit-scrollbar {
    display: none; /* Hide scrollbar in Chrome/Safari/Edge */
}

#messageInput:focus {
    border-color: #0ea5e9;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

/* Themed input field styles */
/* Default focus style will be overridden by theme-specific styles */
:root {
    --focus-ring-color-blue: rgba(37, 99, 235, 0.5);
    --focus-ring-color-green: rgba(16, 185, 129, 0.5);
    --focus-ring-color-purple: rgba(139, 92, 246, 0.5);
    --focus-ring-color-pink: rgba(236, 72, 153, 0.5);
    --focus-ring-color-orange: rgba(249, 115, 22, 0.5);
    --focus-ring-color-red: rgba(239, 68, 68, 0.5);
    --focus-ring-color-yellow: rgba(234, 179, 8, 0.5);
    --focus-ring-color-teal: rgba(20, 184, 166, 0.5);
    --focus-ring-color-cyan: rgba(6, 182, 212, 0.5);
}

/* Blue theme input */
.theme-input-blue {
    border-color: rgba(37, 99, 235, 0.5) !important;
}

.theme-input-blue:focus {
    border-color: rgb(37, 99, 235) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-blue) !important;
}

/* Green theme input */
.theme-input-green {
    border-color: rgba(16, 185, 129, 0.5) !important;
}

.theme-input-green:focus {
    border-color: rgb(16, 185, 129) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-green) !important;
}

/* Purple theme input */
.theme-input-purple {
    border-color: rgba(139, 92, 246, 0.5) !important;
}

.theme-input-purple:focus {
    border-color: rgb(139, 92, 246) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-purple) !important;
}

/* Pink theme input */
.theme-input-pink {
    border-color: rgba(236, 72, 153, 0.5) !important;
}

.theme-input-pink:focus {
    border-color: rgb(236, 72, 153) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-pink) !important;
}

/* Orange theme input */
.theme-input-orange {
    border-color: rgba(249, 115, 22, 0.5) !important;
}

.theme-input-orange:focus {
    border-color: rgb(249, 115, 22) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-orange) !important;
}

/* Red theme input */
.theme-input-red {
    border-color: rgba(239, 68, 68, 0.5) !important;
}

.theme-input-red:focus {
    border-color: rgb(239, 68, 68) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-red) !important;
}

/* Yellow theme input */
.theme-input-yellow {
    border-color: rgba(234, 179, 8, 0.5) !important;
}

.theme-input-yellow:focus {
    border-color: rgb(234, 179, 8) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-yellow) !important;
}

/* Amber theme input (same as yellow) */
.theme-input-amber {
    border-color: rgba(245, 158, 11, 0.5) !important;
}

.theme-input-amber:focus {
    border-color: rgb(245, 158, 11) !important;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.5) !important;
}

/* Teal theme input */
.theme-input-teal {
    border-color: rgba(20, 184, 166, 0.5) !important;
}

.theme-input-teal:focus {
    border-color: rgb(20, 184, 166) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-teal) !important;
}

/* Cyan theme input */
.theme-input-cyan {
    border-color: rgba(6, 182, 212, 0.5) !important;
}

.theme-input-cyan:focus {
    border-color: rgb(6, 182, 212) !important;
    box-shadow: 0 0 0 2px var(--focus-ring-color-cyan) !important;
}

/* Indigo theme input */
.theme-input-indigo {
    border-color: rgba(79, 70, 229, 0.5) !important;
}

.theme-input-indigo:focus {
    border-color: rgb(79, 70, 229) !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5) !important;
}

#messageInput.drag-over {
    border-color: #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

/* Image Button Styling */
#imageBtn {
    position: absolute;
    left: 8px;
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Ensure perfect vertical centering */
    height: 30px;
    width: 30px;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    border-radius: 50%;
    color: #94a3b8; /* text-slate-400 equivalent */
}

#imageBtn:hover {
    background-color: rgba(14, 165, 233, 0.1);
    color: #0ea5e9; /* text-cyan-400 equivalent */
}

/* Responsive adjustments for small screens */
@media (min-width: 640px) {
    #imageBtn {
        left: 12px;
    }
}

/* Image Preview Container */
#imagePreviewContainer {
    position: relative;
    transition: all 0.3s ease;
}

#imagePreviewContainer.hidden {
    display: none;
}

/* Image Preview Styling */
.image-preview {
    max-width: 120px;
    max-height: 120px;
    border-radius: 8px;
    object-fit: contain;
    border: 2px solid rgba(14, 165, 233, 0.2);
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.25rem;
}

.image-preview:hover {
    transform: scale(1.05);
    border-color: rgba(14, 165, 233, 0.4);
}

.image-preview-container {
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.remove-image-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: rgba(239, 68, 68, 0.8);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
    border: none;
    padding: 0;
}

.remove-image-btn:hover {
    background-color: rgb(239, 68, 68);
    transform: scale(1.1);
}

.global-remove-image-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: rgba(51, 65, 85, 0.8);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
    border: none;
    padding: 0;
}

.global-remove-image-btn:hover {
    background-color: rgba(239, 68, 68, 0.8);
}

/* Message Images */
.message-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    max-width: 100%;
}

.message-image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    object-fit: contain;
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-image-preview:hover {
    transform: scale(1.03);
    cursor: pointer;
}

/* Image Viewer Modal */
#imageViewerModal {
    z-index: 9999;
}

#imageViewerModal .modal-content {
    background-color: transparent;
    box-shadow: none;
    transform: none;
    max-width: 100%;
    max-height: 100%;
}

#fullSizeImage {
    max-width: 95vw;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

#closeImageViewerBtn {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#closeImageViewerBtn:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

#sendButton {
    position: absolute;
    right: 8px;
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Ensure perfect vertical centering */
    height: 30px; /* Smaller height */
    width: 30px; /* Smaller width */
    padding: 0.25rem; /* Smaller padding */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    border-radius: 50%; /* Make it circular */
    color: #0ea5e9; /* Cyan color */
    background-color: transparent; /* Transparent background */
}

#sendButton i {
    height: 16px; /* Smaller icon */
    width: 16px; /* Smaller icon */
}

#sendButton:hover:not(:disabled) {
    background-color: rgba(14, 165, 233, 0.1); /* Light cyan background on hover */
    transform: translateY(-50%) scale(1.05); /* Scale up slightly on hover */
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

#sendButton:active:not(:disabled) {
    transform: translateY(-50%) scale(1);
}

/* Responsive adjustments for small screens */
@media (min-width: 640px) {
    #sendButton {
        right: 12px;
    }
}

/* Friend Request Badge */
#requestBadge {
    transition: all 0.3s ease;
}

#requestBadge.pulse {
    animation: pulseBadge 2s infinite;
}

@keyframes pulseBadge {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Modal Styling Enhancements */
.modal {
    backdrop-filter: blur(8px);
    z-index: 9999;
}

.modal-content {
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10000;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.modal:not(.hidden) .modal-content {
    transform: translateY(0);
    opacity: 1;
}

body.modal-open {
    overflow: hidden;
}

.search-result-item {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.search-result-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, rgba(14, 165, 233, 0.1), transparent);
    transition: width 0.3s ease;
}

.search-result-item:hover::before {
    width: 100%;
}

.friend-request-btn {
    transition: all 0.2s ease;
}

.friend-request-btn:hover {
    transform: translateY(-2px);
}

.friend-request-btn:active {
    transform: translateY(0);
}

/* Empty State Styling */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: #94a3b8;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: rgba(30, 41, 59, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.empty-state-text {
    max-width: 300px;
}

/* Group Chat Styling */
.group-chat-item {
    position: relative;
    transition: all 0.2s ease;
    overflow: hidden;
}

.group-chat-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, rgba(124, 58, 237, 0.2), transparent);
    transition: width 0.3s ease;
}

.group-chat-item:hover::before {
    width: 100%;
}

.group-chat-item.active::before {
    width: 100%;
    background: linear-gradient(90deg, rgba(124, 58, 237, 0.3), rgba(124, 58, 237, 0.1));
}

.group-chat-item.active {
    border-color: rgba(124, 58, 237, 0.5);
}

.group-chat-avatar {
    position: relative;
    overflow: hidden;
    background: transparent;
}

/* Sidebar group chat avatar specific styling */
.friend-item .group-chat-avatar,
.group-chat-item .group-chat-avatar {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    margin-right: 12px;
    border-radius: 50%;
}

.group-chat-badge {
    display: none; /* Hide the badge */
}

.create-group-btn {
    background-color: #7c3aed;
    transition: all 0.2s ease;
}

.create-group-btn:hover {
    background-color: #6d28d9;
    transform: translateY(-1px);
}

.create-group-btn:active {
    transform: translateY(0);
}

.group-member-item {
    transition: all 0.2s ease;
    position: relative;
}

.group-member-item:hover {
    background-color: rgba(51, 65, 85, 0.5);
}

.group-member-remove {
    opacity: 0;
    transition: all 0.2s ease;
}

.group-member-item:hover .group-member-remove {
    opacity: 1;
}

.group-chat-name {
    transition: all 0.2s ease;
}

.group-chat-name:hover {
    background-color: rgba(51, 65, 85, 0.5);
}

.group-chat-name-edit {
    display: none;
}

.group-chat-name.editing .group-chat-name-display {
    display: none;
}

.group-chat-name.editing .group-chat-name-edit {
    display: flex;
}

.friend-select-item {
    transition: all 0.2s ease;
}

.friend-select-item:hover {
    background-color: rgba(51, 65, 85, 0.5);
}

.friend-select-item.selected {
    background-color: rgba(124, 58, 237, 0.2);
    border-color: rgba(124, 58, 237, 0.5);
}

/* Friend Action Buttons */
.friend-action-btn {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.friend-action-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.friend-action-btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}

/* Toggle Buttons */
.panel-toggle-btn {
    transition: all 0.3s ease;
    cursor: pointer;
}

.panel-toggle-btn:hover {
    background-color: rgba(51, 65, 85, 0.5);
}

.panel-toggle-btn i {
    transition: transform 0.3s ease;
}

.panel-toggle-btn.collapsed i {
    transform: rotate(180deg);
}

/* Collapsible Panels */
.collapsible-panel {
    max-height: 500px;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease, margin 0.3s ease;
}

@media (max-width: 768px) {
    .collapsible-panel {
        max-height: 300px;
    }
    
    /* Improve mobile chat experience */
    .chat-panel {
        padding: 0.75rem;
    }
    
    .chat-header {
        padding: 0.75rem;
    }
    
    .chat-footer {
        padding: 0.75rem;
    }
    
    .message-input-container {
        padding: 0.5rem;
    }
    
    /* Ensure messages are fully visible */
    .message.self .message-content,
    .message.friend .message-content {
        max-width: 85%;
        margin: 0 4px;
    }
    
    /* Ensure message text is fully visible */
    .message-text {
        width: 100%;
        overflow-wrap: break-word;
        word-break: break-word;
    }
}

.collapsible-panel.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    border-width: 0 !important;
}

/* Friend Header Styling */
.friend-header {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 2px solid transparent;
}

.friend-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.5), transparent);
    transition: all 0.3s ease;
}

/* Themed header styles */
.theme-header-blue {
    border-left-color: rgba(37, 99, 235, 0.7);
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.05), transparent);
}
.theme-header-blue::after {
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.5), transparent);
}
.theme-header-blue #activeFriendName {
    color: rgba(96, 165, 250, 1);
    text-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
}

.theme-header-green {
    border-left-color: rgba(16, 185, 129, 0.7);
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.05), transparent);
}
.theme-header-green::after {
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.5), transparent);
}
.theme-header-green #activeFriendName {
    color: rgba(52, 211, 153, 1);
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.theme-header-purple {
    border-left-color: rgba(139, 92, 246, 0.7);
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.05), transparent);
}
.theme-header-purple::after {
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
}
.theme-header-purple #activeFriendName {
    color: rgba(167, 139, 250, 1);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.theme-header-pink {
    border-left-color: rgba(236, 72, 153, 0.7);
    background: linear-gradient(90deg, rgba(236, 72, 153, 0.05), transparent);
}
.theme-header-pink::after {
    background: linear-gradient(90deg, transparent, rgba(236, 72, 153, 0.5), transparent);
}
.theme-header-pink #activeFriendName {
    color: rgba(244, 114, 182, 1);
    text-shadow: 0 0 10px rgba(236, 72, 153, 0.3);
}

.theme-header-orange {
    border-left-color: rgba(249, 115, 22, 0.7);
    background: linear-gradient(90deg, rgba(249, 115, 22, 0.05), transparent);
}
.theme-header-orange::after {
    background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.5), transparent);
}
.theme-header-orange #activeFriendName {
    color: rgba(251, 146, 60, 1);
    text-shadow: 0 0 10px rgba(249, 115, 22, 0.3);
}

.theme-header-red {
    border-left-color: rgba(239, 68, 68, 0.7);
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.05), transparent);
}
.theme-header-red::after {
    background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.5), transparent);
}
.theme-header-red #activeFriendName {
    color: rgba(248, 113, 113, 1);
    text-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

.theme-header-amber, .theme-header-yellow {
    border-left-color: rgba(234, 179, 8, 0.7);
    background: linear-gradient(90deg, rgba(234, 179, 8, 0.05), transparent);
}
.theme-header-amber::after, .theme-header-yellow::after {
    background: linear-gradient(90deg, transparent, rgba(234, 179, 8, 0.5), transparent);
}
.theme-header-amber #activeFriendName, .theme-header-yellow #activeFriendName {
    color: rgba(250, 204, 21, 1);
    text-shadow: 0 0 10px rgba(234, 179, 8, 0.3);
}

.theme-header-teal {
    border-left-color: rgba(20, 184, 166, 0.7);
    background: linear-gradient(90deg, rgba(20, 184, 166, 0.05), transparent);
}
.theme-header-teal::after {
    background: linear-gradient(90deg, transparent, rgba(20, 184, 166, 0.5), transparent);
}
.theme-header-teal #activeFriendName {
    color: rgba(45, 212, 191, 1);
    text-shadow: 0 0 10px rgba(20, 184, 166, 0.3);
}

.theme-header-indigo {
    border-left-color: rgba(79, 70, 229, 0.7);
    background: linear-gradient(90deg, rgba(79, 70, 229, 0.05), transparent);
}
.theme-header-indigo::after {
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
}
.theme-header-indigo #activeFriendName {
    color: rgba(129, 140, 248, 1);
    text-shadow: 0 0 10px rgba(79, 70, 229, 0.3);
}

/* Encryption Badge */
.encryption-badge {
    display: none !important;
    align-items: center;
    font-size: 0.7rem;
    color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 12px;
    padding: 2px 8px;
    margin-top: 4px;
}

.encryption-badge i {
    margin-right: 4px;
}
