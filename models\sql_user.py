from database import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

class SQLUser(db.Model, UserMixin):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    google_id = db.Column(db.String(128), unique=True, nullable=True)
    profile_picture = db.Column(db.String(255), nullable=True)
    is_admin = db.Column(db.Bo<PERSON>an, default=False)
    is_super_admin = db.Column(db.Boolean, default=False)
    show_on_contacts = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, default=datetime.utcnow)
    display_name = db.Column(db.String(80), default="")
    contact_settings = db.Column(db.String(255), default="")
    two_factor_enabled = db.Column(db.Boolean, default=False)
    country_code = db.Column(db.String(2), default="")
    timezone = db.Column(db.String(50), default="")
    
    # Relationships
    friends = db.relationship('SQLUser', 
                             secondary='friendships',
                             primaryjoin='SQLUser.id==Friendship.user_id',
                             secondaryjoin='SQLUser.id==Friendship.friend_id',
                             backref=db.backref('friended_by', lazy='dynamic'))
    
    @staticmethod
    def normalize_username(username):
        """Normalize username to lowercase and remove special characters"""
        return ''.join(e.lower() for e in username if e.isalnum())
    
    def set_username(self, username):
        """Set normalized username"""
        self.username = self.normalize_username(username)
    
    @classmethod
    def find_by_login(cls, login):
        """Find user by either username or email"""
        normalized_login = cls.normalize_username(login)
        return cls.query.filter(
            (cls.username == normalized_login) | (cls.email == login)
        ).first()
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)
    
    def is_google_user(self):
        """Check if user is authenticated via Google"""
        return bool(self.google_id)
    
    def update_last_login(self):
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def update_country_code(self, country_code):
        """Update user's country code if not already set"""
        if not self.country_code or self.country_code.strip() == "":
            self.country_code = country_code
            db.session.commit()
            return True
        return False
    
    def update_timezone(self, timezone_str):
        """Update user's timezone"""
        if timezone_str and timezone_str.strip() != "":
            self.timezone = timezone_str
            db.session.commit()
            return True
        return False

# Friendship association table
class Friendship(db.Model):
    __tablename__ = 'friendships'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    friend_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Ensure unique friendships
    __table_args__ = (
        db.UniqueConstraint('user_id', 'friend_id', name='unique_friendship'),
    )