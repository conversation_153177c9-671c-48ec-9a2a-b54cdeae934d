/* Global Styles */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background-color: rgb(21,21,21);
    font-family: system-ui, -apple-system, sans-serif;
    overflow: hidden; /* Prevent all scrolling */
  }

  /* Container for centering the chat interface */
  .chat-center {
    display: flex;
    height: 100vh;
    width: 100vw;
    align-items: stretch;
    justify-content: stretch;
    padding: 0;
    overflow: hidden;
    position: fixed; /* Fix position to prevent scrolling */
    top: 0;
    left: 0;
  }

  /* Main Chat Container */
  .chat-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    background: linear-gradient(135deg, rgb(28, 28, 32) 0%, rgb(25, 25, 30) 100%);
    color: #f3f4f6;
    display: flex;
    border: none;
    position: relative;
  }

  /* Left Sidebar */
  .sidebar {
    width: 320px;
    background: linear-gradient(180deg, rgba(25, 25, 30, 0.95) 0%, rgba(28, 28, 32, 0.95) 100%);
    border-right: 1px solid rgba(49, 94, 248, 0.1);
    padding: 1.5rem 1.5rem 5rem 1.5rem; /* Extra padding at bottom for footer */
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    z-index: 10;
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
    overflow: hidden; /* Prevent sidebar from scrolling */
    box-sizing: border-box; /* Include padding in height calculation */
  }

  /* Mobile sidebar styles */
  @media (max-width: 768px) {
    .sidebar {
      position: fixed;
      left: 0;
      top: 0;
      bottom: 0;
      transform: translateX(-100%);
      box-shadow: none;
      width: 85%; /* Wider on mobile */
      max-width: 300px;
      z-index: 1001; /* Higher than input area to ensure it's above */
      height: 100%;
      padding-bottom: calc(env(safe-area-inset-bottom, 0) + 60px); /* Add padding for input area */
    }
    
    .sidebar-footer {
      bottom: calc(env(safe-area-inset-bottom, 0)); /* Account for safe area on mobile */
    }

    .sidebar.open {
      transform: translateX(0);
      box-shadow: 5px 0 15px rgba(0, 0, 0, 0.3);
    }

    /* Add overlay when sidebar is open */
    .sidebar.open::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: -1;
    }
    
    /* Ensure sidebar content doesn't overflow */
    .sidebar-header, #chatList, .sidebar-footer {
      width: 100%;
    }
  }

  /* Sidebar Header */
  .sidebar-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(49, 94, 248, 0.1);
    position: relative;
    flex-shrink: 0; /* Prevent header from shrinking */
  }

  .sidebar-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 50%;
    height: 2px;
    background: linear-gradient(to right, rgb(49, 94, 248), transparent);
  }

  .sidebar-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
  }

  .sidebar-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(80, 120, 255) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    margin: 0;
    letter-spacing: -0.5px;
  }

  .sidebar-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
  }

  /* Close sidebar button */
  #closeSidebarBtn {
    display: none;
    align-items: center;
    justify-content: center;
    height: 2rem;
    width: 2rem;
    padding: 0;
    border-radius: 9999px;
    border: 1px solid rgb(73,73,73);
    color: rgb(186, 193, 205);
    background-color: transparent;
    cursor: pointer;
  }

  #closeSidebarBtn i {
    width: 1rem;
    height: 1rem;
  }

  @media (max-width: 768px) {
    #closeSidebarBtn {
      display: flex;
    }
  }

  /* Mobile-only elements */
  .mobile-only {
    display: none;
  }

  @media (max-width: 768px) {
    .mobile-only {
      display: flex;
    }
  }

  /* Sidebar Buttons */
  .sidebar-buttons {
    grid-template-columns: repeat(2, 1fr);
    width: 100%;
    gap: 0.5rem;
    background-color: transparent;
    padding: 0;
  }

  .sidebar-buttons button {
    border-radius: 0.5rem;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
  }

  /* Chat List in Sidebar */
  #chatList {
    flex: 1; /* Take up available space */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 4rem; /* Space for footer */
    padding-bottom: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: rgba(49, 94, 248, 0.3) transparent;
    max-height: calc(100vh - 180px); /* Adjust based on header and footer height */
  }
  
  #chatList::-webkit-scrollbar {
    width: 6px;
  }
  
  #chatList::-webkit-scrollbar-track {
    background: transparent;
  }
  
  #chatList::-webkit-scrollbar-thumb {
    background-color: rgba(49, 94, 248, 0.3);
    border-radius: 10px;
  }
  
  #chatList::-webkit-scrollbar-thumb:hover {
    background-color: rgba(49, 94, 248, 0.5);
  }

  /* Sidebar Footer */
  .sidebar-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem 1.5rem;
    flex-shrink: 0; /* Prevent footer from shrinking */
    border-top: 1px solid rgba(49, 94, 248, 0.1);
    background: linear-gradient(180deg, rgba(25, 25, 30, 0.8) 0%, rgba(28, 28, 32, 0.95) 100%);
    z-index: 2;
  }

  .sidebar-footer button {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
  }

  #sidebarSettingsBtn {
    height: 2.5rem;
    min-width: 2.5rem;
    border-radius: 9999px;
    border: 1px solid rgb(73,73,73);
    color: rgb(156,163,175);
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    white-space: nowrap;
  }

  #sidebarSettingsBtn:hover {
    width: auto;
    padding: 0 0.75rem;
    background-color: rgba(73,73,73,0.5);
    color: #ffffff;
  }

  #sidebarSettingsBtn svg {
    width: 1.25rem;
    height: 1.25rem;
    min-width: 1.25rem;
  }

  #sidebarSettingsBtn span {
    display: none;
    margin-left: 0.5rem;
    font-size: 0.875rem;
  }

  #sidebarSettingsBtn:hover span {
    display: inline;
  }

  #createChatBtn {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.2) 100%);
    border: 1px solid rgba(49, 94, 248, 0.2);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: rgb(200, 210, 255);
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(49, 94, 248, 0.1);
  }
  
  #createChatBtn:hover {
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
    color: white;
  }

  /* Main Chat Area */
  .main-chat {
    background: linear-gradient(135deg, rgb(30, 30, 35) 0%, rgb(28, 28, 32) 100%);
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: env(safe-area-inset-top, 0); /* Add margin for browser search bar */
    padding-bottom: env(safe-area-inset-bottom, 0); /* Add padding for mobile navigation bar */
  }

  /* Add subtle pattern overlay to main chat */
  .main-chat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.4;
    z-index: 0;
    pointer-events: none;
  }

  /* Main Chat Header */
  .main-chat header {
    background: linear-gradient(90deg, rgba(28, 28, 32, 0.9) 0%, rgba(30, 30, 35, 0.9) 100%);
    border-bottom: 1px solid rgba(49, 94, 248, 0.1);
    padding: 1rem 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 5rem;
  }

  .main-chat header .header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .main-chat header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(200, 200, 220) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    position: relative;
  }

  .main-chat header h2::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30%;
    height: 2px;
    background: linear-gradient(to right, rgb(49, 94, 248), transparent);
    opacity: 0.7;
  }

  /* Header buttons container */
  .main-chat header > div:last-child {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Mobile toggle button */
  #sidebarToggleBtn {
    display: none;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
    border-radius: 9999px;
    border: 1px solid rgb(73,73,73);
    color: rgb(156,163,175);
    background-color: transparent;
    cursor: pointer;
    z-index: 20; /* Ensure it's above other elements */
  }

  #sidebarToggleBtn i {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* Mobile styles */
  @media (max-width: 768px) {
    #sidebarToggleBtn {
      display: flex;
    }

    .main-chat header h2 {
      font-size: 1rem;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-chat header button span {
      display: none;
    }

    .main-chat header button:hover span {
      display: none;
    }

    /* Sidebar overlay is now handled in the mobile sidebar styles */

    /* Adjust chat content padding */
    #chatContent {
      padding: 0.75rem;
      padding-bottom: calc(60px + env(safe-area-inset-bottom, 0)); /* Add padding to account for fixed input area */
    }

    /* Adjust modal positioning */
    #deleteModal .modal-content,
    #settingsModal .modal-content {
      width: 90%;
      max-width: 350px;
    }
  }

  .main-chat header button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.85rem;
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.15) 100%);
    border: 1px solid rgba(49, 94, 248, 0.2);
    border-radius: 0.5rem;
    color: rgb(200, 210, 255);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  .main-chat header button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
  }

  .main-chat header button:hover {
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
    color: white;
  }

  .main-chat header button:hover::before {
    transform: translateX(100%);
  }

  .main-chat header button i {
    color: rgb(49, 94, 248);
    transition: all 0.3s ease;
    width: 1rem;
    height: 1rem;
    min-width: 1rem; /* Prevent icon from shrinking */
  }

  .main-chat header button:hover i {
    color: white;
    transform: scale(1.1);
  }

  .main-chat header button span {
    font-size: 0.875rem;
    transition: all 0.3s ease;
  }

  /* Chat Content Area */
  #chatContent {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    position: relative;
    z-index: 1;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(49, 94, 248, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(49, 94, 248, 0.03) 0%, transparent 50%);
    animation: fadeIn 0.5s ease-in-out;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(49, 94, 248, 0.3) transparent;
    height: calc(100vh - 180px);
    flex: 1;
    max-height: calc(100vh - 180px); /* Ensure it doesn't exceed available space */
  }

  #chatContent::-webkit-scrollbar {
    width: 6px;
  }

  #chatContent::-webkit-scrollbar-track {
    background: transparent;
  }

  #chatContent::-webkit-scrollbar-thumb {
    background-color: rgba(49, 94, 248, 0.3);
    border-radius: 10px;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Input Area */
  .input-area {
    background: linear-gradient(0deg, rgba(25, 25, 30, 0.9) 0%, rgba(28, 28, 32, 0.8) 100%);
    border-top: 1px solid rgba(49, 94, 248, 0.1);
    padding: 1.25rem;
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: env(safe-area-inset-bottom, 0); /* Add margin for mobile navigation bar */
  }

  .input-container {
    max-width: 90%;
    margin: 0 auto;
  }

  /* Message input wrapper */
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: flex-start; /* Changed to flex-start */
    gap: 0.75rem;
    margin-top: 0.5rem;
    width: 100%;
  }

  .function-buttons {
    display: flex; /* Change from grid to flex */
    justify-content: center; /* Already centered */
    gap: 0.5rem; /* Consistent small gap */
    align-items: center;
  }

  /* Add specific styling for single button case */
  .function-buttons button {
    margin: 0; /* Remove any default margins */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: transparent;
    border: 1px solid rgb(73,73,73);
    color: rgb(156,163,175);
    transition: all 0.2s;
    height: 2.5rem;
    width: 2.5rem;
  }

  /* Ensure the button text appears on hover and is centered */
  .function-buttons button span {
    position: absolute;
    background-color: rgb(32, 32, 38);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    transform: translateY(-130%);
    opacity: 0;
    transition: opacity 0.2s;
  }

  .function-buttons button:hover span {
    opacity: 1;
  }

  /* Mobile styles for input area */
  @media (max-width: 768px) {
    .input-area {
      padding: 0.75rem 0.5rem;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
      margin-bottom: env(safe-area-inset-bottom, 0); /* Add margin for mobile navigation bar */
      z-index: 1000; /* Ensure it's above other content but below sidebar */
      background: rgba(25, 25, 30, 0.95); /* More opaque background */
      box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.2); /* Stronger shadow */
    }
    
    /* When sidebar is open, we don't need to adjust input area position
       as the sidebar will be on top of it with higher z-index */

    .input-container {
      padding: 0 0.5rem;
    }

    .function-buttons {
      gap: 0.25rem;
    }

    .function-buttons button {
      height: 2.25rem;
      width: 2.25rem;
    }

    .input-wrapper {
      gap: 0.5rem;
    }

    #messageInput {
      padding: 0.5rem 0.75rem;
      padding-right: 5rem;
      min-height: 40px;
    }

    #sendButton {
      height: 40px;
      width: 40px;
    }
  }

  .function-buttons button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem; /* Increased from 2rem */
    width: 2.5rem; /* Increased from 2rem */
    padding: 0;
    border-radius: 9999px;
    border: 1px solid rgb(73,73,73);
    color: rgb(156,163,175);
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    white-space: nowrap;
  }

  .function-buttons button:hover {
    width: auto;
    padding: 0 0.75rem;
    background-color: rgba(73,73,73,0.5);
    color: #ffffff;
  }

  .function-buttons button i {
    width: 1.25rem; /* Increased from 1rem */
    height: 1.25rem; /* Increased from 1rem */
    min-width: 1.25rem; /* Increased from 1rem */
    stroke-width: 2px; /* Added for better visibility */
  }

  .function-buttons button span {
    font-size: 0.875rem;
    margin-left: 0.5rem;
    display: none;
  }

  .function-buttons button:hover span {
    display: inline;
  }

  /* Hide File Inputs */
  input[type="file"] {
    display: none;
  }

  /* Preview Containers */
  #imagePreviewContainer,
  #pdfPreviewContainer {
    display: none;
    margin-bottom: 1rem;
  }

  #imagePreviewContainer {
    gap: 0.5rem;
    align-items: center;
  }

  /* Image Preview Template */
  .image-preview {
    max-height: 12rem;
    border-radius: 0.5rem;
  }

  .remove-image-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0,0,0,0.5);
    border: none;
    border-radius: 9999px;
    padding: 0.25rem;
    cursor: pointer;
  }

  /* Add button styling for the + icon */
  #addButton {
    position: absolute;
    left: 0.85rem;
    top: 45%;
    transform: translateY(-50%);
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.1) 0%, rgba(49, 94, 248, 0.2) 100%);
    border: 1px solid rgba(49, 94, 248, 0.2);
    color: rgb(49, 94, 248);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(49, 94, 248, 0.1);
    margin: auto 0; /* Ensure vertical centering */
    padding: 0; /* Remove any padding that might affect centering */
  }

  #addButton:hover {
    background: linear-gradient(135deg, rgba(49, 94, 248, 0.15) 0%, rgba(49, 94, 248, 0.25) 100%);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 12px rgba(49, 94, 248, 0.15);
  }

  #addButton:active {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 2px 6px rgba(49, 94, 248, 0.1);
  }

  #addButton i {
    width: 1rem;
    height: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto; /* Center horizontally */
  }

  #addButton:hover i {
    transform: rotate(90deg);
    color: white;
  }

  /* Message Input */
  #messageInput {
    flex: 1;
    min-height: 50px;
    max-height: 100px; /* Reduced max height to prevent excessive scrolling */
    padding: 0.85rem 1rem;
    padding-left: 3.25rem; /* Increased left padding to make room for the + icon */
    padding-right: 7rem;
    background: rgba(45, 45, 51, 0.5);
    border: 1px solid rgba(49, 94, 248, 0.2);
    border-radius: 0.75rem;
    color: #f3f4f6;
    font-size: 0.95rem;
    resize: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    outline: none;
    overflow-y: hidden; /* Hide vertical scrollbar */
    width: 100%;
    line-height: 1.5;
    white-space: pre-wrap; /* Preserve line breaks but wrap text */
    word-wrap: break-word; /* Break long words */
  }

  #messageInput:focus {
    outline: none;
    background: rgba(45, 45, 51, 0.7);
    border-color: rgba(49, 94, 248, 0.4);
    box-shadow: 0 4px 15px rgba(49, 94, 248, 0.1);
  }

  #messageInput::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }

  /* Model Selector Button */
  .model-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
    border-radius: 9999px;
    border: 1px solid rgb(73,73,73);
    color: rgb(156,163,175);
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: visible; /* Changed from hidden to visible to allow dropdown to show */
    white-space: nowrap;
    position: relative;
    z-index: 1001; /* Ensure the button is above other elements */
  }

  .model-btn:hover {
    width: auto;
    padding: 0 0.75rem;
    background-color: rgba(73,73,73,0.5);
    color: #ffffff;
  }

  .model-btn .current-model-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 1.25rem;
  }

  .model-btn .current-model-icon i {
    width: 1.25rem;
    height: 1.25rem;
    min-width: 1.25rem;
    stroke-width: 2px;
  }

  #currentModel {
    display: none;
    margin-left: 0.5rem;
  }

  .model-btn:hover #currentModel {
    display: inline;
  }

  #currentModel {
    color: rgb(156,163,175);
    font-size: 0.875rem;
  }

  /* Legacy model selector styles - kept for compatibility */
  #modelDropup {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 0.5rem;
    background-color: rgb(38,38,44);
    border: 1px solid rgb(73,73,73);
    border-radius: 0.375rem;
    overflow: hidden;
    min-width: 150px;
    display: none;
  }

  .model-option {
    padding: 0.5rem 0.75rem;
    color: rgb(156,163,175);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .model-option:hover {
    background-color: rgba(73,73,73,0.5);
    color: #ffffff;
  }

  /* Send Button */
  #sendButton {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(45, 85, 225) 100%);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(49, 94, 248, 0.2);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    margin-left: 0.5rem;
    margin-top: 0; /* Added to ensure no top margin */
  }

  #sendButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
  }

  #sendButton:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 20px rgba(49, 94, 248, 0.3);
  }

  #sendButton:hover::before {
    transform: translateX(100%);
  }

  #sendButton:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 2px 10px rgba(49, 94, 248, 0.2);
  }

  #sendButton i {
    width: 1.25rem;
    height: 1.25rem;
    color: white;
    transition: all 0.3s ease;
  }

  #sendButton:hover i {
    transform: translateY(-2px);
  }

  /* Delete Modal */
  #deleteModal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    z-index: 50;
  }

  #deleteModal .modal-backdrop {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,0.3);
    backdrop-filter: blur(4px);
  }

  #deleteModal .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgb(30,30,30);
    border: 1px solid rgb(73,73,73);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),
                0 4px 6px -2px rgba(0,0,0,0.05);
    min-width: 300px;
  }

  /* Delete Modal Content */
  #deleteModal h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  #deleteModal p {
    color: rgb(156,163,175);
    text-align: center;
    margin-bottom: 1.5rem;
  }

  #deleteModal button {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
  }

  #cancelDelete {
    border: 1px solid rgb(73,73,73);
    background-color: transparent;
  }

  #confirmDelete {
    background-color: rgb(239,68,68);
    border: none;
  }

  /* Settings Modal */
  #settingsModal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: none;
    z-index: 50;
  }

  #settingsModal .modal-backdrop {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,0.3);
    backdrop-filter: blur(4px);
  }

  #settingsModal .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgb(30,30,30);
    border: 1px solid rgb(73,73,73);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),
                0 4px 6px -2px rgba(0,0,0,0.05);
    min-width: 300px;
  }

  /* Settings Modal Content */
  #settingsModal h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
  }

  #closeSettings {
    color: rgb(186, 193, 205);
    background: transparent;
    border: none;
    cursor: pointer;
  }

  #settingsModal .danger-zone {
    padding-top: 1rem;
    border-top: 1px solid rgb(73,73,73);
  }

  #settingsModal .danger-zone h4 {
    color: rgb(239,68,68);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  #deleteAllChats {
    width: 100%;
    padding: 0.5rem 1rem;
    background-color: rgba(239,68,68,0.1);
    color: rgb(239,68,68);
    border: 1px solid rgba(239,68,68,0.2);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

/* Message Styles */
.message {
  max-width: 70%;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  word-break: break-word; /* Break long words if needed */
  position: relative; /* For positioning the options menu */
}

/* Message Header with Options Button */
.message-header {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 5;
}

/* Message Options Button */
.message-options-btn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0; /* Hidden by default */
}

/* Show options button on hover */
.message:hover .message-options-btn {
  opacity: 1;
}

.message-options-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Dropdown Menu */
.message-dropdown-menu {
  position: absolute;
  top: 2rem;
  right: 0.5rem;
  background-color: rgb(38, 38, 44);
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  min-width: 150px;
  z-index: 10;
  display: none;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

/* Show dropdown when active */
.message-dropdown-menu.show {
  display: block;
}

/* Dropdown Options */
.dropdown-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  text-align: left;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-option i {
  width: 1rem;
  height: 1rem;
}

/* Theme Modal */
.theme-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  z-index: 50;
  animation: fadeIn 0.3s ease;
}

.theme-modal .modal-backdrop {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0,0,0,0.5);
  backdrop-filter: blur(4px);
}

.theme-modal .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgb(32, 32, 38);
  border-radius: 0.75rem;
  padding: 1.5rem;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.theme-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.theme-modal .modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.theme-modal .modal-header button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.theme-modal .modal-header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

/* Theme Option */
.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.theme-option:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.theme-option.selected {
  background-color: rgba(49, 94, 248, 0.1);
  border: 1px solid rgba(49, 94, 248, 0.3);
}

/* Theme Preview */
.theme-preview {
  width: 100%;
  height: 100px;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.75rem;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  background-color: rgb(21, 21, 21);
}

.theme-preview .preview-user,
.theme-preview .preview-ai {
  height: 20px;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

/* Default Theme Preview */
.default-theme .preview-user {
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(45, 85, 225) 100%);
  align-self: flex-end;
  width: 70%;
}

.default-theme .preview-ai {
  background: linear-gradient(135deg, rgb(38, 38, 44) 0%, rgb(32, 32, 38) 100%);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(49, 94, 248);
}

/* Dark Theme Preview */
.dark-theme .preview-user {
  background: linear-gradient(135deg, rgb(75, 75, 75) 0%, rgb(50, 50, 50) 100%);
  align-self: flex-end;
  width: 70%;
}

.dark-theme .preview-ai {
  background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(75, 75, 75);
}

/* Light Theme Preview */
.light-theme {
  background-color: rgb(240, 240, 240);
}

.light-theme .preview-user {
  background: linear-gradient(135deg, rgb(0, 122, 255) 0%, rgb(0, 102, 204) 100%);
  align-self: flex-end;
  width: 70%;
}

.light-theme .preview-ai {
  background: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(240, 240, 240) 100%);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(0, 122, 255);
}

/* Contrast Theme Preview */
.contrast-theme .preview-user {
  background: rgb(255, 255, 0);
  align-self: flex-end;
  width: 70%;
}

.contrast-theme .preview-ai {
  background: rgb(0, 0, 0);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(255, 255, 0);
}

/* Sunset Theme Preview */
.sunset-theme .preview-user {
  background: linear-gradient(135deg, rgb(255, 153, 102) 0%, rgb(255, 94, 98) 100%);
  align-self: flex-end;
  width: 70%;
}

.sunset-theme .preview-ai {
  background: linear-gradient(135deg, rgb(45, 45, 60) 0%, rgb(35, 35, 45) 100%);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(255, 153, 102);
}

/* Ocean Theme Preview */
.ocean-theme .preview-user {
  background: linear-gradient(135deg, rgb(0, 153, 204) 0%, rgb(0, 119, 182) 100%);
  align-self: flex-end;
  width: 70%;
}

.ocean-theme .preview-ai {
  background: linear-gradient(135deg, rgb(25, 42, 86) 0%, rgb(20, 30, 70) 100%);
  align-self: flex-start;
  width: 70%;
  border-left: 2px solid rgb(0, 153, 204);
}

/* Theme Name */
.theme-name {
  font-size: 0.9rem;
  color: white;
  margin-top: 0.25rem;
}

/* Theme Selected Indicator */
.theme-selected {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgb(49, 94, 248);
  color: white;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.theme-option.selected .theme-selected {
  opacity: 1;
}

/* Animation for dropdowns and modals */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Theme Classes */

/* Default theme is already defined in the base styles */

/* Dark Theme */
.theme-dark .message.user {
  background: linear-gradient(135deg, rgb(75, 75, 75) 0%, rgb(50, 50, 50) 100%);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.theme-dark .message.user::after {
  background: rgb(50, 50, 50);
}

.theme-dark .message.ai {
  background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%);
  border-left: 3px solid rgb(75, 75, 75);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Light Theme */
.theme-light {
  background-color: rgb(240, 240, 240) !important;
  color: rgb(50, 50, 50);
}

.theme-light .sidebar,
.theme-light .main-chat,
.theme-light header,
.theme-light .input-area {
  background-color: rgb(240, 240, 240);
  color: rgb(50, 50, 50);
}

.theme-light .message.user {
  background: linear-gradient(135deg, rgb(0, 122, 255) 0%, rgb(0, 102, 204) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 122, 255, 0.2);
}

.theme-light .message.user::after {
  background: rgb(0, 102, 204);
}

.theme-light .message.ai {
  background: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(240, 240, 240) 100%);
  color: rgb(50, 50, 50);
  border-left: 3px solid rgb(0, 122, 255);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.theme-light #messageInput {
  background-color: rgb(255, 255, 255);
  color: rgb(50, 50, 50);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-light #messageInput::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

.theme-light .sidebar-header h1,
.theme-light .sidebar-header p,
.theme-light .sidebar-buttons button,
.theme-light .sidebar-footer button {
  color: rgb(50, 50, 50);
}

/* High Contrast Theme */
.theme-contrast .message.user {
  background: rgb(255, 255, 0);
  color: rgb(0, 0, 0);
  box-shadow: 0 4px 15px rgba(255, 255, 0, 0.3);
}

.theme-contrast .message.user::after {
  background: rgb(255, 255, 0);
}

.theme-contrast .message.ai {
  background: rgb(0, 0, 0);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(255, 255, 0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Sunset Theme */
.theme-sunset .message.user {
  background: linear-gradient(135deg, rgb(255, 153, 102) 0%, rgb(255, 94, 98) 100%);
  color: rgb(255, 255, 255);
  box-shadow: 0 4px 15px rgba(255, 94, 98, 0.3);
}

.theme-sunset .message.user::after {
  background: rgb(255, 94, 98);
}

.theme-sunset .message.ai {
  background: linear-gradient(135deg, rgb(45, 45, 60) 0%, rgb(35, 35, 45) 100%);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(255, 153, 102);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Ocean Theme */
.theme-ocean .message.user {
  background: linear-gradient(135deg, rgb(0, 153, 204) 0%, rgb(0, 119, 182) 100%);
  color: rgb(255, 255, 255);
  box-shadow: 0 4px 15px rgba(0, 119, 182, 0.3);
}

.theme-ocean .message.user::after {
  background: rgb(0, 119, 182);
}

.theme-ocean .message.ai {
  background: linear-gradient(135deg, rgb(25, 42, 86) 0%, rgb(20, 30, 70) 100%);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(0, 153, 204);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.message.user {
  margin-left: auto;
  background: linear-gradient(135deg, rgb(49, 94, 248) 0%, rgb(45, 85, 225) 100%);
  color: rgb(255, 255, 255);
  box-shadow: 0 4px 15px rgba(49, 94, 248, 0.25);
  border-radius: 1rem 0.25rem 1rem 1rem;
  position: relative;
}

.message.user::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 0;
  width: 15px;
  height: 15px;
  background: rgb(45, 85, 225);
  border-radius: 0 0 0 15px;
  z-index: -1;
}

.message.ai {
  margin-right: auto;
  background: linear-gradient(135deg, rgb(38, 38, 44) 0%, rgb(32, 32, 38) 100%);
  color: rgb(255, 255, 255);
  border-left: 3px solid rgb(49, 94, 248);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem 1rem 1rem 0.5rem;
}

/* Mobile message styles */
@media (max-width: 768px) {
  .message {
    max-width: 85%;
    padding: 0.75rem;
    font-size: 0.95rem;
  }

  /* Fix for code blocks on mobile */
  .code-block-container {
    max-width: 100%;
    width: 100%;
  }

  .code-block {
    font-size: 0.8rem;
  }
}

/* Message Content Formatting */
.message-content {
  word-wrap: break-word;
}

.message-content p {
  margin-bottom: 1rem;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.message-content ul,
.message-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.message-content li {
  margin-bottom: 0.25rem;
}

.message-content ul {
  list-style-type: disc;
}

.message-content ol {
  list-style-type: decimal;
}

/* Code Block Styling within Messages */
.message-content code {
  background-color: rgba(0, 0, 0, 0.3);
  color: rgb(255, 255, 255);
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-family: monospace;
  white-space: pre-wrap;  /* Preserve whitespace in inline code */
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: rgb(200, 200, 200);
  animation: typing 1s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-0.5rem); }
}

/* Code Block Styling */
.code-block-container {
    margin: 1rem 0;
    background: rgb(25, 25, 30);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 850px; /* Add max-width */
}
/* Override Prism code block background */
.code-block-container pre.code-block {
    background-color: rgb(38,38,44) !important;
    color: #fff; /* Optional: Change text color for better contrast */
    overflow-x: auto; /* Ensure horizontal scrolling */
    white-space: pre; /* Preserve whitespace and line breaks */
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgb(35, 35, 40);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-block-title {
    font-size: 0.875rem;
    color: rgb(255, 255, 255);
}

.copy-code-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.15);
    color: rgb(255, 255, 255);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.copy-code-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.copy-code-btn i {
    width: 16px;
    height: 16px;
}

.code-block {
    margin: 0;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Scrollbar styling for code blocks */
.code-block::-webkit-scrollbar {
    height: 8px;
}

.code-block::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.code-block::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.code-block::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

.thread-item {
    padding: 14px 18px;
    margin: 10px 0;
    border-radius: 12px;
    background: linear-gradient(145deg, rgb(48, 48, 54), rgb(45, 45, 50));
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.2, 0, 0.2, 1);
    position: relative;
    min-height: 64px;
    width: calc(100% - 16px);
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding-right: 110px; /* Space for action buttons */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
    overflow: hidden;
}

.thread-item:hover {
    background: linear-gradient(145deg, rgb(55, 55, 62), rgb(52, 52, 58));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thread-item.active {
    background: linear-gradient(145deg, rgba(49, 94, 248, 0.25), rgba(49, 94, 248, 0.35));
    border-left: 3px solid rgb(49, 94, 248);
    box-shadow: 0 4px 12px rgba(49, 94, 248, 0.25), 0 0 8px rgba(49, 94, 248, 0.15);
    position: relative;
}

.thread-item.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
    z-index: 1;
}

.thread-item.active .thread-title {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(49, 94, 248, 0.4);
}

.thread-title {
    font-weight: 600;
    font-size: 1.15rem;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 10px 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    max-width: calc(100% - 10px);
    transition: all 0.3s ease;
    letter-spacing: -0.01em;
    position: relative;
    z-index: 2;
}

.thread-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent 70%, rgba(45, 45, 50, 0.9) 100%);
    z-index: 1;
    pointer-events: none;
    opacity: 0.8;
}

.thread-item:hover .thread-title {
    max-width: calc(100% - 90px); /* Increased space for action buttons when hovered */
}

.thread-actions {
    display: flex;
    gap: 14px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.2, 0, 0.2, 1);
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
}

.thread-item:hover .thread-actions {
    opacity: 1;
}

.thread-actions button {
    background: rgba(35, 35, 40, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s cubic-bezier(0.2, 0, 0.2, 1);
    width: 34px;
    height: 34px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.thread-actions button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.thread-actions button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.15);
}

.thread-actions button:hover::before {
    transform: translateX(100%);
}

.thread-actions button:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.1s ease;
}

.thread-actions .edit-thread {
    background: rgba(25, 118, 210, 0.15);
    border-color: rgba(100, 181, 246, 0.3);
}

.thread-actions .edit-thread svg {
    color: #64B5F6;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    transition: all 0.25s ease;
}

.thread-actions .edit-thread:hover {
    background: rgba(25, 118, 210, 0.25);
    border-color: rgba(100, 181, 246, 0.5);
}

.thread-actions .edit-thread:hover svg {
    color: #90CAF9;
    transform: rotate(-10deg) scale(1.1);
}

.thread-actions .delete-thread {
    background: rgba(211, 47, 47, 0.15);
    border-color: rgba(229, 115, 115, 0.3);
}

.thread-actions .delete-thread svg {
    color: #E57373;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    transition: all 0.25s ease;
}

.thread-actions .delete-thread:hover {
    background: rgba(211, 47, 47, 0.25);
    border-color: rgba(229, 115, 115, 0.5);
}

.thread-actions .delete-thread:hover svg {
    color: #EF9A9A;
    transform: rotate(10deg) scale(1.1);
}

/* Button tooltips */
.thread-actions button {
    position: relative;
}

.thread-actions button::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    pointer-events: none;
    z-index: 100;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.thread-actions button:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    background: rgba(30, 30, 35, 0.95);
    border-left: 4px solid #4CAF50;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 9999;
    transform: translateX(120%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: #4CAF50;
}

.notification.error {
    border-left-color: #F44336;
}

.notification.warning {
    border-left-color: #FF9800;
}

.notification.info {
    border-left-color: #2196F3;
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-icon svg {
    width: 20px;
    height: 20px;
}

.notification.success .notification-icon svg {
    color: #4CAF50;
}

.notification.error .notification-icon svg {
    color: #F44336;
}

.notification.warning .notification-icon svg {
    color: #FF9800;
}

.notification.info .notification-icon svg {
    color: #2196F3;
}

.notification-message {
    font-size: 14px;
    color: #f3f4f6;
}

.message-count {
    font-size: 0.8em;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 4px;
    display: inline-block;
    background: rgba(49, 94, 248, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Loading indicators */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
    animation: pulse 1.5s infinite;
}

.empty-thread {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
}

.error-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #ff6b6b;
    font-size: 0.9rem;
    text-align: center;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 0.5rem;
    margin: 1rem;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.thread-date {
    font-size: 0.8em;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
}

.thread-date .date-label {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9em;
}

/* Mobile-specific thread item styles */
@media (max-width: 768px) {
    .thread-item {
        padding: 16px;
        margin: 8px 0;
        min-height: 70px;
    }
    
    .thread-title {
        font-size: 1.05rem;
        max-width: calc(100% - 80px);
    }
    
    .thread-actions {
        opacity: 0.9; /* Always somewhat visible on mobile */
        gap: 10px;
    }
    
    .thread-actions button {
        width: 38px;
        height: 38px;
        padding: 10px;
        background: rgba(35, 35, 40, 0.9);
    }
    
    /* Add touch feedback */
    .thread-item:active {
        background: linear-gradient(145deg, rgb(50, 50, 56), rgb(48, 48, 54));
        transform: scale(0.98);
        transition: all 0.1s ease;
    }
    
    /* Ensure buttons are always visible enough to tap */
    .thread-item .thread-actions {
        opacity: 0.7;
    }
    
    .thread-item:hover .thread-actions,
    .thread-item:active .thread-actions {
        opacity: 1;
    }
}



#shareChatBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#shareChatBtn svg {
    width: 18px;
    height: 18px;
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: rgb(37, 134, 54);
}

.notification.error {
    background: rgb(185, 28, 28);
}

.notification.fade-out {
    animation: slideOut 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.message.ai .highlighted-text {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.1em 0.3em;
  border-radius: 0.2em;
  font-weight: 500;
}

.message-images {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.message-image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 0.5rem;
    object-fit: contain;
}

#imagePreviewContainer {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    position: relative;
    padding: 5px;
}

.image-preview {
    max-height: 100px;
    border-radius: 0.5rem;
    object-fit: cover;
}

.remove-image-btn {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    padding: 0.25rem;
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.global-remove-image-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: rgba(0, 0, 0, 0.8);
    border: none;
    border-radius: 50%;
    padding: 0.35rem;
    width: 28px;
    height: 28px;
    cursor: pointer;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.global-remove-image-btn:hover, .remove-image-btn:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
    transition: all 0.1s ease;
}

/* Live Chat Button Styles */
.live-chat-button {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.live-chat-button button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, rgb(49,94,248) 0%, rgb(45,85,225) 100%);
  border: none;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(49,94,248,0.2);
}

.live-chat-button button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(49,94,248,0.3);
}

.live-chat-button button i {
  width: 1.25rem;
  height: 1.25rem;
}

/* Warning Modal */
.warning-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  z-index: 50;
}

.warning-modal .modal-backdrop {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0,0,0,0.3);
  backdrop-filter: blur(4px);
}

.warning-modal .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgb(32, 32, 38);
  padding: 1.5rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.warning-modal .modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-top: 1.25rem;
}

.warning-modal button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.warning-modal #cancelLiveBtn {
  background-color: transparent;
  border: 1px solid rgb(73,73,73);
  color: rgb(156,163,175);
}

.warning-modal #confirmLiveBtn {
  background-color: rgb(49,94,248);
  border: none;
  color: white;
}

.warning-modal button:hover {
  transform: translateY(-1px);
}
