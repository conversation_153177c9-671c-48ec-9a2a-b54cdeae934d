from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield, <PERSON><PERSON><PERSON><PERSON><PERSON>
from models.user import User
from datetime import datetime, timezone

class FriendRelationship(Document):
    """Model for storing friend relationships between users"""
    user = ReferenceField(User, required=True)
    friend = ReferenceField(User, required=True)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    is_accepted = BooleanField(default=False)

    meta = {
        'collection': 'friend_relationships',
        'indexes': [
            'user',
            'friend',
        ]
    }

    @classmethod
    def are_friends(cls, user_id, friend_id):
        """Check if two users are friends"""
        # Check if there's an accepted relationship in either direction
        return cls.objects(user=user_id, friend=friend_id, is_accepted=True).first() is not None or \
               cls.objects(user=friend_id, friend=user_id, is_accepted=True).first() is not None

    @classmethod
    def get_friends(cls, user_id):
        """Get all friends for a user"""
        # Get all accepted relationships where the user is either the user or the friend
        friends_as_user = cls.objects(user=user_id, is_accepted=True)
        friends_as_friend = cls.objects(friend=user_id, is_accepted=True)

        # Extract the friend objects
        friends = []
        for relationship in friends_as_user:
            friends.append(relationship.friend)
        for relationship in friends_as_friend:
            friends.append(relationship.user)

        return friends

    @classmethod
    def get_pending_requests(cls, user_id):
        """Get all pending friend requests for a user"""
        # Get all non-accepted relationships where the user is the friend
        return cls.objects(friend=user_id, is_accepted=False)

    @classmethod
    def send_request(cls, user_id, friend_id):
        """Send a friend request"""
        # Check if users are the same
        if str(user_id) == str(friend_id):
            return False, "You cannot send a friend request to yourself"

        # Check if they are already friends
        if cls.are_friends(user_id, friend_id):
            return False, "You are already friends with this user"

        # Check if a request already exists in either direction
        existing_request = cls.objects(user=user_id, friend=friend_id, is_accepted=False).first() or \
                          cls.objects(user=friend_id, friend=user_id, is_accepted=False).first()

        if existing_request:
            if existing_request.user.id == user_id:
                return False, "You have already sent a friend request to this user"
            else:
                return False, "This user has already sent you a friend request"

        # Create a new friend request
        relationship = cls(
            user=user_id,
            friend=friend_id,
            is_accepted=False
        )
        relationship.save()

        return True, "Friend request sent successfully"

    @classmethod
    def accept_request(cls, user_id, friend_id):
        """Accept a friend request"""
        # Check if a request exists from friend to user
        request = cls.objects(user=friend_id, friend=user_id, is_accepted=False).first()

        if not request:
            return False, "No friend request found from this user"

        # Accept the request
        request.is_accepted = True
        request.save()

        # Create a reciprocal relationship
        reciprocal = cls(
            user=user_id,
            friend=friend_id,
            is_accepted=True
        )
        reciprocal.save()

        return True, "Friend request accepted"

    @classmethod
    def reject_request(cls, user_id, friend_id):
        """Reject a friend request"""
        # Check if a request exists from friend to user
        request = cls.objects(user=friend_id, friend=user_id, is_accepted=False).first()

        if not request:
            return False, "No friend request found from this user"

        # Delete the request
        request.delete()

        return True, "Friend request rejected"
