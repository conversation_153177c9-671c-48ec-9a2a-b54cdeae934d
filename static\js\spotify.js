
const loadScript = (src) => {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.async = true;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
};

// Initialize Spotify Web Playback SDK
window.onSpotifyWebPlaybackSDKReady = () => {
    console.log('Spotify SDK Ready');
};

class SpotifyPlayer {
    constructor(token) {
        this.token = token;
        this.deviceId = null;
        this.isWebPlaybackActive = false;
        this.isPlaying = false;
        this.updateLock = false;
        this.deviceSwitchInProgress = false;
        this.lastKnownState = null;
        this.setupSearch();
        this.setupProgressBar();
        this.currentPlaybackCache = null;
        this.playbackCacheTime = 0;
        this.CACHE_TIMEOUT = 1000; // 1 second cache
        this.currentTrackUri = null;
        this.nextTrackUri = null;
        this.preloadedImages = new Map(); // Store preloaded images
        this.currentArtwork = null;
        this.artworkUpdateTimeout = null;
        this.nextTrackData = null;
        this.imageCache = new Map(); // Cache for loaded images
        this.currentArtworkUrl = null;
        this.queueUpdateDebounceTimeout = null;
        this.QUEUE_DEBOUNCE_DELAY = 500;
        this.volumeControlVisible = false;
        this.volumeHideTimeout = null;
        this.progressUpdateTimeout = null;  // Add this line

        // Initialize mobile volume control
        this.initializeMobileVolumeControl();
        this.imageLoadingQueue = new Map(); // Track image loading promises
        this.currentTrackLoadPromise = null;
        this.nextTrackLoadPromise = null;
        this.lastKnownTrackUri = null;
        this.skipDebounceTimeout = null;
        this.SKIP_DEBOUNCE_DELAY = 300; // ms
        this.stateUpdateQueue = Promise.resolve(); // For sequential state updates
    }

    async initializeWebPlayback() {
        return new Promise((resolve, reject) => {
            this.player = new Spotify.Player({
                name: 'Kevkofy',
                getOAuthToken: callback => callback(this.token),
                volume: 1.0
            });

            // Error handling
            this.player.addListener('initialization_error', ({ message }) => {
                console.error('Failed to initialize:', message);
                reject(new Error(message));
            });

            this.player.addListener('authentication_error', ({ message }) => {
                console.error('Failed to authenticate:', message);
                reject(new Error(message));
            });

            this.player.addListener('account_error', ({ message }) => {
                console.error('Failed to validate Spotify account:', message);
                reject(new Error(message));
            });

            this.player.addListener('playback_error', ({ message }) => {
                console.error('Failed to perform playback:', message);
                this.handlePlaybackError(message);
            });

            // Playback status updates
            this.player.addListener('player_state_changed', async (state) => {
                if (!state) return;

                this.stateUpdateQueue = this.stateUpdateQueue.then(async () => {
                    try {
                        // Update playing state immediately
                        const wasPlaying = this.isPlaying;
                        this.isPlaying = !state.paused;

                        // Only update UI if state actually changed
                        if (wasPlaying !== this.isPlaying) {
                            this.updatePlayPauseButton();
                        }

                        const currentTrackUri = state.track_window?.current_track?.uri;

                        if (currentTrackUri !== this.lastKnownTrackUri) {
                            this.lastKnownTrackUri = currentTrackUri;
                            await this.handleTrackChange();
                        } else if (this.isPlaying) {
                            this.startProgressTracking(state.position);
                        }
                    } catch (error) {
                        console.error('State update failed:', error);
                    }
                });
            });

            // Ready
            this.player.addListener('ready', ({ device_id }) => {
                console.log('Web Playback SDK ready with Device ID:', device_id);
                this.deviceId = device_id;
                this.isWebPlaybackActive = true;
                this.isInitialized = true;
                resolve();
            });

            // Not Ready
            this.player.addListener('not_ready', ({ device_id }) => {
                console.log('Device ID has gone offline:', device_id);
                this.isWebPlaybackActive = false;
            });

            // Connect to the player
            this.player.connect().then(success => {
                if (!success) {
                    reject(new Error('Failed to connect to Spotify Web Playback SDK'));
                }
            }).catch(reject);
        });
    }

    setupSearch() {
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');
        let debounceTimeout;

        searchInput.addEventListener('input', async (e) => {
            const query = e.target.value.trim();

            if (debounceTimeout) {
                clearTimeout(debounceTimeout);
            }

            if (!query) {
                searchResults.innerHTML = '';
                searchResults.classList.remove('active');
                return;
            }

            debounceTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/api/spotify/search?q=${encodeURIComponent(query)}&type=track&limit=5`);
                    if (!response.ok) throw new Error('Search failed');

                    const data = await response.json();
                    const tracks = data.tracks.items;

                    searchResults.innerHTML = tracks.map(track => `
                        <div class="search-result-item" data-uri="${track.uri}">
                            <img class="search-result-artwork"
                                src="${track.album.images[track.album.images.length - 1].url}"
                                alt="${track.name}">
                            <div class="search-result-info">
                                <div class="search-result-name">${track.name}</div>
                                <div class="search-result-artist">${track.artists.map(a => a.name).join(', ')}</div>
                            </div>
                        </div>
                    `).join('');

                    // Store reference to the player instance
                    const playerInstance = this;

                    // Add click handlers
                    searchResults.querySelectorAll('.search-result-item').forEach(item => {
                        item.addEventListener('click', async () => {
                            const uri = item.dataset.uri;
                            try {
                                // First check current playback state
                                const currentPlayback = await playerInstance.getCurrentPlayback();
                                const isWebPlayer = currentPlayback?.device?.id === playerInstance.deviceId;

                                if (isWebPlayer && playerInstance.player) {
                                    // Use Web Playback SDK
                                    await playerInstance.player._options.getOAuthToken(async token => {
                                        await fetch('https://api.spotify.com/v1/me/player/play', {
                                            method: 'PUT',
                                            headers: {
                                                'Authorization': `Bearer ${token}`,
                                                'Content-Type': 'application/json'
                                            },
                                            body: JSON.stringify({
                                                uris: [uri]
                                            })
                                        });
                                    });
                                } else {
                                    // Use REST API for external devices
                                    await fetch('/api/spotify/play', {
                                        method: 'PUT',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            uris: [uri],
                                            force_device: true
                                        })
                                    });
                                }

                                // Clear search
                                searchInput.value = '';
                                searchResults.innerHTML = '';
                                searchResults.classList.remove('active');

                                // Wait for playback to start
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Update player state and queue
                                await playerInstance.getCurrentPlayback();
                                await playerInstance.updateQueue(true);

                            } catch (error) {
                                console.error('Failed to play track:', error);
                                // Always fallback to REST API if there's an error
                                try {
                                    await fetch('/api/spotify/play', {
                                        method: 'PUT',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            uris: [uri],
                                            force_device: true
                                        })
                                    });

                                    // Wait for playback to start
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // Update player state and queue
                                    await playerInstance.getCurrentPlayback();
                                    await playerInstance.updateQueue(true);
                                } catch (fallbackError) {
                                    console.error('Fallback playback failed:', fallbackError);
                                }
                            }
                        });
                    });

                    // Show search results
                    searchResults.classList.add('active');

                } catch (error) {
                    console.error('Search failed:', error);
                    searchResults.innerHTML = '<div class="search-error">Search failed. Please try again.</div>';
                }
            }, 300);
        });
    }

    cleanup() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    formatTime(ms) {
        try {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        } catch (error) {
            console.error('Error formatting time:', error);
            return '0:00';
        }
    }

    startProgressTracking(initialPosition = 0) {
        // Clear any existing intervals
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }
        if (this.progressUpdateTimeout) {
            clearTimeout(this.progressUpdateTimeout);
        }

        let lastUpdate = Date.now();
        let currentPosition = initialPosition;

        // Initial update without animation
        this.updateProgress(currentPosition, false);

        // Set up the interval for continuous updates
        this.progressInterval = setInterval(() => {
            if (!this.isPlaying) return;

            const now = Date.now();
            const elapsed = now - lastUpdate;
            lastUpdate = now;
            currentPosition += elapsed;

            if (currentPosition >= this.currentTrackDuration) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
                return;
            }

            this.updateProgress(currentPosition, true);
        }, 50);

        // Only use periodic syncs when Web Playback SDK is not active
        if (!this.isWebPlaybackActive) {
            const syncWithSpotify = async () => {
                if (!this.isPlaying) return;

                try {
                    const playback = await this.getCurrentPlayback();
                    if (playback && playback.is_playing) {
                        currentPosition = playback.progress_ms;
                        this.updateProgress(currentPosition, false);
                    }
                } catch (error) {
                    console.error('Failed to sync progress:', error);
                }

                this.progressUpdateTimeout = setTimeout(syncWithSpotify, 3000);
            };

            this.progressUpdateTimeout = setTimeout(syncWithSpotify, 3000);
        }
    }

    updateProgress(position, animate = true) {
        const progressBar = document.getElementById('progress');
        const currentTimeElement = document.getElementById('track-current-time');
        const durationElement = document.getElementById('track-duration');

        if (!progressBar || !currentTimeElement || !durationElement) return;

        // Ensure position is a number and not negative
        position = Math.max(0, Number(position) || 0);

        if (this.currentTrackDuration > 0) {
            const percentage = (position / this.currentTrackDuration) * 100;

            // Apply or remove transition based on animate parameter
            progressBar.style.transition = animate ? 'width 0.05s linear' : 'none';
            progressBar.style.width = `${Math.min(100, percentage)}%`;

            // Update time displays
            currentTimeElement.textContent = this.formatTime(position);
            durationElement.textContent = this.formatTime(this.currentTrackDuration);
        } else {
            progressBar.style.width = '0%';
            currentTimeElement.textContent = '0:00';
            durationElement.textContent = '0:00';
        }
    }

    // Add click handler for progress bar
    setupProgressBar() {
        const progressContainer = document.querySelector('.progress-bar');
        if (!progressContainer) {
            console.log('Progress bar element not found');
            return;
        }

        progressContainer.addEventListener('click', async (e) => {
            if (!this.currentTrackDuration) {
                console.log('No track duration available');
                return;
            }

            const rect = progressContainer.getBoundingClientRect();
            const clickPosition = e.clientX - rect.left;
            const percentage = clickPosition / rect.width;
            const position = Math.floor(percentage * this.currentTrackDuration);

            try {
                // Stop current progress tracking
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                    this.progressInterval = null;
                }

                // Update UI immediately for responsiveness
                this.updateProgress(position, false);

                // Send seek command
                await fetch('/api/spotify/playback/seek', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ position_ms: position })
                });

                // Restart progress tracking from new position if playing
                if (this.isPlaying) {
                    this.startProgressTracking(position);
                }
            } catch (error) {
                console.error('Seek failed:', error);
            }
        });
    }

    async initializePlayer() {
        try {
            // Initialize Spotify Web Playback SDK
            await this.initializeWebPlayback();

            // Get initial playback state
            const playbackState = await this.getCurrentPlayback();

            if (playbackState) {
                // Update playing state
                this.isPlaying = playbackState.is_playing;

                // Update track info if there's a current track
                if (playbackState.item) {
                    this.currentUri = playbackState.item.uri;
                    this.currentTrackDuration = playbackState.item.duration_ms;

                    await this.updateTrackInfo({
                        name: playbackState.item.name,
                        artists: playbackState.item.artists,
                        album: playbackState.item.album,
                        duration_ms: playbackState.item.duration_ms,
                        uri: playbackState.item.uri
                    });

                    // Start progress tracking if playing
                    if (this.isPlaying) {
                        this.startProgressTracking(playbackState.progress_ms);
                    }
                }

                // Update queue to get next track
                await this.updateQueue(true);
            }

            // Update play button state
            this.updatePlayPauseButton();

        } catch (error) {
            console.error('Initialization error:', error);
            // Don't throw the error, just log it and continue
            this.isPlaying = false;
            this.updatePlayPauseButton();
        }
    }

    async handlePlaybackError(message) {
        if (message.includes('no list was loaded')) {
            try {
                // Get current playback state
                const playbackState = await this.getCurrentPlayback();

                if (!playbackState) {
                    // No active playback, reset state
                    this.updatePlayerState({
                        paused: true,
                        track_window: null,
                        position: 0
                    });
                    return;
                }

                // Check if we need to transfer playback to web player
                if (playbackState.device?.id !== this.deviceId) {
                    await this.transferPlayback();
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // If there was a track playing, resume it
                    if (playbackState.item?.uri) {
                        await fetch('/api/spotify/play', {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                uris: [playbackState.item.uri],
                                position_ms: playbackState.progress_ms || 0,
                                force_device: true
                            })
                        });
                    }

                    await this.getCurrentPlayback();
                }
            } catch (error) {
                console.error('Failed to recover from playback error:', error);
            }
        }
    }

    async transferPlayback() {
        if (!this.deviceId) {
            throw new Error('Device ID not available');
        }

        try {
            const response = await fetch('https://api.spotify.com/v1/me/player', {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    device_ids: [this.deviceId],
                    play: false  // Don't auto-play after transfer
                })
            });

            if (response.status === 401) {
                window.location.href = '/spotify/connect';
                return;
            }

            if (!response.ok && response.status !== 204) {
                const errorData = await response.text();
                throw new Error(`Transfer playback failed: ${response.status} ${errorData}`);
            }

            // Wait for transfer to complete
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Get current playback to verify transfer
            const playbackResponse = await this.getCurrentPlayback();
            this.isWebPlaybackActive = playbackResponse?.device?.id === this.deviceId;

        } catch (error) {
            console.error('Error transferring playback:', error);
            throw error;
        }
    }

    async getCurrentPlayback() {
        try {
            // First try Web Playback SDK if active
            if (this.isWebPlaybackActive && this.player) {
                try {
                    const state = await this.player.getCurrentState();
                    if (state) {
                        this.isPlaying = !state.paused;
                        this.updatePlayPauseButton();
                        return {
                            is_playing: !state.paused,
                            progress_ms: state.position,
                            item: state.track_window.current_track,
                            device: {
                                id: this.deviceId
                            }
                        };
                    }
                } catch (sdkError) {
                    console.warn('Web Playback SDK state fetch failed:', sdkError);
                    // Continue to REST API fallback
                }
            }

            // Fallback to REST API
            try {
                const response = await fetch('/api/spotify/playback/current');
                const data = await response.json();

                // Check if we got valid data
                if (data === null) {
                    // No active playback
                    this.isPlaying = false;
                    this.updatePlayPauseButton();
                    return null;
                }

                // Update player state
                if (typeof data.is_playing !== 'undefined') {
                    this.isPlaying = data.is_playing;
                    this.updatePlayPauseButton();
                }

                return data;
            } catch (apiError) {
                console.error('REST API playback fetch failed:', apiError);
                throw new Error('Failed to get playback state from API');
            }
        } catch (error) {
            console.error('Error getting current playback:', error);
            // Reset state on error
            this.isPlaying = false;
            this.updatePlayPauseButton();
            return null;
        }
    }

    async updateQueue(forceUpdate = false) {
        try {
            const response = await fetch('/api/spotify/queue');
            const data = await response.json();

            if (data.queue && data.queue.length > 0) {
                const nextTrack = data.queue[0];
                this.updateNextTrackPreview(nextTrack);
            } else {
                // Clear next track preview if queue is empty
                this.updateNextTrackPreview({
                    name: 'No upcoming track',
                    artists: [{ name: '' }],
                    album: { images: [{ url: '/static/images/default-artwork.jpg' }] }
                });
            }
        } catch (error) {
            console.error('Failed to update queue:', error);
            // Set default state for next track preview
            this.updateNextTrackPreview({
                name: 'Queue unavailable',
                artists: [{ name: '' }],
                album: { images: [{ url: '/static/images/default-artwork.jpg' }] }
            });
        }
    }

    // Unified image loading system
    async loadImage(url, fallbackUrl = '/static/images/default-artwork.jpg') {
        if (!url) return fallbackUrl;

        // Check if image is already loading
        if (this.imageLoadingQueue.has(url)) {
            return this.imageLoadingQueue.get(url);
        }

        const loadPromise = new Promise((resolve) => {
            const img = new Image();

            img.onload = () => {
                this.imageLoadingQueue.delete(url);
                resolve(url);
            };

            img.onerror = () => {
                this.imageLoadingQueue.delete(url);
                resolve(fallbackUrl);
            };

            img.src = url;
        });

        this.imageLoadingQueue.set(url, loadPromise);
        return loadPromise;
    }

    async updateNextTrackPreview(nextTrack, forceRefresh = false) {
        // Cancel any pending next track image loads
        this.nextTrackLoadPromise = null;

        const nextTrackArtwork = document.getElementById('next-track-artwork');
        if (nextTrackArtwork) {
            nextTrackArtwork.classList.remove('fade-in');
        }

        if (!nextTrack) {
            nextTrack = {
                name: 'No upcoming track',
                artists: [{ name: '' }],
                album: { images: [{ url: '/static/images/default-artwork.jpg' }] },
                uri: null
            };
        }

        // Update next track URI
        this.nextTrackUri = nextTrack.uri;

        // Update text immediately
        const updateElement = (id, content) => {
            const element = document.getElementById(id);
            if (element) element.textContent = content;
        };

        updateElement('next-track-name', nextTrack.name || 'No upcoming track');
        updateElement('next-track-artist', nextTrack.artists?.map(artist => artist.name).join(', ') || '');

        // Handle artwork loading
        if (nextTrackArtwork) {
            const artworkUrl = nextTrack.album?.images?.[0]?.url;
            if (artworkUrl) {
                const nextTrackUriForLoad = nextTrack.uri;

                try {
                    // Force a new image load if refresh is requested
                    if (forceRefresh) {
                        this.imageLoadingQueue.delete(artworkUrl);
                    }

                    this.nextTrackLoadPromise = this.loadImage(artworkUrl);
                    const loadedUrl = await this.nextTrackLoadPromise;

                    // Verify this is still the next track
                    if (this.nextTrackUri === nextTrackUriForLoad) {
                        nextTrackArtwork.src = loadedUrl;
                        nextTrackArtwork.alt = nextTrack.name || 'Next Track';
                        requestAnimationFrame(() => {
                            nextTrackArtwork.classList.add('fade-in');
                        });
                    }
                } catch (error) {
                    console.error('Failed to load next track artwork:', error);
                }
            }
        }
    }

    async forceStateUpdate(state) {
        try {
            // Clear any pending image updates
            if (this.currentTrackLoadPromise) {
                this.currentTrackLoadPromise = null;
            }
            if (this.nextTrackLoadPromise) {
                this.nextTrackLoadPromise = null;
            }

            const currentTrack = state.track_window?.current_track;
            if (!currentTrack) return;

            // Prepare current track data
            const trackData = {
                name: currentTrack.name,
                artists: currentTrack.artists,
                album: currentTrack.album,
                duration_ms: state.duration,
                uri: currentTrack.uri
            };

            // Update playing state
            this.isPlaying = !state.paused;

            // Update current track info with force refresh
            await this.updateTrackInfo(trackData, true, true);

            // Handle next track
            const nextTrack = state.track_window?.next_tracks?.[0];
            if (nextTrack) {
                const nextTrackData = {
                    name: nextTrack.name,
                    artists: nextTrack.artists,
                    album: nextTrack.album,
                    uri: nextTrack.uri
                };
                await this.updateNextTrackPreview(nextTrackData, true);
            } else {
                await this.updateNextTrackPreview(null, true);
            }

            // Update progress
            if (this.isPlaying) {
                this.startProgressTracking(state.position);
            } else {
                this.updateProgress(state.position);
            }

            // Update play/pause button
            this.updatePlayPauseButton();

            // Force queue update
            await this.updateQueue(true);
        } catch (error) {
            console.error('Force state update failed:', error);
        }
    }

    updatePlayerState(state) {
        if (!state) return;

        this.isPlaying = !state.paused;

        if (state.track_window?.current_track) {
            const currentTrack = {
                name: state.track_window.current_track.name,
                artists: state.track_window.current_track.artists,
                album: state.track_window.current_track.album,
                duration_ms: state.duration,
                uri: state.track_window.current_track.uri
            };

            // Update current track
            this.updateTrackInfo(currentTrack, true);

            // Update next track if available
            const nextTrack = state.track_window.next_tracks[0];
            if (nextTrack) {
                this.updateNextTrackPreview({
                    name: nextTrack.name,
                    artists: nextTrack.artists,
                    album: nextTrack.album,
                    uri: nextTrack.uri
                });
            } else {
                // Clear next track preview if no next track
                this.updateNextTrackPreview(null);
            }
        }

        // Update progress and play/pause button
        if (this.isPlaying) {
            this.startProgressTracking(state.position);
        } else {
            this.updateProgress(state.position);
        }
        this.updatePlayPauseButton();
    }

    updatePlayPauseButton() {
        const playPauseButton = document.getElementById('play-pause-button');
        if (!playPauseButton) return;

        const icon = playPauseButton.querySelector('i');
        if (!icon) return;

        // Remove existing classes
        icon.classList.remove('fa-play', 'fa-pause');

        // Add appropriate class based on playing state
        icon.classList.add(this.isPlaying ? 'fa-pause' : 'fa-play');
    }

    async togglePlayback() {
        if (this.updateLock || this.deviceSwitchInProgress) return;
        this.updateLock = true;

        try {
            const currentPlayback = await this.getCurrentPlayback();
            const currentDeviceId = currentPlayback?.device?.id;

            // If we have a Web SDK device but it's not the active device, transfer to it first
            if (this.deviceId && currentDeviceId !== this.deviceId) {
                console.log('Transferring playback to Web SDK device before toggling playback');
                await this.handleDeviceChange(this.deviceId);
                await new Promise(resolve => setTimeout(resolve, 500));

                // Get updated playback state after device change
                const updatedPlayback = await this.getCurrentPlayback();
                this.isPlaying = updatedPlayback?.is_playing || false;
            }

            // Prefer using the Web SDK if available
            if (this.player) {
                console.log('Using Web SDK to toggle playback');
                try {
                    if (this.isPlaying) {
                        await this.player.pause();
                    } else {
                        await this.player.resume();
                    }

                    // If the SDK methods don't work, fall back to the API
                    await this.player._options.getOAuthToken(async token => {
                        const endpoint = this.isPlaying ? 'pause' : 'play';
                        const response = await fetch(`https://api.spotify.com/v1/me/player/${endpoint}`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!response.ok && response.status !== 204) {
                            throw new Error('Failed to toggle playback via API');
                        }
                    });
                } catch (sdkError) {
                    console.error('Web SDK playback toggle failed, falling back to API:', sdkError);
                    // Fall back to REST API
                    const response = await fetch(`/api/spotify/playback/control/${this.isPlaying ? 'pause' : 'play'}`, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error('Failed to toggle playback via API');
                    }
                }
            } else {
                // Use REST API if Web SDK is not available
                console.log('Using REST API to toggle playback');
                const response = await fetch(`/api/spotify/playback/control/${this.isPlaying ? 'pause' : 'play'}`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error('Failed to toggle playback');
                }
            }

            // Wait for state to update
            // Use a longer delay for pause actions, especially for TV devices
            const wasPausing = this.isPlaying;
            const deviceType = currentPlayback?.device?.type;
            const isExternalDevice = deviceType === 'TV' || deviceType === 'Speaker';

            if (wasPausing && isExternalDevice) {
                // Longer delay for pausing external devices like TVs
                await new Promise(resolve => setTimeout(resolve, 1500));
            } else if (wasPausing) {
                // Standard delay for pausing other devices
                await new Promise(resolve => setTimeout(resolve, 800));
            } else {
                // Shorter delay for play actions
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            // Sync state and force device refresh
            await this.syncPlaybackState();

        } catch (error) {
            console.error('Toggle playback failed:', error);
            await this.syncPlaybackState();
        } finally {
            this.updateLock = false;
        }
    }

    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        if (this.progressUpdateTimeout) {
            clearTimeout(this.progressUpdateTimeout);
            this.progressUpdateTimeout = null;
        }
    }

    async syncPlaybackState() {
        try {
            // Make multiple attempts to get the most up-to-date playback state
            // This helps with the TV device selection issue
            let playback = null;
            let attempts = 0;
            const maxAttempts = 2;

            while (attempts < maxAttempts) {
                playback = await this.getCurrentPlayback();

                // If we just paused and the state still shows playing, wait and try again
                if (playback && !this.isPlaying && playback.is_playing) {
                    console.log('Playback state not yet updated, waiting...');
                    await new Promise(resolve => setTimeout(resolve, 500));
                    attempts++;
                } else {
                    break;
                }
            }

            if (playback) {
                this.isPlaying = playback.is_playing;

                // Update device information
                const deviceId = playback.device?.id;
                const deviceType = playback.device?.type;

                // Update last known state
                this.lastKnownState = {
                    isPlaying: playback.is_playing,
                    deviceId: deviceId,
                    deviceType: deviceType,
                    trackUri: playback.item?.uri,
                    position: playback.progress_ms
                };

                // Update device display if needed
                if (playback.device) {
                    this.updateCurrentDevice(playback.device);
                }
            } else {
                this.isPlaying = false;
                this.lastKnownState = null;
            }

            this.updatePlayPauseButton();

            if (this.isPlaying) {
                this.startProgressTracking(playback?.progress_ms || 0);
            } else {
                this.stopProgressTracking();
            }

            // If we just paused, force a device refresh to ensure accurate device status
            if (!this.isPlaying) {
                this.updateDevices();
            }
        } catch (error) {
            console.error('Failed to sync playback state:', error);
        }
    }

    async handleDeviceChange(newDeviceId) {
        if (this.deviceSwitchInProgress) return;
        this.deviceSwitchInProgress = true;

        try {
            const currentPlayback = await this.getCurrentPlayback();
            const wasPlaying = currentPlayback?.is_playing || false;
            const position = currentPlayback?.progress_ms || 0;
            const currentTrack = currentPlayback?.item?.uri;

            const playbackData = {
                device_id: newDeviceId,
                force_device: true
            };

            if (wasPlaying && currentTrack) {
                playbackData.uris = [currentTrack];
                playbackData.position_ms = position;
            }

            // Transfer playback
            await this.player._options.getOAuthToken(async token => {
                await fetch('https://api.spotify.com/v1/me/player', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        device_ids: [newDeviceId],
                        play: wasPlaying
                    })
                });
            });

            // Wait for device switch
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Verify the switch and sync state
            await this.syncPlaybackState();

        } catch (error) {
            console.error('Device change failed:', error);
            await this.syncPlaybackState();
        } finally {
            this.deviceSwitchInProgress = false;
        }
    }

    async nextTrack() {
        if (this.updateLock) return;
        this.updateLock = true;

        try {
            const response = await fetch('/api/spotify/playback/control/next', {
                method: 'POST'
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Failed to skip track');
            }

            // Wait briefly for Spotify to update
            await new Promise(resolve => setTimeout(resolve, 300));

            // Handle the track change
            await this.handleTrackChange();

        } catch (error) {
            console.error('Next track failed:', error);
        } finally {
            this.updateLock = false;
        }
    }

    async previousTrack() {
        if (this.updateLock) return;
        this.updateLock = true;

        try {
            const response = await fetch('/api/spotify/playback/control/previous', {
                method: 'POST'
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Failed to go to previous track');
            }

            // Wait briefly for Spotify to update
            await new Promise(resolve => setTimeout(resolve, 300));

            // Handle the track change
            await this.handleTrackChange();

        } catch (error) {
            console.error('Previous track failed:', error);
        } finally {
            this.updateLock = false;
        }
    }

    async setVolume(value) {
        if (this.player) {
            await this.player.setVolume(value / 100);

            // Update mobile volume icon
            const volumeToggle = document.querySelector('.mobile-volume-toggle i');
            if (volumeToggle) {
                volumeToggle.className = 'fa ' + (
                    value == 0 ? 'fa-volume-off' :
                    value < 50 ? 'fa-volume-down' :
                    'fa-volume-up'
                );
            }
        }
    }

    async shuffle() {
        try {
            const response = await fetch(`https://api.spotify.com/v1/me/player/shuffle?state=${!this.shuffleState}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
            });
            if (response.ok) {
                this.shuffleState = !this.shuffleState;
            }
        } catch (error) {
            console.error('Error toggling shuffle:', error);
        }
    }

    async repeat() {
        try {
            const response = await fetch('https://api.spotify.com/v1/me/player', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
            });
            const data = await response.json();
            const newState = data.repeat_state === 'off' ? 'context' : 'off';

            await fetch(`https://api.spotify.com/v1/me/player/repeat?state=${newState}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
            });
        } catch (error) {
            console.error('Error toggling repeat:', error);
        }
    }

    async initializePlaylists() {
        if (this.playlistsLoading) return;
        this.playlistsLoading = true;

        try {
            const response = await fetch('/api/spotify/playlists');
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to fetch playlists');
            }

            const data = await response.json();
            if (!Array.isArray(data)) {
                throw new Error('Invalid playlist data received');
            }

            this.playlists = data;

            // Clear existing content
            const container = document.querySelector('.playlists-container');
            if (container) {
                container.innerHTML = '';
            }

            await this.renderPlaylists();
            this.setupInfiniteScroll();
        } catch (error) {
            console.error('Error initializing playlists:', error);
            // Handle authentication errors
            if (error.message.includes('Not authenticated')) {
                window.location.href = '/spotify/connect';
                return;
            }
            // Show error to user
            const container = document.querySelector('.playlists-container');
            if (container) {
                container.innerHTML = `<div class="error-message">Failed to load playlists. Please try refreshing the page.</div>`;
            }
        } finally {
            this.playlistsLoading = false;
        }
    }

    setupInfiniteScroll() {
        const container = document.querySelector('.playlists-container');
        if (!container || this.playlistsObserver) return;

        try {
            // Disconnect existing observer if it exists
            if (this.playlistsObserver) {
                this.playlistsObserver.disconnect();
            }

            // Remove existing sentinel if it exists
            const existingSentinel = container.querySelector('.playlist-sentinel');
            if (existingSentinel) {
                existingSentinel.remove();
            }

            // Create a sentinel element for infinite scroll
            const sentinel = document.createElement('div');
            sentinel.className = 'playlist-sentinel';
            container.appendChild(sentinel);

            // Setup Intersection Observer
            this.playlistsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.playlistsLoading) {
                        this.loadMorePlaylists();
                    }
                });
            }, {
                root: null,
                rootMargin: '50px',
                threshold: 0.1
            });

            this.playlistsObserver.observe(sentinel);
        } catch (error) {
            console.error('Error setting up infinite scroll:', error);
        }
    }

    async renderPlaylists(startIndex = 0, chunk = 20) {
        const container = document.querySelector('.playlists-container');
        if (!container || !Array.isArray(this.playlists)) return;

        try {
            const fragment = document.createDocumentFragment();
            const endIndex = Math.min(startIndex + chunk, this.playlists.length);

            for (let i = startIndex; i < endIndex; i++) {
                const playlist = this.playlists[i];
                if (!playlist || !playlist.id) continue;

                const div = document.createElement('div');
                div.className = 'playlist-item';
                div.dataset.playlistId = playlist.id;
                div.title = playlist.name;
                div.onclick = () => this.playPlaylist(playlist.id);

                const img = document.createElement('img');
                img.loading = 'lazy';

                // Get image URL from Spotify API response
                // Spotify API returns images array in descending size order
                // Use the smallest image for playlists to save bandwidth
                let imageUrl = '/static/images/default-artwork.jpg';
                if (playlist.images && playlist.images.length > 0) {
                    // Use the smallest image (last in the array) for thumbnails
                    imageUrl = playlist.images[playlist.images.length - 1].url;
                }

                img.src = imageUrl;
                img.alt = playlist.name;
                img.onerror = function() {
                    this.src = '/static/images/default-artwork.jpg';
                };

                div.appendChild(img);
                fragment.appendChild(div);
            }

            if (startIndex === 0) {
                container.innerHTML = '';
            }
            container.appendChild(fragment);

            // Update active state if necessary
            if (this.currentPlaylistId) {
                const activeItem = container.querySelector(`[data-playlist-id="${this.currentPlaylistId}"]`);
                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        } catch (error) {
            console.error('Error rendering playlists:', error);
        }
    }

    loadMorePlaylists() {
        if (this.playlistsLoading) return;

        const container = document.querySelector('.playlists-container');
        if (!container) return;

        const currentCount = container.querySelectorAll('.playlist-item').length;
        if (currentCount < this.playlists.length) {
            this.renderPlaylists(currentCount);
        }
    }

    async playPlaylist(playlistId) {
        try {
            // Update active state visually
            document.querySelectorAll('.playlist-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.playlistId === playlistId) {
                    item.classList.add('active');
                }
            });

            // First check current playback state
            const currentPlayback = await this.getCurrentPlayback();
            const isWebPlayer = currentPlayback?.device?.id === this.deviceId;

            if (isWebPlayer && this.player) {
                // Use Web Playback SDK
                await this.player._options.getOAuthToken(async token => {
                    await fetch('https://api.spotify.com/v1/me/player/play', {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            context_uri: `spotify:playlist:${playlistId}`,
                            offset: { position: 0 }
                        })
                    });
                });
            } else {
                // Use our backend API endpoint
                await fetch('/api/spotify/play', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        context_uri: `spotify:playlist:${playlistId}`,
                        offset: { position: 0 },
                        force_device: true
                    })
                });
            }

            this.currentPlaylistId = playlistId;

            // Wait for playback to start
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Get current playback and update queue
            await this.getCurrentPlayback();
            await this.updateQueue(true);

        } catch (error) {
            console.error('Error playing playlist:', error);

            // Try fallback method
            try {
                await fetch('/api/spotify/play-playlist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        playlist_id: playlistId
                    })
                });

                await this.getCurrentPlayback();
                await this.updateQueue(true);
            } catch (fallbackError) {
                console.error('Fallback method failed:', fallbackError);
            }
        }
    }

    async playLikedSongs() {
        try {
            // First check current playback state
            const currentPlayback = await this.getCurrentPlayback();
            const isWebPlayer = currentPlayback?.device?.id === this.deviceId;

            if (isWebPlayer && this.player) {
                // Use Web Playback SDK
                await this.player._options.getOAuthToken(async token => {
                    try {
                        const response = await fetch('https://api.spotify.com/v1/me/tracks', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });
                        const data = await response.json();

                        if (data.items && data.items.length > 0) {
                            const uris = data.items.map(item => item.track.uri);
                            await fetch('https://api.spotify.com/v1/me/player/play', {
                                method: 'PUT',
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    uris: uris.slice(0, 50) // Spotify has a limit of 50 tracks
                                })
                            });
                        }
                    } catch (error) {
                        // Fallback to REST API
                        throw error;
                    }
                });
            } else {
                // Use REST API
                await fetch('/api/spotify/play', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        context_uri: 'saved',
                        force_device: true
                    })
                });
            }

            // Update active state
            document.querySelectorAll('.playlist-item.active').forEach(item => item.classList.remove('active'));
            document.querySelector('.liked-songs')?.classList.add('active');

            // Wait for playback to start
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Update queue and current playback
            await this.getCurrentPlayback();
            await this.updateQueue(true);

        } catch (error) {
            console.error('Failed to play liked songs:', error);

            // Try fallback method
            try {
                await fetch('/api/spotify/play-liked-songs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                await this.getCurrentPlayback();
                await this.updateQueue(true);
            } catch (fallbackError) {
                console.error('Fallback method failed:', fallbackError);
            }
        }
    }

    async seek(position) {
        try {
            await fetch('/api/spotify/playback/seek', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ position_ms: position })
            });

            // Get fresh playback state
            const playback = await this.getCurrentPlayback();
            if (playback) {
                this.updateProgress(playback.progress_ms || position, false);
            }
        } catch (error) {
            console.error('Seek failed:', error);
            throw error;
        }
    }

    async updateAllElements() {
        try {
            // Get current playback state
            const response = await fetch('https://api.spotify.com/v1/me/player', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to get playback state: ${response.status}`);
            }

            const data = await response.json();
            if (!data) return;

            // Update current track information
            const track = data.item;
            if (track) {
                // Update track artwork
                const artworkElement = document.getElementById('track-artwork');
                if (artworkElement) {
                    artworkElement.src = track.album.images[0].url;
                    artworkElement.alt = track.name;
                }

                // Update track name
                ['track-name', 'current-track-name'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = track.name;
                });

                // Update artist name
                ['track-artist', 'current-track-artist'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = track.artists.map(artist => artist.name).join(', ');
                });

                // Update duration and progress
                this.currentTrackDuration = track.duration_ms;
                this.updateProgress(data.progress_ms || 0);
            }

            // Update queue
            await this.updateQueue();

            // Update next track preview
            const queueResponse = await fetch('/api/spotify/queue');
            const queueData = await queueResponse.json();
            const nextTrack = queueData.queue?.[0];

            if (nextTrack) {
                const nextTrackArtwork = document.getElementById('next-track-artwork');
                if (nextTrackArtwork) {
                    nextTrackArtwork.src = nextTrack.album.images[0].url;
                    nextTrackArtwork.alt = nextTrack.name;
                }

                const nextTrackName = document.getElementById('next-track-name');
                if (nextTrackName) {
                    nextTrackName.textContent = nextTrack.name;
                }

                const nextTrackArtist = document.getElementById('next-track-artist');
                if (nextTrackArtist) {
                    nextTrackArtist.textContent = nextTrack.artists.map(artist => artist.name).join(', ');
                }
            }

            // Update play state
            this.isPlaying = data.is_playing;

            // Reset and restart progress interval if playing
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
            }

            if (this.isPlaying) {
                let lastPosition = data.progress_ms || 0;
                let lastUpdate = Date.now();

                this.progressInterval = setInterval(() => {
                    if (this.isPlaying) {
                        const now = Date.now();
                        const elapsed = now - lastUpdate;
                        lastPosition += elapsed;
                        lastUpdate = now;

                        if (lastPosition <= this.currentTrackDuration) {
                            this.updateProgress(lastPosition);
                        }
                    }
                }, 1000);
            }

        } catch (error) {
            console.error('Error updating elements:', error);
        }
    }

    clearTrackInfo() {
        // Clear track names
        ['track-name', 'current-track-name'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = 'No track playing';
        });

        // Clear artist names
        ['track-artist', 'current-track-artist'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = '';
        });

        // Only set default artwork if no track is playing AND we're not in the middle of a track change
        const artworkElement = document.getElementById('track-artwork');
        if (artworkElement && !this.isPlaying && !this.skipLock) {
            this.currentArtworkUrl = '/static/images/default-artwork.jpg';
            artworkElement.src = this.currentArtworkUrl;
        }

        // Clear progress
        const progressBar = document.getElementById('progress');
        if (progressBar) {
            progressBar.style.width = '0%';
        }

        // Clear times
        const currentTime = document.getElementById('track-current-time');
        const totalTime = document.getElementById('track-duration');
        if (currentTime) currentTime.textContent = '0:00';
        if (totalTime) totalTime.textContent = '0:00';
    }

    async preloadTrackImage(track) {
        if (!track?.album?.images?.[0]?.url) return;

        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                this.preloadedImages.set(track.uri, img.src);
                resolve(img.src);
            };
            img.onerror = () => resolve(null);
            img.src = track.album.images[0].url;
        });
    }

    async updateTrackInfo(track, _isSDK = false, forceRefresh = false) {
        if (!track) return;

        // Clear existing artwork transition
        const currentArtwork = document.getElementById('track-artwork');
        if (currentArtwork) {
            currentArtwork.classList.remove('fade-in');
        }

        // Update track URI and duration
        this.currentTrackUri = track.uri;
        this.currentTrackDuration = track.duration_ms;

        // Immediately update text content
        const updateElement = (id, content) => {
            const element = document.getElementById(id);
            if (element) element.textContent = content;
        };

        updateElement('track-name', track.name || 'No track playing');
        updateElement('current-track-name', track.name || 'No track playing');
        updateElement('track-artist', track.artists?.map(artist => artist.name).join(', ') || '');
        updateElement('current-track-artist', track.artists?.map(artist => artist.name).join(', ') || '');

        // Handle artwork loading
        const artworkUrl = track.album?.images?.[0]?.url;
        if (artworkUrl && currentArtwork) {
            const trackUriForLoad = track.uri;

            try {
                // Force a new image load if refresh is requested
                if (forceRefresh) {
                    this.imageLoadingQueue.delete(artworkUrl);
                }

                this.currentTrackLoadPromise = this.loadImage(artworkUrl);
                const loadedUrl = await this.currentTrackLoadPromise;

                // Verify this is still the current track
                if (this.currentTrackUri === trackUriForLoad) {
                    this.currentArtworkUrl = loadedUrl;
                    currentArtwork.src = loadedUrl;
                    requestAnimationFrame(() => {
                        currentArtwork.classList.add('fade-in');
                    });
                }
            } catch (error) {
                console.error('Failed to load track artwork:', error);
            }
        }
    }

    updateTrackInfoExceptArtwork(track) {
        if (!track) return;

        // Update track name and artist
        ['track-name', 'current-track-name'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = track.name || 'Unknown Track';
        });

        ['track-artist', 'current-track-artist'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = track.artists ?
                    track.artists.map(artist => artist.name).join(', ') :
                    'Unknown Artist';
            }
        });

        // Update track URI without changing artwork
        this.currentTrackUri = track.uri;
    }

    initializeMobileVolumeControl() {
        const volumeToggle = document.querySelector('.mobile-volume-toggle');
        const volumeControl = document.querySelector('.volume-control');

        if (!volumeToggle || !volumeControl) return;

        // Toggle volume control visibility
        volumeToggle.addEventListener('click', (e) => {
            e.stopPropagation();
            this.volumeControlVisible = !this.volumeControlVisible;
            volumeControl.classList.toggle('show', this.volumeControlVisible);

            // Clear any existing timeout
            if (this.volumeHideTimeout) {
                clearTimeout(this.volumeHideTimeout);
            }

            // Auto-hide after 3 seconds if visible
            if (this.volumeControlVisible) {
                this.volumeHideTimeout = setTimeout(() => {
                    this.volumeControlVisible = false;
                    volumeControl.classList.remove('show');
                }, 3000);
            }
        });

        // Prevent volume control from closing when interacting with it
        volumeControl.addEventListener('click', (e) => {
            e.stopPropagation();

            // Reset the auto-hide timeout
            if (this.volumeHideTimeout) {
                clearTimeout(this.volumeHideTimeout);
            }

            this.volumeHideTimeout = setTimeout(() => {
                this.volumeControlVisible = false;
                volumeControl.classList.remove('show');
            }, 3000);
        });

        // Hide volume control when clicking outside
        document.addEventListener('click', (e) => {
            if (this.volumeControlVisible &&
                !volumeControl.contains(e.target) &&
                !volumeToggle.contains(e.target)) {
                this.volumeControlVisible = false;
                volumeControl.classList.remove('show');
            }
        });

        // Update volume icon based on level
        const volumeSlider = document.getElementById('volume');
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                const value = e.target.value;
                const icon = volumeToggle.querySelector('i');

                // Update icon based on volume level
                icon.className = 'fa ' + (
                    value == 0 ? 'fa-volume-off' :
                    value < 50 ? 'fa-volume-down' :
                    'fa-volume-up'
                );
            });
        }
    }

    skipToNext() {
        return this.nextTrack();
    }

    skipToPrevious() {
        return this.previousTrack();
    }

    prevTrack() {
        return this.previousTrack();
    }

    togglePlay() {
        return this.togglePlayback();
    }

    // Add pause and resume methods for the Web SDK
    async pause() {
        if (!this.player) throw new Error('Web Playback SDK not initialized');

        return new Promise((resolve, reject) => {
            this.player._options.getOAuthToken(async token => {
                try {
                    const response = await fetch('https://api.spotify.com/v1/me/player/pause', {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok || response.status === 204) {
                        resolve();
                    } else {
                        reject(new Error(`Failed to pause: ${response.status}`));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    async resume() {
        if (!this.player) throw new Error('Web Playback SDK not initialized');

        return new Promise((resolve, reject) => {
            this.player._options.getOAuthToken(async token => {
                try {
                    const response = await fetch('https://api.spotify.com/v1/me/player/play', {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok || response.status === 204) {
                        resolve();
                    } else {
                        reject(new Error(`Failed to resume: ${response.status}`));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    // New method to handle track changes
    async handleTrackChange() {
        try {
            // Clear current artwork immediately
            const currentArtwork = document.getElementById('track-artwork');
            const nextTrackArtwork = document.getElementById('next-track-artwork');

            if (currentArtwork) currentArtwork.classList.remove('fade-in');
            if (nextTrackArtwork) nextTrackArtwork.classList.remove('fade-in');

            // Get fresh playback state
            const playback = await this.getCurrentPlayback();
            if (!playback) return;

            // Update playing state
            this.isPlaying = playback.is_playing;
            this.updatePlayPauseButton();

            // Update current track
            if (playback.item) {
                const trackData = {
                    name: playback.item.name,
                    artists: playback.item.artists,
                    album: playback.item.album,
                    duration_ms: playback.item.duration_ms,
                    uri: playback.item.uri
                };

                await this.updateTrackInfo(trackData, false, true);

                // Update progress if playing
                if (playback.is_playing) {
                    this.startProgressTracking(playback.progress_ms || 0);
                }
            }

            // Get and update queue information
            const queueResponse = await fetch('/api/spotify/queue');
            const queueData = await queueResponse.json();

            if (queueData.queue && queueData.queue.length > 0) {
                const nextTrack = queueData.queue[0];
                await this.updateNextTrackPreview(nextTrack, true);
            } else {
                await this.updateNextTrackPreview(null, true);
            }

        } catch (error) {
            console.error('Failed to handle track change:', error);
        }
    }
}

// Start initialization when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await loadScript('https://sdk.scdn.co/spotify-player.js');
        console.log('Spotify SDK loaded');

        await new Promise(resolve => {
            if (window.Spotify) {
                resolve();
            } else {
                window.onSpotifyWebPlaybackSDKReady = resolve;
            }
        });

        const token = window.SPOTIFY_TOKEN;
        if (!token) {
            throw new Error('No token found');
        }

        window.player = new SpotifyPlayer(token);
        await window.player.initializePlayer();

        // Get current playback state
        const currentPlayback = await window.player.getCurrentPlayback();

        // If we have a Web Playback SDK device ID, transfer playback to it
        if (window.player.deviceId) {
            try {
                // Only transfer if there's no active playback on the Web SDK device
                if (!currentPlayback || currentPlayback.device?.id !== window.player.deviceId) {
                    console.log('Transferring playback to Web SDK device:', window.player.deviceId);

                    // Use the token to transfer playback to the Web SDK device
                    await fetch('https://api.spotify.com/v1/me/player', {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            device_ids: [window.player.deviceId],
                            play: false // Don't auto-play
                        })
                    });

                    // Wait for transfer to complete
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Update state after transfer
                    await window.player.getCurrentPlayback();
                }
            } catch (transferError) {
                console.error('Failed to transfer playback to Web SDK device:', transferError);
            }
        }

        await window.player.initializePlaylists();

        // Update initial play button state
        window.player.updatePlayPauseButton();
    } catch (error) {
        console.error('Initialization error:', error);
        if (!window.location.href.includes('/spotify/connect')) {
            window.location.href = '/spotify/connect';
        }
    }
});

// Set up volume control
document.addEventListener('DOMContentLoaded', () => {
    const volumeSlider = document.getElementById('volume');
    if (volumeSlider) {
        volumeSlider.addEventListener('input', (e) => {
            if (window.player) {
                window.player.setVolume(e.target.value);
            }
        });
    }
});

// Add some CSS for loading state
const style = document.createElement('style');
style.textContent = `
    .playlists-container {
        position: relative;
    }

    .playlist-sentinel {
        height: 1px;
        margin-bottom: 10px;
    }

    .playlist-item img {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .playlist-item img.loaded {
        opacity: 1;
    }
`;
document.head.appendChild(style);

// Add image loading animation
document.addEventListener('load', (event) => {
    if (event.target.tagName === 'IMG' && event.target.closest('.playlist-item')) {
        event.target.classList.add('loaded');
    }
}, true);

// Add touch gesture handling
document.addEventListener('DOMContentLoaded', () => {
    const menu = document.querySelector('.menu');
    if (!menu) return;

    let inactivityTimer;

    function resetInactivityTimer() {
        clearTimeout(inactivityTimer);
        menu.classList.add('active');

        inactivityTimer = setTimeout(() => {
            if (!menu.matches(':hover') && menu.classList.contains('collapsed')) {
                menu.classList.remove('active');
            }
        }, 2000); // 2 seconds of inactivity before becoming translucent
    }

    // Reset timer on any interaction
    document.addEventListener('touchstart', resetInactivityTimer, { passive: true });
    document.addEventListener('mousemove', resetInactivityTimer, { passive: true });

    // Handle clicks outside the menu - only on mobile
    document.addEventListener('click', (event) => {
        if (window.innerWidth <= 768 && !menu.contains(event.target) && !menu.classList.contains('collapsed')) {
            menu.classList.add('collapsed');
        }
    });

    // Prevent menu from collapsing when clicking inside it
    menu.addEventListener('click', (event) => {
        event.stopPropagation();
    });

    // Initial state - only collapse on mobile
    resetInactivityTimer();
    if (window.innerWidth <= 768) {
        menu.classList.add('collapsed');
    } else {
        menu.classList.remove('collapsed');
    }

    // Update menu state on resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            menu.classList.remove('collapsed');
            menu.style.transform = '';
        } else if (!menu.classList.contains('collapsed')) {
            menu.classList.add('collapsed');
        }
    });

    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'menu-overlay';
    menu.after(overlay);

    // Handle overlay click
    overlay.addEventListener('click', () => {
        menu.classList.add('collapsed');
    });

    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;
    let menuWidth = 250; // Match the CSS width
    let visibleWidth = 20; // Width of visible portion
    let isDragging = false;
    let isHorizontalSwipe = false;
    let startTime = 0;
    let currentTranslateX = 0;

    function updateMenuPosition(translateX) {
        const menu = document.querySelector('.menu');
        if (!menu) return;

        menu.style.transform = `translateY(-50%) translateX(${translateX}px)`;
    }

    function handleGesture() {
        const swipeDistance = touchEndX - touchStartX;
        const swipeTime = Date.now() - startTime;
        const velocity = Math.abs(swipeDistance) / swipeTime;

        if ((velocity > 0.5 && swipeDistance > 50) || swipeDistance > menuWidth / 3) {
            menu.classList.remove('collapsed');
        } else if ((velocity > 0.5 && swipeDistance < -50) || swipeDistance < -(menuWidth / 3)) {
            menu.classList.add('collapsed');
        } else {
            menu.classList.toggle('collapsed', currentTranslateX < -(menuWidth / 2));
        }

        menu.style.transform = '';
        overlay.style.opacity = '0';
    }

    document.addEventListener('touchstart', (e) => {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        startTime = Date.now();

        // Allow dragging if within visible portion or menu is open
        isDragging = touchStartX < (visibleWidth + 10) || !menu.classList.contains('collapsed');
        isHorizontalSwipe = false;

        if (isDragging) {
            menu.style.transition = 'none';
            overlay.style.transition = 'none';
        }
    }, { passive: true });

    document.addEventListener('touchmove', (e) => {
        if (!isDragging) return;

        touchEndX = e.touches[0].clientX;
        touchEndY = e.touches[0].clientY;

        if (!isHorizontalSwipe) {
            const deltaX = Math.abs(touchEndX - touchStartX);
            const deltaY = Math.abs(touchEndY - touchStartY);
            isHorizontalSwipe = deltaX > deltaY;

            if (!isHorizontalSwipe) {
                isDragging = false;
                return;
            }
        }

        const diffX = touchEndX - touchStartX;
        const isCollapsed = menu.classList.contains('collapsed');
        const basePosition = isCollapsed ? -menuWidth + visibleWidth : 0;
        currentTranslateX = Math.min(0, Math.max(-menuWidth + visibleWidth, basePosition + diffX));

        updateMenuPosition(currentTranslateX);

        // Update overlay opacity
        const progress = (currentTranslateX + menuWidth - visibleWidth) / (menuWidth - visibleWidth);
        overlay.style.opacity = progress * 0.5;

        e.preventDefault();
    }, { passive: false });

    document.addEventListener('touchend', () => {
        if (!isDragging || !isHorizontalSwipe) return;

        isDragging = false;
        isHorizontalSwipe = false;
        menu.style.transition = 'transform 0.3s ease';
        overlay.style.transition = 'opacity 0.3s ease';
        handleGesture();
    });

    document.addEventListener('touchcancel', () => {
        if (!isDragging) return;

        isDragging = false;
        isHorizontalSwipe = false;
        menu.style.transition = 'transform 0.3s ease';
        overlay.style.transition = 'opacity 0.3s ease';
        handleGesture();
    });

    // Initialize menu state
    if (window.innerWidth <= 768) {
        menu.classList.add('collapsed');
    }
});

// Update menu width on resize
window.addEventListener('resize', () => {
    const menu = document.querySelector('.menu');
    if (!menu) return;

    if (window.innerWidth > 768) {
        menu.classList.remove('collapsed');
        menu.style.transform = '';
    } else if (!menu.classList.contains('collapsed')) {
        menu.classList.add('collapsed');
    }
});

class DeviceManager {
    constructor(spotifyPlayer) {
        this.deviceButton = document.getElementById('device-selector');
        this.deviceDropdown = document.getElementById('device-dropdown');
        this.currentDevice = null;
        this.spotifyPlayer = spotifyPlayer; // Store reference to SpotifyPlayer instance

        this.init();
    }

    init() {
        // Toggle dropdown on button click
        this.deviceButton.addEventListener('click', () => {
            this.toggleDropdown();
            this.updateDevices();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.deviceButton.contains(e.target) && !this.deviceDropdown.contains(e.target)) {
                this.deviceDropdown.classList.remove('show');
            }
        });

        // Initial device update
        this.updateDevices();
    }

    async updateDevices() {
        try {
            // Make multiple attempts to get the most up-to-date device list
            // This helps with the TV device selection issue
            let attempts = 0;
            const maxAttempts = 2;
            let data = null;

            while (attempts < maxAttempts) {
                const response = await fetch('/api/spotify/devices');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                data = await response.json();

                // If we have devices, break out of the loop
                if (data.devices && data.devices.length > 0) {
                    // If we're checking after a pause action, make sure we have accurate active status
                    if (attempts === 0 && !this.spotifyPlayer.isPlaying) {
                        // Wait a bit and try one more time to ensure we have the latest device status
                        await new Promise(resolve => setTimeout(resolve, 500));
                        attempts++;
                    } else {
                        break;
                    }
                } else {
                    // No devices found, try again after a short delay
                    await new Promise(resolve => setTimeout(resolve, 300));
                    attempts++;
                }
            }

            this.deviceDropdown.innerHTML = '';

            if (data.devices) {
                // Track if we found an active device
                let foundActiveDevice = false;

                data.devices.forEach(device => {
                    const deviceElement = document.createElement('div');
                    deviceElement.className = `device-option ${device.is_active ? 'active' : ''}`;
                    deviceElement.innerHTML = `
                        <i class="fas fa-${this.getDeviceIcon(device.type)}"></i>
                        <span>${device.name}</span>
                        ${device.is_active ? '<i class="fas fa-check"></i>' : ''}
                    `;

                    deviceElement.addEventListener('click', () => this.selectDevice(device));
                    this.deviceDropdown.appendChild(deviceElement);

                    if (device.is_active) {
                        this.updateCurrentDevice(device);
                        foundActiveDevice = true;
                    }
                });

                // If no active device was found and we just paused, update the UI to reflect this
                if (!foundActiveDevice && !this.spotifyPlayer.isPlaying && this.currentDevice) {
                    console.log('No active device found after pause, updating UI');
                    // Find the device that matches our current device ID
                    const matchingDevice = data.devices.find(d => d.id === this.currentDevice.id);
                    if (matchingDevice) {
                        // Update the device but mark it as inactive
                        matchingDevice.is_active = false;
                        this.updateCurrentDevice(matchingDevice);
                    }
                }
            }
        } catch (error) {
            console.error('Failed to fetch devices:', error);
            // Add fallback UI for error state
            this.deviceDropdown.innerHTML = '<div class="device-error">Unable to load devices</div>';
        }
    }

    getDeviceIcon(type) {
        const icons = {
            'Computer': 'desktop',
            'Smartphone': 'mobile',
            'Speaker': 'speaker',
            'TV': 'tv'
        };
        return icons[type] || 'speaker';
    }

    async selectDevice(device) {
        try {
            // Transfer playback to the selected device
            const response = await fetch('/api/spotify/transfer-playback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ device_id: device.id })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update local device state
                this.updateCurrentDevice(device);

                // Only update deviceId if switching to web player
                if (device.id === this.spotifyPlayer.deviceId) {
                    this.spotifyPlayer.isWebPlaybackActive = true;
                } else {
                    this.spotifyPlayer.isWebPlaybackActive = false;
                }

                this.deviceDropdown.classList.remove('show');

                // Wait for transfer to complete
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Update playback state if there was active playback
                if (data.has_active_playback) {
                    await this.spotifyPlayer.getCurrentPlayback();
                }

                // Refresh devices list
                await this.updateDevices();
            } else if (data.error) {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Device transfer failed:', error);
            if (error.message.includes('Premium required') || error.message.includes('premium')) {
                alert('Spotify Premium is required to change devices.');
            } else {
                alert('Failed to switch device. Please try again.');
            }

            await this.updateDevices();
        }
    }

    updateCurrentDevice(device) {
        if (!device) return;

        this.currentDevice = device;
        const deviceName = this.deviceButton.querySelector('.device-name');
        if (deviceName) {
            deviceName.textContent = device.name;
        }

        // Update device icon
        const deviceIcon = this.deviceButton.querySelector('.device-icon');
        if (deviceIcon) {
            deviceIcon.className = `fas fa-${this.getDeviceIcon(device.type)} device-icon`;
        }
    }

    toggleDropdown() {
        this.deviceDropdown.classList.toggle('show');
    }
}

// Initialize device manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for SpotifyPlayer to be initialized
    const initDeviceManager = () => {
        if (window.player) {
            window.deviceManager = new DeviceManager(window.player);
        } else {
            // If player isn't ready yet, try again in 100ms
            setTimeout(initDeviceManager, 100);
        }
    };

    initDeviceManager();
});

// Add this to your existing JavaScript
document.addEventListener('DOMContentLoaded', () => {
    const searchResults = document.querySelector('.search-results');
    const searchInput = document.querySelector('#search-input');

    // Show results when input is focused
    searchInput.addEventListener('focus', () => {
        if (searchResults.children.length > 0) {
            searchResults.classList.add('active');
        }
    });

    // Handle clicks outside search container
    document.addEventListener('click', (e) => {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer.contains(e.target)) {
            searchResults.classList.remove('active');
        }
    });

    // Show results when there are search results
    const showSearchResults = () => {
        if (searchResults.children.length > 0) {
            searchResults.classList.add('active');
        }
    };

    // Modify your existing search function to call showSearchResults
    searchInput.addEventListener('input', async () => {
        // After populating search results:
        showSearchResults();
    });
});

function toggleMenu() {
    const menu = document.querySelector('.menu');
    if (!menu) return;

    // Toggle the collapsed class which triggers the animation
    menu.classList.toggle('collapsed');
}

// Event listener for the pull tab
document.addEventListener('DOMContentLoaded', () => {
    const menu = document.querySelector('.menu');
    if (!menu) return;

    // Add initial state
    if (window.innerWidth <= 768) {
        menu.classList.add('collapsed');
    }

    // Handle click on pull tab
    menu.addEventListener('click', (e) => {
        const rect = menu.getBoundingClientRect();
        const isClickOnPullTab = e.clientX >= rect.right - 20;

        if (isClickOnPullTab) {
            toggleMenu();
        }
    });
});
