{% if restricted %}
<div id="restrictionModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
    <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Access Restricted</h3>
            <a href="{{ url_for('dashboard') }}" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </a>
        </div>
        <div class="space-y-4">
            <div class="bg-red-500/10 p-4 rounded-lg flex items-start">
                <i data-lucide="alert-circle" class="h-5 w-5 text-red-500 mr-3 mt-0.5"></i>
                <div>
                    <h4 class="text-red-500 font-medium">Access Denied</h4>
                    <p class="text-slate-300 text-sm mt-1">
                        Your account has been restricted from accessing the {{ service_name }} service.
                    </p>
                </div>
            </div>
            <p class="text-slate-400 text-sm">
                If you believe this is an error, please contact an administrator for assistance.
            </p>
            <div class="pt-2">
                <a href="{{ url_for('dashboard') }}" class="w-full bg-slate-700 hover:bg-slate-600 text-white rounded-md px-4 py-2 flex items-center justify-center">
                    <i data-lucide="arrow-left" class="h-4 w-4 mr-2"></i>
                    Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
<script>
    // Initialize Lucide icons
    if (window.lucide) {
        lucide.createIcons();
    }
</script>
{% endif %}
