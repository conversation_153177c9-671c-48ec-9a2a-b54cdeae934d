from mongoengine import Document, <PERSON><PERSON>ield, IntField, DateTimeField
from datetime import datetime, timezone

class ModelCreditCost(Document):
    """
    Model for storing credit costs for different AI models
    """
    model_name = StringField(required=True, unique=True)
    credit_cost = IntField(required=True)
    last_updated = DateTimeField(default=lambda: datetime.now(timezone.utc))
    
    meta = {
        'collection': 'model_credit_costs',
        'indexes': [
            'model_name'
        ]
    }
    
    @classmethod
    def get_cost(cls, model_name):
        """
        Get credit cost for a specific model
        Returns default cost if model not found
        """
        try:
            # Normalize model name to ensure consistent lookup
            normalized_model_name = model_name.strip().lower()
            
            # Try to find the model cost - force a fresh query to ensure we get the latest data
            # This ensures admin panel updates are immediately reflected
            model_cost = cls.objects(model_name=normalized_model_name).first()
            
            if model_cost:
                print(f"Found cost in database for {normalized_model_name}: {model_cost.credit_cost}")
                return model_cost.credit_cost
            
            # If model not found, use default costs based on model name
            if 'gpt-4o-mini' in normalized_model_name:
                return 5
            elif 'gemini-2.0-flash' in normalized_model_name:
                return 5
            elif 'qwen-qwq-32b' in normalized_model_name:
                return 3
            elif 'gemma2-9b-it' in normalized_model_name:
                return 2
            elif 'llama-3.3-70b-versatile' in normalized_model_name:
                return 4
            elif 'llama-3.1-8b-instant' in normalized_model_name:
                return 2
            elif 'llama3-70b-8192' in normalized_model_name:
                return 3
            elif 'llama3-8b-8192' in normalized_model_name:
                return 2
            elif 'playai-tts' in normalized_model_name:
                return 5
            else:
                # Default cost for unknown models
                return 1
                
        except Exception as e:
            print(f"Error getting model cost: {str(e)}")
            # Return default cost on error
            return 1
    
    @classmethod
    def set_cost(cls, model_name, credit_cost):
        """
        Set credit cost for a specific model
        """
        try:
            # Normalize model name
            normalized_model_name = model_name.strip().lower()
            
            # Find or create model cost record
            model_cost = cls.objects(model_name=normalized_model_name).first()
            
            if model_cost:
                model_cost.credit_cost = credit_cost
                model_cost.last_updated = datetime.now(timezone.utc)
                print(f"Updating existing model cost for {normalized_model_name} to {credit_cost}")
            else:
                model_cost = cls(
                    model_name=normalized_model_name,
                    credit_cost=credit_cost
                )
                print(f"Creating new model cost for {normalized_model_name} with cost {credit_cost}")
            
            model_cost.save()
            print(f"Successfully saved model cost for {normalized_model_name}: {credit_cost}")
            return True
        except Exception as e:
            print(f"Error setting model cost: {str(e)}")
            return False
    
    @classmethod
    def delete_cost(cls, model_name):
        """
        Delete credit cost for a specific model
        """
        try:
            # Normalize model name
            normalized_model_name = model_name.strip().lower()
            
            # Find the model cost record
            model_cost = cls.objects(model_name=normalized_model_name).first()
            
            if model_cost:
                model_cost.delete()
                print(f"Deleted model cost for {normalized_model_name}")
                return True
            else:
                print(f"Model cost not found for {normalized_model_name}")
                return False
        except Exception as e:
            print(f"Error deleting model cost: {str(e)}")
            return False
    
    @classmethod
    def initialize_default_costs(cls):
        """
        Initialize default costs for common models if they don't exist
        """
        # First, delete any models that are not in our current list
        current_models = [
            'gpt-4o-mini',
            'gemini-2.0-flash',
            'qwen-qwq-32b',
            'llama-3.3-70b-versatile',
            'llama-3.1-8b-instant',
            'llama3-70b-8192',
            'llama3-8b-8192',
            'playai-tts',
        ]
        
        # Get all existing models
        all_models = cls.objects().all()
        for model in all_models:
            if model.model_name not in current_models:
                print(f"Removing obsolete model: {model.model_name}")
                cls.delete_cost(model.model_name)
        
        # Now set up the default costs for current models
        default_costs = {
            'gpt-4o-mini': 5,
            'gemini-2.0-flash': 5,
            'qwen-qwq-32b': 3,
            'llama-3.3-70b-versatile': 4,
            'llama-3.1-8b-instant': 2,
            'llama3-70b-8192': 3,
            'llama3-8b-8192': 2,
            'playai-tts': 5,
        }
        
        for model_name, cost in default_costs.items():
            # Only create if doesn't exist
            if not cls.objects(model_name=model_name).first():
                cls.set_cost(model_name, cost)
