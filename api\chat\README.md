# Chat API

This API provides endpoints for managing chat threads and interactions with AI assistants.

## File Structure

The chat API is organized into the following files:

- `__init__.py`: Initializes the chat API blueprint and imports all routes
- `thread_routes.py`: Contains endpoints for thread management (CRUD operations)
- `message_routes.py`: Contains endpoints for message handling
- `sharing_routes.py`: Contains endpoints for thread sharing functionality
- `routes.py`: Imports all routes from the segregated modules for backward compatibility

## Endpoints

### GET /api/chat/threads
- **Description**: Get all threads for the current user
- **Authentication**: Required
- **Response**: List of thread objects

### GET /api/chat/thread/:thread_id
- **Description**: Get a specific thread by ID
- **Authentication**: Required
- **Response**: Thread object

### POST /api/chat/thread
- **Description**: Create a new thread
- **Authentication**: Required
- **Response**: New thread object

### POST /api/chat/send
- **Description**: Send a message to a thread and get AI response
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "thread_id": "thread_id",
    "message": "User message",
    "model": "gpt-4o-mini",
    "images": []
  }
  ```
- **Response**: Streaming response with AI message chunks

### DELETE /api/chat/thread/:thread_id
- **Description**: Delete a thread
- **Authentication**: Required
- **Response**: Success message

### PUT /api/chat/thread/:thread_id/title
- **Description**: Update thread title
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "title": "New thread title"
  }
  ```
- **Response**: Updated thread object

### GET /api/chat/shared-threads
- **Description**: Get all shared threads for the current user
- **Authentication**: Required
- **Response**: List of shared thread objects

### POST /api/chat/share-thread
- **Description**: Share a thread with another user
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "thread_id": "thread_id",
    "username": "username"
  }
  ```
- **Response**: Shared thread object

### POST /api/chat/thread/:thread_id/share
- **Description**: Create a public share link for a thread
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "share_id": "share_id",
    "share_url": "/chat/shared/share_id"
  }
  ```

### POST /api/chat/shared/:share_id/copy
- **Description**: Copy a shared thread to the current user's threads
- **Authentication**: Required
- **Response**: New thread object

## Models

### Thread
- `id`: Thread ID
- `user`: User ID
- `title`: Thread title
- `messages`: List of message objects
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Message
- `role`: Message role (user/assistant)
- `content`: Message content
- `timestamp`: Message timestamp (optional)
- `model`: AI model used (for assistant messages, optional)
- `images`: List of image objects (for user messages with images, optional)

### Image Object
- `data`: Base64-encoded image data
- `mime_type`: MIME type of the image (e.g., "image/jpeg", "image/png")

## Usage Example

```javascript
// Create a new thread
const response = await fetch('/api/chat/thread', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
});
const thread = await response.json();

// Send a message
const messageResponse = await fetch('/api/chat/send', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    thread_id: thread.id,
    message: 'Hello, AI!',
    model: 'gpt-4o-mini'
  })
});
// Handle streaming response
```
