/**
 * modal-test.js
 * A simple script to test modal functionality
 */

console.log('Modal test script loaded');

// Function to check if a modal exists and log its properties
function checkModal(modalId) {
    console.log(`Checking modal: ${modalId}`);
    const modal = document.getElementById(modalId);
    
    if (!modal) {
        console.error(`Modal not found: ${modalId}`);
        return;
    }
    
    console.log('Modal found!');
    console.log('Classes:', modal.className);
    console.log('Style display:', modal.style.display);
    console.log('Computed style display:', window.getComputedStyle(modal).display);
    console.log('Visibility:', window.getComputedStyle(modal).visibility);
    console.log('Opacity:', window.getComputedStyle(modal).opacity);
    
    // Log parent elements to check if they might be affecting visibility
    let parent = modal.parentElement;
    let i = 0;
    while (parent && i < 3) {
        console.log(`Parent ${i} display:`, window.getComputedStyle(parent).display);
        parent = parent.parentElement;
        i++;
    }
}

// Function to toggle a modal's visibility
function toggleModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) {
        console.error(`Cannot toggle modal: ${modalId} - not found`);
        return;
    }
    
    if (modal.classList.contains('hidden') || modal.style.display === 'none') {
        console.log(`Showing modal: ${modalId}`);
        modal.classList.remove('hidden');
        modal.style.display = 'flex';
    } else {
        console.log(`Hiding modal: ${modalId}`);
        modal.classList.add('hidden');
        modal.style.display = 'none';
    }
}

// Check modals when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Checking modals...');
    
    // Check the add credits modal
    checkModal('addCreditsModal');
    
    // Test functionality is now handled by the main application
    console.log('Test button removed to avoid UI clutter');
    
    // Keep the global function for debugging purposes only
    window.testToggleModal = (modalId) => {
        console.log(`Debug function called for modal: ${modalId || 'addCreditsModal'}`);
        return toggleModal(modalId || 'addCreditsModal');
    };
});