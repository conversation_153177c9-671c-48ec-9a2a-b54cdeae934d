# Country Data Management

This document explains how country data is managed in the application and how to disable/enable this feature.

## What Was Done

The following actions were taken to remove country data:

1. **Cleared Country Data**: All `country_code` fields in the User collection were set to empty strings
2. **Disabled Country Detection**: The country detection middleware was disabled by setting `ENABLE_COUNTRY_SCHEDULER=false` in the `.env` file
3. **Cleared IP Cache**: The IP to country cache was cleared to prevent any cached country data from being used

## Scripts

The following scripts were created:

- `cleanup_country_data.py`: Combined script that performs all cleanup operations
- `clear_country_data.py`: Script that only clears country data from users
- `disable_country_detection.py`: Script that disables the country detection middleware
- `clear_ip_cache.py`: Script that clears the IP to country cache
- `verify_country_data.py`: Script that verifies country data has been removed

## How Country Data Works

Country data is used in the following ways:

1. **User Model**: Each user has a `country_code` field that stores their country code (e.g., 'US', 'GB')
2. **Country Detection**: The application detects user countries based on their IP address using the `country_detection_middleware`
3. **World Map**: The statistics panel displays a world map showing user distribution by country
4. **Scheduled Tasks**: A scheduler periodically updates country codes for users who don't have one set

## Re-enabling Country Data

If you need to re-enable country data in the future:

1. **Enable Country Detection**: Set `ENABLE_COUNTRY_SCHEDULER=true` in the `.env` file
2. **Restart Application**: Restart the application for changes to take effect
3. **Add Mock Data (Optional)**: Run `python add_country_data.py` to add mock country data for testing

## Adding Real Country Data

To add real country data based on user IP addresses:

1. Enable country detection as described above
2. Run `python update_user_countries.py` to update country codes based on API logs

## Notes

- Country data is used only for the world map visualization in the statistics panel
- No personal information is stored with the country data
- The country detection uses the ipinfo.io service to determine country from IP address
