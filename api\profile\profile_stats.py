from flask import jsonify
from flask_login import login_required, current_user
from models.thread import Thread
from models.room import Room
from models.friend_relationship import FriendRelationship
import logging
from . import profile_api

@profile_api.route('/stats', methods=['GET'])
@login_required
def get_user_stats():
    """Get usage statistics for the current user"""
    try:
        # Get chat usage statistics
        threads = Thread.objects(user=current_user.id)
        thread_count = threads.count()

        # Calculate conversation sets for each thread (user + assistant messages)
        thread_conversation_sets = {}
        total_thread_conversation_sets = 0
        for thread in threads:
            # Count user messages in this thread
            user_messages = sum(1 for msg in thread.messages if msg.get('role') == 'user')
            thread_conversation_sets[str(thread.id)] = user_messages
            total_thread_conversation_sets += user_messages

        # Get live room usage statistics
        rooms = Room.objects(creator=current_user.id)
        room_count = rooms.count()

        # Calculate conversation sets for each room (user messages)
        room_conversation_sets = {}
        total_room_conversation_sets = 0
        for room in rooms:
            # Count user messages in this room
            user_messages = sum(1 for msg in room.messages if msg.get('role') == 'user')
            room_conversation_sets[room.room_id] = user_messages
            total_room_conversation_sets += user_messages

        # Get friends count
        # Get all accepted relationships where the user is either the user or the friend
        # But count each unique friend only once
        friends_as_user = FriendRelationship.objects(user=current_user.id, is_accepted=True)

        # Create a set of unique friend IDs
        unique_friend_ids = set()

        # Add friends where current user is the "user"
        for relationship in friends_as_user:
            unique_friend_ids.add(str(relationship.friend.id))

        # Add friends where current user is the "friend"
        friends_as_friend = FriendRelationship.objects(friend=current_user.id, is_accepted=True)
        for relationship in friends_as_friend:
            unique_friend_ids.add(str(relationship.user.id))

        # Count unique friends
        friend_count = len(unique_friend_ids)

        # Get total message counts
        message_count = sum(len(thread.messages) for thread in threads)
        room_message_count = sum(len(room.messages) for room in rooms)

        return jsonify({
            # Chat usage
            'thread_count': thread_count,
            'thread_conversation_sets': thread_conversation_sets,
            'total_thread_conversation_sets': total_thread_conversation_sets,

            # Live usage
            'room_count': room_count,
            'room_conversation_sets': room_conversation_sets,
            'total_room_conversation_sets': total_room_conversation_sets,

            # Friends usage
            'friend_count': friend_count,

            # Legacy stats (kept for backward compatibility)
            'message_count': message_count,
            'room_message_count': room_message_count,
            'total_message_count': message_count + room_message_count
        })
    except Exception as e:
        logging.error(f"Error getting user stats: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500