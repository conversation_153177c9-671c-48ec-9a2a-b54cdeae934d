from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON>ield, <PERSON><PERSON><PERSON><PERSON>
from models.user import User
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

class APILog(Document):
    """Model for storing API request logs"""
    user = ReferenceField(User, required=True)
    action = StringField(required=True)  # 'thread_create', 'thread_interaction', etc.
    service = StringField(required=True, choices=['chat', 'live', 'spotify', 'friends'])
    timestamp = DateTimeField(default=datetime.utcnow)
    details = DictField(default={})  # Additional details about the request

    meta = {
        'collection': 'api_logs',
        'indexes': [
            'user',
            'action',
            'service',
            'timestamp'
        ],
        'ordering': ['-timestamp']  # Default ordering by timestamp descending
    }

    @classmethod
    def log_action(cls, user, action, service, details=None):
        """Log an API action"""
        if details is None:
            details = {}

        try:
            logging.info(f"Creating API log: {action} for service {service} by {user.username}")
            log = cls(
                user=user,
                action=action,
                service=service,
                details=details
            )
            log.save()
            logging.info(f"Successfully saved API log: {log.id}")
            return log
        except Exception as e:
            logging.error(f"Error saving API log: {str(e)}")
            # Re-raise the exception to be handled by the caller
            raise

    @classmethod
    def get_logs(cls, limit=100, service=None, action=None, hours=24):
        """Get logs with optional filtering"""
        query = {}

        if service:
            query['service'] = service

        if action:
            query['action'] = action

        if hours:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            query['timestamp__gte'] = cutoff_time

        return cls.objects(**query).order_by('-timestamp').limit(limit)

    def to_dict(self):
        """Convert log to dictionary"""
        log_data = {
            'id': str(self.id),
            'user_id': str(self.user.id),
            'username': self.user.username,
            'email': self.user.email,
            'action': self.action,
            'service': self.service,
            'timestamp': self.timestamp.isoformat(),
            'details': self.details
        }

        # Only add profile_picture if it exists
        if hasattr(self.user, 'profile_picture') and self.user.profile_picture:
            log_data['profile_picture'] = self.user.profile_picture

        return log_data
