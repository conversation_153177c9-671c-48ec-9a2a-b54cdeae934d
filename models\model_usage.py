from mongoengine import Document, <PERSON><PERSON>ield, IntField, DateTimeField, ReferenceField, <PERSON>oleanField
from datetime import datetime, timezone
from models.user import User
from models.user_credit import UserCredit
from models.model_credit_cost import ModelCreditCost
from models.credit_transaction import CreditTransaction

class ModelUsage(Document):
    """
    Model for tracking AI model usage
    """
    user = ReferenceField(User, required=True)
    model_name = StringField(required=True)
    service = StringField(required=True)  # 'chat', 'live', etc.
    tokens_used = IntField(default=0)
    credits_used = IntField(default=0)  # Number of credits used for this model usage
    timestamp = DateTimeField(default=lambda: datetime.now(timezone.utc))
    is_admin = BooleanField(default=False)  # Flag to indicate if user is admin (admins don't use credits)

    meta = {
        'collection': 'model_usage',
        'indexes': [
            'user',
            'model_name',
            'service',
            'timestamp'
        ]
    }

    @classmethod
    def log_usage(cls, user_id, model_name, service, tokens_used=0):
        """
        Log model usage and handle credit deduction
        Returns:
            - ModelUsage object if successful
            - None if there was an error
            - False if user has insufficient credits
        """
        try:
            # Ensure model_name is not None or empty
            if not model_name or model_name.lower() == 'none':
                model_name = 'unknown'

            # Normalize model name to ensure consistent tracking
            model_name = model_name.strip().lower()

            # Ensure service is valid
            if service not in ['chat', 'live']:
                print(f"Warning: Invalid service '{service}', defaulting to 'chat'")
                service = 'chat'

            # Get user to check if admin
            user = User.objects(id=user_id).first()
            if not user:
                print(f"Error: User {user_id} not found")
                return None

            is_admin = user.is_admin

            # Get credit cost for this model - ensure we get the latest from the database
            credit_cost = ModelCreditCost.get_cost(model_name)
            print(f"Credit cost for model {model_name}: {credit_cost}")
            
            # Force refresh from database to ensure we have the latest cost
            # This ensures admin panel updates are immediately reflected
            model_cost_record = ModelCreditCost.objects(model_name=model_name).first()
            if model_cost_record:
                credit_cost = model_cost_record.credit_cost
                print(f"Updated credit cost from database for {model_name}: {credit_cost}")

            # If user is not admin, check and deduct credits
            if not is_admin:
                # Get current credit balance
                user_credit = UserCredit.get_or_create(user_id)
                if not user_credit:
                    print(f"Error: Failed to get credit record for user {user_id}")
                    return None

                print(f"Current credit balance for user {user_id}: {user_credit.balance}")

                # Check if user has enough credits
                if user_credit.balance < credit_cost:
                    print(f"Insufficient credits for user {user_id} to use model {model_name}. Balance: {user_credit.balance}, Cost: {credit_cost}")
                    # Return False with balance information to handle zero balance specially
                    return {"status": False, "balance": user_credit.balance, "cost": credit_cost}

                # Deduct credits - use the updated credit_cost value
                print(f"Attempting to deduct {credit_cost} credits from user {user_id} for model {model_name}")
                if not UserCredit.deduct_credits(user_id, credit_cost):
                    print(f"Failed to deduct {credit_cost} credits from user {user_id}")
                    return False

                # Log credit transaction with the correct cost
                transaction = CreditTransaction.log_deduction(
                    user_id=user_id,
                    amount=credit_cost,
                    model_name=model_name,
                    service=service
                )
                
                print(f"Successfully deducted {credit_cost} credits from user {user_id} for model {model_name}")

                if not transaction:
                    print(f"Warning: Failed to log credit transaction for user {user_id}")

            print(f"Logging model usage: user={user_id}, model={model_name}, service={service}, credits={credit_cost if not is_admin else 0}")

            # Create and save usage record
            usage = cls(
                user=user_id,
                model_name=model_name,
                service=service,
                tokens_used=tokens_used,
                credits_used=credit_cost if not is_admin else 0,
                is_admin=is_admin
            )
            usage.save()

            # Print confirmation
            print(f"Successfully logged model usage: {usage.id}")

            # Get updated credit balance
            if not is_admin:
                updated_credit = UserCredit.get_or_create(user_id)
                if updated_credit:
                    print(f"Updated credit balance for user {user_id}: {updated_credit.balance}")

            return usage
        except Exception as e:
            import traceback
            print(f"Error logging model usage: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None
