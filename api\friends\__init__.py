from flask import Blueprint

# Create API blueprint
friends_api = Blueprint('friends_api', __name__, url_prefix='/api/friends')

# Import routes after creating blueprint to avoid circular imports
from .routes import friend_management, friend_chat, group_chat, encryption, search
from . import list

# Print registered routes for debugging
print("Registered routes for friends_api:")
for rule in friends_api.deferred_functions:
    print(f"  - {rule}")
