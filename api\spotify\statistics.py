from flask import jsonify, request
from flask_login import current_user, login_required
from . import spotify_api
from models.api_log import APILog
from models.user import User
import logging
from datetime import datetime, timedelta
from utils.api_logger import log_api_request

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@spotify_api.route('/statistics/top-users', methods=['GET'])
@login_required
@log_api_request('statistics_view', 'spotify')
def get_top_kevkofy_users():
    """
    Get top users of KevkoFy service based on specific actions:
    - skip/playnext
    - search+play
    - playlist plays
    """
    try:
        # Get parameters
        days = int(request.args.get('days', 30))
        limit = int(request.args.get('limit', 10))
        
        # Calculate cutoff time
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # Define the actions we want to track
        kevkofy_actions = [
            'playback_control',  # For skip/playnext
            'search_play',       # For search+play
            'playlist_play'      # For playlist plays
        ]
        
        # Aggregate user activity for KevkoFy service
        pipeline = [
            {'$match': {
                'service': 'spotify',
                'action': {'$in': kevkofy_actions},
                'timestamp': {'$gte': cutoff_time}
            }},
            {'$group': {
                '_id': '$user',
                'total_usage': {'$sum': 1},
                'playback_control_count': {
                    '$sum': {'$cond': [{'$eq': ['$action', 'playback_control']}, 1, 0]}
                },
                'search_play_count': {
                    '$sum': {'$cond': [{'$eq': ['$action', 'search_play']}, 1, 0]}
                },
                'playlist_play_count': {
                    '$sum': {'$cond': [{'$eq': ['$action', 'playlist_play']}, 1, 0]}
                }
            }},
            {'$sort': {'total_usage': -1}},
            {'$limit': limit}
        ]
        
        top_users_data = list(APILog.objects.aggregate(pipeline))
        
        # Format the response with user details
        result = []
        
        for user_data in top_users_data:
            user_id = user_data['_id']
            if not user_id:
                continue
                
            user = User.objects(id=user_id).first()
            if not user:
                continue
                
            result.append({
                'user_id': str(user.id),
                'username': user.username,
                'display_name': user.display_name if hasattr(user, 'display_name') else None,
                'profile_picture': user.profile_picture if hasattr(user, 'profile_picture') else None,
                'total_usage': user_data['total_usage'],
                'playback_control_count': user_data['playback_control_count'],
                'search_play_count': user_data['search_play_count'],
                'playlist_play_count': user_data['playlist_play_count']
            })
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error getting top KevkoFy users: {str(e)}")
        return jsonify([]), 500