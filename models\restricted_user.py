from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON><PERSON>
from datetime import datetime

class RestrictedUser(Document):
    """Model for storing users restricted from accessing services"""
    email = StringField(required=True, unique=True)
    restricted_services = ListField(StringField(), default=list)  # List of service IDs: 'chat', 'live', 'spotify', 'friends'
    created_at = DateTimeField(default=datetime.utcnow)
    created_by = StringField()  # Admin email who added the restriction

    meta = {
        'collection': 'restricted_users',
        'indexes': [
            'email',
        ]
    }

    @classmethod
    def is_restricted(cls, email, service):
        """Check if a user is restricted from a specific service"""
        user = cls.objects(email=email).first()
        if not user:
            return False
        return service in user.restricted_services

    @classmethod
    def get_all_restricted(cls):
        """Get all restricted users"""
        return cls.objects.all()

    @classmethod
    def add_restriction(cls, email, services, admin_email):
        """Add a restriction for a user"""
        user = cls.objects(email=email).first()
        if user:
            # Update existing restriction
            for service in services:
                if service not in user.restricted_services:
                    user.restricted_services.append(service)
            user.save()
            return user
        else:
            # Create new restriction
            user = cls(
                email=email,
                restricted_services=services,
                created_by=admin_email
            )
            user.save()
            return user

    @classmethod
    def remove_restriction(cls, email, service=None):
        """Remove a restriction for a user
        If service is None, remove all restrictions"""
        user = cls.objects(email=email).first()
        if not user:
            return False

        if service is None:
            # Remove all restrictions
            user.delete()
            return True
        else:
            # Remove specific service restriction
            if service in user.restricted_services:
                user.restricted_services.remove(service)
                if not user.restricted_services:
                    # If no restrictions left, delete the document
                    user.delete()
                else:
                    user.save()
                return True
            return False
