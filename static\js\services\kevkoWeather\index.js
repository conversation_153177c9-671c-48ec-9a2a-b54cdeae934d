/**
 * KevkoWeather Service
 * Weather and climate data service
 */

// Import dependencies
// None for now

// Service class definition
window.KevkoWeatherService = class KevkoWeatherService extends window.Service {
    constructor(config) {
        super(config);
        this.locations = [];
        this.currentLocation = null;
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // In a real implementation, this might fetch data from an API
        this.locations = [
            { id: 1, name: 'Berlin', temp: 18, condition: 'Partly Cloudy' },
            { id: 2, name: 'Munich', temp: 20, condition: 'Sunny' },
            { id: 3, name: 'Hamburg', temp: 16, condition: 'Rainy' }
        ];

        this.currentLocation = this.locations[0];
    }

    /**
     * Render the service card HTML
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(`Rendering service: ${this.id}`);
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            let actionButton = '';

            if (this.status === 'online' && this.url) {
                // External link for online services with URL
                actionButton = `
                    <div class="w-full">
                        <a href="${this.url}" target="_blank"
                           class="w-full bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group mb-2">
                            <span class="text-sm text-slate-300 group-hover:text-slate-100">Launch ${this.name}</span>
                            <i data-lucide="chevron-right" class="h-4 w-4 ml-1"></i>
                        </a>
                        <button class="w-full bg-slate-700/30 hover:bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-1 text-center transition-colors text-xs text-slate-400 flex items-center justify-center" id="showWeather">
                            <i data-lucide="cloud-sun" class="h-3 w-3 mr-1"></i>
                            Show Weather
                        </button>
                    </div>
                `;
            } else {
                // Disabled button for offline or coming soon services
                actionButton = `
                    <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed">
                        <span class="text-sm text-slate-300 animate-pulse">${this.status === 'offline' ? 'Offline' : 'Coming Soon'}</span>
                    </div>
                `;
            }

            const html = `
                <div class="service-card ${statusClass} bg-slate-800/50 border border-${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-${this.color}-500/10">
                                <i data-lucide="${this.icon}" class="h-6 w-6 text-${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full ${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">${this.description}</p>
                        ${actionButton}
                        <div id="weather-${this.id}" class="hidden mt-3 bg-slate-800/80 rounded-md p-2 border border-slate-700/50">
                            <h4 class="text-xs font-medium text-slate-300 mb-2">Weather Forecast</h4>
                            <div class="space-y-1 max-h-32 overflow-y-auto">
                                <!-- Weather data will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        } catch (error) {
            console.error(`Error rendering service ${this.id}:`, error);
            return `<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: ${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>`;
        }
    }

    /**
     * Initialize event handlers for the service
     * @param {HTMLElement} element The service element
     */
    initEventHandlers(element) {
        super.initEventHandlers(element);

        try {
            // Initialize weather button
            const showWeatherBtn = element.querySelector('#showWeather');
            if (showWeatherBtn) {
                console.log(`Found weather button for service: ${this.id}`);
                showWeatherBtn.addEventListener('click', () => {
                    this.toggleWeather(element);
                });
            } else {
                console.log(`No weather button found for service: ${this.id}`);
            }
        } catch (error) {
            console.error(`Error setting up event handlers for KevkoWeatherService ${this.id}:`, error);
        }
    }

    /**
     * Toggle weather visibility
     * @param {HTMLElement} element The service element
     */
    async toggleWeather(element) {
        const weatherContainer = element.querySelector(`#weather-${this.id}`);
        if (!weatherContainer) return;

        const isHidden = weatherContainer.classList.contains('hidden');

        if (isHidden) {
            // Initialize locations if needed
            if (this.locations.length === 0) {
                await this.init();
            }

            // Populate weather data
            const weatherContent = weatherContainer.querySelector('.space-y-1');
            if (weatherContent) {
                weatherContent.innerHTML = this.locations.map(location => {
                    const tempClass = location.temp > 18 ? 'text-orange-400' : 'text-blue-400';

                    return `
                        <div class="flex items-center justify-between bg-slate-700/50 rounded px-2 py-1 hover:bg-slate-700 transition-colors">
                            <div class="flex items-center">
                                <i data-lucide="${this.getWeatherIcon(location.condition)}" class="h-3 w-3 mr-1 text-${this.color}-400"></i>
                                <span class="text-xs text-slate-300">${location.name}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-xs ${tempClass}">${location.temp}°C</span>
                                <span class="text-xs text-slate-500 ml-1">${location.condition}</span>
                            </div>
                        </div>
                    `;
                }).join('');

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-3", "w-3"]
                        },
                        elements: [weatherContent]
                    });
                }
            }

            // Show weather
            weatherContainer.classList.remove('hidden');
        } else {
            // Hide weather
            weatherContainer.classList.add('hidden');
        }
    }

    /**
     * Get icon for weather condition
     * @param {string} condition Weather condition
     * @returns {string} Icon name
     */
    getWeatherIcon(condition) {
        switch (condition.toLowerCase()) {
            case 'sunny':
                return 'sun';
            case 'partly cloudy':
                return 'cloud-sun';
            case 'cloudy':
                return 'cloud';
            case 'rainy':
                return 'cloud-rain';
            case 'stormy':
                return 'cloud-lightning';
            case 'snowy':
                return 'cloud-snow';
            default:
                return 'cloud';
        }
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: 'kevkoWeather',
            name: 'KevkoWeather',
            description: 'Weather and climate data',
            icon: 'cloud-sun',
            color: 'orange',
            category: 'Utilities',
            version: '1.0.0',
            author: 'Kevko',
            dependencies: [],
            features: [
                'Real-time weather data',
                'Multi-location support',
                'Forecasts',
                'Climate statistics'
            ],
            screenshots: [
                '/img/services/kevkoWeather/screenshot1.jpg'
            ]
        };
    }

    /**
     * Get service updates
     * @returns {Array} Service updates
     */
    static getUpdates() {
        // Updates are now managed through the admin panel
        return [];
    }

    /**
     * Get service downloads
     * @returns {Array} Service downloads
     */
    static getDownloads() {
        return [
            {
                name: 'KevkoWeather Mobile App',
                description: 'Weather forecasts on the go',
                icon: 'smartphone',
                size: '12 MB',
                version: '1.1.0',
                date: '2023-02-20',
                url: '#'
            }
        ];
    }
};
