from functools import wraps
from flask import jsonify, request, abort
from flask_login import current_user
from models.restricted_user import RestrictedUser
from models.feature_restriction import FeatureRestriction
import logging

def check_service_access(service_name):
    """
    Decorator to check if a user is restricted from accessing a service.

    Args:
        service_name (str): The name of the service to check ('chat', 'live', 'spotify', 'friends')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is authenticated and restricted
            if current_user.is_authenticated and RestrictedUser.is_restricted(current_user.email, service_name):
                return jsonify({
                    'error': f'Access denied. Your account is restricted from using the {service_name} service.',
                    'restricted': True,
                    'service': service_name
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_thread_limit():
    """
    Decorator to check if a user has reached their thread limit.
    Used for endpoints that create new threads.

    NOTE: Automatic feature restriction has been removed.
    This decorator now only enforces restrictions set by admins.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Only check if there's an admin-set restriction
            if current_user.is_authenticated:
                restriction = FeatureRestriction.objects(user=current_user.id).first()
                if restriction:  # Only check if admin has set a restriction
                    if not FeatureRestriction.can_create_thread(current_user.id):
                        return jsonify({
                            'error': 'You have reached the maximum number of threads allowed. Please delete some existing threads to create new ones.',
                            'feature_restricted': True
                        }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_message_limit():
    """
    Decorator to check if a user has reached their message limit for a thread.
    Used for endpoints that add messages to threads.

    NOTE: Automatic feature restriction has been removed.
    This decorator now only enforces restrictions set by admins.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Only check for POST requests to endpoints that add messages
            if request.method == 'POST':
                thread_id = kwargs.get('thread_id') or request.json.get('thread_id')
                if thread_id and current_user.is_authenticated:
                    restriction = FeatureRestriction.objects(user=current_user.id).first()
                    if restriction:  # Only check if admin has set a restriction
                        if not FeatureRestriction.can_add_message(current_user.id, thread_id):
                            return jsonify({
                                'error': 'You have reached the maximum number of messages allowed in this thread.',
                                'feature_restricted': True
                            }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_live_room_limit():
    """
    Decorator to check if a user has reached their live room limit.
    Used for endpoints that create new live rooms.

    NOTE: Automatic feature restriction has been removed.
    This decorator now only enforces restrictions set by admins.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Only check if there's an admin-set restriction
            if current_user.is_authenticated:
                restriction = FeatureRestriction.objects(user=current_user.id).first()
                if restriction:  # Only check if admin has set a restriction
                    if not FeatureRestriction.can_create_live_room(current_user.id):
                        return jsonify({
                            'error': 'You have reached the maximum number of live rooms allowed. Please delete some existing rooms to create new ones.',
                            'feature_restricted': True
                        }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def check_live_message_limit():
    """
    Decorator to check if a user has reached their message limit for a live room.
    Used for endpoints that add messages to live rooms.

    NOTE: Automatic feature restriction has been removed.
    This decorator now only enforces restrictions set by admins.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Only check for POST requests to endpoints that add messages
            if request.method == 'POST':
                room_id = kwargs.get('room_id') or request.json.get('room_id')
                if room_id and current_user.is_authenticated:
                    restriction = FeatureRestriction.objects(user=current_user.id).first()
                    if restriction:  # Only check if admin has set a restriction
                        if not FeatureRestriction.can_add_live_message(current_user.id, room_id):
                            return jsonify({
                                'error': 'You have reached the maximum number of messages allowed in this live room.',
                                'feature_restricted': True
                            }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator
