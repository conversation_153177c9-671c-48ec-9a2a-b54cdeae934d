from flask import jsonify, request
from flask_login import login_required
from models.api_log import APILog
from models.user import User
from . import admin_api
from .routes import admin_required
import logging
from bson import ObjectId
from functools import lru_cache

# Cache for checking if a user exists
# This will significantly speed up repeated checks for the same user IDs
@lru_cache(maxsize=1000)
def user_exists(user_id):
    """Check if a user with the given ID exists in the database"""
    try:
        return User.objects(id=user_id).first() is not None
    except Exception:
        return False

# Cache for known deleted user IDs
deleted_user_ids = set()

@admin_api.route('/logs', methods=['GET'])
@login_required
@admin_required
def get_logs():
    """Get API logs with filtering options"""
    try:
        # Get query parameters
        user_id = request.args.get('user_id')
        service = request.args.get('service')
        action = request.args.get('action')
        limit = request.args.get('limit', 100, type=int)
        skip = request.args.get('skip', 0, type=int)

        # Build query
        query = {}
        if user_id:
            query['user'] = user_id
        if service:
            query['service'] = service
        if action:
            query['action'] = action

        # Get logs
        logs = APILog.objects(**query).order_by('-timestamp').skip(skip).limit(limit)

        # Format response
        result = []
        for log in logs:
            try:
                # Check if user exists and handle case where it doesn't
                if hasattr(log, 'user') and log.user is not None:
                    # Get user ID as string
                    user_id_str = str(log.user.id)

                    # Check if this is a known deleted user
                    if user_id_str in deleted_user_ids:
                        user_info = {
                            'id': 'deleted_user',
                            'username': 'Deleted User',
                            'email': 'deleted@user',
                            'profile_picture': None
                        }
                    else:
                        try:
                            # Try to access user properties
                            user_info = {
                                'id': user_id_str,
                                'username': log.user.username,
                                'email': log.user.email,
                                'profile_picture': log.user.profile_picture if hasattr(log.user, 'profile_picture') else None
                            }
                        except Exception:
                            # User reference exists but can't be dereferenced (user was deleted)
                            # Add to known deleted users for faster future lookups
                            deleted_user_ids.add(user_id_str)
                            user_info = {
                                'id': 'deleted_user',
                                'username': 'Deleted User',
                                'email': 'deleted@user',
                                'profile_picture': None
                            }
                else:
                    user_info = {
                        'id': 'unknown',
                        'username': 'Unknown User',
                        'email': 'unknown@user',
                        'profile_picture': None
                    }

                result.append({
                    'id': str(log.id),
                    'user': user_info,
                    'action': log.action,
                    'service': log.service,
                    'details': log.details,
                    'timestamp': log.timestamp.isoformat()
                })
            except Exception:
                # Skip this log entry and continue with the next one
                pass

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in get_logs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/logs/summary', methods=['GET'])
@login_required
@admin_required
def get_logs_summary():
    """Get summary of API logs"""
    try:
        # Get total count
        try:
            total_count = APILog.objects.count()
        except Exception as count_error:
            logging.warning(f"Error counting total logs: {str(count_error)}")
            total_count = 0

        # Get counts by service
        service_counts = {}
        for service in ['chat', 'live', 'spotify', 'friends']:
            try:
                service_counts[service] = APILog.objects(service=service).count()
            except Exception as service_error:
                logging.warning(f"Error counting logs for service {service}: {str(service_error)}")
                service_counts[service] = 0

        # Get counts by action
        action_counts = {}
        for action in ['thread_create', 'thread_interaction', 'live_create', 'live_interaction',
                      'friend_request', 'friend_accept', 'friend_chat_message', 'playback_control']:
            try:
                action_counts[action] = APILog.objects(action=action).count()
            except Exception as action_error:
                logging.warning(f"Error counting logs for action {action}: {str(action_error)}")
                action_counts[action] = 0

        # Get recent activity (last 24 hours)
        from datetime import datetime, timedelta
        try:
            recent_count = APILog.objects(timestamp__gte=datetime.now() - timedelta(days=1)).count()
        except Exception as recent_error:
            logging.warning(f"Error counting recent logs: {str(recent_error)}")
            recent_count = 0

        return jsonify({
            'total_count': total_count,
            'service_counts': service_counts,
            'action_counts': action_counts,
            'recent_count': recent_count
        })
    except Exception as e:
        logging.error(f"Error in get_logs_summary: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/logs/user/<user_id>', methods=['GET'])
@login_required
@admin_required
def get_user_logs(user_id):
    """Get logs for a specific user"""
    try:
        # Get query parameters
        limit = request.args.get('limit', 100, type=int)
        skip = request.args.get('skip', 0, type=int)

        # Get logs
        logs = APILog.objects(user=user_id).order_by('-timestamp').skip(skip).limit(limit)

        # Format response
        result = []
        for log in logs:
            try:
                result.append({
                    'id': str(log.id),
                    'action': log.action,
                    'service': log.service,
                    'details': log.details,
                    'timestamp': log.timestamp.isoformat()
                })
            except Exception:
                # Skip this log entry and continue with the next one
                pass

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in get_user_logs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
