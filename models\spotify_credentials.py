from mongoengine import Document, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from models.user import User
from datetime import datetime

class SpotifyCredentials(Document):
    user = ReferenceField(User, required=True, unique=True)
    access_token = StringField(required=True)
    refresh_token = StringField(required=True)
    token_type = StringField(default="Bearer")
    expires_at = DateTimeField()
    scope = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'spotify_credentials',
        'indexes': [
            'user'
        ]
    }
    
    def to_token_info(self):
        """Convert to token_info dict format used by spotipy"""
        return {
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'token_type': self.token_type,
            'expires_at': self.expires_at.timestamp() if self.expires_at else None,
            'scope': self.scope
        }
    
    @classmethod
    def from_token_info(cls, user, token_info):
        """Create or update from token_info dict"""
        expires_at = datetime.fromtimestamp(token_info['expires_at']) if 'expires_at' in token_info else None
        
        # Try to find existing credentials
        credentials = cls.objects(user=user).first()
        
        if credentials:
            # Update existing credentials
            credentials.access_token = token_info['access_token']
            credentials.refresh_token = token_info['refresh_token']
            credentials.token_type = token_info.get('token_type', 'Bearer')
            credentials.expires_at = expires_at
            credentials.scope = token_info.get('scope', '')
            credentials.updated_at = datetime.utcnow()
            credentials.save()
            return credentials
        else:
            # Create new credentials
            credentials = cls(
                user=user,
                access_token=token_info['access_token'],
                refresh_token=token_info['refresh_token'],
                token_type=token_info.get('token_type', 'Bearer'),
                expires_at=expires_at,
                scope=token_info.get('scope', '')
            )
            credentials.save()
            return credentials
