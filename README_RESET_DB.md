# Database Reset Tool

This tool allows you to reset the database to a fresh state.

## What it does

The `reset_db.py` script will:

1. Drop all collections in the database
2. Create admin users (<EMAIL> and kevin<PERSON><EMAIL>)
3. Set up all necessary indexes

## Admin Users

After running the script, the following admin users will be available:

- Username: admin
- Email: <EMAIL>
- Password: amogh@6969

- Username: kevko
- Email: kevin<PERSON><EMAIL>
- Password: kevko

## How to Use

1. Make sure your `.env` file contains the correct `MONGO_URI` for your database
2. Run the script:

```
python reset_db.py
```

3. Confirm the reset when prompted by typing 'y'

## Warning

**This script will delete all data in your database!** Only use it in development or testing environments.