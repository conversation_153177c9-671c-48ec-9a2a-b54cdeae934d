/**
 * CreditManager.js
 * Handles user credits and model costs functionality
 */
class CreditManager {
    constructor() {
        this.userCredits = null;
        this.modelCosts = null;
        this.isAdmin = false;
        this.isLoading = false;
    }

    /**
     * Initialize the credit manager
     */
    async init() {
        console.log('Initializing CreditManager...');

        // Check if user is admin
        await this.checkAdminStatus();

        // Load user credits for profile page
        this.loadUserCredits();

        // If admin, load model costs and user credits for admin panel
        if (this.isAdmin) {
            this.loadModelCosts();
            this.loadAllUserCredits();
        }

        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Check if the current user is an admin
     */
    async checkAdminStatus() {
        try {
            const response = await fetch('/api/admin/check');
            if (response.ok) {
                const data = await response.json();
                this.isAdmin = data.is_admin || false;
                console.log('Admin status:', this.isAdmin);
            } else {
                this.isAdmin = false;
            }
        } catch (error) {
            console.error('Error checking admin status:', error);
            this.isAdmin = false;
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Reset defaults button for model costs
        const resetDefaultsBtn = document.getElementById('resetModelCostsDefaults');
        if (resetDefaultsBtn) {
            resetDefaultsBtn.addEventListener('click', () => this.resetModelCostsDefaults());
        }

        // Initialize defaults button in admin panel
        const initializeDefaultCostsBtn = document.getElementById('initializeDefaultCostsBtn');
        if (initializeDefaultCostsBtn) {
            initializeDefaultCostsBtn.addEventListener('click', () => this.resetModelCostsDefaults());
        }

        // Refresh buttons
        const refreshUserCreditsBtn = document.getElementById('refreshUserCredits');
        if (refreshUserCreditsBtn) {
            refreshUserCreditsBtn.addEventListener('click', () => this.loadUserCredits());
        }

        const refreshUserCreditsBtnAdmin = document.getElementById('refreshUserCreditsBtn');
        if (refreshUserCreditsBtnAdmin) {
            refreshUserCreditsBtnAdmin.addEventListener('click', () => this.loadAllUserCredits());
        }

        const refreshModelCostsBtn = document.getElementById('refreshModelCosts');
        if (refreshModelCostsBtn) {
            refreshModelCostsBtn.addEventListener('click', () => this.loadModelCosts());
        }

        const refreshModelCostsBtnAdmin = document.getElementById('refreshModelCostsBtn');
        if (refreshModelCostsBtnAdmin) {
            refreshModelCostsBtnAdmin.addEventListener('click', () => this.loadModelCosts());
        }

        // Add credits button in admin panel
        const addCreditsBtn = document.getElementById('addCreditsBtn');
        if (addCreditsBtn) {
            addCreditsBtn.addEventListener('click', () => this.showAddCreditsModal());
        }

        // Add credits modal buttons
        const closeAddCreditsModal = document.getElementById('closeAddCreditsModal');
        if (closeAddCreditsModal) {
            closeAddCreditsModal.addEventListener('click', () => this.hideAddCreditsModal());
        }

        const cancelAddCredits = document.getElementById('cancelAddCredits');
        if (cancelAddCredits) {
            cancelAddCredits.addEventListener('click', () => this.hideAddCreditsModal());
        }

        const confirmAddCredits = document.getElementById('confirmAddCredits');
        if (confirmAddCredits) {
            confirmAddCredits.addEventListener('click', () => this.handleAddCredits());
        }

        // User search input in add credits modal
        const creditUserSearch = document.getElementById('creditUserSearch');
        if (creditUserSearch) {
            creditUserSearch.addEventListener('input', debounce((e) => this.searchUsers(e.target.value), 300));
        }

        // Edit cost button in admin panel
        const updateModelCostBtn = document.getElementById('updateModelCostBtn');
        if (updateModelCostBtn) {
            updateModelCostBtn.addEventListener('click', () => this.showUpdateModelCostModal());
        }

        // Edit cost buttons (will be added dynamically when costs are loaded)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-model-cost-btn') ||
                (e.target.parentElement && e.target.parentElement.classList.contains('edit-model-cost-btn'))) {
                const button = e.target.classList.contains('edit-model-cost-btn') ?
                    e.target : e.target.parentElement;
                const modelName = button.getAttribute('data-model-name');
                const currentCost = parseInt(button.getAttribute('data-cost'));
                this.showUpdateModelCostModal(modelName, currentCost);
            }
        });

        // Modal buttons
        const closeUpdateModelCostModal = document.getElementById('closeUpdateModelCostModal');
        if (closeUpdateModelCostModal) {
            closeUpdateModelCostModal.addEventListener('click', () => this.hideUpdateModelCostModal());
        }

        const cancelUpdateModelCost = document.getElementById('cancelUpdateModelCost');
        if (cancelUpdateModelCost) {
            cancelUpdateModelCost.addEventListener('click', () => this.hideUpdateModelCostModal());
        }

        const confirmUpdateModelCost = document.getElementById('confirmUpdateModelCost');
        if (confirmUpdateModelCost) {
            confirmUpdateModelCost.addEventListener('click', () => this.handleUpdateModelCost());
        }
    }

    /**
     * Load user credits from the API
     */
    async loadUserCredits() {
        try {
            // Show loading state
            this.showUserCreditsLoading();

            const response = await fetch('/api/user/credits');
            if (!response.ok) {
                throw new Error('Failed to fetch user credits');
            }

            const data = await response.json();
            this.userCredits = data;

            // Update UI with user credits
            this.updateUserCreditsUI();
        } catch (error) {
            console.error('Error loading user credits:', error);
            this.showUserCreditsError();
        }
    }

    /**
     * Load model costs from the API
     */
    async loadModelCosts() {
        if (!this.isAdmin) return;

        try {
            // Show loading state
            this.showModelCostsLoading();

            const response = await fetch('/api/admin/credits/model-costs');
            if (!response.ok) {
                throw new Error('Failed to fetch model costs');
            }

            const data = await response.json();
            this.modelCosts = data;

            // Update UI with model costs
            this.updateModelCostsUI();
        } catch (error) {
            console.error('Error loading model costs:', error);
            this.showModelCostsError();
        }
    }

    /**
     * Reset model costs to default values
     */
    async resetModelCostsDefaults() {
        if (!this.isAdmin) return;

        try {
            // Show loading state
            this.showModelCostsLoading();

            const response = await fetch('/api/admin/credits/initialize-defaults', {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error('Failed to reset model costs');
            }

            const data = await response.json();

            if (data.success) {
                this.modelCosts = data.model_costs;
                this.updateModelCostsUI();
            } else {
                throw new Error(data.message || 'Failed to reset model costs');
            }
        } catch (error) {
            console.error('Error resetting model costs:', error);
            this.showModelCostsError();
        }
    }

    /**
     * Update model cost
     * @param {string} modelName - Model name
     * @param {number} cost - New cost value
     */
    async updateModelCost(modelName, cost) {
        if (!this.isAdmin) return;

        try {
            const response = await fetch('/api/admin/credits/model-cost', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model_name: modelName,
                    credit_cost: cost
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update model cost');
            }

            const data = await response.json();

            if (data.success) {
                // Reload model costs
                this.loadModelCosts();
                return true;
            } else {
                throw new Error(data.message || 'Failed to update model cost');
            }
        } catch (error) {
            console.error('Error updating model cost:', error);
            return false;
        }
    }

    /**
     * Show loading state for user credits
     */
    showUserCreditsLoading() {
        const userCreditsContainer = document.getElementById('userCreditsContainer');
        if (userCreditsContainer) {
            userCreditsContainer.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400 text-sm">Loading credits...</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [userCreditsContainer]
                });
            }
        }
    }

    /**
     * Show error state for user credits
     */
    showUserCreditsError() {
        const userCreditsContainer = document.getElementById('userCreditsContainer');
        if (userCreditsContainer) {
            userCreditsContainer.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="h-4 w-4 text-red-400 mr-2"></i>
                        <span class="text-red-400 text-sm">Failed to load credits</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [userCreditsContainer]
                });
            }
        }
    }

    /**
     * Show loading state for model costs
     */
    showModelCostsLoading() {
        const modelCostsContainer = document.getElementById('modelCostsContainer');
        if (modelCostsContainer) {
            modelCostsContainer.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400 text-sm">Loading model costs...</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [modelCostsContainer]
                });
            }
        }
    }

    /**
     * Show error state for model costs
     */
    showModelCostsError() {
        const modelCostsContainer = document.getElementById('modelCostsContainer');
        if (modelCostsContainer) {
            modelCostsContainer.innerHTML = `
                <div class="flex justify-center items-center py-4">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="h-4 w-4 text-red-400 mr-2"></i>
                        <span class="text-red-400 text-sm">Failed to load model costs</span>
                    </div>
                </div>
            `;

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [modelCostsContainer]
                });
            }
        }
    }

    /**
     * Update UI with user credits
     */
    updateUserCreditsUI() {
        if (!this.userCredits) return;

        // Update user credits in profile page
        const userCreditsContainer = document.getElementById('userCreditsContainer');
        if (userCreditsContainer) {
            userCreditsContainer.innerHTML = this.renderUserCreditsHTML();

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [userCreditsContainer]
                });
            }
        }
    }

    /**
     * Update UI with model costs
     */
    updateModelCostsUI() {
        if (!this.modelCosts || !this.isAdmin) return;

        // Update model costs in admin panel
        const modelCostsContainer = document.getElementById('modelCostsContainer');
        if (modelCostsContainer) {
            modelCostsContainer.innerHTML = this.renderModelCostsHTML();

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [modelCostsContainer]
                });
            }
        }
    }

    /**
     * Render user credits HTML
     */
    renderUserCreditsHTML() {
        const { balance, total_earned, recent_transactions } = this.userCredits;

        let transactionsHTML = '';
        if (recent_transactions && recent_transactions.length > 0) {
            transactionsHTML = recent_transactions.map(transaction => `
                <div class="bg-slate-700/30 rounded p-2 mb-2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <i data-lucide="${transaction.type === 'addition' ? 'plus-circle' : 'minus-circle'}"
                               class="h-4 w-4 ${transaction.type === 'addition' ? 'text-green-400' : 'text-red-400'} mr-2"></i>
                            <span class="text-sm font-medium text-slate-200">
                                ${transaction.type === 'addition' ? '+' : '-'}${transaction.amount} credits
                            </span>
                        </div>
                        <span class="text-xs text-slate-400">
                            ${new Date(transaction.timestamp).toLocaleString()}
                        </span>
                    </div>
                    <div class="text-xs text-slate-300 mt-1">
                        ${transaction.description || (transaction.model_name ? `Model: ${transaction.model_name}` : 'No description')}
                    </div>
                </div>
            `).join('');
        } else {
            transactionsHTML = `
                <div class="text-center py-3 text-slate-400 text-sm">
                    No recent transactions
                </div>
            `;
        }

        return `
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <div>
                        <div class="text-2xl font-bold text-slate-100">${balance}</div>
                        <div class="text-sm text-slate-400">Available Credits</div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-medium text-slate-200">${total_earned}</div>
                        <div class="text-sm text-slate-400">Total Earned</div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium text-slate-300">Recent Transactions</h4>
                        <button id="refreshUserCredits" class="text-xs text-blue-400 hover:text-blue-300 flex items-center">
                            <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i> Refresh
                        </button>
                    </div>
                    <div class="transactions-list max-h-60 overflow-y-auto">
                        ${transactionsHTML}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render model costs HTML
     */
    renderModelCostsHTML() {
        if (!this.modelCosts || this.modelCosts.length === 0) {
            return `
                <div class="text-center py-4 text-slate-400">
                    No model costs found.
                    <button id="initializeDefaultCostsBtn" class="text-blue-400 hover:text-blue-300">
                        Initialize defaults
                    </button>
                </div>
            `;
        }

        // Sort model costs by name
        const sortedCosts = [...this.modelCosts].sort((a, b) =>
            a.model_name.localeCompare(b.model_name)
        );

        // Create a table for better organization
        let tableHTML = `
            <table class="w-full">
                <thead>
                    <tr class="bg-slate-700/50 border-b border-slate-600/50">
                        <th class="text-left p-3 text-sm font-medium text-slate-300">Model Name</th>
                        <th class="text-center p-3 text-sm font-medium text-slate-300">Credit Cost</th>
                        <th class="text-right p-3 text-sm font-medium text-slate-300">Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Add rows for each model
        sortedCosts.forEach((cost, index) => {
            tableHTML += `
                <tr class="${index % 2 === 0 ? 'bg-slate-700/20' : 'bg-slate-700/10'}">
                    <td class="p-3 text-sm text-slate-200">${cost.model_name}</td>
                    <td class="p-3 text-sm text-center font-bold text-slate-100">${cost.credit_cost} credits</td>
                    <td class="p-3 text-right">
                        <button class="edit-model-cost-btn text-blue-400 hover:text-blue-300 p-1 rounded hover:bg-slate-600/30"
                                data-model-name="${cost.model_name}"
                                data-cost="${cost.credit_cost}">
                            <i data-lucide="edit" class="h-4 w-4"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return `
            <div class="p-4">
                <div class="overflow-x-auto">
                    ${tableHTML}
                </div>
            </div>
        `;
    }

    /**
     * Show update model cost modal
     * @param {string} modelName - Model name (optional)
     * @param {number} currentCost - Current cost value (optional)
     */
    showUpdateModelCostModal(modelName = null, currentCost = null) {
        const modal = document.getElementById('updateModelCostModal');
        if (!modal) return;

        // Ensure the modal is a direct child of the body to avoid positioning issues
        if (modal.parentElement && modal.parentElement.id !== 'body') {
            document.body.appendChild(modal);
        }

        // Set proper positioning styles to ensure it's centered in the viewport
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.right = '0';
        modal.style.bottom = '0';
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
        modal.style.zIndex = '9999';

        // Show the modal
        modal.classList.remove('hidden');

        // Get the model select dropdown
        const modelSelect = document.getElementById('modelName');
        const modelCostInput = document.getElementById('modelCost');

        // Clear existing options
        modelSelect.innerHTML = '<option value="">Select a model</option>';

        // Populate with available models
        if (this.modelCosts && this.modelCosts.length > 0) {
            // Sort model costs by name
            const sortedCosts = [...this.modelCosts].sort((a, b) =>
                a.model_name.localeCompare(b.model_name)
            );

            sortedCosts.forEach(cost => {
                const option = document.createElement('option');
                option.value = cost.model_name;
                option.textContent = cost.model_name;
                option.setAttribute('data-cost', cost.credit_cost);
                modelSelect.appendChild(option);
            });

            // If a specific model was provided, select it
            if (modelName) {
                modelSelect.value = modelName;
                modelCostInput.value = currentCost;
            } else {
                // Otherwise, set the cost to the first model's cost
                modelCostInput.value = sortedCosts[0].credit_cost;
            }
        }

        // Add change event to update cost when model changes
        modelSelect.onchange = () => {
            const selectedOption = modelSelect.options[modelSelect.selectedIndex];
            if (selectedOption && selectedOption.getAttribute('data-cost')) {
                modelCostInput.value = selectedOption.getAttribute('data-cost');
            }
        };

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                elements: [modal]
            });
        }
    }

    /**
     * Hide update model cost modal
     */
    hideUpdateModelCostModal() {
        const modal = document.getElementById('updateModelCostModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.style.display = 'none';

            // Reset any inline styles that might interfere with future positioning
            modal.style.position = '';
            modal.style.top = '';
            modal.style.left = '';
            modal.style.right = '';
            modal.style.bottom = '';
            modal.style.alignItems = '';
            modal.style.justifyContent = '';
        }
    }

    /**
     * Handle update model cost form submission
     */
    async handleUpdateModelCost() {
        const modelSelect = document.getElementById('modelName');
        const modelCostInput = document.getElementById('modelCost');

        const modelName = modelSelect.value;
        const cost = parseInt(modelCostInput.value);

        if (!modelName) {
            alert('Please select a model');
            return;
        }

        if (isNaN(cost) || cost < 1) {
            alert('Please enter a valid cost (minimum 1)');
            return;
        }

        const success = await this.updateModelCost(modelName, cost);
        if (success) {
            this.hideUpdateModelCostModal();
        } else {
            alert('Failed to update model cost');
        }
    }

    /**
     * Show the add credits modal for admin
     */
    showAddCreditsModal() {
        if (!this.isAdmin) return;

        const modal = document.getElementById('addCreditsModal');
        if (modal) {
            // Reset form fields
            const userSearchInput = document.getElementById('creditUserSearch');
            if (userSearchInput) {
                userSearchInput.value = '';
            }

            const userSelect = document.getElementById('creditUser');
            if (userSelect) {
                userSelect.innerHTML = '<option value="">Select a user</option>';
            }

            const creditAmount = document.getElementById('creditAmount');
            if (creditAmount) {
                creditAmount.value = '10';
            }

            const creditReason = document.getElementById('creditReason');
            if (creditReason) {
                creditReason.value = 'Admin adjustment';
            }

            const searchResultsContainer = document.getElementById('userSearchResults');
            if (searchResultsContainer) {
                searchResultsContainer.innerHTML = '';
            }

            // Ensure the modal is a direct child of the body to avoid positioning issues
            if (modal.parentElement && modal.parentElement.id !== 'body') {
                document.body.appendChild(modal);
            }

            // Set proper positioning styles to ensure it's centered in the viewport
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.right = '0';
            modal.style.bottom = '0';
            modal.style.display = 'flex';
            modal.style.alignItems = 'center';
            modal.style.justifyContent = 'center';
            modal.style.zIndex = '9999';

            // Show the modal - remove hidden class
            modal.classList.remove('hidden');

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-5", "w-5"]
                    },
                    elements: [modal]
                });
            }
        }
    }

    /**
     * Hide the add credits modal
     */
    hideAddCreditsModal() {
        const modal = document.getElementById('addCreditsModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.style.display = 'none';

            // Reset any inline styles that might interfere with future positioning
            modal.style.position = '';
            modal.style.top = '';
            modal.style.left = '';
            modal.style.right = '';
            modal.style.bottom = '';
            modal.style.alignItems = '';
            modal.style.justifyContent = '';
        }
    }

    /**
     * Search for users by username or email
     * @param {string} query - The search query
     */
    async searchUsers(query) {
        if (!this.isAdmin || !query || query.length < 3) {
            return;
        }

        try {
            const searchResultsContainer = document.getElementById('userSearchResults');
            if (searchResultsContainer) {
                searchResultsContainer.innerHTML = `
                    <div class="flex justify-center items-center py-2">
                        <div class="flex items-center">
                            <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                            <span class="text-slate-400 text-sm">Searching...</span>
                        </div>
                    </div>
                `;

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [searchResultsContainer]
                    });
                }
            }

            const response = await fetch(`/api/admin/search-users?query=${encodeURIComponent(query)}`);
            if (!response.ok) {
                throw new Error('Failed to search users');
            }

            const users = await response.json();

            if (searchResultsContainer) {
                if (users.length === 0) {
                    searchResultsContainer.innerHTML = `
                        <div class="text-slate-400 text-sm py-2">No users found</div>
                    `;
                    return;
                }

                // Create user list
                searchResultsContainer.innerHTML = '';
                users.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'user-search-item flex items-center p-2 hover:bg-slate-700 cursor-pointer rounded';
                    userItem.setAttribute('data-user-id', user.id);
                    userItem.setAttribute('data-username', user.username);
                    userItem.setAttribute('data-email', user.email);

                    // Create user avatar
                    const avatar = document.createElement('div');
                    avatar.className = 'w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white mr-2';
                    avatar.textContent = user.username.charAt(0).toUpperCase();

                    // Create user info
                    const userInfo = document.createElement('div');
                    userInfo.className = 'flex flex-col';

                    const username = document.createElement('div');
                    username.className = 'text-sm font-medium text-slate-200';
                    username.textContent = user.username;

                    const email = document.createElement('div');
                    email.className = 'text-xs text-slate-400';
                    email.textContent = user.email;

                    userInfo.appendChild(username);
                    userInfo.appendChild(email);

                    userItem.appendChild(avatar);
                    userItem.appendChild(userInfo);

                    // Add click event
                    userItem.addEventListener('click', () => {
                        this.selectUser(user);
                    });

                    searchResultsContainer.appendChild(userItem);
                });
            }
        } catch (error) {
            console.error('Error searching users:', error);

            const searchResultsContainer = document.getElementById('userSearchResults');
            if (searchResultsContainer) {
                searchResultsContainer.innerHTML = `
                    <div class="text-red-400 text-sm py-2">Error searching users</div>
                `;
            }
        }
    }

    /**
     * Select a user from search results
     * @param {Object} user - The selected user
     */
    selectUser(user) {
        const userSearchInput = document.getElementById('creditUserSearch');
        if (userSearchInput) {
            userSearchInput.value = user.username;
        }

        const userSelect = document.getElementById('creditUser');
        if (userSelect) {
            userSelect.innerHTML = `<option value="${user.id}" selected>${user.username} (${user.email})</option>`;
        }

        const searchResultsContainer = document.getElementById('userSearchResults');
        if (searchResultsContainer) {
            searchResultsContainer.innerHTML = '';
        }
    }

    /**
     * Handle adding credits to a user
     */
    async handleAddCredits() {
        if (!this.isAdmin) return;

        const userSelect = document.getElementById('creditUser');
        const creditAmount = document.getElementById('creditAmount');
        const creditReason = document.getElementById('creditReason');

        if (!userSelect || !creditAmount || !creditReason) {
            return;
        }

        const userId = userSelect.value;
        const amount = parseInt(creditAmount.value);
        const reason = creditReason.value;

        // Validate inputs
        if (!userId) {
            alert('Please select a user');
            return;
        }

        if (isNaN(amount) || amount <= 0) {
            alert('Please enter a valid amount');
            return;
        }

        if (amount > 500) {
            alert('Maximum amount is 500 credits');
            return;
        }

        try {
            // Show loading state
            const confirmBtn = document.getElementById('confirmAddCredits');
            if (confirmBtn) {
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = `
                    <i data-lucide="loader" class="h-4 w-4 animate-spin mr-2"></i>
                    Processing...
                `;

                // Initialize Lucide icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [confirmBtn]
                    });
                }
            }

            // Call API to add credits
            const response = await fetch('/api/admin/credits/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: userId,
                    amount: amount,
                    reason: reason
                })
            });

            if (!response.ok) {
                throw new Error('Failed to add credits');
            }

            const data = await response.json();

            // Hide modal
            this.hideAddCreditsModal();

            // Show success notification
            alert(`Successfully added ${amount} credits to ${data.username}`);

            // Refresh user credits table
            this.loadAllUserCredits();
        } catch (error) {
            console.error('Error adding credits:', error);
            alert('Failed to add credits: ' + error.message);
        } finally {
            // Reset button state
            const confirmBtn = document.getElementById('confirmAddCredits');
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = 'Add Credits';
            }
        }
    }

    /**
     * Load all user credits for admin panel
     */
    async loadAllUserCredits() {
        if (!this.isAdmin) {
            console.log('User is not an admin, skipping loadAllUserCredits');
            return;
        }

        console.log('Loading all user credits for admin panel');
        try {
            // Show loading state
            const userCreditsTable = document.getElementById('userCreditsTable');
            if (userCreditsTable) {
                console.log('Showing loading state in userCreditsTable');
                userCreditsTable.innerHTML = `
                    <div class="flex justify-center items-center py-8">
                        <div id="usersLoadingIndicator" class="flex items-center">
                            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-cyan-500 mr-3"></div>
                            <span class="text-slate-400">Loading user credits...</span>
                        </div>
                    </div>
                `;
            } else {
                console.warn('userCreditsTable element not found');
            }

            console.log('Fetching user credits from API');
            const response = await fetch('/api/admin/credits/users');
            console.log('API response status:', response.status);
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Failed to fetch user credits:', response.status, errorText);
                throw new Error(`Failed to fetch user credits: ${response.status} ${errorText}`);
            }

            const userCredits = await response.json();
            console.log(`Received ${userCredits.length} user credit records`);

            // Update UI with user credits
            if (userCreditsTable) {
                if (userCredits.length === 0) {
                    userCreditsTable.innerHTML = `
                        <div class="text-slate-400 text-center py-4">No user credits found</div>
                    `;
                    return;
                }

                // Create table
                const table = document.createElement('table');
                table.className = 'min-w-full divide-y divide-slate-700';

                // Create table header
                const thead = document.createElement('thead');
                thead.className = 'bg-slate-800';
                thead.innerHTML = `
                    <tr>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Email</th>
                        <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">Balance</th>
                        <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">Total Earned</th>
                        <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">Last Updated</th>
                    </tr>
                `;

                // Create table body
                const tbody = document.createElement('tbody');
                tbody.className = 'bg-slate-800/50 divide-y divide-slate-700';

                // Sort users by balance (lowest first)
                userCredits.sort((a, b) => a.balance - b.balance);

                userCredits.forEach((credit, index) => {
                    const tr = document.createElement('tr');
                    tr.className = index % 2 === 0 ? 'bg-slate-800/30' : 'bg-slate-800/10';

                    // Format date
                    const lastUpdated = new Date(credit.last_updated);
                    const formattedDate = lastUpdated.toLocaleDateString() + ' ' + lastUpdated.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                    tr.innerHTML = `
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-300">${credit.username}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-slate-400">${credit.email}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right ${credit.balance === 0 ? 'text-red-400' : 'text-slate-300'}">${credit.balance}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-slate-300">${credit.total_earned}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-slate-400">${formattedDate}</td>
                    `;

                    tbody.appendChild(tr);
                });

                table.appendChild(thead);
                table.appendChild(tbody);

                userCreditsTable.innerHTML = '';
                userCreditsTable.appendChild(table);
            }
        } catch (error) {
            console.error('Error loading all user credits:', error);

            const userCreditsTable = document.getElementById('userCreditsTable');
            if (userCreditsTable) {
                userCreditsTable.innerHTML = `
                    <div class="text-red-400 text-center py-4">
                        <p>Failed to load user credits</p>
                        <p class="text-xs mt-2">${error.message}</p>
                        <button id="retryUserCreditsBtn" class="mt-3 px-3 py-1 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded text-xs">
                            Retry
                        </button>
                    </div>
                `;
                
                // Add event listener to retry button
                const retryBtn = document.getElementById('retryUserCreditsBtn');
                if (retryBtn) {
                    retryBtn.addEventListener('click', () => {
                        this.loadAllUserCredits();
                    });
                }
            }
        }
    }
}

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The time to wait in milliseconds
 * @returns {Function} - The debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// Initialize credit manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.creditManager = new CreditManager();
    window.creditManager.init();
});
