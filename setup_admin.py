import os
import sys
from dotenv import load_dotenv
from mongoengine import connect, disconnect
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from models.user import User

def setup_initial_admin():
    """Set primary admin users"""
    # Load environment variables
    load_dotenv()

    # Connect to MongoDB
    disconnect()
    connect(
        db='kevko_systems',
        host=os.getenv('MONGO_URI'),
        alias='default'
    )

    # List of primary admin emails
    primary_admin_emails = ['<EMAIL>', '<EMAIL>']

    for primary_admin_email in primary_admin_emails:
        # Find user by email
        user = User.objects(email=primary_admin_email).first()

        if not user:
            print(f"User with email {primary_admin_email} not found")
            continue

        # Set as admin
        if user.is_admin:
            print(f"User {primary_admin_email} is already an admin")
        else:
            user.is_admin = True
            user.save()
            print(f"User {primary_admin_email} is now an admin")

if __name__ == '__main__':
    setup_initial_admin()
    print("\nRun this script to set up the primary admin users (0hamogh@gmail.<NAME_EMAIL>)")
    print("Usage: python setup_admin.py")
