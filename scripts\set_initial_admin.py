import os
import sys
from dotenv import load_dotenv
from mongoengine import connect, disconnect
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.user import User

def set_initial_admin():
    """Set <EMAIL> as an admin"""
    # Load environment variables
    load_dotenv()
    
    # Connect to MongoDB
    disconnect()
    connect(
        db='kevko_systems',
        host=os.getenv('MONGO_URI'),
        alias='default'
    )
    
    # Find user by email
    primary_admin_email = '<EMAIL>'
    user = User.objects(email=primary_admin_email).first()
    
    if not user:
        print(f"User with email {primary_admin_email} not found")
        return
    
    # Set as admin
    if user.is_admin:
        print(f"User {primary_admin_email} is already an admin")
    else:
        user.is_admin = True
        user.save()
        print(f"User {primary_admin_email} is now an admin")

if __name__ == '__main__':
    set_initial_admin()
