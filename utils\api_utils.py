"""
API Utilities

This module provides utility functions for API endpoints to improve organization,
performance, and security.
"""
from flask import Blueprint, jsonify, request
from functools import wraps
import logging
import time
import traceback

def create_api_blueprint(name, import_name, url_prefix=None):
    """
    Create a new API blueprint with standardized settings.
    
    Args:
        name (str): The name of the blueprint
        import_name (str): The import name (usually __name__)
        url_prefix (str, optional): URL prefix for the blueprint
    
    Returns:
        Blueprint: A Flask blueprint configured for API use
    """
    if url_prefix is None:
        url_prefix = f'/api/{name}'
    
    return Blueprint(f'api_{name}', import_name, url_prefix=url_prefix)

def api_response(func):
    """
    Decorator for API endpoints to standardize response format and error handling.
    
    Args:
        func: The view function to decorate
    
    Returns:
        function: The decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            # Call the original function
            result = func(*args, **kwargs)
            
            # If the result is already a response, return it
            if isinstance(result, tuple) and len(result) >= 2:
                return result
            
            # Otherwise, wrap it in a standard response
            return jsonify({
                'success': True,
                'data': result,
                'execution_time': round((time.time() - start_time) * 1000, 2)  # ms
            })
        except Exception as e:
            # Log the error
            logging.error(f"API Error in {func.__name__}: {str(e)}")
            logging.error(traceback.format_exc())
            
            # Return a standardized error response
            return jsonify({
                'success': False,
                'error': str(e),
                'error_type': e.__class__.__name__,
                'execution_time': round((time.time() - start_time) * 1000, 2)  # ms
            }), 500
    
    return wrapper

def validate_json_payload(*required_fields):
    """
    Decorator to validate that required fields are present in the JSON payload.
    
    Args:
        *required_fields: Variable list of required field names
    
    Returns:
        function: The decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check if the request has JSON data
            if not request.is_json:
                return jsonify({
                    'success': False,
                    'error': 'Missing JSON payload',
                    'message': 'This endpoint requires a JSON payload'
                }), 400
            
            # Get the JSON data
            data = request.json
            
            # Check for required fields
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': 'Missing required fields',
                    'message': f'The following fields are required: {", ".join(missing_fields)}'
                }), 400
            
            # All required fields are present, proceed
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def cache_control(max_age=0, private=True, no_store=False):
    """
    Decorator to set Cache-Control headers for API responses.
    
    Args:
        max_age (int): Maximum age in seconds (default: 0)
        private (bool): Whether the response is private (default: True)
        no_store (bool): Whether to prevent storing the response (default: False)
    
    Returns:
        function: The decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            response = func(*args, **kwargs)
            
            # Build the Cache-Control header
            cache_control_parts = []
            if private:
                cache_control_parts.append('private')
            else:
                cache_control_parts.append('public')
            
            cache_control_parts.append(f'max-age={max_age}')
            
            if no_store:
                cache_control_parts.append('no-store')
            
            # If the response is a tuple (response, status_code), get the response object
            if isinstance(response, tuple):
                response_obj = response[0]
                status_code = response[1]
                headers = response[2] if len(response) > 2 else {}
            else:
                response_obj = response
                status_code = 200
                headers = {}
            
            # Set the Cache-Control header
            headers['Cache-Control'] = ', '.join(cache_control_parts)
            
            # Return the response with the updated headers
            return response_obj, status_code, headers
        
        return wrapper
    
    return decorator