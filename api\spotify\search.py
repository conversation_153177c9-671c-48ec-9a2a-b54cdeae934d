from flask import request, jsonify
from . import spotify_api
from .services.spotify_client import get_spotify_client
from .decorators import require_auth
from utils.api_logger import log_api_request
import logging

@spotify_api.route('/search', methods=['GET'])
@require_auth
def search():
    """Search for tracks, albums, artists, or playlists"""
    try:
        sp = get_spotify_client()
        query = request.args.get('q', '')
        limit = request.args.get('limit', 5, type=int)
        search_type = request.args.get('type', 'track')

        if not query:
            return jsonify({'tracks': {'items': []}})

        # Validate search type
        valid_types = ['track', 'album', 'artist', 'playlist']
        if search_type not in valid_types:
            search_type = 'track'

        # Search for items
        results = sp.search(q=query, limit=limit, type=search_type)

        # Add error handling for malformed response
        if not isinstance(results, dict):
            return jsonify({'error': 'Invalid response from Spotify API'}), 500

        return jsonify(results)
    except Exception as e:
        logging.error(f"Search error: {str(e)}")
        return jsonify({'error': str(e)}), 500
        
@spotify_api.route('/search-and-play', methods=['POST'])
@require_auth
@log_api_request('search_play', 'spotify')
def search_and_play():
    """Search for a track and play it immediately"""
    try:
        sp = get_spotify_client()
        data = request.json or {}
        query = data.get('query', '')
        
        if not query:
            return jsonify({'error': 'Search query is required'}), 400
            
        # Search for tracks
        results = sp.search(q=query, limit=1, type='track')
        
        # Check if we found any tracks
        if not results or 'tracks' not in results or not results['tracks']['items']:
            return jsonify({'error': 'No tracks found matching your search'}), 404
            
        # Get the first track
        track = results['tracks']['items'][0]
        track_uri = track['uri']
        
        # Get current playback state first
        playback = sp.current_playback()
        if not playback or not playback.get('device'):
            devices = sp.devices()
            if not devices['devices']:
                return jsonify({'error': 'No available devices'}), 400
            # Use first available device
            sp.transfer_playback(device_id=devices['devices'][0]['id'], force_play=True)
            import time
            time.sleep(1)  # Wait for transfer
        
        # Start playback with the track
        sp.start_playback(uris=[track_uri])
        
        # Wait briefly for the change to take effect
        time.sleep(0.5)
        
        # Get updated playback state
        updated_playback = sp.current_playback()
        
        return jsonify({
            'success': True,
            'track': track,
            'is_playing': updated_playback.get('is_playing', False) if updated_playback else False,
            'progress_ms': updated_playback.get('progress_ms', 0) if updated_playback else 0
        })
    except Exception as e:
        logging.error(f"Search and play error: {str(e)}")
        return jsonify({'error': str(e)}), 500
