/**
 * UserManager.js
 * Manages users and their data
 */

class UserManager {
    constructor() {
        this.users = new Map();
        this.currentUser = null;
        this.friends = new Map();
        this.userActivity = new Map();

        // Load users from localStorage
        this.loadUsers();

        // Create default users if none exist
        if (this.users.size === 0) {
            this.createDefaultUsers();
        }
    }

    /**
     * Load users from localStorage
     */
    loadUsers() {
        const storedUsers = localStorage.getItem('users');
        if (storedUsers) {
            try {
                const users = JSON.parse(storedUsers);
                users.forEach(user => {
                    this.users.set(user.id, user);
                });
                console.log(`Loaded ${this.users.size} users from localStorage`);
            } catch (e) {
                console.warn('Failed to parse stored users');
            }
        }

        // Load friends
        const storedFriends = localStorage.getItem('userFriends');
        if (storedFriends) {
            try {
                const friends = JSON.parse(storedFriends);
                Object.entries(friends).forEach(([userId, friendIds]) => {
                    this.friends.set(parseInt(userId), friendIds);
                });
                console.log(`Loaded friend relationships for ${this.friends.size} users`);
            } catch (e) {
                console.warn('Failed to parse stored friends');
            }
        }

        // Load user activity
        const storedActivity = localStorage.getItem('userActivity');
        if (storedActivity) {
            try {
                const activity = JSON.parse(storedActivity);
                Object.entries(activity).forEach(([userId, logs]) => {
                    this.userActivity.set(parseInt(userId), logs);
                });
                console.log(`Loaded activity logs for ${this.userActivity.size} users`);
            } catch (e) {
                console.warn('Failed to parse stored activity');
            }
        }
    }

    /**
     * Save users to localStorage
     */
    saveUsers() {
        const users = Array.from(this.users.values());
        localStorage.setItem('users', JSON.stringify(users));

        // Save friends
        const friends = {};
        this.friends.forEach((friendIds, userId) => {
            friends[userId] = friendIds;
        });
        localStorage.setItem('userFriends', JSON.stringify(friends));

        // Save user activity
        const activity = {};
        this.userActivity.forEach((logs, userId) => {
            activity[userId] = logs;
        });
        localStorage.setItem('userActivity', JSON.stringify(activity));
    }

    /**
     * Create default users
     */
    createDefaultUsers() {
        // Don't create any default users
        console.log('No default users created');

        // Save empty users to localStorage
        this.saveUsers();
    }

    /**
     * Get all users
     * @returns {Array} Array of users
     */
    getAllUsers() {
        return Array.from(this.users.values());
    }

    /**
     * Get user by ID
     * @param {number} id User ID
     * @returns {Object|null} User object or null if not found
     */
    getUserById(id) {
        return this.users.get(id) || null;
    }

    /**
     * Get user by username
     * @param {string} username Username
     * @returns {Object|null} User object or null if not found
     */
    getUserByUsername(username) {
        for (const user of this.users.values()) {
            if (user.username === username) {
                return user;
            }
        }
        return null;
    }

    /**
     * Get user's friends
     * @param {number} userId User ID
     * @returns {Array} Array of friend objects
     */
    getUserFriends(userId) {
        const friendIds = this.friends.get(userId) || [];
        return friendIds.map(id => this.getUserById(id)).filter(Boolean);
    }

    /**
     * Get user's activity
     * @param {number} userId User ID
     * @param {number} limit Maximum number of activities to return
     * @returns {Array} Array of activity objects
     */
    getUserActivity(userId, limit = 10) {
        const activities = this.userActivity.get(userId) || [];
        return activities.sort((a, b) => b.timestamp - a.timestamp).slice(0, limit);
    }

    /**
     * Add activity for a user
     * @param {number} userId User ID
     * @param {string} service Service name
     * @param {string} action Action performed
     */
    addUserActivity(userId, service, action) {
        if (!this.userActivity.has(userId)) {
            this.userActivity.set(userId, []);
        }

        const activities = this.userActivity.get(userId);
        activities.push({
            service,
            timestamp: Date.now(),
            action
        });

        // Limit to 100 activities per user
        if (activities.length > 100) {
            activities.sort((a, b) => b.timestamp - a.timestamp);
            activities.length = 100;
        }

        // Update user's last active time
        const user = this.getUserById(userId);
        if (user) {
            user.lastActive = 'Now';
            this.users.set(userId, user);
        }

        // Save to localStorage
        this.saveUsers();
    }

    /**
     * Get active services for a user
     * @param {number} userId User ID
     * @returns {number} Number of active services
     */
    getUserActiveServices(userId) {
        // In a real implementation, this would check which services the user has active
        // For now, we'll return a random number between 2 and 5
        return Math.floor(Math.random() * 4) + 2;
    }

    /**
     * Format timestamp to relative time
     * @param {number} timestamp Timestamp in milliseconds
     * @returns {string} Relative time string
     */
    formatRelativeTime(timestamp) {
        // Use the user's local timezone for time calculations
        const now = Date.now();
        const diff = now - timestamp;

        // Less than a minute
        if (diff < 60 * 1000) {
            return 'Just now';
        }

        // Less than an hour
        if (diff < 60 * 60 * 1000) {
            const minutes = Math.floor(diff / (60 * 1000));
            return `${minutes} min ago`;
        }

        // Less than a day
        if (diff < 24 * 60 * 60 * 1000) {
            const hours = Math.floor(diff / (60 * 60 * 1000));
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        }

        // Less than a week
        if (diff < 7 * 24 * 60 * 60 * 1000) {
            const days = Math.floor(diff / (24 * 60 * 60 * 1000));
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }

        // Format as date
        const date = new Date(timestamp);
        return date.toLocaleDateString();
    }
}

// Create global user manager instance
window.userManager = new UserManager();
