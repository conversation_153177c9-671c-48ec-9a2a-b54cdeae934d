"""
Scheduler <PERSON><PERSON><PERSON>
Handles scheduled tasks for the application
"""
import logging
import threading
import time
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Scheduler:
    """
    Simple scheduler for running periodic tasks
    """
    def __init__(self):
        self.tasks = {}
        self.running = False
        self.thread = None
        
    def add_task(self, name, func, interval_minutes):
        """
        Add a task to the scheduler
        
        Args:
            name (str): Task name
            func (callable): Function to call
            interval_minutes (int): Interval in minutes
        """
        self.tasks[name] = {
            'func': func,
            'interval': interval_minutes * 60,  # Convert to seconds
            'last_run': None
        }
        logger.info(f"Added task '{name}' with interval {interval_minutes} minutes")
        
    def remove_task(self, name):
        """
        Remove a task from the scheduler
        
        Args:
            name (str): Task name
        """
        if name in self.tasks:
            del self.tasks[name]
            logger.info(f"Removed task '{name}'")
        
    def start(self):
        """
        Start the scheduler
        """
        if self.running:
            logger.warning("Scheduler is already running")
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        logger.info("Scheduler started")
        
    def stop(self):
        """
        Stop the scheduler
        """
        if not self.running:
            logger.warning("Scheduler is not running")
            return
            
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
            self.thread = None
        logger.info("Scheduler stopped")
        
    def _run(self):
        """
        Run the scheduler loop
        """
        while self.running:
            now = time.time()
            
            for name, task in self.tasks.items():
                # Check if task should run
                if task['last_run'] is None or (now - task['last_run']) >= task['interval']:
                    try:
                        logger.info(f"Running task '{name}'")
                        task['func']()
                        task['last_run'] = now
                    except Exception as e:
                        logger.error(f"Error running task '{name}': {str(e)}")
            
            # Sleep for a short time to avoid high CPU usage
            time.sleep(10)

# Create a global scheduler instance
scheduler = Scheduler()
