<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KevkoSystems</title>

    <!-- Favicon links -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/kevkoai-landing.css') }}">

    <!-- Flask-Login current_user is automatically available in templates -->
</head>
<body>
    <!-- Custom Cursor -->
    <div class="cursor-outer"></div>
    <div class="cursor-inner"></div>

    <!-- Typographic Elements -->
    <div class="geometric-shapes">
        <div class="shape letter-a">A</div>
        <div class="shape letter-i">I</div>
        <div class="shape letter-k">K</div>
        <div class="shape grid-lines"></div>
        <div class="shape dot-grid"></div>
        <div class="shape line"></div>
        <div class="shape line-2"></div>
        <div class="shape circle"></div>
    </div>

    <!-- Header -->
    <header>
        <div class="logo">KevkoSystems</div>
        <nav>
            <ul>
                <li><a href="#features">Features</a></li>
                <li><a href="#about">About</a></li>
                {% if current_user.is_authenticated %}
                <li><a href="{{ url_for('dashboard') }}" class="login-btn"><span>Dashboard</span></a></li>
                {% else %}
                <li><a href="{{ url_for('auth.login') }}" class="login-btn"><span>Login</span></a></li>
                {% endif %}
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-badge">Next-Gen AI Platform</div>
            <h1>Experience the <span class="highlight">future</span> of AI conversation</h1>
            <p>KevkoAI combines cutting-edge language models with a beautiful interface for seamless human-AI interaction</p>
            <div class="hero-cta">
                {% if current_user.is_authenticated %}
                <a href="{{ url_for('dashboard') }}" class="cta-button primary-cta"><span>Go to Dashboard</span></a>
                {% else %}
                <a href="{{ url_for('auth.login') }}" class="cta-button primary-cta"><span>Get Started Now</span></a>
                <a href="#features" class="cta-button secondary-cta"><span>Explore Features</span></a>
                {% endif %}
            </div>
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">10+</span>
                    <span class="stat-label">AI Models</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.9%</span>
                    <span class="stat-label">Uptime</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Support</span>
                </div>
            </div>
        </div>
        <div class="hero-visual">
            <div class="ai-dashboard">
                <div class="dashboard-header">
                    <div class="dashboard-title">KevkoAI Assistant</div>
                    <div class="dashboard-controls">
                        <span class="control-dot"></span>
                        <span class="control-dot"></span>
                        <span class="control-dot"></span>
                    </div>
                </div>
                <div class="dashboard-content">
                    <div class="model-selector">
                        <div class="model-label">AI Model:</div>
                        <div class="model-dropdown">
                            <span class="selected-model">GPT-4o</span>
                            <span class="dropdown-arrow">▼</span>
                        </div>
                    </div>
                    <div class="ai-conversation">
                        <div class="ai-message">
                            <div class="ai-avatar"></div>
                            <div class="message-content">
                                <p>I've analyzed your data and found three key insights:</p>
                                <ul>
                                    <li>User engagement increased by 27% this month</li>
                                    <li>Most active time period: 2-5pm weekdays</li>
                                    <li>Top feature request: collaborative editing</li>
                                </ul>
                            </div>
                        </div>
                        <div class="user-message">
                            <div class="message-content">
                                <p>Can you create a visualization of the engagement trend?</p>
                            </div>
                            <div class="user-avatar"></div>
                        </div>
                        <div class="ai-message">
                            <div class="ai-avatar"></div>
                            <div class="message-content">
                                <p>Here's the visualization of user engagement over time:</p>
                                <div class="chart-placeholder">
                                    <div class="chart-bar" style="height: 30%"></div>
                                    <div class="chart-bar" style="height: 45%"></div>
                                    <div class="chart-bar" style="height: 60%"></div>
                                    <div class="chart-bar" style="height: 40%"></div>
                                    <div class="chart-bar" style="height: 75%"></div>
                                    <div class="chart-bar" style="height: 90%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ai-tools">
                        <div class="tool-button">Data Analysis</div>
                        <div class="tool-button">Code Generation</div>
                        <div class="tool-button">Content Creation</div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- AI Response Bubbles -->
    <section class="ai-responses">
        <div class="response-bubble" data-delay="0">
            <div class="bubble-content">
                <p>I can help you draft an email to your team about the upcoming project deadline.</p>
            </div>
        </div>
        <div class="response-bubble" data-delay="2000">
            <div class="bubble-content">
                <p>Let me analyze that data and create a visualization to help identify trends.</p>
            </div>
        </div>
        <div class="response-bubble" data-delay="4000">
            <div class="bubble-content">
                <p>I've found three research papers on that topic. Would you like me to summarize them?</p>
            </div>
        </div>
        <div class="response-bubble" data-delay="6000">
            <div class="bubble-content">
                <p>Here's a step-by-step guide to solve that programming challenge.</p>
            </div>
        </div>
        <div class="response-bubble" data-delay="8000">
            <div class="bubble-content">
                <p>I can translate that text into 20+ languages while preserving the original tone.</p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <h2>Powerful AI Features</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>
                </div>
                <h3>Multi-Model Support</h3>
                <p>Access GPT-4, Gemini, Qwen and more advanced AI models in one seamless interface. Choose the perfect model for your specific needs.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                </div>
                <h3>Real-time Chat</h3>
                <p>Engage in fluid conversations with instant responses and advanced context awareness. Experience natural, human-like interactions with cutting-edge AI.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line></svg>
                </div>
                <h3>Live Collaboration</h3>
                <p>Work together with friends or colleagues in shared AI-powered rooms. Solve problems, brainstorm ideas, and create content as a team in real-time.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                </div>
                <h3>End-to-End Encryption</h3>
                <p>Your conversations are protected with state-of-the-art encryption. Enjoy complete privacy and security while chatting with friends or AI assistants.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                </div>
                <h3>Rich Media Support</h3>
                <p>Share images, documents, and more with both AI models and friends. Get AI analysis of visual content and collaborate on multimedia projects.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                </div>
                <h3>Enterprise Security</h3>
                <p>Built with security in mind from the ground up. KevkoAI provides enterprise-grade protection for your data and conversations at all times.</p>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <h2>What Our Users Say</h2>
        <div class="testimonial-slider">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                            <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                        </svg>
                    </div>
                    <p>KevkoAI has completely transformed how our team collaborates. The multi-model support and real-time features have boosted our productivity by at least 40%.</p>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background-color: var(--accent-color-1);">JD</div>
                        <div class="author-info">
                            <h4>Jane Doe</h4>
                            <p>Product Manager, TechCorp</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                            <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                        </svg>
                    </div>
                    <p>As a developer, I appreciate the attention to security and privacy. The end-to-end encryption gives me confidence when sharing sensitive information with my team.</p>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background-color: var(--accent-color-2);">MS</div>
                        <div class="author-info">
                            <h4>Michael Smith</h4>
                            <p>Senior Developer, CodeWorks</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="quote-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                            <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                        </svg>
                    </div>
                    <p>The rich media support has been a game-changer for our creative team. Being able to share and analyze visual content with AI assistance has streamlined our workflow.</p>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background-color: var(--accent-color-3);">AJ</div>
                        <div class="author-info">
                            <h4>Alex Johnson</h4>
                            <p>Creative Director, DesignHub</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="testimonial-controls">
            <button class="control-prev">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
            </button>
            <div class="control-indicators">
                <span class="indicator active"></span>
                <span class="indicator"></span>
                <span class="indicator"></span>
            </div>
            <button class="control-next">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
            </button>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="about-content">
            <h2>About KevkoSystems</h2>
            <p class="about-description">
                KevkoSystem is a collection of next-generation AI platform designed to make advanced artificial intelligence accessible,
                powerful, and intuitive. Our mission is to create seamless human-AI collaboration tools that enhance
                creativity, productivity, and knowledge sharing.
            </p>
            <div class="about-grid">
                <div class="about-item">
                    <h3>Our Vision</h3>
                    <p>We believe in a future where AI becomes a natural extension of human capabilities,
                    empowering people to achieve more than ever before through intuitive, powerful tools.</p>
                </div>
                <div class="about-item">
                    <h3>Technology</h3>
                    <p>KevkoAI integrates multiple state-of-the-art language models with our proprietary
                    interface to deliver the most responsive, accurate, and helpful AI experience available today.</p>
                </div>
                <div class="about-item">
                    <h3>Privacy First</h3>
                    <p>Your data belongs to you. We implement end-to-end encryption and strict privacy controls
                    to ensure your conversations and information remain secure and private.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-background">
            <div class="cta-glow"></div>
        </div>
        <div class="cta-content">
            <h2>Ready to transform your AI experience?</h2>
            <p>Join thousands of users who are already leveraging the power of KevkoAI</p>
            <div class="cta-actions">
                {% if current_user.is_authenticated %}
                <a href="{{ url_for('dashboard') }}" class="cta-button primary-cta"><span>Go to Dashboard</span></a>
                {% else %}
                <a href="{{ url_for('auth.login') }}" class="cta-button primary-cta"><span>Get Started Now</span></a>
                {% endif %}
            </div>
            <div class="cta-features">
                <div class="cta-feature">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>No credit card required</span>
                </div>
                <div class="cta-feature">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Free tier available</span>
                </div>
                <div class="cta-feature">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Instant setup</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-brand">
                <div class="footer-logo">KevkoSystems</div>
                <p class="footer-tagline">Next-generation AI for everyone</p>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                        </svg>
                    </a>
                    <a href="#" class="social-link">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                        </svg>
                    </a>
                    <a href="#" class="social-link">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                            <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                        </svg>
                    </a>
                    <a href="#" class="social-link">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                            <rect x="2" y="9" width="4" height="12"></rect>
                            <circle cx="4" cy="4" r="2"></circle>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="footer-links">
                <div class="footer-links-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#">Pricing</a></li>
                        <li><a href="#">API</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Tutorials</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Support</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Partners</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                        <li><a href="#">GDPR</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Kevko Systems. All rights reserved.</p>
            <div class="footer-language-selector">
                <select>
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="fr">Français</option>
                    <option value="de">Deutsch</option>
                </select>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/kevkoai-landing.js') }}"></script>
</body>
</html>
