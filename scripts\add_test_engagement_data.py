"""
Script to add test service engagement data to the database
"""
import sys
import os
import random
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import the models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize MongoDB connection
from mongoengine import connect
connect('kevko_db')

from models.user import User
from models.service_engagement import ServiceEngagement

def add_test_engagement_data():
    """Add test service engagement data to the database"""
    print("Adding test service engagement data...")

    # Get all users
    users = list(User.objects)
    if not users:
        print("No users found in the database. Please create some users first.")
        return

    # Services to create engagements for
    services = ['chat', 'live', 'spotify', 'friends', 'discord']

    # Create 50 random engagements over the past 30 days
    for _ in range(50):
        # Random user
        user = random.choice(users)

        # Random service
        service = random.choice(services)

        # Random start time in the past 30 days
        days_ago = random.randint(0, 30)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        start_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)

        # Random duration between 1 and 60 minutes
        duration_minutes = random.randint(1, 60)

        # End time
        end_time = start_time + timedelta(minutes=duration_minutes)

        # Create the engagement
        engagement = ServiceEngagement(
            user=user.id,
            service=service,
            start_time=start_time,
            end_time=end_time,
            duration_minutes=duration_minutes,
            is_completed=duration_minutes >= 5  # Only mark as completed if duration is at least 5 minutes
        )

        # Save the engagement
        engagement.save()

        print(f"Created engagement for user {user.username}, service {service}, duration {duration_minutes} minutes")

    print("Done adding test service engagement data.")

if __name__ == "__main__":
    add_test_engagement_data()
