# API Best Practices

This document outlines best practices for creating and maintaining API endpoints in the KevkoWebsite application.

## Table of Contents

1. [API Organization](#api-organization)
2. [Error Handling](#error-handling)
3. [Performance Optimization](#performance-optimization)
4. [Security](#security)
5. [Documentation](#documentation)

## API Organization

### Directory Structure

APIs should be organized by feature or domain:

```
api/
  ├── feature1/
  │   ├── __init__.py
  │   ├── routes.py
  │   └── utils.py
  ├── feature2/
  │   ├── __init__.py
  │   ├── routes.py
  │   └── utils.py
  └── ...
```

### Blueprint Creation

Use the `create_api_blueprint` utility to create new API blueprints:

```python
from utils.api_utils import create_api_blueprint

# Create a blueprint for this API module
my_api = create_api_blueprint('my_feature', __name__)
```

### Route Definition

Define routes with clear HTTP methods and use decorators for common functionality:

```python
@my_api.route('/resource', methods=['GET'])
@login_required
@check_service_access('my_feature')
@api_response
def get_resource():
    # Implementation
    return {'data': 'value'}
```

## Error Handling

### Standard Error Responses

Use the `api_response` decorator to standardize error handling:

```python
@my_api.route('/resource', methods=['POST'])
@login_required
@api_response
def create_resource():
    # This will automatically handle exceptions and return standardized responses
    # If an exception occurs, it will return a JSON response with error details
    # ...
```

### Input Validation

Use the `validate_json_payload` decorator to validate request data:

```python
@my_api.route('/resource', methods=['POST'])
@validate_json_payload('name', 'value')
@api_response
def create_resource():
    # This will ensure that 'name' and 'value' are present in the JSON payload
    data = request.json
    # ...
```

### Method Not Allowed

All API endpoints are automatically protected against unsupported HTTP methods. The system will return a 405 Method Not Allowed response for any request using an unsupported method.

## Performance Optimization

### Caching

Use the `cache_control` decorator to set appropriate caching headers:

```python
@my_api.route('/resource', methods=['GET'])
@cache_control(max_age=60, private=True)
@api_response
def get_resource():
    # This response will be cached for 60 seconds
    # ...
```

### Database Optimization

- Use indexes for frequently queried fields
- Limit the number of returned fields
- Use pagination for large result sets

```python
@my_api.route('/resources', methods=['GET'])
@api_response
def get_resources():
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))
    
    # Calculate skip value for pagination
    skip = (page - 1) * per_page
    
    # Query with pagination
    resources = Resource.objects().skip(skip).limit(per_page)
    
    return {
        'resources': resources,
        'page': page,
        'per_page': per_page,
        'total': Resource.objects().count()
    }
```

## Security

### Authentication

All API endpoints should require authentication unless explicitly public:

```python
@my_api.route('/public-resource', methods=['GET'])
@api_response
def get_public_resource():
    # This endpoint is public
    # ...

@my_api.route('/private-resource', methods=['GET'])
@login_required
@api_response
def get_private_resource():
    # This endpoint requires authentication
    # ...
```

### Authorization

Use the `check_service_access` decorator to enforce service-level access control:

```python
@my_api.route('/resource', methods=['POST'])
@login_required
@check_service_access('my_feature')
@api_response
def create_resource():
    # This endpoint requires access to the 'my_feature' service
    # ...
```

### Request Logging

Use the `log_api_request` decorator to log important API requests:

```python
@my_api.route('/resource', methods=['DELETE'])
@login_required
@log_api_request('resource_delete', 'my_feature')
@api_response
def delete_resource():
    # This request will be logged
    # ...
```

## Documentation

### Docstrings

All API endpoints should have clear docstrings that describe:

1. What the endpoint does
2. Required parameters
3. Return values
4. Possible errors

```python
@my_api.route('/resource/<resource_id>', methods=['GET'])
@login_required
@api_response
def get_resource(resource_id):
    """
    Get a specific resource by ID.
    
    Args:
        resource_id (str): The ID of the resource to retrieve
    
    Returns:
        dict: A dictionary containing the resource data
    
    Raises:
        404: If the resource is not found
        403: If the user doesn't have permission to access the resource
    """
    # Implementation
    # ...
```

### Example Template

See the `api/template` directory for a complete example of a well-structured API module.