/**
 * Debug script to check localStorage for service configurations
 */
console.log('Checking localStorage for service configurations...');
const storedConfigs = localStorage.getItem('serviceConfigurations');
console.log('Stored service configurations in localStorage:', storedConfigs);

// Function to clear all service configurations
function clearAllServiceConfigurations() {
    localStorage.removeItem('serviceConfigurations');
    console.log('Cleared all service configurations from localStorage');
    alert('All service configurations have been cleared. Please refresh the page.');
}

// Add a button to clear all service configurations
window.addEventListener('DOMContentLoaded', () => {
    const dashboardView = document.getElementById('dashboardView');
    if (dashboardView) {
        const clearButton = document.createElement('button');
        clearButton.className = 'fixed bottom-4 right-4 bg-red-600 hover:bg-red-500 text-white rounded-md px-4 py-2 text-sm';
        clearButton.textContent = 'Clear Service Configs';
        clearButton.addEventListener('click', clearAllServiceConfigurations);
        document.body.appendChild(clearButton);
    }
});

if (storedConfigs) {
    try {
        const configs = JSON.parse(storedConfigs);
        console.log('Parsed service configurations:', configs);

        // Look for any suspicious services
        const suspiciousServices = configs.filter(service =>
            service.name === 'asd' ||
            service.description === 'asd' ||
            service.name === 'sda' ||
            service.description === 'sda' ||
            service.id === 'asd' ||
            service.id === 'sda'
        );

        console.log('Suspicious services found:', suspiciousServices);

        // Remove suspicious services
        const cleanedConfigs = configs.filter(service =>
            service.name !== 'asd' &&
            service.description !== 'asd' &&
            service.name !== 'sda' &&
            service.description !== 'sda' &&
            service.id !== 'asd' &&
            service.id !== 'sda'
        );

        console.log('Cleaned service configurations:', cleanedConfigs);

        // Save cleaned configurations back to localStorage
        localStorage.setItem('serviceConfigurations', JSON.stringify(cleanedConfigs));
        console.log('Saved cleaned service configurations to localStorage');

        // If suspicious services were found, show an alert
        if (suspiciousServices.length > 0) {
            console.log('Removed suspicious services:', suspiciousServices);
            alert(`Removed ${suspiciousServices.length} suspicious service(s). Please refresh the page.`);
        }
    } catch (e) {
        console.warn('Failed to parse stored service configurations:', e);
    }
}
