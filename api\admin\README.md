# Admin API

This API provides endpoints for administrative functions such as user management, feature restrictions, and system monitoring.

## Endpoints

### User Management

#### GET /api/admin/check
- **Description**: Check if current user is an admin
- **Authentication**: Required
- **Response**: Admin status and email

#### POST /api/admin/request-verification
- **Description**: Request verification code for admin actions
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "action": "add", // or "remove"
    "email": "<EMAIL>"
  }
  ```
- **Response**: Success message

#### POST /api/admin/add-admin
- **Description**: Add a new admin
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "verification_code": "123456"
  }
  ```
- **Response**: Success message

#### POST /api/admin/remove-admin
- **Description**: Remove an admin
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "verification_code": "123456"
  }
  ```
- **Response**: Success message

#### GET /api/admin/list-admins
- **Description**: List all admins
- **Authentication**: Admin required
- **Response**: List of admin objects

#### GET /api/admin/active-users
- **Description**: Get active users across all sections
- **Authentication**: Admin required
- **Query Parameters**:
  - `minutes`: Time window in minutes (default: 15)
- **Response**: List of active user objects

#### GET /api/admin/user-count
- **Description**: Get total user count
- **Authentication**: Admin required
- **Response**: User count

### Restricted Users

#### GET /api/admin/restricted-users
- **Description**: Get all restricted users
- **Authentication**: Admin required
- **Response**: List of restricted user objects

#### POST /api/admin/restrict-user
- **Description**: Restrict a user from using specific services
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "services": ["chat", "live", "spotify", "friends"]
  }
  ```
- **Response**: Success message

#### POST /api/admin/unrestrict-user
- **Description**: Remove restrictions from a user
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "services": ["chat", "live", "spotify", "friends"]
  }
  ```
- **Response**: Success message

#### GET /api/admin/check-restriction
- **Description**: Check if a user is restricted from specific services
- **Authentication**: Admin required
- **Query Parameters**:
  - `email`: User email
- **Response**: Restriction status and services

### Feature Restrictions

#### GET /api/admin/feature-restrictions
- **Description**: Get all feature restrictions
- **Authentication**: Admin required
- **Response**: List of feature restriction objects

#### POST /api/admin/feature-restrictions
- **Description**: Add a feature restriction for a user
- **Authentication**: Admin required
- **Request Body**:
  ```json
  {
    "user_id": "user_id",
    "max_threads": 5,
    "max_conversation_sets": 5,
    "max_live_rooms": 5,
    "max_live_conversation_sets": 5
  }
  ```
- **Response**: Success message with user and restriction details

#### GET /api/admin/feature-restrictions/:user_id
- **Description**: Get feature restrictions for a specific user
- **Authentication**: Admin required
- **Response**: User and restriction details

#### DELETE /api/admin/feature-restrictions/:user_id
- **Description**: Remove feature restrictions for a user
- **Authentication**: Admin required
- **Response**: Success message

### Logs

#### GET /api/admin/logs
- **Description**: Get API logs with filtering options
- **Authentication**: Admin required
- **Query Parameters**:
  - `user_id`: Filter by user ID
  - `service`: Filter by service
  - `action`: Filter by action
  - `limit`: Maximum number of logs to return (default: 100)
  - `skip`: Number of logs to skip (for pagination)
- **Response**: List of log objects

#### GET /api/admin/logs/summary
- **Description**: Get summary of API logs
- **Authentication**: Admin required
- **Response**: Log summary object

#### GET /api/admin/logs/user/:user_id
- **Description**: Get logs for a specific user
- **Authentication**: Admin required
- **Query Parameters**:
  - `limit`: Maximum number of logs to return (default: 100)
  - `skip`: Number of logs to skip (for pagination)
- **Response**: List of log objects

## Usage Example

```javascript
// Check if current user is an admin
const adminCheckResponse = await fetch('/api/admin/check', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const adminStatus = await adminCheckResponse.json();

if (adminStatus.is_admin) {
  // Get active users
  const activeUsersResponse = await fetch('/api/admin/active-users?minutes=30', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const activeUsers = await activeUsersResponse.json();
  
  // Get logs summary
  const logsSummaryResponse = await fetch('/api/admin/logs/summary', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const logsSummary = await logsSummaryResponse.json();
}
```
