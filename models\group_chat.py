from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON>ield
from models.user import User
from datetime import datetime, timezone
import uuid
import logging
from utils.encryption import MessageEncryption

# ANSI color codes for terminal output
class TermColors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    RESET = '\033[0m'

class GroupChat(Document):
    """Model for storing group chat messages between multiple friends"""
    chat_id = StringField(required=True, unique=True, default=lambda: str(uuid.uuid4()))
    name = StringField(required=True, default="Group Chat")
    creator = ReferenceField(User, required=True)
    members = ListField(ReferenceField(User), required=True)
    messages = ListField(DictField(), default=list)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    updated_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    profile_picture = StringField(default=None)

    meta = {
        'collection': 'group_chats',
        'indexes': [
            'chat_id',
            'creator',
            'members',
        ]
    }

    def to_dict(self, limit=20, skip=0):
        """
        Convert the group chat to a dictionary

        Args:
            limit (int): Maximum number of messages to return (default: 20)
            skip (int): Number of messages to skip from the end (for pagination, default: 0)
        """
        # Get total message count
        total_messages = len(self.messages)

        # Apply pagination - get the latest messages first
        # We reverse the messages list to get newest first, then apply skip and limit
        messages_to_return = list(reversed(self.messages))

        # Apply skip and limit
        if skip >= 0:
            messages_to_return = messages_to_return[skip:skip+limit]

        return {
            'chat_id': self.chat_id,
            'name': self.name,
            'creator_id': str(self.creator.id) if self.creator else None,
            'creator_name': self.creator.username if self.creator else None,
            'profile_picture': self.profile_picture,  # Include the group chat profile picture
            'members': [{
                'id': str(member.id),
                'username': member.username,
                'display_name': member.display_name or member.username,
                'profile_picture': member.profile_picture
            } for member in self.members],
            'messages': messages_to_return,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'total_messages': total_messages,  # Include total message count for pagination
            'has_more': skip + limit < total_messages  # Flag indicating if there are more messages to load
        }

    def is_member(self, user):
        """Check if a user is a member of this group chat"""
        if not user:
            return False

        return any(str(member.id) == str(user.id) for member in self.members)

    def add_member(self, user):
        """Add a member to the group chat"""
        if not self.is_member(user):
            self.members.append(user)
            self.save()
            return True
        return False

    def remove_member(self, user):
        """Remove a member from the group chat"""
        if self.is_member(user):
            self.members = [member for member in self.members if str(member.id) != str(user.id)]
            self.save()
            return True
        return False

    def update_profile_picture(self, user, profile_picture_url):
        """Update the group chat profile picture

        Args:
            user: The user updating the profile picture
            profile_picture_url: The URL of the new profile picture

        Returns:
            bool: True if successful, False otherwise
        """
        # Import logging at the method level to ensure it's available
        import logging
        
        try:
            if not self.is_member(user):
                print(f"{TermColors.RED}[GROUP CHAT PROFILE] User {user.username} is not a member of group chat {self.chat_id}{TermColors.RESET}")
                return False

            # Log the profile picture update with colored output
            old_picture = self.profile_picture
            print(f"{TermColors.RED}[GROUP CHAT PROFILE] Updating profile picture for group chat {self.chat_id}{TermColors.RESET}")
            print(f"{TermColors.RED}[GROUP CHAT PROFILE] Old picture: {old_picture}{TermColors.RESET}")
            print(f"{TermColors.RED}[GROUP CHAT PROFILE] New picture: {profile_picture_url}{TermColors.RESET}")

            self.profile_picture = profile_picture_url
            self.updated_at = datetime.now(timezone.utc)
            self.save()
            
            print(f"{TermColors.RED}[GROUP CHAT PROFILE] Profile picture updated successfully{TermColors.RESET}")
            return True
            
        except Exception as e:
            error_msg = f"Error updating group chat profile picture: {str(e)}"
            logging.error(error_msg)
            print(f"{TermColors.RED}[GROUP CHAT PROFILE ERROR] {error_msg}{TermColors.RESET}")
            
            import traceback
            traceback_str = traceback.format_exc()
            logging.error(f"Traceback: {traceback_str}")
            print(f"{TermColors.RED}[GROUP CHAT PROFILE ERROR] Traceback: {traceback_str}{TermColors.RESET}")
            
            return False

    def add_message(self, user, content, is_system=False, message_type='info', images=None):
        """Add a message to the group chat"""
        # Import logging at the method level to ensure it's available in case of exceptions
        import logging
        
        try:
            if not is_system and not self.is_member(user):
                raise ValueError("User is not a member of this group chat")

            timestamp = datetime.now(timezone.utc)

            if is_system:
                # System messages are not encrypted
                message = {
                    "system": True,
                    "content": content,
                    "timestamp": timestamp.isoformat(),
                    "type": message_type
                }
            else:
                # Regular user messages
                message = {
                    "user_id": str(user.id),
                    "username": user.username,
                    "display_name": user.display_name or user.username,
                    "content": content,
                    "timestamp": timestamp.isoformat(),
                    "profile_picture": user.profile_picture
                }

                # Add images if provided
                if images and isinstance(images, list) and len(images) > 0:
                    message["images"] = images
                    # Use colored logging for image uploads
                    print(f"{TermColors.RED}[GROUP CHAT IMAGE] Adding {len(images)} images to message in group chat {self.chat_id}{TermColors.RESET}")
                    logging.info(f"Adding {len(images)} images to message in group chat {self.chat_id}")

            self.messages.append(message)
            self.updated_at = datetime.now(timezone.utc)
            self.save()

            return message
            
        except Exception as e:
            # Log the error with colored output
            error_msg = f"Error adding message to group chat: {str(e)}"
            logging.error(error_msg)
            print(f"{TermColors.RED}[GROUP CHAT ERROR] {error_msg}{TermColors.RESET}")
            
            # Log the full traceback for better debugging
            import traceback
            traceback_str = traceback.format_exc()
            logging.error(f"Traceback: {traceback_str}")
            print(f"{TermColors.RED}[GROUP CHAT ERROR] Traceback: {traceback_str}{TermColors.RESET}")
            
            # Return a basic message object with error info
            return {
                "user_id": str(user.id) if user else "unknown",
                "username": "Unknown User",
                "display_name": "Unknown User",
                "content": "Error sending message. Please try again.",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "profile_picture": None,
                "error": str(e)
            }

    @classmethod
    def get_chats_for_user(cls, user_id):
        """Get all group chats for a user"""
        return list(cls.objects(members=user_id))

    @classmethod
    def create_group_chat(cls, creator_id, name, member_ids):
        """Create a new group chat"""
        from models.user import User

        # Verify creator exists
        creator = User.objects(id=creator_id).first()
        if not creator:
            return None, "Creator not found"

        # Verify all members exist and are friends with the creator
        from models.friend_relationship import FriendRelationship

        members = [creator]  # Creator is always a member

        for member_id in member_ids:
            # Skip if member_id is the creator
            if str(member_id) == str(creator_id):
                continue

            member = User.objects(id=member_id).first()
            if not member:
                return None, f"Member with ID {member_id} not found"

            # Check if they are friends
            if not FriendRelationship.are_friends(creator_id, member_id):
                return None, f"User {member.username} is not your friend"

            members.append(member)

        # Create the group chat
        group_chat = cls(
            name=name,
            creator=creator,
            members=members
        )
        group_chat.save()

        # Add a system message
        group_chat.add_message(
            creator,
            f"{creator.display_name or creator.username} created the group chat",
            is_system=True
        )

        return group_chat, "Group chat created successfully"
