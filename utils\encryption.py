import base64
import os
import hashlib
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

class MessageEncryption:
    """
    Utility class for encrypting and decrypting messages using strong encryption.
    Each chat has its own encryption key derived from the chat_id and a server-side secret.
    """

    # This should be set from an environment variable in production
    # We'll use a default for development, but this should be overridden
    SERVER_SECRET = os.environ.get('ENCRYPTION_SECRET', 'kevko_default_encryption_secret_key')

    @staticmethod
    def generate_key(chat_id, salt=None):
        """
        Generate a Fernet key from the chat_id and server secret.
        This ensures each chat has a unique encryption key that can't be derived
        without knowing the server secret.

        Args:
            chat_id (str): The unique chat identifier
            salt (bytes, optional): Custom salt for key derivation

        Returns:
            bytes: A URL-safe base64-encoded 32-byte key for Fernet
        """
        try:
            if not salt:
                # Combine chat_id with server secret to create a unique but reproducible salt
                salt = hashlib.sha256((chat_id + MessageEncryption.SERVER_SECRET).encode()).digest()[:16]

            # Use PBKDF2 to derive a secure key
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,  # 32 bytes = 256 bits
                salt=salt,
                iterations=100000,  # High number of iterations for security
            )

            # Derive key from the server secret and chat_id
            key_bytes = kdf.derive((chat_id + MessageEncryption.SERVER_SECRET).encode())

            # Convert to URL-safe base64-encoded format required by Fernet
            key = base64.urlsafe_b64encode(key_bytes)

            return key
        except Exception as e:
            logging.error(f"Error generating encryption key: {str(e)}")
            # Fall back to a basic key derivation in case of error
            # This is not ideal but better than failing completely
            fallback = hashlib.sha256((chat_id + MessageEncryption.SERVER_SECRET).encode()).digest()
            return base64.urlsafe_b64encode(fallback)

    @staticmethod
    def encrypt_message(message, chat_id):
        """
        Encrypt a message using the chat_id to derive the key

        Args:
            message (str): The plaintext message to encrypt
            chat_id (str): The unique chat identifier

        Returns:
            str: The encrypted message with a prefix indicating encryption method
        """
        try:
            if not message or not chat_id:
                return message

            # Generate key from chat_id
            key = MessageEncryption.generate_key(chat_id)

            # Create a Fernet cipher with the key
            cipher = Fernet(key)

            # Encrypt the message
            encrypted_bytes = cipher.encrypt(message.encode('utf-8'))

            # Return with a prefix to indicate encryption method
            return f"FERN_{encrypted_bytes.decode('utf-8')}"
        except Exception as e:
            logging.error(f"Error encrypting message: {str(e)}")
            # In case of error, return a placeholder
            return f"[Encryption Error]"

    @staticmethod
    def decrypt_message(encrypted_message, chat_id):
        """
        Decrypt a message using the chat_id to derive the key

        Args:
            encrypted_message (str): The encrypted message
            chat_id (str): The unique chat identifier

        Returns:
            str: The decrypted plaintext message
        """
        try:
            if not encrypted_message or not chat_id:
                return encrypted_message

            # Check if this is an E2E encrypted message
            if encrypted_message.startswith("E2E_"):
                # E2E encrypted messages can only be decrypted by the client
                return "[End-to-end encrypted message - Can only be decrypted by the recipient]"

            # Check if this is a Fernet-encrypted message
            if encrypted_message.startswith("FERN_"):
                # Remove the prefix
                encrypted_content = encrypted_message[5:]

                # Generate key from chat_id
                key = MessageEncryption.generate_key(chat_id)

                # Create a Fernet cipher with the key
                cipher = Fernet(key)

                # Decrypt the message
                decrypted_bytes = cipher.decrypt(encrypted_content.encode('utf-8'))

                # Return the decrypted message
                return decrypted_bytes.decode('utf-8')
            # Handle legacy Base64 encoding
            elif encrypted_message.startswith("ENC_"):
                # Remove the prefix
                encoded = encrypted_message[4:]

                # Decode from base64
                decoded_bytes = base64.b64decode(encoded)

                # Convert back to string
                return decoded_bytes.decode('utf-8')
            # Handle old Fernet encryption without prefix
            elif not encrypted_message.startswith('b\'') and not encrypted_message.startswith('b"'):
                try:
                    # Try to decode as if it's the old format
                    # Generate key from chat_id
                    key = MessageEncryption.generate_key(chat_id)

                    # Create a Fernet cipher with the key
                    cipher = Fernet(key)

                    # Decode the base64 encoded encrypted message
                    encrypted_bytes = base64.urlsafe_b64decode(encrypted_message)

                    # Decrypt the message
                    decrypted_message = cipher.decrypt(encrypted_bytes).decode()

                    return decrypted_message
                except:
                    # If that fails, it's probably not encrypted
                    return encrypted_message
            else:
                # Message is not encrypted
                return encrypted_message
        except Exception as e:
            logging.error(f"Error decrypting message: {str(e)}")
            return f"[Decryption Error]"
