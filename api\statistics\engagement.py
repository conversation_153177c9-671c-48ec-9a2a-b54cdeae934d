from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from models.service_engagement import ServiceEngagement
from datetime import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

# Create blueprint
engagement_api = Blueprint('engagement_api', __name__)

@engagement_api.route('/api/statistics/engagement/start', methods=['POST'])
@login_required
def start_engagement():
    """
    Start tracking a user's engagement with a service
    """
    try:
        data = request.get_json()
        service = data.get('service')
        
        if not service:
            return jsonify({'success': False, 'error': 'Service is required'}), 400
        
        # Start tracking the engagement
        engagement = ServiceEngagement.start_engagement(current_user.id, service)
        
        if engagement:
            return jsonify({
                'success': True,
                'engagement_id': str(engagement.id)
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to start engagement tracking'}), 500
    
    except Exception as e:
        logger.error(f"Error starting engagement tracking: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@engagement_api.route('/api/statistics/engagement/complete', methods=['POST'])
@login_required
def complete_engagement():
    """
    Complete a user's engagement with a service
    """
    try:
        data = request.get_json()
        engagement_id = data.get('engagement_id')
        end_time_str = data.get('end_time')
        
        if not engagement_id:
            return jsonify({'success': False, 'error': 'Engagement ID is required'}), 400
        
        # Parse end time if provided
        end_time = None
        if end_time_str:
            try:
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'success': False, 'error': 'Invalid end time format'}), 400
        
        # Complete the engagement
        engagement = ServiceEngagement.complete_engagement(engagement_id, end_time)
        
        if engagement:
            return jsonify({
                'success': True,
                'is_completed': engagement.is_completed,
                'duration_minutes': engagement.duration_minutes
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to complete engagement tracking'}), 500
    
    except Exception as e:
        logger.error(f"Error completing engagement tracking: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
