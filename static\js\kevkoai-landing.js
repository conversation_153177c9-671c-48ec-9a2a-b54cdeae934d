/**
 * KevkoAI Landing Page JavaScript
 * Handles custom cursor, animated text bubbles, and other interactive elements
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize all components
    initCustomCursor();
    initTextBubbles();
    initScrollEffects();
    initGradientAnimations();
    initTestimonialSlider();
});

/**
 * Custom Cursor Implementation
 */
function initCustomCursor() {
    const cursorOuter = document.querySelector('.cursor-outer');
    const cursorInner = document.querySelector('.cursor-inner');

    // Check if it's a touch device
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;

    // If it's a touch device, don't use custom cursor
    if (isTouchDevice) {
        document.body.style.cursor = 'auto';
        cursorOuter.style.display = 'none';
        cursorInner.style.display = 'none';
        return;
    }

    // Hide cursor when leaving window
    document.addEventListener('mouseout', (e) => {
        // Only if leaving the window completely, not just moving between elements
        if (e.relatedTarget === null) {
            cursorOuter.style.opacity = '0';
            cursorInner.style.opacity = '0';
        }
    });

    // Show cursor when entering window
    document.addEventListener('mouseover', () => {
        cursorOuter.style.opacity = '1';
        cursorInner.style.opacity = '1';
    });

    // Use requestAnimationFrame for smoother cursor movement
    let mouseX = -100;
    let mouseY = -100;
    let outerX = -100;
    let outerY = -100;
    let innerX = -100;
    let innerY = -100;

    // Update mouse position on move
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Check if hovering over interactive elements
        const target = e.target;
        const isLinkOrButton =
            target.tagName.toLowerCase() === 'a' ||
            target.tagName.toLowerCase() === 'button' ||
            target.closest('a') ||
            target.closest('button');

        // Apply hover effect
        if (isLinkOrButton) {
            cursorOuter.classList.add('hover');
            cursorInner.classList.add('hover');
        } else {
            cursorOuter.classList.remove('hover');
            cursorInner.classList.remove('hover');
        }
    });

    // Animation loop for smooth cursor movement
    function animateCursor() {
        // Smooth follow for outer cursor (lagging effect)
        const lagFactor = 0.2;
        outerX += (mouseX - outerX) * lagFactor;
        outerY += (mouseY - outerY) * lagFactor;

        // Inner cursor follows mouse exactly
        innerX = mouseX;
        innerY = mouseY;

        // Apply positions
        cursorOuter.style.left = outerX + 'px';
        cursorOuter.style.top = outerY + 'px';
        cursorInner.style.left = innerX + 'px';
        cursorInner.style.top = innerY + 'px';

        // Continue animation loop
        requestAnimationFrame(animateCursor);
    }

    // Start animation loop
    animateCursor();

    // Add click animation
    document.addEventListener('mousedown', () => {
        cursorOuter.classList.add('click');
        cursorInner.classList.add('click');
    });

    document.addEventListener('mouseup', () => {
        cursorOuter.classList.remove('click');
        cursorInner.classList.remove('click');
    });
}

/**
 * Animated Text Bubbles
 */
function initTextBubbles() {
    const bubbles = document.querySelectorAll('.response-bubble');
    const bubbleTexts = [
        "I can help you draft an email to your team about the project deadline.",
        "Let me analyze that data and create a visualization to identify trends.",
        "I've found three research papers on that topic. Want me to summarize them?",
        "Here's a step-by-step guide to solve that programming challenge.",
        "I can translate that text into 20+ languages while preserving the tone."
    ];

    // Set initial text content
    bubbles.forEach((bubble, index) => {
        const textElement = bubble.querySelector('p');
        if (textElement) {
            textElement.textContent = bubbleTexts[index % bubbleTexts.length];
        }
    });

    // Initial animation of bubbles with staggered delay
    bubbles.forEach((bubble, index) => {
        // Longer initial delay to let the hero content load first
        const delay = parseInt(bubble.getAttribute('data-delay')) || (2000 + index * 2000);

        // Show bubble after delay
        setTimeout(() => {
            bubble.classList.add('visible');

            // Hide bubble after 6-8 seconds (random)
            const displayTime = 6000 + Math.random() * 2000;
            setTimeout(() => {
                bubble.classList.remove('visible');

                // Show again after 4-8 seconds (creates a cycle with more time between bubbles)
                const hideTime = 4000 + Math.random() * 4000;
                setTimeout(() => {
                    initBubbleCycle(bubble, bubbleTexts);
                }, hideTime);
            }, displayTime);
        }, delay);
    });
}

/**
 * Creates an infinite cycle of showing/hiding for a bubble with text changes
 */
function initBubbleCycle(bubble, bubbleTexts) {
    // Change text occasionally
    const textElement = bubble.querySelector('p');
    if (textElement && Math.random() > 0.6) { // 40% chance to change text
        const randomIndex = Math.floor(Math.random() * bubbleTexts.length);

        // Fade out text
        textElement.style.opacity = '0';

        // Change text after fade out
        setTimeout(() => {
            textElement.textContent = bubbleTexts[randomIndex];
            textElement.style.opacity = '1';
        }, 300);
    }

    // Show bubble with animation
    bubble.classList.add('visible');

    // Hide after random time between 6-10 seconds
    const displayTime = 6000 + Math.random() * 4000;
    setTimeout(() => {
        bubble.classList.remove('visible');

        // Show again after random time between 5-10 seconds (longer gaps between appearances)
        const hideTime = 5000 + Math.random() * 5000;
        setTimeout(() => {
            initBubbleCycle(bubble, bubbleTexts);
        }, hideTime);
    }, displayTime);
}

/**
 * Scroll Effects
 */
function initScrollEffects() {
    // Get all elements that should animate on scroll
    const animatedElements = document.querySelectorAll('.feature-card, .cta-section');

    // Create intersection observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                // Unobserve after animation is triggered
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1 // Trigger when 10% of the element is visible
    });

    // Observe each element
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Helper function to create a throttled version of a function
 * @param {Function} func - The function to throttle
 * @param {number} limit - The time limit in milliseconds
 * @returns {Function} - Throttled function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Add CSS class for animation on scroll
document.addEventListener('scroll', throttle(() => {
    const scrollPosition = window.scrollY;

    // Parallax effect for shapes
    const shapes = document.querySelectorAll('.shape');
    shapes.forEach(shape => {
        const speed = 0.05;
        const yPos = -scrollPosition * speed;
        shape.style.transform = `translateY(${yPos}px)`;
    });

    // Fade in elements as they come into view
    const fadeElements = document.querySelectorAll('.feature-card, .cta-section');
    fadeElements.forEach(element => {
        const elementPosition = element.getBoundingClientRect().top;
        const screenHeight = window.innerHeight;

        if (elementPosition < screenHeight * 0.8) {
            element.classList.add('fade-in');
        }
    });
}, 50)); // Throttle to run at most every 50ms

/**
 * Initialize gradient animations and effects
 */
function initGradientAnimations() {
    // Add subtle movement to gradient backgrounds
    const gradientElements = document.querySelectorAll('.cta-button, .feature-icon');

    gradientElements.forEach(element => {
        element.addEventListener('mouseover', () => {
            element.style.backgroundSize = '200% 200%';
            element.style.transition = 'all 0.3s ease';
        });

        element.addEventListener('mouseout', () => {
            element.style.backgroundSize = '100% 100%';
            element.style.transition = 'all 0.3s ease';
        });
    });

    // Add shine effect to CTA buttons
    const ctaButtons = document.querySelectorAll('.cta-button');

    ctaButtons.forEach(button => {
        button.addEventListener('mousemove', (e) => {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            button.style.setProperty('--x-pos', `${x}px`);
            button.style.setProperty('--y-pos', `${y}px`);
        });
    });
}

/**
 * Initialize testimonial slider
 */
function initTestimonialSlider() {
    const slider = document.querySelector('.testimonial-slider');
    const cards = document.querySelectorAll('.testimonial-card');
    const prevButton = document.querySelector('.control-prev');
    const nextButton = document.querySelector('.control-next');
    const indicators = document.querySelectorAll('.indicator');
    
    if (!slider || cards.length === 0) return;
    
    let currentIndex = 0;
    const cardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);
    
    // Set initial active indicator
    updateIndicators();
    
    // Handle next button click
    nextButton.addEventListener('click', () => {
        if (currentIndex < cards.length - 1) {
            currentIndex++;
            updateSlider();
        } else {
            // Loop back to first slide
            currentIndex = 0;
            updateSlider();
        }
    });
    
    // Handle previous button click
    prevButton.addEventListener('click', () => {
        if (currentIndex > 0) {
            currentIndex--;
            updateSlider();
        } else {
            // Loop to last slide
            currentIndex = cards.length - 1;
            updateSlider();
        }
    });
    
    // Handle indicator clicks
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            currentIndex = index;
            updateSlider();
        });
    });
    
    // Update slider position
    function updateSlider() {
        // Calculate the translation value
        const translateValue = -currentIndex * cardWidth;
        
        // Apply the translation to the slider
        slider.style.transform = `translateX(${translateValue}px)`;
        slider.style.transition = 'transform 0.5s ease';
        
        // Update indicators
        updateIndicators();
    }
    
    // Update indicator active states
    function updateIndicators() {
        indicators.forEach((indicator, index) => {
            if (index === currentIndex) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
    }
    
    // Add responsive behavior
    window.addEventListener('resize', () => {
        // Recalculate card width on resize
        const newCardWidth = cards[0].offsetWidth + parseInt(window.getComputedStyle(cards[0]).marginRight);
        
        // Update slider position with new dimensions
        const translateValue = -currentIndex * newCardWidth;
        slider.style.transform = `translateX(${translateValue}px)`;
        slider.style.transition = 'none'; // Disable transition during resize
    });
    
    // Auto-advance the slider every 5 seconds
    setInterval(() => {
        currentIndex = (currentIndex + 1) % cards.length;
        updateSlider();
    }, 5000);
}
