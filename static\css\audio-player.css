/* Audio Player Styles */
.speech-result {
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

.speech-result-title {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.audio-player-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.waveform-container {
  position: relative;
  height: 60px;
  width: 100%;
  background-color: rgba(40, 40, 40, 0.6);
  border-radius: 6px;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.waveform {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  padding: 0 10px;
}

.waveform-bar {
  width: 3px;
  background-color: rgba(200, 200, 200, 0.5);
  border-radius: 1px;
  margin: 0 1px;
}

.audio-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audio-buttons {
  display: flex;
  gap: 0.5rem;
}

.audio-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: rgba(50, 50, 50, 0.8);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.audio-button:hover {
  background-color: rgba(70, 70, 70, 0.8);
}

.audio-button i {
  width: 16px;
  height: 16px;
}

.audio-metadata {
  display: flex;
  flex-direction: column;
  font-size: 0.8rem;
  color: rgba(200, 200, 200, 0.7);
}

/* Animation for playing state */
@keyframes playing-animation {
  0% { height: 10px; }
  50% { height: 30px; }
  100% { height: 10px; }
}

.waveform-bar.playing {
  animation: playing-animation 1s infinite;
  animation-delay: calc(var(--bar-index) * 0.05s);
}

/* Progress indicator */
.waveform-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%;
  background-color: rgba(100, 100, 255, 0.2);
  transition: width 0.1s linear;
  pointer-events: none;
}

/* Playhead indicator */
.waveform-playhead {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateX(-50%);
  pointer-events: none;
}