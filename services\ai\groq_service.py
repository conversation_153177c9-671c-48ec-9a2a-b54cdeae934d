import os
from groq import Groq

class GroqService:
    def __init__(self):
        self.client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        self.default_model = "qwen-qwq-32b"  # Default model
        
        # Define supported models with their max token limits
        self.supported_models = {
            "qwen-qwq-32b": 4096,
            "gemma2-9b-it": 8192,
            "llama-3.3-70b-versatile": 32768,
            "llama-3.1-8b-instant": 32768,
            "llama3-70b-8192": 8192,
            "llama3-8b-8192": 8192
        }

    def generate_stream(self, messages, system_prompt_file='prompts/system-prompt.txt', model=None):
        # Read the system prompt from the specified file
        with open(system_prompt_file, 'r') as f:
            system_prompt = f.read()
            
        # Use specified model or default
        model_name = model if model in self.supported_models else self.default_model
        max_tokens = self.supported_models.get(model_name, 4096)

        # Limit user messages to 1500 characters
        limited_messages = [
            {
                "role": msg["role"],
                "content": msg["content"][:1500] if msg["role"] == "user" else msg["content"]
            }
            for msg in messages
        ]

        # Add system message at the beginning of the conversation
        full_messages = [
            {"role": "system", "content": system_prompt}
        ] + limited_messages

        # Create the completion with streaming
        completion = self.client.chat.completions.create(
            model=model_name,
            messages=full_messages,
            temperature=0.6,
            max_completion_tokens=max_tokens,
            top_p=0.95,
            stream=True,
            stop=None,
        )

        # Yield content chunks
        for chunk in completion:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
