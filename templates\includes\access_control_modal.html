<div id="accessControlModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg p-6 w-[450px] max-w-full">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-100">Add Service Restriction</h3>
            <button id="closeAccessControlModal" class="text-slate-400 hover:text-slate-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>

        <!-- Step indicator -->
        <div class="flex items-center mb-4 relative">
            <div class="w-full absolute h-1 bg-slate-700"></div>
            <div class="flex justify-between w-full relative z-10">
                <div id="step1Indicator" class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-cyan-600 flex items-center justify-center text-white font-medium mb-1">1</div>
                    <span class="text-xs text-slate-300">Select User</span>
                </div>
                <div id="step2Indicator" class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center text-slate-400 font-medium mb-1">2</div>
                    <span class="text-xs text-slate-400">Select Services</span>
                </div>
                <div id="step3Indicator" class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center text-slate-400 font-medium mb-1">3</div>
                    <span class="text-xs text-slate-400">Confirm</span>
                </div>
            </div>
        </div>

        <div class="space-y-4">
            <!-- Step 1: User Search (initially visible) -->
            <div id="step1Content">
                <div>
                    <label for="accessControlUserSearch" class="block text-sm font-medium text-slate-300 mb-1">Search User by Username/Email</label>
                    <div class="relative">
                        <input type="text" id="accessControlUserSearch" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 pl-10" placeholder="Enter username or email (min. 3 characters)">
                        <i data-lucide="search" class="h-4 w-4 text-slate-400 absolute left-3 top-2.5"></i>
                    </div>
                </div>

                <!-- Search Results -->
                <div id="accessControlSearchResults" class="max-h-40 overflow-y-auto bg-slate-700/50 rounded-md mt-2">
                    <!-- Search results will be populated by JS -->
                </div>

                <!-- Step 1 Navigation -->
                <div class="flex justify-end mt-4">
                    <button id="cancelStep1Button" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- Step 2: Service Selection (initially hidden) -->
            <div id="step2Content" class="hidden">
                <!-- Selected User Info -->
                <div class="bg-slate-700/50 p-3 rounded-md mb-4">
                    <div class="flex items-center">
                        <div id="selectedUserAvatar" class="w-10 h-10 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-3">
                            <!-- User initials will be populated by JS -->
                        </div>
                        <div class="flex-1 min-w-0">
                            <div id="selectedUserName" class="text-sm font-medium text-slate-200 truncate">
                                <!-- Username will be populated by JS -->
                            </div>
                            <div id="selectedUserEmail" class="text-xs text-slate-400 truncate">
                                <!-- Email will be populated by JS -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Selection -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">Select Services to Restrict:</label>
                    <div class="p-4 bg-slate-700/30 rounded-md border border-slate-600/30">
                        <div class="grid grid-cols-2 gap-3">
                            <div class="service-option p-3 bg-slate-700/50 rounded-md border border-slate-600/30 cursor-pointer hover:bg-slate-600/50" data-service="chat">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i data-lucide="message-circle" class="h-4 w-4 text-cyan-400 mr-2"></i>
                                        <span class="text-sm font-medium text-slate-200">Chat</span>
                                    </div>
                                    <input type="checkbox" id="chatServiceCheckbox" class="service-checkbox w-4 h-4 bg-slate-700 border-slate-600 rounded text-cyan-500 focus:ring-cyan-500 focus:ring-offset-slate-800" value="chat">
                                </div>
                                <div class="text-xs text-slate-400">KevkoAI Chat Service</div>
                            </div>

                            <div class="service-option p-3 bg-slate-700/50 rounded-md border border-slate-600/30 cursor-pointer hover:bg-slate-600/50" data-service="live">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i data-lucide="video" class="h-4 w-4 text-green-400 mr-2"></i>
                                        <span class="text-sm font-medium text-slate-200">Live</span>
                                    </div>
                                    <input type="checkbox" id="liveServiceCheckbox" class="service-checkbox w-4 h-4 bg-slate-700 border-slate-600 rounded text-cyan-500 focus:ring-cyan-500 focus:ring-offset-slate-800" value="live">
                                </div>
                                <div class="text-xs text-slate-400">Live Chat Service</div>
                            </div>

                            <div class="service-option p-3 bg-slate-700/50 rounded-md border border-slate-600/30 cursor-pointer hover:bg-slate-600/50" data-service="spotify">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i data-lucide="music" class="h-4 w-4 text-purple-400 mr-2"></i>
                                        <span class="text-sm font-medium text-slate-200">KevkoFy</span>
                                    </div>
                                    <input type="checkbox" id="spotifyServiceCheckbox" class="service-checkbox w-4 h-4 bg-slate-700 border-slate-600 rounded text-cyan-500 focus:ring-cyan-500 focus:ring-offset-slate-800" value="spotify">
                                </div>
                                <div class="text-xs text-slate-400">Music Streaming Service</div>
                            </div>

                            <div class="service-option p-3 bg-slate-700/50 rounded-md border border-slate-600/30 cursor-pointer hover:bg-slate-600/50" data-service="friends">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <i data-lucide="users" class="h-4 w-4 text-blue-400 mr-2"></i>
                                        <span class="text-sm font-medium text-slate-200">Friends</span>
                                    </div>
                                    <input type="checkbox" id="friendsServiceCheckbox" class="service-checkbox w-4 h-4 bg-slate-700 border-slate-600 rounded text-cyan-500 focus:ring-cyan-500 focus:ring-offset-slate-800" value="friends">
                                </div>
                                <div class="text-xs text-slate-400">Friends & Messaging</div>
                            </div>
                        </div>

                        <div class="mt-3 text-xs text-amber-400/80 flex items-center">
                            <i data-lucide="alert-triangle" class="h-3 w-3 mr-1"></i>
                            <span>User will not be able to access selected services</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2 Navigation -->
                <div class="flex justify-between mt-4">
                    <button id="backToStep1Button" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md">
                        Back
                    </button>
                    <button id="continueToStep3Button" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md">
                        Continue
                    </button>
                </div>
            </div>

            <!-- Step 3: Confirmation (initially hidden) -->
            <div id="step3Content" class="hidden">
                <div class="bg-slate-700/30 p-4 rounded-md border border-slate-600/30">
                    <h4 class="text-sm font-medium text-slate-200 mb-3">Confirm Service Restrictions</h4>

                    <div class="mb-3">
                        <div class="text-xs text-slate-400 mb-1">User:</div>
                        <div class="flex items-center bg-slate-700/50 p-2 rounded-md">
                            <div id="confirmUserAvatar" class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-2">
                                <!-- User initials will be populated by JS -->
                            </div>
                            <div class="flex-1 min-w-0">
                                <div id="confirmUserName" class="text-sm font-medium text-slate-200 truncate">
                                    <!-- Username will be populated by JS -->
                                </div>
                                <div id="confirmUserEmail" class="text-xs text-slate-400 truncate">
                                    <!-- Email will be populated by JS -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="text-xs text-slate-400 mb-1">Will be restricted from:</div>
                        <div id="confirmServicesList" class="flex flex-wrap gap-1 bg-slate-700/50 p-2 rounded-md">
                            <!-- Services will be populated by JS -->
                        </div>
                    </div>

                    <div class="mt-3 text-xs text-amber-400/80 flex items-center">
                        <i data-lucide="alert-triangle" class="h-3 w-3 mr-1"></i>
                        <span>This action can be reversed later if needed</span>
                    </div>
                </div>

                <!-- Step 3 Navigation -->
                <div class="flex justify-between mt-4">
                    <button id="backToStep2Button" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md">
                        Back
                    </button>
                    <button id="saveRestrictionButton" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md">
                        Apply Restrictions
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
