"""
Search routes.
This module contains routes for searching users.
"""
from flask import jsonify, request
from flask_login import login_required, current_user
from .. import friends_api
from models.user import User
from models.friend_relationship import FriendRelationship
from utils.decorators import check_service_access
import logging


@friends_api.route('/search', methods=['GET'])
@login_required
@check_service_access('friends')
def search_users():
    """Search for users by username"""
    query = request.args.get('q', '')
    logging.info(f"Search query: {query}")

    if len(query) < 3:
        logging.warning(f"Search query too short: {query}")
        return jsonify({'error': 'Search query must be at least 3 characters'}), 400

    # Search for users by username (case insensitive)
    try:
        normalized_query = User.normalize_username(query)
        logging.info(f"Normalized query: {normalized_query}")
        users = User.objects(username__icontains=normalized_query).limit(10)
        logging.info(f"Found {len(users)} users matching query")
    except Exception as e:
        logging.error(f"Error searching users: {str(e)}")
        return jsonify({'error': f'Error searching users: {str(e)}'}), 500

    # Filter out current user
    users = [user for user in users if str(user.id) != str(current_user.id)]
    logging.info(f"After filtering current user, {len(users)} users remain")

    # Convert to list of dictionaries with friendship status
    results = []
    for user in users:
        try:
            # Check friendship status
            is_friend = FriendRelationship.are_friends(current_user.id, user.id)
            request_sent = FriendRelationship.objects(user=current_user.id, friend=user.id, is_accepted=False).first() is not None
            request_received = FriendRelationship.objects(user=user.id, friend=current_user.id, is_accepted=False).first() is not None

            user_data = {
                'id': str(user.id),
                'username': user.username,
                'display_name': user.display_name or user.username,
                'profile_picture': user.profile_picture,
                'is_friend': is_friend,
                'request_sent': request_sent,
                'request_received': request_received
            }

            logging.info(f"User data for {user.username}: {user_data}")
            results.append(user_data)
        except Exception as e:
            logging.error(f"Error processing user {user.username}: {str(e)}")
            # Continue with next user

    return jsonify(results)