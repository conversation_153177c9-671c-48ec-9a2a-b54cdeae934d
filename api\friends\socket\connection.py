"""
Socket.IO connection handlers for the friends API.
This module contains handlers for connection, disconnection, and namespace joining.
"""
from flask import request
from flask_login import current_user
from flask_socketio import emit, join_room
from models.friend_relationship import FriendRelationship
from models.friend_chat import FriendChat
from models.group_chat import GroupChat
from models.restricted_user import RestrictedUser
import logging

def init_connection_handlers(socketio):
    """Initialize Socket.IO connection event handlers for friends"""

    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        logging.info(f"Socket.IO connection established: {request.sid}")
        if current_user.is_authenticated:
            logging.info(f"Authenticated user connected: {current_user.username}")

            # Join a room specific to this user for private notifications
            user_room = f"user_{current_user.id}"
            join_room(user_room)
            logging.info(f"User {current_user.username} joined personal room: {user_room}")
            
            # Also join a room based on the session ID for direct communication
            session_room = request.sid
            join_room(session_room)
            logging.info(f"User {current_user.username} joined session room: {session_room}")

        return True

    @socketio.on('join_friends_namespace')
    def handle_join_friends_namespace(data):
        """Handle client joining the friends namespace"""
        if not current_user.is_authenticated:
            emit('friends_namespace_joined', {
                'success': False,
                'message': 'Authentication required'
            })
            return

        # Check if user is restricted from friends service
        if RestrictedUser.is_restricted(current_user.email, 'friends'):
            emit('friends_namespace_joined', {
                'success': False,
                'message': 'You are restricted from using the friends service',
                'restricted': True
            })
            return

        logging.info(f"User {current_user.username} joined friends namespace")

        # Join a room for the user to receive private messages
        user_room = f"user_{current_user.id}"
        join_room(user_room)
        logging.info(f"User {current_user.username} joined personal room: {user_room}")
        
        # Also join a room based on the session ID for direct communication
        session_room = request.sid
        join_room(session_room)
        logging.info(f"User {current_user.username} joined session room: {session_room}")

        # Join rooms for all friend chats the user is part of
        try:
            chats = FriendChat.get_chats_for_user(current_user.id)
            for chat in chats:
                chat_room = f"chat_{chat.chat_id}"
                join_room(chat_room)
                logging.info(f"User {current_user.username} joined friend chat room: {chat_room}")
        except Exception as e:
            logging.error(f"Error joining friend chat rooms: {str(e)}")
            
        # Join rooms for all group chats the user is part of
        try:
            group_chats = GroupChat.objects(members=current_user.id)
            for chat in group_chats:
                group_room = f"group_{chat.chat_id}"
                join_room(group_room)
                logging.info(f"User {current_user.username} joined group chat room: {group_room}")
        except Exception as e:
            logging.error(f"Error joining group chat rooms: {str(e)}")

        # Notify friends that user is online
        try:
            friends = FriendRelationship.get_friends(current_user.id)
            for friend in friends:
                friend_room = f"user_{friend.id}"
                emit('friend_status', {
                    'user_id': str(current_user.id),
                    'status': 'online'
                }, room=friend_room, broadcast=False)
                logging.info(f"Sent online status to friend {friend.username} in room {friend_room}")
        except Exception as e:
            logging.error(f"Error notifying friends of online status: {str(e)}")

        # Send confirmation to the client
        emit('friends_namespace_joined', {
            'success': True,
            'message': 'Successfully joined friends namespace'
        })
        
        # Log success
        logging.info(f"User {current_user.username} successfully joined friends namespace")

    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        logging.info(f"Socket.IO client disconnected: {request.sid}")

        if current_user.is_authenticated:
            # Notify friends that user is offline
            friends = FriendRelationship.get_friends(current_user.id)
            for friend in friends:
                friend_room = f"user_{friend.id}"
                emit('friend_status', {
                    'user_id': str(current_user.id),
                    'status': 'offline'
                }, room=friend_room)
                logging.info(f"Sent offline status to friend {friend.username} in room {friend_room}")