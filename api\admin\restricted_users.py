from flask import jsonify, request
from flask_login import current_user, login_required
from models.restricted_user import RestrictedUser
from . import admin_api
from .routes import admin_required
import logging

@admin_api.route('/restricted-users', methods=['GET'])
@login_required
@admin_required
def get_restricted_users():
    """Get all restricted users"""
    try:
        restricted_users = RestrictedUser.get_all_restricted()
        result = []

        for restricted_user in restricted_users:
            # Find the user in the User collection to get the username
            from models.user import User
            user_obj = User.objects(email=restricted_user.email).first()
            username = user_obj.username if user_obj else "Unknown User"

            result.append({
                'email': restricted_user.email,
                'username': username,
                'restricted_services': restricted_user.restricted_services,
                'created_at': restricted_user.created_at.isoformat(),
                'created_by': restricted_user.created_by
            })

        return jsonify(result)
    except Exception as e:
        logging.error(f"Error in get_restricted_users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/restrict-user', methods=['POST'])
@login_required
@admin_required
def restrict_user():
    """Restrict a user from using specific services"""
    try:
        data = request.get_json()
        email = data.get('email')
        services = data.get('services', [])

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        if not services or not isinstance(services, list):
            return jsonify({'error': 'Services must be a non-empty list'}), 400

        # Validate services
        valid_services = ['chat', 'live', 'spotify', 'friends']
        for service in services:
            if service not in valid_services:
                return jsonify({'error': f'Invalid service: {service}'}), 400

        # Restrict user
        RestrictedUser.add_restriction(email, services, current_user.email)

        return jsonify({'message': f'User {email} restricted from services: {", ".join(services)}'})
    except Exception as e:
        logging.error(f"Error in restrict_user: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/unrestrict-user', methods=['POST'])
@login_required
@admin_required
def unrestrict_user():
    """Remove restrictions from a user"""
    try:
        data = request.get_json()
        email = data.get('email')
        services = data.get('services', [])

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        if not services or not isinstance(services, list):
            return jsonify({'error': 'Services must be a non-empty list'}), 400

        # Validate services
        valid_services = ['chat', 'live', 'spotify', 'friends']
        for service in services:
            if service not in valid_services:
                return jsonify({'error': f'Invalid service: {service}'}), 400

        # Unrestrict user
        for service in services:
            RestrictedUser.remove_restriction(email, service)

        return jsonify({'message': f'User {email} unrestricted from services: {", ".join(services)}'})
    except Exception as e:
        logging.error(f"Error in unrestrict_user: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/check-restriction', methods=['GET'])
@login_required
@admin_required
def check_restriction():
    """Check if a user is restricted from specific services"""
    try:
        email = request.args.get('email')

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        # Get restricted services
        user = RestrictedUser.objects(email=email).first()
        restricted_services = user.restricted_services if user else []

        return jsonify({
            'email': email,
            'is_restricted': bool(restricted_services),
            'restricted_services': restricted_services
        })
    except Exception as e:
        logging.error(f"Error in check_restriction: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@admin_api.route('/restricted-users/<email>', methods=['DELETE'])
@login_required
@admin_required
def remove_restricted_user(email):
    """Remove a restriction for a user"""
    try:
        service = request.args.get('service')

        # Get username for the message
        from models.user import User
        user_obj = User.objects(email=email).first()
        username = user_obj.username if user_obj else email

        # Remove restriction
        result = RestrictedUser.remove_restriction(email, service)

        if result:
            service_name = service if service else "all services"
            return jsonify({'message': f'User {username} unrestricted from {service_name} successfully'})
        else:
            return jsonify({'error': 'User not found or not restricted'}), 404
    except Exception as e:
        logging.error(f"Error in remove_restricted_user: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500