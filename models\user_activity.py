from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, DateT<PERSON><PERSON>ield
from models.user import User
from models.constants import VALID_ACTIVITY_SECTIONS
from datetime import datetime, timedelta
import logging

class UserActivity(Document):
    user = ReferenceField(User, required=True)
    section = StringField(required=True, choices=VALID_ACTIVITY_SECTIONS)
    last_active = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'user_activities',
        'indexes': [
            'user',
            'section',
            'last_active',
            ('user', 'section'),  # Compound index for queries that filter by both user and section
            ('section', 'last_active'),  # Compound index for queries that filter by section and sort by last_active
            ('user', 'last_active')  # Compound index for queries that filter by user and sort by last_active
        ],
        'ordering': ['-last_active']  # Default ordering by last_active (descending)
    }

    @classmethod
    def update_activity(cls, user, section):
        """Update or create activity record for a user in a specific section"""
        # Validate section is in allowed choices
        if section not in VALID_ACTIVITY_SECTIONS:
            raise ValueError(f"Invalid section '{section}'. Must be one of: {', '.join(VALID_ACTIVITY_SECTIONS)}")
            
        # Use update_one with upsert for better performance (atomic operation)
        now = datetime.utcnow()
        result = cls.objects(user=user, section=section).update_one(
            set__last_active=now,
            upsert=True
        )
        
        # If no document was modified, it means a new one was created
        if result == 0:
            # Document was created via upsert, retrieve it
            activity = cls.objects(user=user, section=section).first()
        else:
            # Document was updated, retrieve the updated version
            activity = cls.objects(user=user, section=section).first()
            
        return activity

    @classmethod
    def get_active_users(cls, section, minutes=15):
        """Get users active in the specified section within the last X minutes"""
        # Validate section is in allowed choices
        if section not in VALID_ACTIVITY_SECTIONS:
            raise ValueError(f"Invalid section '{section}'. Must be one of: {', '.join(VALID_ACTIVITY_SECTIONS)}")
            
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        # Use only=[] to fetch only the fields we need for better performance
        # Add index hint to use the compound index
        activities = cls.objects(
            section=section, 
            last_active__gte=cutoff_time
        ).hint([('section', 1), ('last_active', 1)])
        
        return activities

    @classmethod
    def get_all_active_users(cls, minutes=15):
        """Get all active users across all sections within the last X minutes"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        # Use hint to ensure we're using the last_active index for better performance
        activities = cls.objects(
            last_active__gte=cutoff_time
        ).hint([('last_active', 1)])
        
        return activities

    @classmethod
    def get_last_activity(cls, user_id, section):
        """Get the last activity time for a user in a specific section"""
        # Validate section is in allowed choices
        if section not in VALID_ACTIVITY_SECTIONS:
            raise ValueError(f"Invalid section '{section}'. Must be one of: {', '.join(VALID_ACTIVITY_SECTIONS)}")
            
        # Only fetch the last_active field for better performance
        activity = cls.objects(
            user=user_id, 
            section=section
        ).only('last_active').first()
        
        if activity:
            return activity.last_active
            
        # Return a date far in the past if no activity
        return datetime.utcnow() - timedelta(days=365)

    def to_dict(self):
        """Convert activity to dictionary"""
        try:
            # Ensure we only access fields that exist in the User model
            user_data = {
                'user_id': str(self.user.id),
                'username': self.user.username,
                'email': self.user.email,
                'section': self.section,
                'last_active': self.last_active.isoformat()
            }
    
            # Only add profile_picture if it exists
            if hasattr(self.user, 'profile_picture') and self.user.profile_picture:
                user_data['profile_picture'] = self.user.profile_picture
    
            return user_data
        except Exception as e:
            # Log the error and return a minimal dictionary
            import logging
            logging.error(f"Error in UserActivity.to_dict: {str(e)}")
            
            return {
                'user_id': str(self.user.id) if self.user else 'unknown',
                'section': self.section,
                'last_active': self.last_active.isoformat() if self.last_active else None,
                'error': 'Failed to load complete user data'
            }
