import os
from dotenv import load_dotenv
from mongoengine import connect, disconnect
from datetime import datetime, timezone

# Load environment variables
load_dotenv()

# Connect to MongoDB
disconnect()
connect(
    db='kevko_systems',
    host=os.getenv('MONGO_URI'),
    alias='default'
)

# Import models after connecting to the database
from models.user import User
from models.thread import Thread
from models.shared_thread import SharedThread
from models.room import Room
from models.restricted_user import RestrictedUser
from models.user_activity import UserActivity
from models.spotify_credentials import SpotifyCredentials
from models.feature_restriction import FeatureRestriction
from models.api_log import APILog
from models.friend_relationship import FriendRelationship
from models.friend_chat import FriendChat
from models.admin_contact import AdminContact
from models.daily_stats import DailyStats
from models.group_chat import GroupChat
from models.login_history import LoginHistory
from models.model_usage import ModelUsage
from models.service_engagement import ServiceEngagement
from models.service_update import ServiceUpdate
from models.service_usage import ServiceUsage
from models.uploaded_file import UploadedFile
from mongoengine.connection import get_db

def reset_database():
    """Reset the database by dropping all collections and creating default data"""
    print("Resetting database...")

    # Method 1: Drop specific collections
    User.drop_collection()
    Thread.drop_collection()
    SharedThread.drop_collection()
    Room.drop_collection()
    RestrictedUser.drop_collection()
    UserActivity.drop_collection()
    SpotifyCredentials.drop_collection()
    FeatureRestriction.drop_collection()
    APILog.drop_collection()
    FriendRelationship.drop_collection()
    FriendChat.drop_collection()
    AdminContact.drop_collection()
    DailyStats.drop_collection()
    GroupChat.drop_collection()
    LoginHistory.drop_collection()
    ModelUsage.drop_collection()
    ServiceEngagement.drop_collection()
    ServiceUpdate.drop_collection()
    ServiceUsage.drop_collection()
    UploadedFile.drop_collection()

    # Method 2: Drop all collections in the database
    db = get_db()
    for collection_name in db.list_collection_names():
        if not collection_name.startswith('system.'):
            print(f"Dropping collection: {collection_name}")
            db.drop_collection(collection_name)

    print("All collections dropped.")

    # Create default admin users
    create_admin_users()

    # Ensure indexes are created
    create_indexes()

    print("Database reset complete!")

def create_admin_users():
    """Create admin users only"""
    # Create primary admin users
    admin1 = User(
        username="gitar",
        email="<EMAIL>",
        is_admin=True,
        created_at=datetime.now(timezone.utc),
        last_login=datetime.now(timezone.utc),
        display_name="gitar",
        contact_settings=""
    )
    admin1.set_password("amogh@6969")
    admin1.save()
    admin2 = User(
        username="kevko",
        email="<EMAIL>",
        is_admin=True,
        created_at=datetime.now(timezone.utc),
        last_login=datetime.now(timezone.utc),
        display_name="Kevin",
        contact_settings=""
    )
    admin2.set_password("kevko")
    admin2.save()
    print(f"Created admin user: {admin2.email}")

def create_indexes():
    """Ensure all indexes are created"""
    User.ensure_indexes()
    Thread.ensure_indexes()
    SharedThread.ensure_indexes()
    Room.ensure_indexes()
    RestrictedUser.ensure_indexes()
    UserActivity.ensure_indexes()
    SpotifyCredentials.ensure_indexes()
    FeatureRestriction.ensure_indexes()
    APILog.ensure_indexes()
    FriendRelationship.ensure_indexes()
    FriendChat.ensure_indexes()
    AdminContact.ensure_indexes()
    DailyStats.ensure_indexes()
    GroupChat.ensure_indexes()
    LoginHistory.ensure_indexes()
    ModelUsage.ensure_indexes()
    ServiceEngagement.ensure_indexes()
    ServiceUpdate.ensure_indexes()
    ServiceUsage.ensure_indexes()
    UploadedFile.ensure_indexes()
    print("All indexes created.")

if __name__ == "__main__":
    # Ask for confirmation
    print("WARNING: This will delete all data in the database!")
    confirmation = input("Are you sure you want to continue? (y/n): ")

    if confirmation.lower() == 'y':
        reset_database()
    else:
        print("Database reset cancelled.")
