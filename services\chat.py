import json
from datetime import datetime
from models.thread import Thread
from models.feature_restriction import FeatureRestriction
from models.model_usage import ModelUsage
from services.ai.gemini_service import GeminiService
from services.ai.gpt_service import GPTService
from services.ai.groq_service import GroqService
from services.ai.gemini_image import GeminiImageService
from services.ai.tts_service import TTSService
from services.thread_rename import ThreadRenameService
import logging

class ChatService:
    def __init__(self):
        self.gemini_service = GeminiService()
        self.gpt_service = GPTService()
        self.groq_service = GroqService()
        self.gemini_image_service = GeminiImageService()
        self.tts_service = TTSService()
        self.thread_rename_service = ThreadRenameService()

    def generate_response(self, data, user):
        message = data.get('message', '')
        thread_id = data.get('thread_id')
        selected_model = data.get('model', 'gpt-4o-mini')
        images = data.get('images', [])

        # Create or get thread
        if thread_id:
            thread = Thread.objects(id=thread_id, user=user.id).first()
            if not thread:
                # Check if user can create a new thread
                if not FeatureRestriction.can_create_thread(user.id):
                    yield f"data: {json.dumps({'error': 'You have reached the maximum number of threads allowed. Please delete some existing threads to create new ones.', 'feature_restricted': True})}\n\n"
                    return
                thread = Thread(user=user.id).save()
        else:
            # Check if user can create a new thread
            if not FeatureRestriction.can_create_thread(user.id):
                yield f"data: {json.dumps({'error': 'You have reached the maximum number of threads allowed. Please delete some existing threads to create new ones.', 'feature_restricted': True})}\n\n"
                return
            thread = Thread(user=user.id).save()

        # Check if user can add more messages to this thread
        if not FeatureRestriction.can_add_message(user.id, thread.id):
            yield f"data: {json.dumps({'error': 'You have reached the maximum number of messages allowed in this thread.', 'feature_restricted': True})}\n\n"
            return

        # Add user message to thread with images if present
        user_message = {"role": "user", "content": message}
        if images:
            user_message["images"] = images
        thread.messages.append(user_message)
        thread.updated_at = datetime.utcnow()
        thread.save()

        try:
            if images:
                # Use Gemini Image service if images are present
                print("Using Gemini Image service for generation")
                generator = self.gemini_image_service.generate_stream({
                    "prompt": message,
                    "images": images
                })
            elif selected_model == 'gemini-2.0-flash':
                print("Using Gemini service for generation")
                generator = self.gemini_service.generate_stream(thread.messages)
            elif selected_model == 'playai-tts':
                # For PlayAI TTS, use Llama 3.3 70B model and then generate speech
                print("Using Llama 3.3 70B with PlayAI TTS")
                generator = self.groq_service.generate_stream(thread.messages, model='llama-3.3-70b-versatile')
            elif selected_model in ['qwen-qwq-32b', 'gemma2-9b-it', 'llama-3.3-70b-versatile',
                                   'llama-3.1-8b-instant', 'llama3-70b-8192', 'llama3-8b-8192']:
                print(f"Using Groq service for generation with model {selected_model}")
                generator = self.groq_service.generate_stream(thread.messages, model=selected_model)
            else:
                print("Using GPT service for generation")
                generator = self.gpt_service.generate_stream(thread.messages)

            assistant_message = ""
            for chunk in generator:
                assistant_message += chunk
                yield f"data: {json.dumps({'text': chunk})}\n\n"

            # Generate speech from the assistant's message
            speech_data = None
            if selected_model == 'playai-tts':  # Only generate speech for PlayAI TTS model
                try:
                    # First try to store in DB
                    speech_data = self.tts_service.generate_speech(assistant_message, store_in_db=True)
                except Exception as e:
                    if 'document too large' in str(e).lower():
                        # If DB storage fails due to size, use file storage instead
                        print("Audio file too large for DB storage, using file storage instead")
                        speech_data = self.tts_service.generate_speech(assistant_message, store_in_db=False)
                    else:
                        # If it's another error, re-raise it
                        raise e

            # Save the complete message to thread with speech data if available
            message_data = {"role": "assistant", "content": assistant_message}
            if speech_data:
                message_data["speech"] = speech_data

            try:
                # Update the thread with the new message and update the timestamp
                now = datetime.utcnow()
                thread.messages.append(message_data)
                thread.updated_at = now
                thread.save()
            except Exception as e:
                if 'document too large' in str(e).lower():
                    # If saving to DB fails due to size, regenerate with file storage
                    print("Message with audio too large for DB, regenerating with file storage")
                    speech_data = self.tts_service.generate_speech(assistant_message, store_in_db=False)
                    message_data["speech"] = speech_data
                    thread.messages.append(message_data)
                    thread.updated_at = datetime.utcnow()
                    thread.save()
                else:
                    # If it's another error, re-raise it
                    raise e

            # Generate new title after 2 or more messages
            if len(thread.messages) >= 2:
                thread = self.thread_rename_service.rename_thread(thread)

            # Include speech data in the response if available
            response_data = {
                'full_message': assistant_message,
                'thread_id': str(thread.id),
                'title': thread.title
            }
            if speech_data:
                response_data['speech'] = speech_data

            # Send the final response data
            yield f"data: {json.dumps(response_data)}\n\n"

            # Only log model usage after the entire response is complete and sent
            # This prevents duplicate logging
            print("Response complete, now logging model usage")
            try:
                result = ModelUsage.log_usage(
                    user_id=user.id,
                    model_name=selected_model,
                    service='chat'
                )
                if result is False or (isinstance(result, dict) and result.get('status') is False):
                    balance = result.get('balance', 0) if isinstance(result, dict) else 0
                    cost = result.get('cost', 0) if isinstance(result, dict) else 0
                    
                    print(f"Warning: User {user.id} has insufficient credits to use model {selected_model}. Balance: {balance}, Cost: {cost}")
                    
                    # If balance is 0, return a special error code that will be handled as 404
                    if balance == 0:
                        error_message = "You've run out of credits. Please add more credits to continue using AI features."
                        yield f'data: {json.dumps({"error": error_message, "insufficient_credits": True, "zero_balance": True})}\n\n'
                    else:
                        yield f"data: {json.dumps({'error': 'You have insufficient credits to use this model. Please add more credits or use a different model.', 'insufficient_credits': True})}\n\n"
                elif result is None:
                    print(f"Error: Failed to log model usage for user {user.id}, model {selected_model}")
                else:
                    print(f"Successfully logged model usage and deducted credits for user {user.id}, model {selected_model}")
            except Exception as e:
                import traceback
                print(f"Exception logging model usage: {str(e)}")
                print(f"Traceback: {traceback.format_exc()}")

        except Exception as e:
            print(f"Error generating response: {str(e)}")
            error_message = f"Error generating response: {str(e)}"
            yield f"data: {json.dumps({'error': error_message})}\n\n"

    def _get_or_create_thread(self, thread_id, user):
        if thread_id:
            thread = Thread.objects(id=thread_id, user=user.id).first()
            if thread:
                return thread
        return Thread(user=user.id)



