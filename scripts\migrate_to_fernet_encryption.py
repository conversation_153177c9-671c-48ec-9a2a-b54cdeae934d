"""
Migration script to re-encrypt existing friend chat messages with Fernet encryption

This script:
1. Finds all friend chats in the database
2. For each chat, decrypts any messages using the old Base64 encoding
3. Re-encrypts them using the new Fernet encryption
4. Updates the messages in the database

Usage:
    python scripts/migrate_to_fernet_encryption.py
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/encryption_migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

# Import models after setting up path
from models.friend_chat import FriendChat
from utils.encryption import MessageEncryption
from mongoengine import connect
import os

def migrate_to_fernet_encryption():
    """Migrate all friend chat messages to use Fernet encryption"""
    # Connect to MongoDB
    mongo_uri = os.environ.get('MONGO_URI', 'mongodb://localhost:27017/kevko')
    connect(host=mongo_uri)
    
    logging.info("Starting migration to Fernet encryption")
    
    # Get all friend chats
    chats = FriendChat.objects()
    total_chats = chats.count()
    logging.info(f"Found {total_chats} chats to process")
    
    # Track statistics
    total_messages = 0
    encrypted_messages = 0
    skipped_messages = 0
    error_messages = 0
    
    # Process each chat
    for i, chat in enumerate(chats):
        logging.info(f"Processing chat {i+1}/{total_chats}: {chat.chat_id}")
        
        # Process messages in this chat
        updated = False
        for message in chat.messages:
            total_messages += 1
            
            # Skip messages that are already using Fernet encryption
            if message.get('content', '').startswith("FERN_"):
                skipped_messages += 1
                continue
            
            # Skip system messages that don't have content
            if not message.get('content'):
                skipped_messages += 1
                continue
            
            try:
                # Get the original content by decrypting if needed
                content = message['content']
                if message.get('encrypted', False) and content.startswith("ENC_"):
                    # Decrypt using the old method
                    content = FriendChat.decrypt_message(content, chat.chat_id)
                
                # Re-encrypt using the new Fernet method
                encrypted_content = MessageEncryption.encrypt_message(content, chat.chat_id)
                
                # Update the message
                message['content'] = encrypted_content
                message['encrypted'] = True
                
                encrypted_messages += 1
                updated = True
                
                logging.debug(f"Re-encrypted message in chat {chat.chat_id}")
            except Exception as e:
                logging.error(f"Error re-encrypting message in chat {chat.chat_id}: {str(e)}")
                error_messages += 1
        
        # Save the chat if any messages were updated
        if updated:
            try:
                chat.save()
                logging.info(f"Saved updated chat {chat.chat_id}")
            except Exception as e:
                logging.error(f"Error saving chat {chat.chat_id}: {str(e)}")
    
    # Log summary
    logging.info("Migration complete!")
    logging.info(f"Total chats processed: {total_chats}")
    logging.info(f"Total messages: {total_messages}")
    logging.info(f"Messages re-encrypted: {encrypted_messages}")
    logging.info(f"Messages skipped: {skipped_messages}")
    logging.info(f"Errors: {error_messages}")

if __name__ == "__main__":
    migrate_to_fernet_encryption()
