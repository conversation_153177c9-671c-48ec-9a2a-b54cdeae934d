/**
 * AdminUpdates.js
 * Handles the admin updates functionality
 */
class AdminUpdates {
    constructor() {
        this.updates = [];
        this.services = [];
        this.currentUpdateId = null;
        this.initialized = false;
        this.scrollPosition = 0; // Store scroll position when opening modals
    }

    /**
     * Initialize the admin updates functionality
     */
    async init() {
        if (this.initialized) {
            return;
        }

        console.log('Initializing AdminUpdates...');

        // Initialize admin check if it doesn't exist
        if (!window.adminCheck) {
            console.log('AdminCheck not available in AdminUpdates, creating instance');
            window.adminCheck = new AdminCheck();
        }

        // Wait for admin check to initialize
        await window.adminCheck.init();

        // Only proceed if user is an admin
        if (!window.adminCheck.isUserAdmin()) {
            console.log('User is not an admin, skipping AdminUpdates initialization');
            return;
        }

        // Set up event listeners
        this.setupEventListeners();

        // Load available services
        await this.loadServices();

        // Load updates
        await this.loadUpdates();

        this.initialized = true;
        console.log('AdminUpdates initialized');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Publish update button
        const publishBtn = document.getElementById('publishUpdateBtn');
        if (publishBtn) {
            publishBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showPublishModal();
            });
        }

        // Publish update form submit
        const publishSubmitBtn = document.getElementById('publishUpdateSubmit');
        if (publishSubmitBtn) {
            publishSubmitBtn.addEventListener('click', () => this.publishUpdate());
        }

        // Delete update confirm button
        const deleteConfirmBtn = document.getElementById('deleteUpdateConfirm');
        if (deleteConfirmBtn) {
            deleteConfirmBtn.addEventListener('click', () => this.deleteUpdate());
        }

        // Modal close buttons
        document.querySelectorAll('.modal-close, .modal-cancel').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal);
                }
            });
        });

        // Modal overlay click to close
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', (e) => {
                // Only close if the click was directly on the overlay, not on its children
                if (e.target === overlay) {
                    const modal = overlay.closest('.modal');
                    if (modal) {
                        this.closeModal(modal);
                    }
                }
            });
        });

        // Close modals with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal:not(.hidden)');
                if (openModals.length > 0) {
                    this.closeModal(openModals[openModals.length - 1]);
                }
            }
        });
    }

    /**
     * Close a modal with animation
     * @param {HTMLElement} modal The modal element to close
     */
    closeModal(modal) {
        if (!modal) return;

        // Add a class for the closing animation
        modal.classList.add('closing');

        // Immediately disable scrolling on the overlay to prevent interaction
        const overlay = modal.querySelector('.modal-overlay');
        if (overlay) {
            overlay.style.pointerEvents = 'none';
            overlay.style.opacity = '0'; // Immediately hide the overlay
        }

        // Restore body scrolling immediately
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Restore scroll position if needed
        if (this.scrollPosition !== undefined) {
            window.scrollTo(0, this.scrollPosition);
        }

        // Wait for the animation to complete before hiding
        setTimeout(() => {
            modal.classList.remove('closing');
            modal.classList.add('hidden');

            // Reset overlay pointer events
            if (overlay) {
                overlay.style.pointerEvents = '';
                overlay.style.opacity = ''; // Reset opacity
            }

            // Move modal back to its original container if needed
            // This is optional and depends on your DOM structure
            const settingsSection = document.querySelector('.admin-updates-section');
            if (settingsSection && modal.parentElement === document.body) {
                // Only move it back if there's a specific container for it
                // This is just a safeguard in case you need it
            }
        }, 150); // Reduced for faster closing
    }

    /**
     * Load available services for the dropdown
     */
    async loadServices() {
        try {
            const response = await fetch('/api/admin/updates/services');

            // Handle different response formats
            let services = [];
            try {
                const data = await response.json();

                // Check if the response is an array or has a services property
                if (Array.isArray(data)) {
                    services = data;
                } else if (data.success && Array.isArray(data.services)) {
                    services = data.services;
                } else if (data.error) {
                    throw new Error(data.error);
                }
            } catch (e) {
                console.error('Error parsing services response:', e);
                // Fallback to default services if API fails
                services = [
                    {id: 'kevkoAI', name: 'KevkoAI'},
                    {id: 'kevkoFy', name: 'KevkoFy'},
                    {id: 'kevkoHome', name: 'KevkoHome'},
                    {id: 'kevkoNotes', name: 'KevkoNotes'},
                    {id: 'kevkoCloud', name: 'KevkoCloud'},
                    {id: 'kevkoWeather', name: 'KevkoWeather'},
                    {id: 'system', name: 'System'}
                ];
            }

            this.services = services;
            this.updateServicesDropdown();

        } catch (error) {
            console.error('Error loading services:', error);
            // Show error notification
            this.showNotification('Failed to load services. Using default services instead.', 'error');

            // Fallback to default services
            this.services = [
                {id: 'kevkoAI', name: 'KevkoAI'},
                {id: 'kevkoFy', name: 'KevkoFy'},
                {id: 'kevkoHome', name: 'KevkoHome'},
                {id: 'kevkoNotes', name: 'KevkoNotes'},
                {id: 'kevkoCloud', name: 'KevkoCloud'},
                {id: 'kevkoWeather', name: 'KevkoWeather'},
                {id: 'system', name: 'System'}
            ];
            this.updateServicesDropdown();
        }
    }

    /**
     * Update the services dropdown
     */
    updateServicesDropdown() {
        const dropdown = document.getElementById('updateServiceId');
        if (!dropdown) return;

        // Clear existing options except the first one
        while (dropdown.options.length > 1) {
            dropdown.remove(1);
        }

        // Group services by category
        const categories = {
            'Core Services': ['kevkoAI', 'kevkoFy', 'kevkoHome'],
            'Productivity': ['kevkoNotes', 'kevkoCloud'],
            'Utilities': ['kevkoWeather', 'system']
        };

        // Add services to dropdown with optgroup for categories
        Object.entries(categories).forEach(([category, serviceIds]) => {
            const group = document.createElement('optgroup');
            group.label = category;

            // Add services in this category
            serviceIds.forEach(serviceId => {
                const service = this.services.find(s => s.id === serviceId);
                if (service) {
                    const option = document.createElement('option');
                    option.value = service.id;
                    option.textContent = service.name;
                    group.appendChild(option);
                }
            });

            dropdown.appendChild(group);
        });

        // Add any services not in categories
        const categorizedIds = Object.values(categories).flat();
        const uncategorizedServices = this.services.filter(s => !categorizedIds.includes(s.id));

        if (uncategorizedServices.length > 0) {
            const group = document.createElement('optgroup');
            group.label = 'Other Services';

            uncategorizedServices.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = service.name;
                group.appendChild(option);
            });

            dropdown.appendChild(group);
        }
    }

    /**
     * Load updates from the server
     */
    async loadUpdates() {
        try {
            const response = await fetch('/api/admin/updates');

            // Handle different response formats
            let updates = [];
            try {
                const data = await response.json();

                // Check if the response is an array or has an updates property
                if (Array.isArray(data)) {
                    updates = data;
                } else if (data.success && Array.isArray(data.updates)) {
                    updates = data.updates;
                } else if (data.error) {
                    throw new Error(data.error);
                }
            } catch (e) {
                console.error('Error parsing updates response:', e);
                updates = [];
            }

            this.updates = updates;
            this.renderUpdates();

        } catch (error) {
            console.error('Error loading updates:', error);
            this.showNotification('Failed to load updates. Please try again later.', 'error');
            this.updates = [];
            this.renderUpdates();
        }
    }

    /**
     * Render updates in the UI
     */
    renderUpdates() {
        const container = document.getElementById('adminUpdatesList');
        if (!container) return;

        if (this.updates.length === 0) {
            container.innerHTML = `
                <div class="admin-updates-empty">
                    <i data-lucide="megaphone" class="h-8 w-8 text-slate-600"></i>
                    <div class="admin-updates-empty-title">No updates yet</div>
                    <div class="admin-updates-empty-text">Publish your first service update to keep users informed.</div>
                </div>
            `;
        } else {
            container.innerHTML = this.updates.map(update => this.createUpdateCard(update)).join('');
        }

        // Initialize delete buttons
        document.querySelectorAll('.update-delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const updateId = e.currentTarget.dataset.updateId;
                this.showDeleteConfirmation(updateId);
            });
        });

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * Create an update card HTML
     * @param {Object} update The update object
     * @returns {string} HTML string
     */
    createUpdateCard(update) {
        // Find service color and icon
        const service = this.services.find(s => s.id === update.service_id);
        const serviceColor = service ? this.getServiceColor(service.id) : 'slate';
        const serviceIcon = service ? this.getServiceIcon(service.id) : 'box';

        // Format date
        const date = new Date(update.published_at);
        const formattedDate = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        return `
            <div class="update-card">
                <div class="update-card-header">
                    <div class="update-card-service">
                        <div class="update-card-service-icon bg-${serviceColor}-500/10">
                            <i data-lucide="${serviceIcon}" class="h-4 w-4 text-${serviceColor}-500"></i>
                        </div>
                        <span>${service ? service.name : update.service_id}</span>
                    </div>
                    <div class="update-card-date">${formattedDate}</div>
                </div>
                <div class="update-card-title">${update.title}</div>
                <div class="update-card-description">${update.description}</div>
                <div class="update-card-footer">
                    <div class="update-card-meta">
                        ${update.version ? `<span class="update-card-version">${update.version}</span>` : ''}
                        <span class="update-card-author">
                            <i data-lucide="user" class="h-3 w-3"></i>
                            ${update.published_by}
                        </span>
                    </div>
                    <div class="update-card-actions">
                        <button class="update-card-action delete update-delete-btn" data-update-id="${update.id}">
                            <i data-lucide="trash-2" class="h-4 w-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get service color based on service ID
     * @param {string} serviceId Service ID
     * @returns {string} Color name
     */
    getServiceColor(serviceId) {
        const colorMap = {
            'kevkoAI': 'purple',
            'kevkoFy': 'cyan',
            'kevkoHome': 'amber',
            'kevkoNotes': 'pink',
            'kevkoCloud': 'green',
            'kevkoWeather': 'blue',
            'system': 'slate'
        };
        return colorMap[serviceId] || 'slate';
    }

    /**
     * Get service icon based on service ID
     * @param {string} serviceId Service ID
     * @returns {string} Icon name
     */
    getServiceIcon(serviceId) {
        const iconMap = {
            'kevkoAI': 'bot',
            'kevkoFy': 'music',
            'kevkoHome': 'home',
            'kevkoNotes': 'clipboard',
            'kevkoCloud': 'cloud',
            'kevkoWeather': 'cloud-sun',
            'system': 'settings'
        };
        return iconMap[serviceId] || 'box';
    }

    /**
     * Show the publish update modal
     */
    showPublishModal() {
        // Reset form
        const form = document.getElementById('publishUpdateForm');
        if (form) {
            form.reset();
        }

        // Show modal with animation
        const modal = document.getElementById('publishUpdateModal');
        if (modal) {
            // Save current scroll position
            this.scrollPosition = window.scrollY || document.documentElement.scrollTop;

            // Ensure modal is positioned at the root of the document
            if (modal.parentElement !== document.body) {
                document.body.appendChild(modal);
            }

            // Remove hidden class to show the modal
            modal.classList.remove('hidden');

            // Prevent body scrolling while modal is open
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = '15px'; // Prevent layout shift

            // Focus the first input after a short delay to allow animation to complete
            setTimeout(() => {
                const firstInput = modal.querySelector('select, input, textarea');
                if (firstInput) {
                    firstInput.focus();
                }

                // Ensure modal container is properly centered in the viewport
                const container = modal.querySelector('.modal-container');
                if (container) {
                    container.style.position = 'fixed';
                    container.style.top = '50%';
                    container.style.left = '50%';
                    container.style.transform = 'translate(-50%, -50%)';
                    container.style.zIndex = '10000';
                }
            }, 50);

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons();
            }
        }
    }

    /**
     * Show delete confirmation modal
     * @param {string} updateId The update ID to delete
     */
    showDeleteConfirmation(updateId) {
        this.currentUpdateId = updateId;

        // Show modal with animation
        const modal = document.getElementById('deleteUpdateModal');
        if (modal) {
            // Save current scroll position
            this.scrollPosition = window.scrollY || document.documentElement.scrollTop;

            // Ensure modal is positioned at the root of the document
            if (modal.parentElement !== document.body) {
                document.body.appendChild(modal);
            }

            // Remove hidden class to show the modal
            modal.classList.remove('hidden');

            // Prevent body scrolling while modal is open
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = '15px'; // Prevent layout shift

            // Focus the delete button after a short delay
            setTimeout(() => {
                const deleteBtn = modal.querySelector('#deleteUpdateConfirm');
                if (deleteBtn) {
                    deleteBtn.focus();
                }

                // Ensure modal container is properly centered in the viewport
                const container = modal.querySelector('.modal-container');
                if (container) {
                    container.style.position = 'fixed';
                    container.style.top = '50%';
                    container.style.left = '50%';
                    container.style.transform = 'translate(-50%, -50%)';
                    container.style.zIndex = '10000';
                }
            }, 50);

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons();
            }
        }
    }

    /**
     * Publish a new update
     */
    async publishUpdate() {
        try {
            // Get form data
            const form = document.getElementById('publishUpdateForm');
            if (!form) return;

            const serviceId = document.getElementById('updateServiceId').value;
            const title = document.getElementById('updateTitle').value;
            const description = document.getElementById('updateDescription').value;
            const version = document.getElementById('updateVersion').value;

            // Validate required fields
            if (!serviceId || !title || !description) {
                alert('Please fill in all required fields');
                return;
            }

            // Disable submit button
            const submitBtn = document.getElementById('publishUpdateSubmit');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i data-lucide="loader" class="h-4 w-4 animate-spin"></i> Publishing...';
                if (window.lucide) lucide.createIcons();
            }

            // Send request
            const response = await fetch('/api/admin/updates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    service_id: serviceId,
                    title,
                    description,
                    version
                })
            });

            const data = await response.json();

            // Re-enable submit button
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Publish';
            }

            if (data.success) {
                // Hide modal with animation
                const modal = document.getElementById('publishUpdateModal');
                if (modal) {
                    this.closeModal(modal);
                }

                // Reload updates
                await this.loadUpdates();

                // Show success message
                if (window.dashboard && typeof window.dashboard.showToast === 'function') {
                    window.dashboard.showToast('Update published successfully');
                } else {
                    alert('Update published successfully');
                }
            } else {
                console.error('Error publishing update:', data.error);
                alert(`Error publishing update: ${data.error}`);
            }
        } catch (error) {
            console.error('Error publishing update:', error);
            alert(`Error publishing update: ${error.message}`);

            // Re-enable submit button
            const submitBtn = document.getElementById('publishUpdateSubmit');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Publish';
            }
        }
    }

    /**
     * Delete an update
     */
    async deleteUpdate() {
        if (!this.currentUpdateId) return;

        try {
            // Disable delete button
            const deleteBtn = document.getElementById('deleteUpdateConfirm');
            if (deleteBtn) {
                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '<i data-lucide="loader" class="h-4 w-4 animate-spin"></i> Deleting...';
                if (window.lucide) lucide.createIcons();
            }

            // Send request
            const response = await fetch(`/api/admin/updates/${this.currentUpdateId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            let data;
            try {
                data = await response.json();
            } catch (e) {
                // If the response is not JSON, create a default response
                data = { success: response.ok };
            }

            // Re-enable delete button
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = 'Delete';
            }

            if (response.ok || (data && data.success)) {
                // Hide modal with animation
                const modal = document.getElementById('deleteUpdateModal');
                if (modal) {
                    this.closeModal(modal);
                }

                // Remove the deleted update from the local array
                this.updates = this.updates.filter(update => update.id !== this.currentUpdateId);

                // Re-render updates without making another API call
                this.renderUpdates();

                // Show success message
                if (window.dashboard && typeof window.dashboard.showToast === 'function') {
                    window.dashboard.showToast('Update deleted successfully');
                } else {
                    // Create a custom toast notification
                    this.showNotification('Update deleted successfully', 'success');
                }
            } else {
                console.error('Error deleting update:', data?.error || 'Unknown error');
                this.showNotification(`Error deleting update: ${data?.error || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            console.error('Error deleting update:', error);
            this.showNotification(`Error deleting update: ${error.message}`, 'error');

            // Re-enable delete button
            const deleteBtn = document.getElementById('deleteUpdateConfirm');
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = 'Delete';
            }
        }

        // Reset current update ID
        this.currentUpdateId = null;
    }

    /**
     * Show a notification
     * @param {string} message The message to show
     * @param {string} type The type of notification (success, error, info)
     */
    showNotification(message, type = 'info') {
        // Check if notification container exists, create if not
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            container.style.position = 'fixed';
            container.style.top = '1rem';
            container.style.right = '1rem';
            container.style.zIndex = '9999';
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.gap = '0.5rem';
            document.body.appendChild(container);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.style.padding = '0.75rem 1rem';
        notification.style.borderRadius = '0.5rem';
        notification.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.gap = '0.75rem';
        notification.style.maxWidth = '24rem';
        notification.style.animation = 'fadeIn 0.3s ease';

        // Set styles based on type
        if (type === 'success') {
            notification.style.backgroundColor = 'rgba(16, 185, 129, 0.9)';
            notification.style.color = '#ffffff';
            notification.innerHTML = `<i data-lucide="check-circle" class="h-5 w-5"></i> ${message}`;
        } else if (type === 'error') {
            notification.style.backgroundColor = 'rgba(239, 68, 68, 0.9)';
            notification.style.color = '#ffffff';
            notification.innerHTML = `<i data-lucide="alert-circle" class="h-5 w-5"></i> ${message}`;
        } else {
            notification.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';
            notification.style.color = '#ffffff';
            notification.innerHTML = `<i data-lucide="info" class="h-5 w-5"></i> ${message}`;
        }

        // Add to container
        container.appendChild(notification);

        // Initialize icons
        if (window.lucide) lucide.createIcons();

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                notification.remove();

                // Remove container if empty
                if (container.children.length === 0) {
                    container.remove();
                }
            }, 300);
        }, 3000);
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Create global instance
    window.adminUpdates = new AdminUpdates();

    // Initialize when admin panel is shown
    document.addEventListener('view-changed', (e) => {
        if (e.detail.view === 'settings' || e.detail.view === 'admin') {
            window.adminUpdates.init();
        }
    });
    
    // Also check if we're already on the admin page when the script loads
    const adminSection = document.querySelector('.admin-updates-section');
    if (adminSection) {
        console.log('Admin updates section found, initializing...');
        window.adminUpdates.init();
    }
});
