from flask import jsonify, request, Response, stream_with_context
from flask_login import login_required, current_user

from models.thread import Thread
from . import chat_api
from services.chat import ChatService
from utils.decorators import check_service_access, check_message_limit
from utils.api_logger import log_api_request
import logging

chat_service = ChatService()

@chat_api.route('/send', methods=['POST'])
@login_required
@check_service_access('chat')
@check_message_limit()
@log_api_request('thread_interaction', 'chat')
def send_message():
    """Send a message to a thread and get AI response"""
    data = request.json
    thread_id = data.get('thread_id')
    selected_model = data.get('model', 'gpt-4o-mini')
    images = data.get('images', [])

    # Check if thread exists
    if thread_id:
        thread = Thread.objects(id=thread_id, user=current_user.id).first()
        if not thread:
            return jsonify({'error': 'Thread not found'}), 404

    # If images are present, force Gemini model (unless PlayAI TTS is selected)
    if images and selected_model != 'playai-tts':
        selected_model = 'gemini-2.0-flash'

    logging.info(f"Chat API: Processing message with model {selected_model}")

    # Check user credit balance before processing
    from models.user_credit import UserCredit
    from models.model_credit_cost import ModelCreditCost
    
    user_credit = UserCredit.get_or_create(current_user.id)
    
    # Get the credit cost for the selected model
    credit_cost = ModelCreditCost.get_cost(selected_model)
    
    # Check if user has sufficient credits for this model
    if user_credit:
        if user_credit.balance == 0:
            # If user has zero credits, return 404 with a specific message
            return jsonify({
                'error': 'You\'ve run out of credits. Please add more credits to continue using AI features.',
                'insufficient_credits': True,
                'zero_balance': True
            }), 404
        elif user_credit.balance < credit_cost:
            # If user has credits but not enough for this model, return 404
            return jsonify({
                'error': f'You have insufficient credits to use this model. Please add more credits or use a different model.',
                'insufficient_credits': True,
                'balance': user_credit.balance,
                'cost': credit_cost
            }), 404
    
    # Only proceed if user has sufficient credits
    return Response(
        stream_with_context(chat_service.generate_response(data, current_user)),
        mimetype='text/event-stream'
    )