/**
 * Profile Page JavaScript - Enhanced Modern UI
 * Handles the profile page functionality with advanced animations and interactions
 * Redesigned for better visual appeal and performance
 */
document.addEventListener('DOMContentLoaded', function() {
    // Load profile data
    const username = profileData.username;
    
    // Initialize UI elements with enhanced animations
    initializeUI();
    
    // Initialize online status tracking
    initializeOnlineStatus();
    
    // Theme toggle removed as per requirements
    
    // Set banner color based on profile picture with enhanced gradients
    const bannerElement = document.querySelector('.profile-banner');
    const profilePicture = document.querySelector('.profile-picture');
    
    if (bannerElement && profilePicture) {
        // Create a canvas to analyze the image with improved color extraction
        extractColorsFromImage(profilePicture, (colors) => {
            // Create a vibrant gradient from the extracted colors
            if (colors && colors.length >= 2) {
                // Use the first two dominant colors for the gradient
                const primaryColor = colors[0];
                const secondaryColor = colors[1];
                const accentColor = colors[2] || colors[0];
                
                // Create a more dynamic gradient with the extracted colors
                bannerElement.style.background = `linear-gradient(135deg, 
                    rgb(${primaryColor[0]}, ${primaryColor[1]}, ${primaryColor[2]}) 0%, 
                    rgb(${secondaryColor[0]}, ${secondaryColor[1]}, ${secondaryColor[2]}) 100%)`;
                
                // Add enhanced background properties for better animation
                bannerElement.style.backgroundSize = '300% 300%';
                
                // Apply accent color to decorative elements with improved visual effect
                document.querySelectorAll('.profile-decoration').forEach((el, index) => {
                    // Alternate between primary and accent colors for more visual interest
                    const useColor = index % 2 === 0 ? accentColor : primaryColor;
                    el.style.background = `radial-gradient(circle, rgba(${useColor[0]}, ${useColor[1]}, ${useColor[2]}, 0.4) 0%, transparent 70%)`;
                    
                    // Add subtle animation delay for each decoration
                    el.style.animationDelay = `${index * 0.5}s`;
                });
                
                // Add subtle text color adjustments based on extracted colors
                adjustTextColors(primaryColor, secondaryColor);
            } else {
                // Enhanced fallback gradient if color extraction fails
                // Generate a consistent color based on username with better algorithm
                let hash = 0;
                for (let i = 0; i < username.length; i++) {
                    hash = username.charCodeAt(i) + ((hash << 5) - hash);
                }
                
                // Convert to vibrant RGB
                const r = Math.min(255, Math.max(50, (hash & 0xFF0000) >> 16));
                const g = Math.min(255, Math.max(50, (hash & 0x00FF00) >> 8));
                const b = Math.min(255, Math.max(50, hash & 0x0000FF));
                
                // Create complementary color for better contrast
                const r2 = (r + 120) % 255;
                const g2 = (g + 120) % 255;
                const b2 = (b + 120) % 255;
                
                // Set a more dynamic gradient with the generated colors
                bannerElement.style.background = `linear-gradient(135deg, 
                    rgb(${r}, ${g}, ${b}) 0%, 
                    rgb(${r2}, ${g2}, ${b2}) 100%)`;
                
                // Add enhanced background properties
                bannerElement.style.backgroundSize = '300% 300%';
            }
            
            // Add enhanced decorative elements to the banner
            addBannerDecorations(bannerElement);
        });
    }
    
    // Load mutual friends if enabled with improved loading animation
    if (profileData.showFriends) {
        loadMutualFriends(username);
    }
    
    // Add timestamp with enhanced formatting
    addTimestamp();
    
    // Add enhanced scroll animations with intersection observer
    addScrollAnimations();
    
    // Add parallax effect to decorative elements
    addParallaxEffects();
});

/**
 * Initialize UI with enhanced entrance animations
 * Uses improved timing and easing functions for a more polished look
 */
function initializeUI() {
    // Animate profile card entrance with improved animation
    const profileCard = document.querySelector('.profile-card');
    if (profileCard) {
        profileCard.style.opacity = '0';
        profileCard.style.transform = 'translateY(30px) scale(0.98)';
        profileCard.style.transition = 'opacity 0.8s cubic-bezier(0.16, 1, 0.3, 1), transform 0.8s cubic-bezier(0.16, 1, 0.3, 1)';
        
        setTimeout(() => {
            profileCard.style.opacity = '1';
            profileCard.style.transform = 'translateY(0) scale(1)';
        }, 100);
    }
    
    // Stagger animate profile sections with improved timing
    const sections = document.querySelectorAll('.profile-section');
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(40px) scale(0.96)';
        section.style.transition = 'opacity 0.7s cubic-bezier(0.16, 1, 0.3, 1), transform 0.7s cubic-bezier(0.16, 1, 0.3, 1)';
        
        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0) scale(1)';
        }, 400 + (index * 180));
    });
    
    // Animate profile picture with subtle pop effect
    const profilePicture = document.querySelector('.profile-picture-container');
    if (profilePicture) {
        profilePicture.style.opacity = '0';
        profilePicture.style.transform = 'scale(0.8) rotate(-8deg)';
        profilePicture.style.transition = 'opacity 1s cubic-bezier(0.34, 1.56, 0.64, 1), transform 1s cubic-bezier(0.34, 1.56, 0.64, 1)';
        
        setTimeout(() => {
            profilePicture.style.opacity = '1';
            profilePicture.style.transform = 'scale(1) rotate(-3deg)';
        }, 300);
    }
    
    // Animate username and tag with staggered fade in
    const username = document.querySelector('.profile-username');
    const userTag = document.querySelector('.profile-tag');
    
    if (username) {
        username.style.opacity = '0';
        username.style.transform = 'translateY(15px)';
        username.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            username.style.opacity = '1';
            username.style.transform = 'translateY(0)';
        }, 500);
    }
    
    if (userTag) {
        userTag.style.opacity = '0';
        userTag.style.transform = 'translateY(10px)';
        userTag.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            userTag.style.opacity = '1';
            userTag.style.transform = 'translateY(0)';
        }, 700);
    }
    
    // Update friends count in stats with animation
    updateFriendsCount();
    
    // Add subtle hover effects to interactive elements
    addInteractiveEffects();
}

/**
 * Add interactive effects to various elements on the profile page
 * Enhances user experience with subtle animations and transitions
 */
function addInteractiveEffects() {
    // Add hover effects to buttons
    const buttons = document.querySelectorAll('.profile-button');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = '';
            button.style.boxShadow = '';
        });
    });
    
    // Add hover effects to social links
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', () => {
            link.style.transform = 'scale(1.15)';
        });
        
        link.addEventListener('mouseleave', () => {
            link.style.transform = '';
        });
    });
    
    // Add hover effects to badges
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        badge.addEventListener('mouseenter', () => {
            badge.style.transform = 'translateY(-3px)';
            badge.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.1)';
        });
        
        badge.addEventListener('mouseleave', () => {
            badge.style.transform = '';
            badge.style.boxShadow = '';
        });
    });
    
    // Theme toggle button removed as per requirements
}

/**
 * Add decorative elements to the banner
 * @param {HTMLElement} bannerElement - The banner element
 */
function addBannerDecorations(bannerElement) {
    // Add geometric shapes for visual interest
    const shapes = [
        { type: 'circle', size: 80, top: '10%', left: '5%', opacity: 0.1 },
        { type: 'circle', size: 40, top: '70%', left: '15%', opacity: 0.15 },
        { type: 'square', size: 60, top: '20%', left: '80%', opacity: 0.1 },
        { type: 'triangle', size: 70, top: '60%', left: '85%', opacity: 0.12 }
    ];
    
    // Check if dark theme is active
    const isDarkTheme = document.body.classList.contains('dark-theme');
    const decorationColor = isDarkTheme ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.8)';
    
    shapes.forEach(shape => {
        const element = document.createElement('div');
        element.style.position = 'absolute';
        element.style.width = `${shape.size}px`;
        element.style.height = `${shape.size}px`;
        element.style.top = shape.top;
        element.style.left = shape.left;
        element.style.opacity = isDarkTheme ? shape.opacity * 1.5 : shape.opacity; // Increase visibility in dark mode
        element.style.background = decorationColor;
        element.style.zIndex = '1';
        
        if (shape.type === 'circle') {
            element.style.borderRadius = '50%';
        } else if (shape.type === 'triangle') {
            element.style.width = '0';
            element.style.height = '0';
            element.style.borderLeft = `${shape.size/2}px solid transparent`;
            element.style.borderRight = `${shape.size/2}px solid transparent`;
            element.style.borderBottom = `${shape.size}px solid rgba(255, 255, 255, 0.8)`;
            element.style.background = 'transparent';
        }
        
        // Add subtle animation
        element.style.animation = `float ${3 + Math.random() * 4}s ease-in-out infinite alternate`;
        
        bannerElement.appendChild(element);
    });
    
    // Add keyframes for floating animation if not already added
    if (!document.getElementById('float-keyframes')) {
        const style = document.createElement('style');
        style.id = 'float-keyframes';
        style.textContent = `
            @keyframes float {
                0% { transform: translateY(0) rotate(0); }
                100% { transform: translateY(-10px) rotate(5deg); }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Load mutual friends from the API
 * @param {string} username - The username to load friends for
 */
function loadMutualFriends(username) {
    fetch(`/api/friends/list/${username}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const friendsContent = document.getElementById('friends-content');
                if (data.friends && data.friends.length > 0) {
                    let friendsHtml = '<div class="friends-grid">';
                    
                    // Create an array to store promises for fetching online status
                    const statusPromises = [];
                    const friendElements = [];
                    
                    // Create HTML for each friend and prepare status fetch
                    for (const friend of data.friends) {
                        // Create a unique ID for this friend's status
                        const statusId = `friend-status-${friend.username}`;
                        
                        // Create the friend HTML element
                        const friendHtml = `
                            <div class="friend-item" data-username="${friend.username}">
                                <div class="friend-avatar-container">
                                    <img src="${friend.profile_picture || '/static/images/default-avatar.png'}" 
                                         alt="${friend.username}" class="friend-avatar">
                                    <span id="${statusId}" class="friend-status-dot loading"></span>
                                </div>
                                <div class="friend-info">
                                    <a href="/profile/${friend.username}" class="friend-name">
                                        ${friend.display_name || friend.username}
                                    </a>
                                    <span class="friend-username">@${friend.username}</span>
                                </div>
                            </div>
                        `;
                        
                        friendElements.push({
                            username: friend.username,
                            html: friendHtml,
                            statusId: statusId
                        });
                        
                        // Create a promise to fetch this friend's online status
                        const statusPromise = fetch(`/api/profile/online-status/${friend.username}`)
                            .then(response => response.json())
                            .then(statusData => {
                                return {
                                    username: friend.username,
                                    isOnline: statusData.success ? statusData.is_online : false,
                                    statusId: statusId
                                };
                            })
                            .catch(error => {
                                console.error(`Error fetching status for ${friend.username}:`, error);
                                return {
                                    username: friend.username,
                                    isOnline: false,
                                    statusId: statusId
                                };
                            });
                        
                        statusPromises.push(statusPromise);
                    }
                    
                    // Add all friend elements to the HTML
                    for (const friendElement of friendElements) {
                        friendsHtml += friendElement.html;
                    }
                    
                    friendsHtml += '</div>';
                    friendsContent.innerHTML = friendsHtml;
                    
                    // Update friends count
                    updateFriendsCount(data.friends.length);
                    
                    // Add hover effects to friend items
                    addFriendItemEffects();
                    
                    // Process all status promises and update the UI
                    Promise.all(statusPromises)
                        .then(statuses => {
                            for (const status of statuses) {
                                const statusDot = document.getElementById(status.statusId);
                                if (statusDot) {
                                    statusDot.classList.remove('loading');
                                    statusDot.classList.add(status.isOnline ? 'online' : 'offline');
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error updating friend statuses:', error);
                        });
                    
                    // Set up periodic status updates for friends (every 30 seconds)
                    setInterval(() => {
                        updateFriendStatuses(friendElements.map(f => f.username));
                    }, 30000);
                    
                } else {
                    friendsContent.innerHTML = '<p class="empty-message">No mutual friends to display.</p>';
                    updateFriendsCount(0);
                }
            }
        })
        .catch(error => {
            console.error('Error loading friends:', error);
            document.getElementById('friends-content').innerHTML = 
                '<p class="error-message">Failed to load mutual friends.</p>';
            updateFriendsCount('-');
        });
}

/**
 * Update online status for a list of friends
 * @param {Array} usernames - Array of usernames to update
 */
function updateFriendStatuses(usernames) {
    // Filter out duplicate usernames
    const uniqueUsernames = [...new Set(usernames)];
    
    // If no usernames, return early
    if (uniqueUsernames.length === 0) {
        return;
    }
    
    // Use the batch endpoint for efficiency
    fetch('/api/profile/online-status-batch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            usernames: uniqueUsernames
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status for each user
            for (const username of uniqueUsernames) {
                const statusId = `friend-status-${username}`;
                const statusDot = document.getElementById(statusId);
                
                if (statusDot && data.results[username] && data.results[username].success) {
                    const isOnline = data.results[username].is_online;
                    statusDot.classList.remove('loading', 'online', 'offline');
                    statusDot.classList.add(isOnline ? 'online' : 'offline');
                } else if (statusDot) {
                    // Handle error case
                    statusDot.classList.remove('loading', 'online');
                    statusDot.classList.add('offline');
                }
            }
        }
    })
    .catch(error => {
        console.error('Error updating friend statuses:', error);
        // Set all to offline on error
        for (const username of uniqueUsernames) {
            const statusDot = document.getElementById(`friend-status-${username}`);
            if (statusDot) {
                statusDot.classList.remove('loading', 'online');
                statusDot.classList.add('offline');
            }
        }
    });
}

/**
 * Update the friends count in the stats section
 * @param {number} count - The number of friends
 */
function updateFriendsCount(count = '-') {
    const countElement = document.getElementById('friends-count');
    if (countElement) {
        countElement.textContent = count;
    }
}

/**
 * Add hover effects to friend items
 */
function addFriendItemEffects() {
    const friendItems = document.querySelectorAll('.friend-item');
    friendItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            const avatar = item.querySelector('.friend-avatar');
            if (avatar) {
                avatar.style.transform = 'rotate(5deg) scale(1.1)';
            }
        });
        
        item.addEventListener('mouseleave', () => {
            const avatar = item.querySelector('.friend-avatar');
            if (avatar) {
                avatar.style.transform = '';
            }
        });
    });
}

/**
 * Extract dominant colors from an image
 * @param {HTMLImageElement} imgElement - The image element to analyze
 * @param {Function} callback - Callback function that receives the extracted colors
 */
function extractColorsFromImage(imgElement, callback) {
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set a timeout to handle cases where the image might not load properly
    const timeoutId = setTimeout(() => {
        console.log('Color extraction timed out, using fallback colors');
        provideFallbackColors();
    }, 3000);
    
    // Wait for the image to load
    if (imgElement.complete) {
        processImage();
    } else {
        imgElement.onload = processImage;
        imgElement.onerror = () => {
            clearTimeout(timeoutId);
            console.error('Image failed to load for color extraction');
            provideFallbackColors();
        };
    }
    
    function provideFallbackColors() {
        // Generate visually pleasing fallback colors based on our color scheme
        // Check if dark theme is active
        const isDarkTheme = document.body.classList.contains('dark-theme');
        
        const fallbackColors = isDarkTheme ? 
            [
                [76, 144, 183],  // Brighter blue for dark mode
                [233, 210, 170],  // Lighter cream for dark mode
                [244, 60, 60],    // Brighter red for dark mode
                [194, 225, 194],  // Lighter green for dark mode
                [234, 180, 196]   // Lighter pink for dark mode
            ] : 
            [
                [56, 124, 163],  // Primary blue
                [253, 230, 190],  // Secondary cream
                [224, 40, 40],    // Accent red
                [214, 245, 214],  // Light green
                [254, 200, 216]   // Pink highlight
            ];
        
        callback(fallbackColors);
    }
    
    function processImage() {
        clearTimeout(timeoutId);
        try {
            // Set canvas dimensions to match the image
            canvas.width = imgElement.naturalWidth || imgElement.width;
            canvas.height = imgElement.naturalHeight || imgElement.height;
            
            // Draw the image onto the canvas
            ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height);
            
            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;
            
            // Sample pixels at regular intervals to improve performance
            const sampleSize = Math.max(1, Math.floor(pixels.length / 4 / 1000));
            const colorCounts = {};
            
            // Count color occurrences
            for (let i = 0; i < pixels.length; i += 4 * sampleSize) {
                const r = pixels[i];
                const g = pixels[i + 1];
                const b = pixels[i + 2];
                const a = pixels[i + 3];
                
                // Skip transparent pixels
                if (a < 128) continue;
                
                // Reduce color space to improve grouping
                const quantizedR = Math.floor(r / 16) * 16;
                const quantizedG = Math.floor(g / 16) * 16;
                const quantizedB = Math.floor(b / 16) * 16;
                
                const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;
                
                if (!colorCounts[colorKey]) {
                    colorCounts[colorKey] = {
                        count: 0,
                        color: [r, g, b]
                    };
                }
                
                colorCounts[colorKey].count++;
            }
            
            // Convert to array and sort by frequency
            const colorArray = Object.values(colorCounts)
                .sort((a, b) => b.count - a.count)
                .map(item => item.color);
            
            // Filter out colors that are too light or too dark
            const filteredColors = colorArray.filter(color => {
                const brightness = (color[0] * 299 + color[1] * 587 + color[2] * 114) / 1000;
                return brightness > 30 && brightness < 230; // Not too dark or too light
            });
            
            if (filteredColors.length >= 2) {
                // Return the most common colors
                callback(filteredColors.slice(0, 5));
            } else {
                // Not enough good colors found, use fallback
                provideFallbackColors();
            }
        } catch (error) {
            console.error('Error extracting colors:', error);
            provideFallbackColors();
        }
    }
}

/**
 * Adjust text colors based on extracted colors from the profile picture
 * @param {Array} primaryColor - The primary color as RGB array [r, g, b]
 * @param {Array} secondaryColor - The secondary color as RGB array [r, g, b]
 */
function adjustTextColors(primaryColor, secondaryColor) {
    // Calculate brightness of the primary color (0-255)
    const primaryBrightness = (primaryColor[0] * 299 + primaryColor[1] * 587 + primaryColor[2] * 114) / 1000;
    
    // Calculate brightness of the secondary color
    const secondaryBrightness = (secondaryColor[0] * 299 + secondaryColor[1] * 587 + secondaryColor[2] * 114) / 1000;
    
    // Determine if we need light or dark text based on background brightness
    const useLightText = primaryBrightness < 128 || secondaryBrightness < 128;
    
    // Get text elements that should be adjusted
    const usernameElement = document.querySelector('.profile-username');
    const bioElement = document.querySelector('.profile-bio');
    const statsElements = document.querySelectorAll('.profile-stat');
    
    // Create a subtle text color based on the primary color but with better contrast
    let textColor;
    if (useLightText) {
        // Light text for dark backgrounds - slightly tinted towards the secondary color
        textColor = `rgba(${Math.min(255, secondaryColor[0] + 150)}, 
                          ${Math.min(255, secondaryColor[1] + 150)}, 
                          ${Math.min(255, secondaryColor[2] + 150)}, 0.95)`;
    } else {
        // Dark text for light backgrounds - slightly tinted towards the primary color
        textColor = `rgba(${Math.max(0, primaryColor[0] - 100)}, 
                          ${Math.max(0, primaryColor[1] - 100)}, 
                          ${Math.max(0, primaryColor[2] - 100)}, 0.85)`;
    }
    
    // Apply text color to elements
    if (usernameElement) {
        usernameElement.style.color = textColor;
        usernameElement.style.textShadow = useLightText ? 
            '0 1px 2px rgba(0, 0, 0, 0.3)' : 
            '0 1px 2px rgba(255, 255, 255, 0.2)';
    }
    
    if (bioElement) {
        bioElement.style.color = textColor;
    }
    
    // Apply slightly different shades to stats for visual interest
    statsElements.forEach((stat, index) => {
        const opacity = 0.75 + (index * 0.05);
        stat.style.color = textColor.replace('0.85', opacity.toString()).replace('0.95', opacity.toString());
    });
}

/**
 * Add a timestamp to the profile header
 */
function addTimestamp() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    
    // Create a small timestamp badge
    const timestamp = document.createElement('div');
    timestamp.className = 'profile-timestamp';
    timestamp.style.fontSize = '0.8rem';
    timestamp.style.color = 'var(--theme-primary)';
    timestamp.style.background = 'var(--theme-stats-bg)';
    timestamp.style.padding = '6px 12px';
    timestamp.style.borderRadius = '20px';
    timestamp.style.boxShadow = '0 2px 8px var(--theme-shadow)';
    timestamp.style.fontWeight = '500';
    timestamp.style.marginTop = '12px';
    timestamp.style.display = 'inline-block';
    timestamp.style.transition = 'all 0.3s ease';
    timestamp.innerHTML = `<span style="color: var(--theme-accent);">●</span> Last seen: Today at ${hours}:${minutes}`;
    
    // Add data attribute for theme tracking
    timestamp.setAttribute('data-theme-aware', 'true');
    
    // Add hover effect
    timestamp.addEventListener('mouseenter', () => {
        timestamp.style.transform = 'translateY(-3px)';
        timestamp.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    });
    
    timestamp.addEventListener('mouseleave', () => {
        timestamp.style.transform = 'translateY(0)';
        timestamp.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
    });
    
    // Insert after the status container
    const statusContainer = document.querySelector('.profile-status-container');
    if (statusContainer && statusContainer.parentNode) {
        statusContainer.parentNode.insertBefore(timestamp, statusContainer.nextSibling);
    } else {
        document.querySelector('.profile-header').appendChild(timestamp);
    }
}

/**
 * Add scroll animations to profile sections
 */
function addScrollAnimations() {
    // Only add these animations on larger screens
    if (window.innerWidth < 768) return;
    
    const sections = document.querySelectorAll('.profile-section');
    const content = document.querySelector('.profile-content');
    
    if (!content) return;
    
    content.addEventListener('scroll', () => {
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            const contentRect = content.getBoundingClientRect();
            
            // Check if section is in viewport
            if (rect.top < contentRect.bottom && rect.bottom > contentRect.top) {
                const distance = (contentRect.bottom - rect.top) / contentRect.height;
                const opacity = Math.min(1, Math.max(0.3, distance));
                const scale = Math.min(1, Math.max(0.95, 0.95 + (distance * 0.05)));
                
                section.style.opacity = opacity;
                section.style.transform = `scale(${scale})`;
            }
        });
    });
    
    // Trigger initial scroll event
    content.dispatchEvent(new Event('scroll'));
}

/**
 * Initialize theme toggle functionality
 */
/**
 * Theme toggle functionality has been removed as per requirements
 * This function is kept commented for reference
 */
/*
function initializeThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Check for saved theme preference or use the system preference
    const savedTheme = localStorage.getItem('profile-theme');
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDarkScheme.matches)) {
        document.body.classList.add('dark-theme');
    }
    
    // Toggle theme when button is clicked
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            // Toggle dark mode class on body
            document.body.classList.toggle('dark-theme');
            
            // Save preference to localStorage
            if (document.body.classList.contains('dark-theme')) {
                localStorage.setItem('profile-theme', 'dark');
                
                // Update banner decorations for dark theme
                updateBannerForDarkTheme();
            } else {
                localStorage.setItem('profile-theme', 'light');
                
                // Update banner decorations for light theme
                updateBannerForLightTheme();
            }
        });
    }
    
    // Initial banner update based on current theme
    if (document.body.classList.contains('dark-theme')) {
        updateBannerForDarkTheme();
    }
}
*/

/**
 * Update banner decorations for dark theme
 */
function updateBannerForDarkTheme() {
    // Make banner decorations more visible in dark mode
    const decorations = document.querySelectorAll('.profile-banner > div');
    decorations.forEach(decoration => {
        decoration.style.opacity = '0.15';  // Increase visibility in dark mode
    });
    
    // Adjust any other elements that need theme-specific styling
    const bannerElement = document.querySelector('.profile-banner');
    if (bannerElement) {
        bannerElement.style.filter = 'brightness(0.8) saturate(0.9)';
    }
}

/**
 * Update banner decorations for light theme
 */
function updateBannerForLightTheme() {
    // Reset banner decorations for light mode
    const decorations = document.querySelectorAll('.profile-banner > div');
    decorations.forEach(decoration => {
        decoration.style.opacity = '0.1';  // Default opacity
    });
    
    // Reset any other elements
    const bannerElement = document.querySelector('.profile-banner');
    if (bannerElement) {
        bannerElement.style.filter = 'none';
    }
}

/**
 * Add parallax effects to decorative elements
 * Creates a subtle movement effect when scrolling or moving the mouse
 */
function addParallaxEffects() {
    // Only add parallax on larger screens to avoid performance issues on mobile
    if (window.innerWidth < 768) return;
    
    const banner = document.querySelector('.profile-banner');
    const decorations = document.querySelectorAll('.profile-decoration');
    const profilePicture = document.querySelector('.profile-picture-container');
    
    // Add mouse movement parallax
    document.addEventListener('mousemove', (e) => {
        // Calculate mouse position as percentage of screen
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        // Apply subtle movement to banner background
        if (banner) {
            banner.style.backgroundPosition = `${50 + (mouseX - 0.5) * 10}% ${50 + (mouseY - 0.5) * 10}%`;
        }
        
        // Apply movement to decorative elements
        decorations.forEach((decoration, index) => {
            // Different elements move at different rates for depth effect
            const factor = 1 + (index % 3) * 0.5;
            const offsetX = (mouseX - 0.5) * 20 * factor;
            const offsetY = (mouseY - 0.5) * 20 * factor;
            
            decoration.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
        });
        
        // Apply subtle tilt to profile picture
        if (profilePicture) {
            const rotateX = (mouseY - 0.5) * -5;
            const rotateY = (mouseX - 0.5) * 5;
            profilePicture.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        }
    });
    
    // Add scroll parallax
    const content = document.querySelector('.profile-content');
    if (content) {
        content.addEventListener('scroll', () => {
            const scrollY = content.scrollTop;
            
            // Move banner elements on scroll
            if (banner) {
                banner.style.transform = `translateY(${scrollY * 0.3}px)`;
            }
            
            // Move decorations at different rates
            decorations.forEach((decoration, index) => {
                const factor = 0.1 + (index % 3) * 0.05;
                decoration.style.transform = `translateY(${scrollY * factor}px)`;
            });
        });
    }
}

/**
 * Initialize online status tracking
 * Shows online/offline status based on user activity
 * - Online: If a request was sent to the backend in the last 30 seconds
 * - Offline: If no request was sent for 30 seconds or longer
 */
function initializeOnlineStatus() {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');
    
    if (!statusDot || !statusText) return;
    
    // Function to update the UI based on online status
    function updateStatusUI(online) {
        if (online) {
            statusDot.classList.remove('offline');
            statusDot.classList.add('online');
            statusText.textContent = 'Online';
        } else {
            statusDot.classList.remove('online');
            statusDot.classList.add('offline');
            statusText.textContent = 'Offline';
        }
    }
    
    // Set initial status to loading
    statusDot.classList.remove('online', 'offline');
    statusDot.classList.add('loading');
    statusText.textContent = 'Loading...';
    
    // Get the username from the profile data
    const username = profileData.username;
    
    // Check if this is the current user's profile
    const isCurrentUser = profileData.isCurrentUser;
    
    // If this is the current user's profile, update activity status periodically
    if (isCurrentUser) {
        // Update activity immediately
        updateActivity();
        
        // Set up periodic activity updates (every 15 seconds)
        setInterval(updateActivity, 15000);
        
        // Also update on user interaction with the page
        document.addEventListener('click', updateActivity);
        document.addEventListener('keydown', updateActivity);
        document.addEventListener('mousemove', debounce(updateActivity, 1000));
    }
    
    // Create a debounced version of fetchOnlineStatus
    const debouncedFetchOnlineStatus = debounce(fetchOnlineStatus, 500);
    
    // Fetch the initial online status
    debouncedFetchOnlineStatus();
    
    // Set up periodic status checks (every 15 seconds)
    // Using a longer interval to reduce server load
    const statusInterval = setInterval(debouncedFetchOnlineStatus, 15000);
    
    // Clean up interval when page is unloaded
    window.addEventListener('beforeunload', () => {
        clearInterval(statusInterval);
    });
    
    // Track last fetch time to prevent too frequent requests
    let lastFetchTime = 0;
    
    // Function to fetch online status from the backend
    function fetchOnlineStatus() {
        // Prevent fetching too frequently (minimum 5 seconds between requests)
        const now = Date.now();
        if (now - lastFetchTime < 5000) {
            return;
        }
        lastFetchTime = now;
        
        // Use a cache buster to prevent browser caching
        const cacheBuster = `?_=${now}`;
        fetch(`/api/profile/online-status/${username}${cacheBuster}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusUI(data.is_online);
                }
            })
            .catch(error => {
                console.error('Error fetching online status:', error);
                // In case of error, default to offline
                updateStatusUI(false);
            });
    }
    
    // Function to update the current user's activity
    function updateActivity() {
        fetch('/api/profile/update-activity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                section: 'profile'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Activity updated successfully
                updateStatusUI(true);
            }
        })
        .catch(error => {
            console.error('Error updating activity:', error);
        });
    }
    
    // Debounce function to limit the frequency of activity updates
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }
}