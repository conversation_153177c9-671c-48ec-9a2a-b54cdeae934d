/**
 * CreditNotification.js
 * Handles credit-related notifications and UI interactions
 */

class CreditNotification {
    constructor() {
        this.initialized = false;
        this.modalId = 'insufficientCreditsModal';
    }

    init() {
        if (this.initialized) return;
        
        // Create modal if it doesn't exist
        if (!document.getElementById(this.modalId)) {
            this.createModal();
        }
        
        this.initialized = true;
    }

    createModal() {
        const modalHtml = `
        <div id="${this.modalId}" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Insufficient Credits
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500" id="credit-notification-message">
                                        You've run out of credits. Please add more credits to continue using AI features.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" id="addCreditsBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Add Credits
                        </button>
                        <button type="button" id="closeCreditsModalBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        // Add modal to body
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer.firstElementChild);
        
        // Add event listeners
        document.getElementById('closeCreditsModalBtn').addEventListener('click', () => {
            this.hideModal();
        });
        
        document.getElementById('addCreditsBtn').addEventListener('click', () => {
            this.hideModal();
            // Navigate to credits page or open credits modal
            if (window.openAddCreditsModal) {
                // Use our global function
                window.openAddCreditsModal();
            } else if (window.creditManager && typeof window.creditManager.showAddCreditsModal === 'function') {
                // Fallback to creditManager if available
                window.creditManager.showAddCreditsModal();
            } else {
                // Fallback to profile page with credits tab
                console.log('No modal function found, redirecting to dashboard');
                window.location.href = '/dashboard?tab=credits';
            }
        });
    }
    
    showInsufficientCreditsNotification(message = null) {
        this.init();
        
        // Update message if provided
        if (message) {
            const messageElement = document.getElementById('credit-notification-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
        
        // Show modal
        const modal = document.getElementById(this.modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }
    
    hideModal() {
        const modal = document.getElementById(this.modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }
}

// Initialize credit notification system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.creditNotification = new CreditNotification();
    
    // Initialize immediately to ensure the modal is ready
    window.creditNotification.init();
});

// Global function to show insufficient credits notification
window.showInsufficientCreditsNotification = function(message) {
    if (window.creditNotification) {
        window.creditNotification.showInsufficientCreditsNotification(message);
    } else {
        // Fallback if the notification system isn't initialized
        alert(message || "You've run out of credits. Please add more credits to continue using AI features.");
    }
};