from flask_login import UserMixin
from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, BooleanField, Q
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone

class User(UserMixin, Document):
    username = <PERSON><PERSON>ield(required=True, unique=True)
    email = StringField(required=True, unique=True)
    password_hash = StringField()
    google_id = StringField(unique=True, sparse=True)
    profile_picture = StringField()
    is_admin = <PERSON>oleanField(default=False)
    is_super_admin = <PERSON>oleanField(default=False)
    show_on_contacts = BooleanField(default=True)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    last_login = DateTimeField(default=lambda: datetime.now(timezone.utc))
    display_name = StringField(default="")
    contact_settings = StringField(default="")
    two_factor_enabled = BooleanField(default=False)
    country_code = <PERSON><PERSON>ield(default="")
    timezone = <PERSON><PERSON><PERSON>(default="")

    @staticmethod
    def normalize_username(username):
        """Normalize username to lowercase and remove special characters"""
        return ''.join(e.lower() for e in username if e.isalnum())

    def set_username(self, username):
        """Set normalized username"""
        self.username = self.normalize_username(username)

    @classmethod
    def find_by_login(cls, login):
        """Find user by either username or email"""
        normalized_login = cls.normalize_username(login)
        return cls.objects(Q(username=normalized_login) | Q(email=login)).first()

    def get_id(self):
        return str(self.id)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)

    def is_google_user(self):
        """Check if user is authenticated via Google"""
        return bool(self.google_id)

    def update_last_login(self):
        self.last_login = datetime.now(timezone.utc)
        self.save()

    def update_country_code(self, country_code):
        """Update user's country code if not already set"""
        if not self.country_code or self.country_code.strip() == "":
            self.country_code = country_code
            self.save()
            return True
        return False
        
    def update_timezone(self, timezone_str):
        """Update user's timezone"""
        if timezone_str and timezone_str.strip() != "":
            self.timezone = timezone_str
            self.save()
            return True
        return False

    meta = {
        'collection': 'users',
        'indexes': [
            'username',
            'email',
            'google_id'
        ]
    }
