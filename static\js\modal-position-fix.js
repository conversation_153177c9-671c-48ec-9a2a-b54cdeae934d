/**
 * modal-position-fix.js
 * Ensures modals are properly positioned at the center of the screen but not fixed
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize modal positioning

    // Function to ensure the add credits modal is properly positioned
    function setupAddCreditsModal() {
        const modal = document.getElementById('addCreditsModal');
        if (!modal) {
            console.error('Add credits modal not found');
            return;
        }

        // Only set up the modal once - add a data attribute to track this
        if (modal.getAttribute('data-positioned') === 'true') {
            return;
        }

        // Ensure the modal is a direct child of the body to avoid positioning issues
        if (modal.parentElement && modal.parentElement.id !== 'body') {
            document.body.appendChild(modal);
        }

        // Set proper positioning styles to ensure it's centered in the viewport
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.right = '0';
        modal.style.bottom = '0';
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
        modal.style.zIndex = '9999';

        // Make sure the modal is hidden by default
        if (!modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }

        // Mark the modal as positioned
        modal.setAttribute('data-positioned', 'true');
    }

    // Set up the modal when the page loads
    setupAddCreditsModal();

    // No MutationObserver needed - this was causing performance issues
});