{% extends "base.html" %}

{% block title %}User Not Found{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/user-not-found.css') }}">
{% endblock %}

{% block content %}
<div class="not-found-container">
    <!-- Decorative elements -->
    <div class="not-found-decoration not-found-decoration-1"></div>
    <div class="not-found-decoration not-found-decoration-2"></div>
    
    <div class="not-found-icon">
        <i class="fas fa-user-slash"></i>
    </div>
    
    <h1 class="not-found-title">User Not Found</h1>
    
    <p class="not-found-message">
        {% if message %}
            {{ message }}
        {% else %}
            We couldn't find a user with the username <span class="not-found-username">@{{ username }}</span>. 
            The user may have changed their username or the account doesn't exist.
        {% endif %}
    </p>
    
    <div class="not-found-actions">
        <a href="{{ url_for('landing') }}" class="btn btn-secondary">
            <i class="fas fa-home"></i> Go Home
        </a>
        <a href="{{ url_for('profile_page.view_profile', username=current_user.username) if current_user.is_authenticated else url_for('auth.login') }}" class="btn btn-primary">
            {% if current_user.is_authenticated %}
                <i class="fas fa-user"></i> My Profile
            {% else %}
                <i class="fas fa-sign-in-alt"></i> Sign In
            {% endif %}
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/user-not-found.js') }}"></script>
{% endblock %}