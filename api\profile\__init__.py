from flask import Blueprint

# Create API blueprint
profile_api = Blueprint('profile_api', __name__, url_prefix='/api/profile')

# Import routes after creating blueprint to avoid circular imports
from . import profile_basic
from . import profile_security
from . import profile_data
from . import profile_stats
from . import profile_cleanup
from . import profile_utils
from . import avatar
from . import customization
from . import activity
from . import stats
