from mongoengine import Document, <PERSON><PERSON>ield, IntField, DateTimeField, ReferenceField, BooleanField
from datetime import datetime, timezone
from models.user import User

class ServiceEngagement(Document):
    """
    Model for tracking user engagement with services
    Tracks when a user visits a service and stays for at least 5 minutes
    """
    user = ReferenceField(User, required=True)
    service = StringField(required=True)  # 'chat', 'live', 'spotify', etc.
    start_time = DateTimeField(default=lambda: datetime.now(timezone.utc))
    end_time = DateTimeField()
    duration_minutes = IntField()  # Duration in minutes
    is_completed = BooleanField(default=False)  # Whether the engagement was completed (user stayed for at least 5 minutes)

    meta = {
        'collection': 'service_engagement',
        'indexes': [
            'user',
            'service',
            'start_time',
            'is_completed'
        ]
    }

    @classmethod
    def start_engagement(cls, user_id, service):
        """
        Start tracking a user's engagement with a service
        """
        try:
            # Use timezone-aware datetime
            engagement = cls(
                user=user_id,
                service=service,
                start_time=datetime.now(timezone.utc)
            )
            engagement.save()
            return engagement
        except Exception as e:
            print(f"Error starting service engagement: {str(e)}")
            return None

    @classmethod
    def complete_engagement(cls, engagement_id, end_time=None):
        """
        Complete a user's engagement with a service
        """
        try:
            engagement = cls.objects.get(id=engagement_id)
            engagement.end_time = end_time or datetime.now(timezone.utc)

            # Calculate duration in minutes
            # Ensure both datetimes are timezone-aware
            start_time = engagement.start_time
            end_time = engagement.end_time

            # Add timezone info if missing
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            if end_time.tzinfo is None:
                end_time = end_time.replace(tzinfo=timezone.utc)

            duration = (end_time - start_time).total_seconds() / 60
            engagement.duration_minutes = int(duration)

            # Mark as completed if duration is at least 5 minutes
            engagement.is_completed = duration >= 5

            engagement.save()
            return engagement
        except Exception as e:
            print(f"Error completing service engagement: {str(e)}")
            return None
