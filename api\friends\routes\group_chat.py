"""
Group chat routes.
This module contains routes for managing group chats.
"""
from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from .. import friends_api
from models.user import User
from models.friend_relationship import FriendRelationship
from models.group_chat import GroupChat
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
import logging


@friends_api.route('/group-chats', methods=['GET'])
@login_required
@check_service_access('friends')
def get_group_chats():
    """Get all group chats for the current user"""
    group_chats = GroupChat.get_chats_for_user(current_user.id)

    # Convert to list of dictionaries
    group_chat_list = []
    for chat in group_chats:
        # Get basic info for the group chat list
        group_chat_list.append({
            'chat_id': chat.chat_id,
            'name': chat.name,
            'creator_id': str(chat.creator.id),
            'creator_name': chat.creator.username,
            'member_count': len(chat.members),
            'last_message': chat.messages[-1] if chat.messages else None,
            'created_at': chat.created_at.isoformat(),
            'updated_at': chat.updated_at.isoformat(),
            'profile_picture': chat.profile_picture  # Include the profile picture
        })

    return jsonify(group_chat_list)


@friends_api.route('/group-chat', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('group_chat_create', 'friends')
def create_group_chat():
    """Create a new group chat"""
    data = request.json
    name = data.get('name', 'Group Chat')
    member_ids = data.get('member_ids', [])

    if not member_ids:
        return jsonify({'error': 'At least one member is required'}), 400

    # Create the group chat
    group_chat, message = GroupChat.create_group_chat(current_user.id, name, member_ids)

    if not group_chat:
        return jsonify({'error': message}), 400

    # Get group chat data for WebSocket notification
    group_chat_data = group_chat.to_dict()

    # Emit WebSocket event to all members
    from flask import current_app

    # Notify all members about the new group chat
    # Get the socketio instance from the extensions
    socketio = current_app.extensions.get('socketio')

    if socketio:
        for member in group_chat.members:
            if str(member.id) != str(current_user.id):  # Don't notify the creator
                user_room = f"user_{member.id}"
                socketio.emit('group_chat_created', group_chat_data, room=user_room)

    return jsonify({
        'success': True,
        'message': message,
        'group_chat': group_chat_data
    })


@friends_api.route('/group-chat/<chat_id>', methods=['GET'])
@login_required
@check_service_access('friends')
def get_group_chat(chat_id):
    """Get a group chat by ID with pagination support"""
    chat = GroupChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this group chat'}), 403

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    return jsonify(chat.to_dict(limit=limit, skip=skip))


@friends_api.route('/group-chat/<chat_id>/messages', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('group_message', 'friends')
def send_group_message(chat_id):
    """Send a message to a group chat"""
    data = request.json
    message = data.get('message')
    images = data.get('images', [])
    is_system = data.get('is_system', False)
    message_type = data.get('message_type', 'info')

    # Handle both string messages and dictionary messages
    if isinstance(message, dict):
        # If message is a dictionary, extract content and system flag
        content = message.get('content')
        is_system = message.get('system', is_system)
        message_type = message.get('type', message_type)

        if not content and not images:
            return jsonify({'error': 'Message content or images are required'}), 400
    else:
        # If message is a string, use it directly
        content = message
        if (not content or not isinstance(content, str) or not content.strip()) and not images:
            return jsonify({'error': 'Message or images are required'}), 400

    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is allowed to send messages to this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to send messages to this group chat'}), 403

    # Add the message
    message_obj = chat.add_message(current_user, content, is_system, message_type, images)

    return jsonify({
        'success': True,
        'message': message_obj
    })


@friends_api.route('/group-chat/<chat_id>/members', methods=['POST'])
@login_required
@check_service_access('friends')
def add_group_member(chat_id):
    """Add a member to a group chat"""
    data = request.json
    member_id = data.get('member_id')

    if not member_id:
        return jsonify({'error': 'Member ID is required'}), 400

    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is the creator of the chat
    if str(chat.creator.id) != str(current_user.id):
        return jsonify({'error': 'Only the creator can add members to the group chat'}), 403

    # Get the member
    member = User.objects(id=member_id).first()
    if not member:
        return jsonify({'error': 'User not found'}), 404

    # Check if they are friends
    if not FriendRelationship.are_friends(current_user.id, member.id):
        return jsonify({'error': 'You can only add friends to your group chats'}), 400

    # Add the member
    if chat.add_member(member):
        # Add system message
        chat.add_message(
            current_user,
            f"{member.display_name or member.username} was added to the group",
            is_system=True
        )

        return jsonify({
            'success': True,
            'message': f"{member.username} added to the group chat"
        })
    else:
        return jsonify({
            'success': False,
            'message': f"{member.username} is already a member of this group chat"
        })


@friends_api.route('/group-chat/<chat_id>/members/<member_id>', methods=['DELETE'])
@login_required
@check_service_access('friends')
def remove_group_member(chat_id, member_id):
    """Remove a member from a group chat"""
    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is the creator of the chat
    if str(chat.creator.id) != str(current_user.id):
        return jsonify({'error': 'Only the creator can remove members from the group chat'}), 403

    # Get the member
    member = User.objects(id=member_id).first()
    if not member:
        return jsonify({'error': 'User not found'}), 404

    # Remove the member
    if chat.remove_member(member):
        # Add system message
        chat.add_message(
            current_user,
            f"{member.display_name or member.username} was removed from the group",
            is_system=True
        )

        return jsonify({
            'success': True,
            'message': f"{member.username} removed from the group chat"
        })
    else:
        return jsonify({
            'success': False,
            'message': f"{member.username} is not a member of this group chat"
        })


@friends_api.route('/group-chat/<chat_id>/leave', methods=['POST'])
@login_required
@check_service_access('friends')
def leave_group_chat(chat_id):
    """Leave a group chat"""
    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is a member of the chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'You are not a member of this group chat'}), 403

    # Add system message that the user is leaving
    try:
        chat.add_message(
            current_user,
            f"{current_user.display_name or current_user.username} left the group chat.",
            is_system=True
        )
    except Exception as e:
        logging.error(f"Error adding system message when leaving group chat: {str(e)}")
        # Continue with leaving even if the system message fails

    # If user is the creator and there are other members, transfer ownership to the first member
    if str(chat.creator.id) == str(current_user.id) and len(chat.members) > 1:
        # Find the first member who is not the current user
        for member in chat.members:
            if str(member.id) != str(current_user.id):
                chat.creator = member
                break

    # Remove the user from the group chat
    if chat.remove_member(current_user):
        # If there are no members left, delete the chat
        if len(chat.members) == 0:
            chat.delete()
            return jsonify({
                'success': True,
                'message': 'You left the group chat and it was deleted as there are no members left'
            })
        else:
            chat.save()
            return jsonify({
                'success': True,
                'message': 'You left the group chat'
            })
    else:
        return jsonify({
            'success': False,
            'message': 'Failed to leave the group chat'
        }), 400


@friends_api.route('/group-chat/<chat_id>/rename', methods=['POST'])
@login_required
@check_service_access('friends')
def rename_group_chat(chat_id):
    """Rename a group chat"""
    data = request.json
    new_name = data.get('name')

    if not new_name or not new_name.strip():
        return jsonify({'error': 'Name is required'}), 400

    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is a member of the chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'You are not a member of this group chat'}), 403

    # Save the old name for the system message
    old_name = chat.name

    # Update the name
    chat.name = new_name
    chat.save()

    # Add system message
    chat.add_message(
        current_user,
        f"Group chat renamed from '{old_name}' to '{new_name}'",
        is_system=True
    )

    # Get updated chat data for WebSocket notification
    chat_data = {
        'chat_id': chat.chat_id,
        'name': new_name,
        'updated_by': {
            'id': str(current_user.id),
            'username': current_user.username,
            'display_name': current_user.display_name or current_user.username
        }
    }

    # Emit WebSocket event to all members
    from flask import current_app
    socketio = current_app.extensions.get('socketio')

    if socketio:
        group_chat_room = f"group_chat_{chat_id}"
        socketio.emit('group_chat_renamed', chat_data, room=group_chat_room)

    return jsonify({
        'success': True,
        'message': 'Group chat renamed successfully',
        'name': new_name
    })


@friends_api.route('/group-chat/<chat_id>/profile-picture', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('group_chat_profile_picture', 'friends')
def update_group_chat_profile_picture(chat_id):
    """Update the profile picture of a group chat"""
    data = request.json
    profile_picture_url = data.get('profile_picture')
    old_profile_picture_url = data.get('old_profile_picture')

    if not profile_picture_url:
        return jsonify({'error': 'Profile picture URL is required'}), 400

    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is a member of the chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'You are not a member of this group chat'}), 403

    # Import the TermColors class for colored terminal output
    from models.uploaded_file import TermColors
    
    # Always get the current profile picture from the chat to ensure we have it
    current_profile_picture = chat.profile_picture
    
    # Debug log to check the profile picture URLs
    print(f"{TermColors.RED}[GROUP CHAT IMAGE] Current profile picture in database: {current_profile_picture}{TermColors.RESET}")
    print(f"{TermColors.RED}[GROUP CHAT IMAGE] Old profile picture from request: {old_profile_picture_url}{TermColors.RESET}")
    print(f"{TermColors.RED}[GROUP CHAT IMAGE] New profile picture: {profile_picture_url}{TermColors.RESET}")
    
    # Use the provided old_profile_picture_url if available, otherwise use the current one
    if not old_profile_picture_url and current_profile_picture:
        old_profile_picture_url = current_profile_picture
        print(f"{TermColors.RED}[GROUP CHAT IMAGE] Using current profile picture as old_profile_picture_url: {old_profile_picture_url}{TermColors.RESET}")
        logging.info(f"Using current profile picture as old_profile_picture_url: {old_profile_picture_url}")

    # Delete the old profile picture if it exists and is different from the new one
    if old_profile_picture_url and old_profile_picture_url != profile_picture_url:
        try:
            from models.uploaded_file import UploadedFile, TermColors
            
            # Check if the URL is properly formatted
            if not old_profile_picture_url.startswith('/api/upload/'):
                print(f"{TermColors.RED}[GROUP CHAT IMAGE] WARNING: Old profile picture URL is not in the expected format: {old_profile_picture_url}{TermColors.RESET}")
                # Try to fix the URL if it's a relative path without the /api prefix
                if old_profile_picture_url.startswith('/upload/'):
                    old_profile_picture_url = f"/api{old_profile_picture_url}"
                    print(f"{TermColors.RED}[GROUP CHAT IMAGE] Fixed URL to: {old_profile_picture_url}{TermColors.RESET}")
            
            # Print a clear message in red about the deletion attempt
            print(f"{TermColors.RED}[GROUP CHAT IMAGE] Attempting to delete old profile picture: {old_profile_picture_url}{TermColors.RESET}")
            
            deleted = UploadedFile.delete_by_url(old_profile_picture_url)
            if deleted:
                print(f"{TermColors.RED}[GROUP CHAT IMAGE] Successfully deleted old group chat profile picture: {old_profile_picture_url}{TermColors.RESET}")
                logging.info(f"Deleted old group chat profile picture: {old_profile_picture_url}")
            else:
                print(f"{TermColors.RED}[GROUP CHAT IMAGE] Failed to delete old group chat profile picture: {old_profile_picture_url}{TermColors.RESET}")
                logging.warning(f"Failed to delete old group chat profile picture: {old_profile_picture_url}")
        except Exception as e:
            print(f"{TermColors.RED}[GROUP CHAT IMAGE] Error deleting old profile picture: {str(e)}{TermColors.RESET}")
            logging.error(f"Error deleting old profile picture: {str(e)}")
            import traceback
            print(f"{TermColors.RED}[GROUP CHAT IMAGE] Error traceback: {traceback.format_exc()}{TermColors.RESET}")
            # Continue with the update even if deletion fails

    # Update the profile picture
    success = chat.update_profile_picture(current_user, profile_picture_url)
    if not success:
        return jsonify({'error': 'Failed to update profile picture'}), 500

    # Add system message
    chat.add_message(
        current_user,
        f"{current_user.display_name or current_user.username} updated the group profile picture",
        is_system=True
    )

    # Get updated chat data for WebSocket notification
    chat_data = {
        'chat_id': chat.chat_id,
        'profile_picture': profile_picture_url,
        'updated_by': {
            'id': str(current_user.id),
            'username': current_user.username,
            'display_name': current_user.display_name or current_user.username
        }
    }

    # Emit WebSocket event to all members
    from flask import current_app
    socketio = current_app.extensions.get('socketio')

    if socketio:
        group_chat_room = f"group_chat_{chat_id}"
        socketio.emit('group_chat_profile_picture_updated', chat_data, room=group_chat_room)

    return jsonify({
        'success': True,
        'message': 'Group chat profile picture updated successfully',
        'profile_picture': profile_picture_url
    })


@friends_api.route('/group-chat/<chat_id>', methods=['DELETE'])
@login_required
@check_service_access('friends')
def delete_group_chat(chat_id):
    """Delete a group chat (creator only)"""
    # Get the chat
    chat = GroupChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is the creator of the chat
    if str(chat.creator.id) != str(current_user.id):
        return jsonify({'error': 'Only the creator can delete the group chat'}), 403

    # Get member IDs before deleting the chat for WebSocket notification
    member_ids = [str(member.id) for member in chat.members]
    chat_info = {
        'chat_id': chat.chat_id,
        'name': chat.name,
        'deleted_by': {
            'id': str(current_user.id),
            'username': current_user.username,
            'display_name': current_user.display_name or current_user.username
        }
    }

    # Delete the chat
    chat.delete()

    # Emit WebSocket event to all members
    from flask import current_app

    # Get the socketio instance from the extensions
    socketio = current_app.extensions.get('socketio')

    if socketio:
        for member_id in member_ids:
            user_room = f"user_{member_id}"
            socketio.emit('group_chat_deleted', chat_info, room=user_room)

    return jsonify({
        'success': True,
        'message': 'Group chat deleted successfully'
    })


@friends_api.route('/group-chat/<chat_id>/messages', methods=['GET'])
@login_required
@check_service_access('friends')
def get_group_chat_messages(chat_id):
    """Get messages for a group chat with pagination support"""
    chat = GroupChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Group chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this group chat'}), 403

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    # Get the chat data with pagination
    chat_data = chat.to_dict(limit=limit, skip=skip)

    # Return only the messages part and pagination info
    return jsonify({
        'messages': chat_data['messages'],
        'total_messages': chat_data['total_messages'],
        'has_more': chat_data['has_more']
    })