/* Profile.css - Styles for the profile section */

/* Profile tab navigation */
.profile-tab {
    position: relative;
    transition: all 0.2s ease;
    font-weight: 500;
}

.profile-tab.active {
    color: rgb(34, 211, 238);
    border-color: rgb(34, 211, 238);
}

.profile-tab:not(.active) {
    border-color: transparent;
}

.profile-tab:hover:not(.active) {
    color: rgb(226, 232, 240);
    background-color: rgba(30, 41, 59, 0.3);
}

/* Profile tab content */
.profile-tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

.profile-tab-pane.hidden {
    display: none;
}

/* Profile avatar */
#profileAvatar {
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(56, 189, 248, 0.3);
}

#profileInitials {
    font-weight: 700;
    letter-spacing: 0.05em;
}

#changeAvatarBtn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

#changeAvatarBtn:hover {
    transform: scale(1.1);
    background-color: rgb(51, 65, 85);
}

/* Profile sections */
.profile-section {
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.profile-section:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-section-header {
    padding: 0.75rem 1rem;
    background-color: rgba(30, 41, 59, 0.5);
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.profile-section-content {
    padding: 1rem;
    background-color: rgba(15, 23, 42, 0.3);
}

/* Form controls */
.profile-input-group {
    margin-bottom: 1rem;
}

.profile-input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: rgb(226, 232, 240);
}

.profile-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background-color: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(51, 65, 85, 0.8);
    border-radius: 0.375rem;
    color: rgb(241, 245, 249);
    transition: all 0.2s ease;
}

.profile-input:focus {
    outline: none;
    border-color: rgb(56, 189, 248);
    box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.2);
}

/* Toggle switches */
.profile-toggle {
    position: relative;
    display: inline-block;
    width: 2.75rem;
    height: 1.5rem;
}

.profile-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.profile-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(51, 65, 85, 0.8);
    transition: .4s;
    border-radius: 1.5rem;
}

.profile-toggle-slider:before {
    position: absolute;
    content: "";
    height: 1.125rem;
    width: 1.125rem;
    left: 0.1875rem;
    bottom: 0.1875rem;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .profile-toggle-slider {
    background-color: rgb(56, 189, 248);
}

input:focus + .profile-toggle-slider {
    box-shadow: 0 0 1px rgb(56, 189, 248);
}

input:checked + .profile-toggle-slider:before {
    transform: translateX(1.25rem);
}

/* Login history */
.login-history-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.login-history-item {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background-color: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.login-history-item:hover {
    background-color: rgba(30, 41, 59, 0.6);
}

/* Usage statistics */
.usage-stat-card {
    background-color: rgba(30, 41, 59, 0.4);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.375rem;
    padding: 1rem;
    transition: all 0.2s ease;
}

.usage-stat-card:hover {
    background-color: rgba(30, 41, 59, 0.6);
    transform: translateY(-2px);
}

.usage-progress-bar {
    height: 0.375rem;
    border-radius: 0.1875rem;
    background-color: rgba(15, 23, 42, 0.5);
    overflow: hidden;
}

.usage-progress-fill {
    height: 100%;
    border-radius: 0.1875rem;
    transition: width 0.5s ease;
}

/* Avatar Upload Modal */
#avatarUploadModal {
    transition: opacity 0.3s ease;
}

#avatarUploadModal.hidden {
    opacity: 0;
    pointer-events: none;
}

#avatarPreview {
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(56, 189, 248, 0.3);
    margin: 0 auto;
}

#avatarPreviewInitials {
    font-weight: 700;
    letter-spacing: 0.05em;
}

#uploadAvatarBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#uploadSpinner {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    #profileAvatar {
        width: 5rem;
        height: 5rem;
    }
    
    #profileInitials {
        font-size: 1.5rem;
    }
    
    #avatarPreview {
        width: 6rem;
        height: 6rem;
    }
}