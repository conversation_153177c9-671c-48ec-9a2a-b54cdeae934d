# API Documentation

This directory contains all the API endpoints for the application, organized by feature. Each API module has its own folder with a README file explaining its purpose and endpoints.

## API Structure

- **[/api/auth](./auth/README.md)**: Authentication endpoints for user registration, login, and password management
- **[/api/chat](./chat/README.md)**: Chat endpoints for managing threads and AI interactions
- **[/api/live](./live/README.md)**: Live chat endpoints for real-time AI interactions with multiple users
- **[/api/friends](./friends/README.md)**: Friend management endpoints for adding, removing, and chatting with friends
- **[/api/profile](./profile/README.md)**: User profile endpoints for managing account settings and preferences
- **[/api/admin](./admin/README.md)**: Administrative endpoints for user management, feature restrictions, and system monitoring
- **[/api/spotify](./spotify/README.md)**: Spotify integration endpoints for music playback, search, and playlist management

## Common Response Formats

All API endpoints follow a consistent response format:

### Success Responses

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... } // Optional data object
}
```

### Error Responses

```json
{
  "error": "Error message describing what went wrong"
}
```

## Authentication

Most API endpoints require authentication. Include the user's session cookie with each request.

## Rate Limiting

API endpoints are subject to rate limiting to prevent abuse. If you exceed the rate limit, you'll receive a 429 Too Many Requests response.

## Adding New API Endpoints

To add a new API endpoint:

1. Create a new file in the appropriate API folder
2. Define your endpoint using the Flask Blueprint
3. Update the README.md file in the API folder to document the new endpoint
4. Import your new file in the API folder's `__init__.py`

Example:

```python
# api/chat/new_feature.py
from . import chat_api

@chat_api.route('/new-feature', methods=['GET'])
def new_feature():
    # Implementation
    return jsonify({'success': True})
```

Then in `api/chat/__init__.py`:

```python
from . import routes, new_feature
```
