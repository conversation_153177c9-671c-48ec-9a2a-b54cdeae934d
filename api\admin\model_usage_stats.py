from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from functools import wraps
import logging
from datetime import datetime, timedelta

from models.model_usage import ModelUsage

model_usage_stats = Blueprint('model_usage_stats', __name__, url_prefix='/api/admin/statistics')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            return jsonify({'error': 'Admin privileges required'}), 403
        return f(*args, **kwargs)
    return decorated_function

@model_usage_stats.route('/service-model-usage', methods=['GET'])
@login_required
def get_service_model_usage():
    """
    Get model usage statistics by service over time with different period options
    """
    try:
        # Get parameters
        days = int(request.args.get('days', 30))
        period = request.args.get('period', 'daily')  # daily, weekly, monthly, yearly

        today = datetime.now()
        date_labels = []
        
        # Set up time boundaries and grouping format based on period
        if period == 'daily':
            # For daily view, show last N days
            points = min(days, 30)  # Limit to 30 days for daily view
            start_date = today - timedelta(days=points-1)
            start_date = datetime(start_date.year, start_date.month, start_date.day)  # Start of day
            
            # Generate labels for each day
            date_labels = [(today - timedelta(days=i)).strftime('%b %d') for i in range(points-1, -1, -1)]
            
            # Date format for MongoDB aggregation
            date_format = {
                'year': {'$year': '$timestamp'},
                'month': {'$month': '$timestamp'},
                'day': {'$dayOfMonth': '$timestamp'}
            }
            
        elif period == 'weekly':
            # For weekly view, show last N weeks
            points = min(days // 7, 12)  # Limit to 12 weeks
            if points < 2:
                points = 2  # Ensure at least 2 weeks
                
            start_date = today - timedelta(days=(points-1)*7 + (today.weekday()))
            start_date = datetime(start_date.year, start_date.month, start_date.day)  # Start of day
            
            # Generate labels for each week
            for i in range(points):
                week_end = today - timedelta(days=i*7)
                week_start = week_end - timedelta(days=6)
                date_labels.insert(0, f"{week_start.strftime('%b %d')} - {week_end.strftime('%b %d')}")
                
            # Date format for MongoDB aggregation - group by week
            date_format = {
                'year': {'$year': '$timestamp'},
                'week': {'$week': '$timestamp'}
            }
            
        elif period == 'monthly':
            # For monthly view, show last N months
            points = min(days // 30, 12)  # Limit to 12 months
            if points < 2:
                points = 2  # Ensure at least 2 months
                
            # Calculate start date (beginning of month)
            month = today.month - (points - 1)
            year = today.year
            while month <= 0:
                month += 12
                year -= 1
            start_date = datetime(year, month, 1)
            
            # Generate labels for each month
            current_month = today.month
            current_year = today.year
            for i in range(points):
                month_to_show = current_month - i
                year_to_show = current_year
                while month_to_show <= 0:
                    month_to_show += 12
                    year_to_show -= 1
                month_date = datetime(year_to_show, month_to_show, 1)
                date_labels.insert(0, month_date.strftime('%b %Y'))
                
            # Date format for MongoDB aggregation
            date_format = {
                'year': {'$year': '$timestamp'},
                'month': {'$month': '$timestamp'}
            }
            
        elif period == 'yearly':
            # For yearly view, show data by quarters for the last year
            points = 4  # 4 quarters
            
            # Calculate start date (beginning of first quarter to show)
            current_quarter = (today.month - 1) // 3
            year = today.year
            quarter_start_month = (current_quarter - (points - 1)) * 3 + 1
            while quarter_start_month <= 0:
                quarter_start_month += 12
                year -= 1
            start_date = datetime(year, max(1, quarter_start_month), 1)
            
            # Generate labels for each quarter
            for i in range(points):
                quarter = (current_quarter - (points - 1 - i)) % 4 + 1
                quarter_year = year if quarter <= current_quarter else year - 1
                date_labels.append(f"Q{quarter} {quarter_year}")
                
            # Date format for MongoDB aggregation
            date_format = {
                'year': {'$year': '$timestamp'},
                'quarter': {'$ceil': {'$divide': [{'$month': '$timestamp'}, 3]}}
            }
        
        # Create a single aggregation pipeline for both services
        # This is much more efficient than running multiple queries
        pipeline = [
            # Match documents within our time range
            {'$match': {
                'timestamp': {'$gte': start_date}
            }},
            # Group by service, model_name, and time period
            {'$group': {
                '_id': {
                    'service': '$service',
                    'model_name': '$model_name',
                    'date': date_format
                },
                'count': {'$sum': 1}
            }},
            # Sort by date
            {'$sort': {'_id.date': 1}}
        ]
        
        # Execute the aggregation
        results = list(ModelUsage.objects.aggregate(pipeline))
        
        # Process results into the expected format
        chat_model_usage = {}
        live_model_usage = {}
        
        # Create a mapping from date format to index in the result array
        date_mapping = {}
        
        # Initialize the date mapping based on the period
        if period == 'daily':
            for i in range(points):
                day_date = today - timedelta(days=points-1-i)
                date_key = (day_date.year, day_date.month, day_date.day)
                date_mapping[date_key] = i
        elif period == 'weekly':
            for i in range(points):
                week_date = today - timedelta(days=i*7)
                # MongoDB week numbers start at 0, so we need to adjust
                week_num = week_date.isocalendar()[1] - 1
                date_key = (week_date.year, week_num)
                date_mapping[date_key] = points-1-i
        elif period == 'monthly':
            for i in range(points):
                month = today.month - i
                year = today.year
                while month <= 0:
                    month += 12
                    year -= 1
                date_key = (year, month)
                date_mapping[date_key] = points-1-i
        elif period == 'yearly':
            for i in range(points):
                quarter = (current_quarter - i) % 4 + 1
                quarter_year = today.year if quarter <= current_quarter else today.year - 1
                date_key = (quarter_year, quarter)
                date_mapping[date_key] = points-1-i
        
        # Process the aggregation results
        for result in results:
            model_name = result['_id']['model_name']
            service = result['_id']['service']
            date_info = result['_id']['date']
            count = result['count']
            
            # Skip empty model names
            if not model_name or model_name in ['', 'None', None]:
                continue
                
            # Create date key based on period
            if period == 'daily':
                date_key = (date_info['year'], date_info['month'], date_info['day'])
            elif period == 'weekly':
                date_key = (date_info['year'], date_info['week'])
            elif period == 'monthly':
                date_key = (date_info['year'], date_info['month'])
            elif period == 'yearly':
                date_key = (date_info['year'], date_info['quarter'])
                
            # Skip if date is not in our mapping (outside our range)
            if date_key not in date_mapping:
                continue
                
            # Get the index for this date
            idx = date_mapping[date_key]
            
            # Add to the appropriate service
            if service == 'chat':
                if model_name not in chat_model_usage:
                    chat_model_usage[model_name] = [0] * points
                chat_model_usage[model_name][idx] = count
            elif service == 'live':
                if model_name not in live_model_usage:
                    live_model_usage[model_name] = [0] * points
                live_model_usage[model_name][idx] = count
        
        return jsonify({
            'period': period,
            'chat': {
                'days': date_labels,
                'models': chat_model_usage
            },
            'live': {
                'days': date_labels,
                'models': live_model_usage
            }
        })

    except Exception as e:
        logger.error(f"Error getting service model usage statistics: {str(e)}")
        return jsonify({
            'period': 'daily',
            'chat': {
                'days': [],
                'models': {}
            },
            'live': {
                'days': [],
                'models': {}
            }
        }), 500