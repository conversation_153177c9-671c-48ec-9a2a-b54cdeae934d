/**
 * StatisticsPanel.js
 * Handles statistics panel functionality in the dashboard
 */
class StatisticsPanel {
    constructor() {
        this.dataCache = {
            userActivity: null,
            dailyModelUsage: null,
            userModelUsage: null,
            userCountries: null,
            kevkofyTopUsers: null
        };
        this.charts = {
            userActivity: null,
            dailyModelUsage: null,
            modalChart: null
        };
        this.worldMap = null;
        this.currentPeriod = 'daily';
        this.colors = {
            chat: 'rgba(49, 94, 248, 0.8)',
            live: 'rgba(234, 67, 53, 0.8)',
            spotify: 'rgba(30, 215, 96, 0.8)',
            friends: 'rgba(255, 184, 0, 0.8)',
            discord: 'rgba(114, 137, 218, 0.8)',
            'GPT-4o Mini': 'rgba(49, 94, 248, 0.8)',
            'Gemini': 'rgba(234, 67, 53, 0.8)',
            'Qwen': 'rgba(0, 174, 239, 0.8)',
            'Gemma 2': 'rgba(255, 122, 0, 0.8)',
            'Llama 3.3 70B': 'rgba(162, 89, 255, 0.8)',
            'Llama 3.1 8B': 'rgba(255, 64, 129, 0.8)',
            '<PERSON>lama 3 70B': 'rgba(162, 89, 255, 0.7)',
            'Llama 3 8B': 'rgba(255, 64, 129, 0.7)',
            'PlayAI TTS': 'rgba(0, 200, 83, 0.8)',
            background: 'rgba(38, 38, 44, 0.2)',
            border: 'rgba(38, 38, 44, 0.1)'
        };
        this.isInitialized = false;
        this.updateInterval = null;
    }

    /**
     * Initialize the statistics panel
     */
    init() {
        if (this.isInitialized) return;

        console.log('Initializing statistics panel...');

        // Load all data
        this.loadAllData();

        // Set up auto-refresh every 5 minutes
        this.updateInterval = setInterval(() => {
            this.loadAllData();
        }, 5 * 60 * 1000); // 5 minutes

        // Set up period selector buttons
        this.setupPeriodButtons();

        // Set up chart click handlers
        this.setupChartClickHandlers();

        // Set up modal close button
        this.setupModalCloseButton();

        this.isInitialized = true;
    }

    /**
     * Set up period selector buttons
     */
    setupPeriodButtons() {
        const periodButtons = document.querySelectorAll('.period-btn');
        if (!periodButtons.length) return;

        periodButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                periodButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                button.classList.add('active');

                // Get period value
                const period = button.getAttribute('data-period');
                if (period && period !== this.currentPeriod) {
                    this.currentPeriod = period;

                    // Load data with new period
                    this.loadDailyModelUsageData(period).then(() => {
                        this.createDailyModelUsageChart();
                    }).catch(error => {
                        console.error(`Error loading ${period} model usage data:`, error);
                        this.showError(`Failed to load ${period} model usage data. Please try again.`);
                    });
                }
            });
        });
    }

    /**
     * Set up chart click handlers
     */
    setupChartClickHandlers() {
        const chartContainers = document.querySelectorAll('.chart-clickable');
        if (!chartContainers.length) return;

        chartContainers.forEach(container => {
            container.addEventListener('click', () => {
                const chartId = container.getAttribute('data-chart-id');
                if (!chartId) return;

                // Show the chart in a modal
                this.showChartInModal(chartId);
            });
        });
    }

    /**
     * Set up modal close button
     */
    setupModalCloseButton() {
        const closeButton = document.getElementById('closeChartModal');
        if (!closeButton) return;

        closeButton.addEventListener('click', () => {
            this.closeModal();
        });

        // Also close modal when clicking outside the content
        const modal = document.getElementById('chartModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal();
                }
            });
        }

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    /**
     * Show chart in modal
     */
    showChartInModal(chartId) {
        console.log(`Showing chart in modal: ${chartId}`);
        const modal = document.getElementById('chartModal');
        const modalTitle = document.getElementById('chartModalTitle');
        if (!modal || !modalTitle) {
            console.error('Modal elements not found');
            return;
        }

        // Set modal title based on chart
        let title = 'Chart';
        if (chartId === 'userActivityChart') {
            title = 'User Activity';
        } else if (chartId === 'dailyModelUsageChart') {
            title = `Model Usage (${this.currentPeriod.charAt(0).toUpperCase() + this.currentPeriod.slice(1)})`;
        }

        modalTitle.textContent = title;
        console.log(`Set modal title to: ${title}`);

        // Clone the chart data and options
        const sourceChart = this.charts[chartId === 'userActivityChart' ? 'userActivity' : 'dailyModelUsage'];
        if (!sourceChart) {
            console.error(`Source chart not found for ${chartId}`);
            return;
        }

        // Destroy existing modal chart if it exists
        if (this.charts.modalChart) {
            console.log('Destroying existing modal chart');
            this.charts.modalChart.destroy();
            this.charts.modalChart = null;
        }

        // First show the modal to ensure the canvas is visible
        modal.classList.remove('hidden');

        // Initialize Lucide icons in the modal
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': 1.5
                },
                nameAttr: 'data-lucide',
            });
        }

        // Create a new chart in the modal
        const ctx = document.getElementById('modalChart');
        if (!ctx) {
            console.error('Modal chart canvas not found');
            return;
        }

        console.log('Creating new modal chart');

        // Create a deep clone of the chart configuration
        const config = {
            type: sourceChart.config.type,
            data: {
                labels: [...sourceChart.data.labels],
                datasets: sourceChart.data.datasets.map(ds => ({
                    ...ds,
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    borderWidth: 3
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: 'rgba(148, 163, 184, 1)',
                            font: { size: 14 },
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: '#f1f5f9',
                        bodyColor: '#e2e8f0',
                        titleFont: { size: 16 },
                        bodyFont: { size: 14 },
                        padding: 12,
                        borderColor: 'rgba(51, 65, 85, 0.5)',
                        borderWidth: 1,
                        cornerRadius: 6
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)',
                            font: { size: 14 }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)',
                            font: { size: 14 },
                            precision: 0
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        };

        // Wait a moment for the DOM to update before creating the chart
        setTimeout(() => {
            try {
                // Create the modal chart using the global Chart object from Chart.js
                this.charts.modalChart = new window.Chart(ctx, config);

                // Make the modal visible with animation
                modal.classList.add('visible');
                console.log('Modal chart created and displayed');
            } catch (error) {
                console.error('Error creating modal chart:', error);
            }
        }, 50);
    }

    /**
     * Close the modal
     */
    closeModal() {
        console.log('Closing modal');
        const modal = document.getElementById('chartModal');
        if (!modal) {
            console.error('Modal element not found');
            return;
        }

        modal.classList.remove('visible');
        setTimeout(() => {
            modal.classList.add('hidden');

            // Destroy the chart to free up resources
            if (this.charts.modalChart) {
                console.log('Destroying modal chart');
                this.charts.modalChart.destroy();
                this.charts.modalChart = null;
            }
        }, 300);
    }

    /**
     * Clean up resources when panel is closed
     */
    cleanup() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        // Destroy charts to prevent memory leaks
        for (const chartName in this.charts) {
            if (this.charts[chartName]) {
                this.charts[chartName].destroy();
                this.charts[chartName] = null;
            }
        }

        // Clean up world map
        try {
            const mapContainer = document.getElementById('worldMap');
            if (mapContainer) {
                mapContainer.innerHTML = '';
            }
            this.worldMap = null;
        } catch (error) {
            console.error('Error cleaning up world map:', error);
        }
    }

    /**
     * Show loading state
     * @param {boolean} isLoading Whether to show or hide loading state
     */
    showLoading(isLoading) {
        const loadingIndicator = document.getElementById('statsLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = isLoading ? 'flex' : 'none';
        }
    }

    /**
     * Show error message
     * @param {string} message Error message to display
     */
    showError(message) {
        console.error('Statistics error:', message);
        const errorContainer = document.getElementById('statsErrorContainer');
        if (errorContainer) {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
            setTimeout(() => {
                errorContainer.style.display = 'none';
            }, 5000);
        }
    }

    /**
     * Load all data
     */
    async loadAllData() {
        try {
            // Show loading state
            this.showLoading(true);

            // Load all data types
            await Promise.all([
                this.loadUserActivityData(),
                this.loadDailyModelUsageData(),
                this.loadUserModelUsageData(),
                this.loadUserCountriesData(),
                this.loadAssistantMessagesData(),
                this.loadKevkoFyTopUsersData()
            ]);

            // Create all charts
            this.createCharts();

        } catch (error) {
            console.error('Error loading statistics data:', error);
            this.showError('Failed to load statistics data. Please try again later.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Load assistant messages count data
     */
    async loadAssistantMessagesData() {
        try {
            const response = await fetch('/api/admin/statistics/assistant-messages');
            if (!response.ok) {
                throw new Error('Failed to fetch assistant messages data');
            }

            const data = await response.json();
            this.dataCache.assistantMessages = data;
            return data;
        } catch (error) {
            console.error('Error loading assistant messages data:', error);
            throw error;
        }
    }

    /**
     * Load user countries data
     */
    async loadUserCountriesData() {
        try {
            const response = await fetch('/api/admin/statistics/user-countries');
            if (!response.ok) {
                throw new Error('Failed to fetch user countries data');
            }

            const data = await response.json();
            if (data.success) {
                this.dataCache.userCountries = data.data;
            } else {
                throw new Error(data.message || 'Failed to fetch user countries data');
            }
            return data;
        } catch (error) {
            console.error('Error loading user countries data:', error);
            throw error;
        }
    }

    // Service usage data loading has been removed

    // Model usage data loading has been removed

    /**
     * Load user activity data
     */
    async loadUserActivityData() {
        try {
            const response = await fetch('/api/admin/statistics/user-activity');
            if (!response.ok) {
                throw new Error('Failed to fetch user activity data');
            }

            const data = await response.json();
            this.dataCache.userActivity = data;
            return data;
        } catch (error) {
            console.error('Error loading user activity data:', error);
            throw error;
        }
    }

    /**
     * Load model usage data with specified period
     * @param {string} period - The period to load data for (daily, weekly, monthly, yearly)
     */
    async loadDailyModelUsageData(period = 'daily') {
        try {
            console.log(`Loading model usage data for period: ${period}`);
            const response = await fetch(`/api/admin/statistics/service-model-usage?period=${period}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${period} model usage data`);
            }

            const data = await response.json();
            this.dataCache.dailyModelUsage = data;
            this.currentPeriod = data.period || period;
            return data;
        } catch (error) {
            console.error(`Error loading ${period} model usage data:`, error);
            throw error;
        }
    }

    /**
     * Load user model usage data
     */
    async loadUserModelUsageData() {
        try {
            const response = await fetch('/api/admin/statistics/user-model-usage');
            if (!response.ok) {
                throw new Error('Failed to fetch user model usage data');
            }

            const data = await response.json();
            this.dataCache.userModelUsage = data;
            return data;
        } catch (error) {
            console.error('Error loading user model usage data:', error);
            throw error;
        }
    }
    
    /**
     * Load KevkoFy top users data
     */
    async loadKevkoFyTopUsersData() {
        try {
            const response = await fetch('/api/spotify/statistics/top-users');
            if (!response.ok) {
                throw new Error('Failed to fetch KevkoFy top users data');
            }

            const data = await response.json();
            this.dataCache.kevkofyTopUsers = data;
            
            // Update the KevkoFy top users table
            this.updateKevkoFyTopUsersTable();
            
            return data;
        } catch (error) {
            console.error('Error loading KevkoFy top users data:', error);
            throw error;
        }
    }

    /**
     * Create all charts
     */
    createCharts() {
        this.createUserActivityChart();
        this.createDailyModelUsageChart();
        this.createWorldMap();
        this.updateMetrics();
        this.updateTopUsersTable();
        this.updateKevkoFyTopUsersTable();
    }

    /**
     * Create world map visualization using D3.js
     */
    createWorldMap() {
        const data = this.dataCache.userCountries;
        if (!data) return;

        const mapContainer = document.getElementById('worldMap');
        if (!mapContainer) return;

        // Clear previous content
        mapContainer.innerHTML = '';

        // Check if we have data to display
        if (data.length === 0) {
            mapContainer.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">No country data available</p></div>';
            return;
        }

        // Check if D3 is available
        if (typeof d3 === 'undefined') {
            console.error('D3.js not loaded');
            mapContainer.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">Map library not loaded. Please refresh the page.</p></div>';
            return;
        }

        // Prepare data for the map
        const countryData = {};

        // ISO country code to numeric ID mapping (for D3 world map)
        const countryCodeMap = {
            'US': 840, 'GB': 826, 'CA': 124, 'AU': 36, 'DE': 276, 'FR': 250, 'JP': 392,
            'CN': 156, 'IN': 356, 'BR': 76, 'RU': 643, 'ZA': 710, 'MX': 484, 'ES': 724,
            'IT': 380, 'NL': 528, 'SE': 752, 'NO': 578, 'DK': 208, 'FI': 246, 'PL': 616,
            'TR': 792, 'AE': 784, 'SA': 682, 'SG': 702, 'KR': 410, 'TH': 764, 'VN': 704,
            'MY': 458, 'ID': 360, 'PH': 608, 'NZ': 554, 'AR': 32, 'CL': 152, 'CO': 170, 'PE': 604
        };

        data.forEach(country => {
            if (country.code && country.code !== 'unknown') {
                // Convert ISO code to numeric ID if available
                const countryId = countryCodeMap[country.code] || country.code;
                countryData[countryId] = country.value;
            }
        });

        try {
            // Set up dimensions
            const width = mapContainer.clientWidth;
            const height = mapContainer.clientHeight || 400;

            // Create SVG element
            const svg = d3.select(mapContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height)
                .attr('viewBox', [0, 0, width, height])
                .attr('style', 'max-width: 100%; height: auto;');

            // Create a group for the map
            const g = svg.append('g');

            // Create a tooltip
            // First remove any existing tooltip to avoid duplicates
            d3.select('.map-tooltip').remove();

            const tooltip = d3.select('body') // Attach to body instead of mapContainer for better positioning
                .append('div')
                .attr('class', 'map-tooltip')
                .style('opacity', 0)
                .style('display', 'none'); // Initially hidden

            // Create a projection
            const projection = d3.geoNaturalEarth1()
                .scale(width / 5.5)
                .translate([width / 2, height / 1.8]);

            // Create a path generator
            const path = d3.geoPath()
                .projection(projection);

            // Create zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([1, 8])
                .on('zoom', (event) => {
                    g.attr('transform', event.transform);
                });

            // Apply zoom behavior to SVG
            svg.call(zoom);

            // Add zoom controls
            const controls = d3.select(mapContainer)
                .append('div')
                .attr('class', 'map-controls');

            controls.append('button')
                .attr('class', 'map-control-btn')
                .text('+')
                .on('click', () => {
                    svg.transition()
                        .duration(750)
                        .call(zoom.scaleBy, 1.5);
                });

            controls.append('button')
                .attr('class', 'map-control-btn')
                .text('-')
                .on('click', () => {
                    svg.transition()
                        .duration(750)
                        .call(zoom.scaleBy, 0.75);
                });

            controls.append('button')
                .attr('class', 'map-control-btn')
                .text('⟲')
                .on('click', () => {
                    svg.transition()
                        .duration(750)
                        .call(zoom.transform, d3.zoomIdentity);
                });

            // Load world map data
            d3.json('https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json')
                .then(world => {
                    // Convert TopoJSON to GeoJSON
                    const countries = topojson.feature(world, world.objects.countries).features;

                    // Find the maximum user count for color scaling
                    const maxUsers = Math.max(...Object.values(countryData));

                    // Create color scale with vibrant colors
                    const colorScale = d3.scaleLinear()
                        .domain([1, Math.max(maxUsers, 5)])
                        .range(['rgba(6, 182, 212, 0.5)', 'rgba(6, 182, 212, 1)'])
                        .clamp(true);

                    // Draw the countries

                    // Draw countries
                    g.selectAll('path')
                        .data(countries)
                        .enter()
                        .append('path')
                        .attr('d', path)
                        .attr('class', d => {
                            const countryCode = d.id;
                            const hasUsers = countryData[countryCode] ? true : false;
                            return hasUsers ? 'country country-with-users' : 'country';
                        })
                        .attr('fill', d => {
                            const countryCode = d.id;
                            const hasUsers = countryData[countryCode] ? true : false;
                            return hasUsers ? colorScale(countryData[countryCode]) : 'rgba(30, 41, 59, 0.6)';
                        })
                        .on('mouseover', function(event, d) {
                            const countryCode = d.id;
                            const users = countryData[countryCode] || 0;
                            const countryName = d.properties.name;

                            // Find ISO code from numeric ID
                            let isoCode = "";
                            for (const [code, id] of Object.entries(countryCodeMap)) {
                                if (id === countryCode) {
                                    isoCode = code;
                                    break;
                                }
                            }

                            // Create tooltip content with only country name and user count
                            const tooltipContent = `
                                <div class="tooltip-country">${countryName}</div>
                                <div class="tooltip-users">${users} user${users !== 1 ? 's' : ''}</div>
                            `;

                            // Show tooltip
                            tooltip
                                .html(tooltipContent)
                                .style('display', 'block')
                                .style('left', (event.clientX + 15) + 'px')
                                .style('top', (event.clientY - 35) + 'px')
                                .transition()
                                .duration(200)
                                .style('opacity', 1);

                            // Highlight the country
                            d3.select(this)
                                .style('stroke-width', '2px')
                                .style('stroke', 'rgba(255, 255, 255, 0.9)');
                        })
                        .on('mousemove', function(event) {
                            // Update tooltip position as mouse moves
                            tooltip
                                .style('left', (event.clientX + 15) + 'px')
                                .style('top', (event.clientY - 35) + 'px');
                        })
                        .on('mouseout', function() {
                            // Hide tooltip
                            tooltip
                                .transition()
                                .duration(200)
                                .style('opacity', 0)
                                .on('end', function() {
                                    d3.select(this).style('display', 'none');
                                });

                            // Reset country highlight
                            d3.select(this)
                                .style('stroke-width', null)
                                .style('stroke', null);
                        });

                    // Add custom legend
                    const legendContainer = document.createElement('div');
                    legendContainer.className = 'map-legend';

                    // Add legend title
                    const legendTitle = document.createElement('div');
                    legendTitle.className = 'text-xs font-medium mb-1';
                    legendTitle.textContent = 'Users by Country';
                    legendContainer.appendChild(legendTitle);

                    // Add legend items with more vibrant colors
                    const legendItems = [
                        { label: '1-5 users', color: 'rgba(6, 182, 212, 0.5)' },
                        { label: '6-20 users', color: 'rgba(6, 182, 212, 0.7)' },
                        { label: '21-50 users', color: 'rgba(6, 182, 212, 0.85)' },
                        { label: '50+ users', color: 'rgba(6, 182, 212, 1.0)' }
                    ];

                    legendItems.forEach(item => {
                        const legendItem = document.createElement('div');
                        legendItem.className = 'map-legend-item';

                        const legendColor = document.createElement('div');
                        legendColor.className = 'map-legend-color';
                        legendColor.style.backgroundColor = item.color;

                        const legendLabel = document.createElement('div');
                        legendLabel.textContent = item.label;

                        legendItem.appendChild(legendColor);
                        legendItem.appendChild(legendLabel);
                        legendContainer.appendChild(legendItem);
                    });

                    mapContainer.appendChild(legendContainer);
                })
                .catch(error => {
                    console.error('Error loading world map data:', error);
                    mapContainer.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">Error loading map data. Please refresh the page.</p></div>';
                });

        } catch (error) {
            console.error('Error creating world map:', error);
            mapContainer.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">Error loading map visualization</p></div>';
        }
    }

    // Service usage chart and legend methods have been removed

    // Model usage chart functionality has been removed

    /**
     * Create user activity chart
     */
    createUserActivityChart() {
        const data = this.dataCache.userActivity;
        if (!data || !data.timeline) return;

        const ctx = document.getElementById('userActivityChart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts.userActivity) {
            this.charts.userActivity.destroy();
        }

        // Prepare data
        const timeline = data.timeline;
        const labels = timeline.labels || [];
        const values = timeline.values || [];

        // Check if we have data to display
        if (labels.length === 0 || values.length === 0) {
            // Display no data message
            ctx.parentElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">No user activity data available</p></div>';
            return;
        }

        // Create chart
        this.charts.userActivity = new window.Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Active Users',
                    data: values,
                    backgroundColor: 'rgba(6, 182, 212, 0.2)',
                    borderColor: 'rgba(6, 182, 212, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(6, 182, 212, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 1,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: '#f1f5f9',
                        bodyColor: '#e2e8f0',
                        borderColor: 'rgba(51, 65, 85, 0.5)',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 6
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)',
                            precision: 0
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    /**
     * Create model usage chart with time period options
     */
    createDailyModelUsageChart() {
        const data = this.dataCache.dailyModelUsage;
        if (!data) return;

        const ctx = document.getElementById('dailyModelUsageChart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts.dailyModelUsage) {
            this.charts.dailyModelUsage.destroy();
        }

        // Get the current period from data or use default
        const period = data.period || this.currentPeriod;

        // Update the active button to match the current period
        const periodButtons = document.querySelectorAll('.period-btn');
        periodButtons.forEach(btn => {
            if (btn.getAttribute('data-period') === period) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Prepare data
        const services = Object.keys(data).filter(key => key !== 'period');

        // Check if we have data to display
        if (services.length === 0) {
            // Display no data message
            ctx.parentElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">No ${period} model usage data available</p></div>`;
            return;
        }

        const datasets = [];

        // Get all unique days across all services
        const allDays = new Set();
        services.forEach(service => {
            if (data[service] && data[service].days) {
                data[service].days.forEach(day => allDays.add(day));
            }
        });

        const sortedDays = Array.from(allDays).sort();

        // Check if we have any days
        if (sortedDays.length === 0) {
            // Display no data message
            ctx.parentElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">No ${period} model usage data available</p></div>`;
            return;
        }

        // Create datasets for each model
        const models = new Set();
        services.forEach(service => {
            if (data[service] && data[service].models) {
                Object.keys(data[service].models).forEach(model => models.add(model));
            }
        });

        // Check if we have any models
        if (models.size === 0) {
            // Display no data message
            ctx.parentElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-slate-400 text-sm">No model usage data available for ${period} view</p></div>`;
            return;
        }

        Array.from(models).forEach(model => {
            const modelData = Array(sortedDays.length).fill(0);

            services.forEach(service => {
                if (data[service] && data[service].models && data[service].models[model]) {
                    data[service].days.forEach((day, index) => {
                        const dayIndex = sortedDays.indexOf(day);
                        if (dayIndex !== -1) {
                            modelData[dayIndex] += data[service].models[model][index] || 0;
                        }
                    });
                }
            });

            datasets.push({
                label: model,
                data: modelData,
                backgroundColor: this.colors[model] || this.getRandomColor(),
                borderColor: this.colors[model] || this.getRandomColor(),
                borderWidth: 2,
                pointRadius: 3,
                pointHoverRadius: 5
            });
        });

        // Get title based on period
        let title = 'Model Usage';
        switch (period) {
            case 'daily':
                title = 'Daily Model Usage';
                break;
            case 'weekly':
                title = 'Weekly Model Usage';
                break;
            case 'monthly':
                title = 'Monthly Model Usage';
                break;
            case 'yearly':
                title = 'Yearly Model Usage (Quarterly)';
                break;
        }

        // Create chart
        this.charts.dailyModelUsage = new window.Chart(ctx, {
            type: 'line',
            data: {
                labels: sortedDays,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false,
                        text: title,
                        color: '#e2e8f0',
                        font: {
                            size: 14,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: 'rgba(148, 163, 184, 1)',
                            boxWidth: 12,
                            padding: 10
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: '#f1f5f9',
                        bodyColor: '#e2e8f0',
                        borderColor: 'rgba(51, 65, 85, 0.5)',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 6
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(51, 65, 85, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: 'rgba(148, 163, 184, 1)',
                            precision: 0
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    /**
     * Update metrics display
     */
    updateMetrics() {
        const data = this.dataCache.userActivity;
        if (!data || !data.metrics) {
            // Set all metrics to 0 if no data is available
            const metrics = ['activeUsersMetric', 'totalMessagesMetric', 'totalUsersMetric'];
            metrics.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = '0';
                }
            });
            return;
        }

        // Update active users
        const activeUsersElement = document.getElementById('activeUsersMetric');
        if (activeUsersElement) {
            activeUsersElement.textContent = data.metrics.activeUsers.toLocaleString();
        }

        // Update total messages - show only assistant messages count
        const totalMessagesElement = document.getElementById('totalMessagesMetric');
        if (totalMessagesElement) {
            // Use the accurate count from our new endpoint if available
            if (this.dataCache.assistantMessages && this.dataCache.assistantMessages.total !== undefined) {
                totalMessagesElement.textContent = this.dataCache.assistantMessages.total.toLocaleString();
            } else {
                // Fallback to 0 if data isn't available
                totalMessagesElement.textContent = '0';
            }
        }

        // Update total users
        const totalUsersElement = document.getElementById('totalUsersMetric');
        if (totalUsersElement) {
            totalUsersElement.textContent = data.metrics.totalUsers.toLocaleString();
        }
    }

    /**
     * Update top users table
     */
    updateTopUsersTable() {
        const tableBody = document.getElementById('topUsersTable');
        if (!tableBody) return;

        const data = this.dataCache.userModelUsage;
        if (!data || !data.length) {
            tableBody.innerHTML = `
                <tr class="text-center">
                    <td colspan="3" class="px-3 py-4 text-sm text-slate-400">No user data available</td>
                </tr>
            `;
            return;
        }

        let tableHTML = '';

        data.forEach((user, index) => {
            // Create avatar
            let avatarHTML = '';
            if (user.profile_picture) {
                avatarHTML = `<img src="${user.profile_picture}" alt="${user.username}" class="w-8 h-8 rounded-full mr-2 object-cover">`;
            } else {
                const initials = this.getInitials(user.display_name || user.username);
                avatarHTML = `
                    <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-2">
                        ${initials}
                    </div>
                `;
            }

            // Get preferred model from user data
            const preferredModelDisplay = user.preferred_model || 'N/A';

            tableHTML += `
                <tr class="${index % 2 === 0 ? 'bg-slate-800/30' : ''} user-table-row">
                    <td class="px-3 py-2">
                        <div class="flex items-center">
                            ${avatarHTML}
                            <div>
                                <div class="text-sm font-medium text-slate-200">${user.display_name || user.username}</div>
                                <div class="text-xs text-slate-400">@${user.username}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-2 text-center">
                        <span class="text-sm font-medium text-slate-200">${user.total_usage.toLocaleString()}</span>
                    </td>
                    <td class="px-3 py-2">
                        <span class="text-sm text-slate-200">${preferredModelDisplay}</span>
                    </td>
                </tr>
            `;
        });

        tableBody.innerHTML = tableHTML;
    }
    
    /**
     * Update KevkoFy top users table
     */
    updateKevkoFyTopUsersTable() {
        const tableBody = document.getElementById('kevkofyTopUsersTable');
        if (!tableBody) return;

        const data = this.dataCache.kevkofyTopUsers;
        if (!data || !data.length) {
            tableBody.innerHTML = `
                <tr class="text-center">
                    <td colspan="4" class="px-3 py-4 text-sm text-slate-400">No KevkoFy user data available</td>
                </tr>
            `;
            return;
        }

        let tableHTML = '';

        data.forEach((user, index) => {
            // Create avatar
            let avatarHTML = '';
            if (user.profile_picture) {
                avatarHTML = `<img src="${user.profile_picture}" alt="${user.username}" class="w-8 h-8 rounded-full mr-2 object-cover">`;
            } else {
                const initials = this.getInitials(user.display_name || user.username);
                avatarHTML = `
                    <div class="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center text-white text-sm font-medium mr-2">
                        ${initials}
                    </div>
                `;
            }

            tableHTML += `
                <tr class="${index % 2 === 0 ? 'bg-slate-800/30' : ''} user-table-row">
                    <td class="px-3 py-2">
                        <div class="flex items-center">
                            ${avatarHTML}
                            <div>
                                <div class="text-sm font-medium text-slate-200">${user.display_name || user.username}</div>
                                <div class="text-xs text-slate-400">@${user.username}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-2 text-center">
                        <span class="text-sm font-medium text-slate-200">${user.playback_control_count.toLocaleString()}</span>
                    </td>
                    <td class="px-3 py-2 text-center">
                        <span class="text-sm font-medium text-slate-200">${user.search_play_count.toLocaleString()}</span>
                    </td>
                    <td class="px-3 py-2 text-center">
                        <span class="text-sm font-medium text-slate-200">${user.playlist_play_count.toLocaleString()}</span>
                    </td>
                </tr>
            `;
        });

        tableBody.innerHTML = tableHTML;
    }

    /**
     * Get initials from name
     * @param {string} name Name to get initials from
     * @returns {string} Initials
     */
    getInitials(name) {
        if (!name) return '?';

        const parts = name.split(' ');
        if (parts.length === 1) {
            return name.charAt(0).toUpperCase();
        }

        return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
    }

    /**
     * Get random color
     * @returns {string} Random color
     */
    getRandomColor() {
        const hue = Math.floor(Math.random() * 360);
        return `hsla(${hue}, 70%, 60%, 0.8)`;
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Create global instance
    window.statisticsPanel = new StatisticsPanel();

    // Check if we're in the admin context
    if (window.isAdmin || window.isSuperAdmin) {
        console.log('User is admin or superadmin, showing statistics panel link');

        // Show the statistics link in the sidebar
        const statsLink = document.querySelector('.nav-item[data-view="statistics"]');
        if (statsLink) {
            statsLink.classList.remove('hidden');
        }

        // Initialize when statistics panel is shown
        document.addEventListener('view-changed', (e) => {
                if (e.detail.view === 'statistics') {
                    window.statisticsPanel.init();
                }
            });
        }
    });

