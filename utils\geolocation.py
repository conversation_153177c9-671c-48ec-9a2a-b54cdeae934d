"""
Utility for detecting user country based on IP address
"""
import requests
import logging
import time
import threading
from flask import request, g
from functools import lru_cache
import os

# Configure logging
logger = logging.getLogger(__name__)

# Cache for IP to country mapping (to reduce API calls)
# This is an in-memory cache that will be cleared when the server restarts
IP_COUNTRY_CACHE = {}  # Cache cleared by cleanup_country_data.py

# Rate limiting
MAX_REQUESTS_PER_MINUTE = 10
request_times = []
request_lock = threading.Lock()

# Get API key from environment (optional)
IPINFO_API_KEY = os.getenv('IPINFO_API_KEY', '')

@lru_cache(maxsize=1000)
def get_country_from_ip_cached(ip_address):
    """
    Cached version of country lookup to reduce API calls
    Uses Python's lru_cache for efficient caching
    """
    return _get_country_from_ip_internal(ip_address)

def get_country_from_ip(ip_address=None):
    """
    Get country code from IP address using ipinfo.io service
    Returns ISO country code (e.g., 'US', 'GB', 'DE')

    Args:
        ip_address (str, optional): IP address to lookup. If None, uses the current request IP.

    Returns:
        str: Two-letter country code or None if detection failed
    """
    try:
        # Check if we've already processed this IP in this request
        if hasattr(g, 'current_ip_country'):
            return g.current_ip_country

        # If no IP provided, get it from the request
        if not ip_address:
            # Try to get real IP from headers (for when behind proxy/load balancer)
            ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)
            if ip_address:
                # X-Forwarded-For can contain multiple IPs, take the first one
                ip_address = ip_address.split(',')[0].strip()

            # Fallback to remote_addr if still no IP
            if not ip_address:
                ip_address = request.remote_addr

        # Skip for localhost/internal IPs
        if ip_address in ['127.0.0.1', 'localhost', '::1'] or ip_address.startswith('192.168.') or ip_address.startswith('10.'):
            logger.debug(f"Skipping geolocation for internal IP: {ip_address}")
            return None

        # Check in-memory cache first
        if ip_address in IP_COUNTRY_CACHE:
            country_code = IP_COUNTRY_CACHE[ip_address]
            # Store in request context
            g.current_ip_country = country_code
            return country_code

        # Use cached function for actual lookup
        country_code = get_country_from_ip_cached(ip_address)

        # Store in request context
        g.current_ip_country = country_code

        return country_code

    except Exception as e:
        logger.error(f"Error detecting country from IP {ip_address}: {str(e)}")
        return None

def _get_country_from_ip_internal(ip_address):
    """
    Internal function to get country from IP with rate limiting
    """
    # Apply rate limiting
    with request_lock:
        current_time = time.time()
        # Remove requests older than 60 seconds
        while request_times and request_times[0] < current_time - 60:
            request_times.pop(0)

        # Check if we've exceeded the rate limit
        if len(request_times) >= MAX_REQUESTS_PER_MINUTE:
            logger.warning("Rate limit exceeded for geolocation API")
            return None

        # Add current request time
        request_times.append(current_time)

    try:
        # Build URL with API key if available
        url = f'https://ipinfo.io/{ip_address}/json'
        if IPINFO_API_KEY:
            url += f'?token={IPINFO_API_KEY}'

        # Make request to ipinfo.io
        response = requests.get(url, timeout=3)

        if response.status_code == 200:
            data = response.json()
            country_code = data.get('country')
            if country_code:
                # Cache the result
                IP_COUNTRY_CACHE[ip_address] = country_code
                return country_code

        logger.debug(f"Failed to get country for IP {ip_address}: {response.status_code}")
        return None

    except Exception as e:
        logger.error(f"Error detecting country from IP {ip_address}: {str(e)}")
        return None
