from flask import jsonify, request
from flask_login import login_required, current_user
from . import user_api
from models.spotify_credentials import SpotifyCredentials
from models.user import User
import logging

@user_api.route('/connected-services', methods=['GET'])
@login_required
def get_connected_services():
    """Get all connected external services for the current user"""
    try:
        services = []

        # Log user ID for debugging
        logging.info(f"Getting connected services for user: {current_user.id}")

        # Check for Spotify connection
        spotify_credentials = SpotifyCredentials.objects(user=current_user.id).first()

        # Log Spotify credentials for debugging
        if spotify_credentials:
            logging.info(f"Found Spotify credentials: {spotify_credentials.id}")
            services.append({
                'id': 'spotify',
                'name': 'Spotify',
                'icon': 'music',
                'color': 'green',
                'connected_at': spotify_credentials.created_at.isoformat(),
                'last_used': spotify_credentials.updated_at.isoformat()
            })
        else:
            logging.info("No Spotify credentials found for user")

            # Check if there are any Spotify credentials in the database
            all_creds = SpotifyCredentials.objects().count()
            logging.info(f"Total Spotify credentials in database: {all_creds}")

        # Check for Discord connection (placeholder for future implementation)
        # discord_credentials = DiscordCredentials.objects(user=current_user.id).first()
        # if discord_credentials:
        #     services.append({
        #         'id': 'discord',
        #         'name': 'Discord',
        #         'icon': 'message-square',
        #         'color': 'purple',
        #         'connected_at': discord_credentials.created_at.isoformat(),
        #         'last_used': discord_credentials.updated_at.isoformat()
        #     })

        # Log response for debugging
        logging.info(f"Returning services: {services}")

        return jsonify({
            'success': True,
            'services': services
        })
    except Exception as e:
        logging.error(f"Error getting connected services: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_api.route('/disconnect-service/<service_id>', methods=['POST'])
@login_required
def disconnect_service(service_id):
    """Disconnect an external service"""
    try:
        if service_id == 'spotify':
            # Delete Spotify credentials
            spotify_credentials = SpotifyCredentials.objects(user=current_user.id).first()
            if spotify_credentials:
                spotify_credentials.delete()
                return jsonify({
                    'success': True,
                    'message': 'Spotify disconnected successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'No Spotify connection found'
                }), 404
        # elif service_id == 'discord':
        #     # Delete Discord credentials
        #     discord_credentials = DiscordCredentials.objects(user=current_user.id).first()
        #     if discord_credentials:
        #         discord_credentials.delete()
        #         return jsonify({
        #             'success': True,
        #             'message': 'Discord disconnected successfully'
        #         })
        #     else:
        #         return jsonify({
        #             'success': False,
        #             'error': 'No Discord connection found'
        #         }), 404
        else:
            return jsonify({
                'success': False,
                'error': f'Unknown service: {service_id}'
            }), 400
    except Exception as e:
        logging.error(f"Error disconnecting service {service_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_api.route('/member-since', methods=['GET'])
@login_required
def get_member_since():
    """Get the user's account creation date"""
    try:
        # Get the user's creation date
        created_at = current_user.created_at

        # Format the date as ISO string
        created_at_iso = created_at.isoformat() if created_at else None

        return jsonify({
            'success': True,
            'created_at': created_at_iso,
            'username': current_user.username
        })
    except Exception as e:
        logging.error(f"Error getting member since date: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@user_api.route('/member-since/<username>', methods=['GET'])
@login_required
def get_user_member_since(username):
    """Get a specific user's account creation date by username"""
    try:
        # Find the user by username
        user = User.objects(username=username).first()

        if not user:
            return jsonify({
                'success': False,
                'error': 'User not found'
            }), 404

        # Get the user's creation date
        created_at = user.created_at

        # Format the date as ISO string
        created_at_iso = created_at.isoformat() if created_at else None

        return jsonify({
            'success': True,
            'created_at': created_at_iso,
            'username': user.username
        })
    except Exception as e:
        logging.error(f"Error getting member since date for {username}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500