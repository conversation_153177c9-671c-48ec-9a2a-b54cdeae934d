/**
 * ServiceEditor.js
 * Provides functionality for editing service code
 */

class ServiceEditor {
    constructor() {
        this.currentService = null;
        this.currentFile = null;
        this.isEditing = false;
    }

    /**
     * Open the code editor for a service
     * @param {string} serviceId Service ID
     */
    openEditor(serviceId) {
        this.currentService = serviceId;

        // Create modal if it doesn't exist
        let modal = document.getElementById('serviceEditorModal');

        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'serviceEditorModal';
            modal.className = 'serviceEditorModal';
            modal.innerHTML = `
                <div class="bg-slate-900 rounded-lg w-11/12 max-w-5xl h-5/6 flex flex-col">
                    <div class="flex items-center justify-between p-4 border-b border-slate-700">
                        <div class="flex items-center">
                            <h3 class="text-lg font-medium text-slate-100">Editing Service: <span id="editorServiceName">Loading...</span></h3>
                            <div class="ml-4 flex space-x-2">
                                <button id="editorFileJs" class="editor-tab active px-3 py-1 text-xs rounded-t border-b-2 border-cyan-500 text-slate-200 bg-slate-800">index.js</button>
                                <button id="editorFileHtml" class="editor-tab px-3 py-1 text-xs rounded-t text-slate-400 bg-slate-700">HTML</button>
                                <button id="editorFileCss" class="editor-tab px-3 py-1 text-xs rounded-t text-slate-400 bg-slate-700">CSS</button>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button id="saveServiceCode" class="px-3 py-1 bg-green-600 hover:bg-green-500 text-white rounded text-sm flex items-center">
                                <i data-lucide="save" class="h-4 w-4 mr-1"></i>
                                Save
                            </button>
                            <button id="closeServiceEditor" class="text-slate-400 hover:text-slate-100">
                                <i data-lucide="x" class="h-5 w-5"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex-grow p-4 overflow-hidden">
                        <textarea id="serviceCodeEditor" class="w-full h-full bg-slate-900 text-slate-300 font-mono text-sm p-2 focus:outline-none border border-slate-700 rounded" spellcheck="false"></textarea>
                    </div>
                    <div class="flex justify-between items-center p-4 border-t border-slate-700 text-xs text-slate-400">
                        <div>Press Tab to indent</div>
                        <div id="editorStatus">Ready</div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons();
            }

            // Add event listeners
            const closeButton = document.getElementById('closeServiceEditor');
            const saveButton = document.getElementById('saveServiceCode');
            const jsTab = document.getElementById('editorFileJs');
            const htmlTab = document.getElementById('editorFileHtml');
            const cssTab = document.getElementById('editorFileCss');

            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    this.closeEditor();
                });
            }

            if (saveButton) {
                saveButton.addEventListener('click', () => {
                    this.saveCurrentFile();
                });
            }

            if (jsTab && htmlTab && cssTab) {
                jsTab.addEventListener('click', () => {
                    this.switchFile('index.js');
                    this.activateTab(jsTab);
                    this.deactivateTab(htmlTab);
                    this.deactivateTab(cssTab);
                });

                htmlTab.addEventListener('click', () => {
                    this.switchFile('index.html');
                    this.activateTab(htmlTab);
                    this.deactivateTab(jsTab);
                    this.deactivateTab(cssTab);
                });

                cssTab.addEventListener('click', () => {
                    this.switchFile('index.css');
                    this.activateTab(cssTab);
                    this.deactivateTab(jsTab);
                    this.deactivateTab(htmlTab);
                });
            }

            // Set up keyboard shortcuts
            const editor = document.getElementById('serviceCodeEditor');
            if (editor) {
                editor.addEventListener('keydown', (e) => {
                    // Tab key
                    if (e.key === 'Tab') {
                        e.preventDefault();

                        // Insert tab at cursor position
                        const start = editor.selectionStart;
                        const end = editor.selectionEnd;

                        editor.value = editor.value.substring(0, start) + '    ' + editor.value.substring(end);

                        // Move cursor after tab
                        editor.selectionStart = editor.selectionEnd = start + 4;
                    }

                    // Ctrl+S to save
                    if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        this.saveCurrentFile();
                    }
                });
            }
        }

        // Load service data
        this.loadServiceData(serviceId);
    }

    /**
     * Close the code editor
     */
    closeEditor() {
        const modal = document.getElementById('serviceEditorModal');
        if (modal) {
            modal.remove();
        }

        this.currentService = null;
        this.currentFile = null;
        this.isEditing = false;
    }

    /**
     * Load service data
     * @param {string} serviceId Service ID
     */
    loadServiceData(serviceId) {
        // In a real implementation, this would load the service data from the server
        // For now, we'll simulate loading the service data

        // Update service name
        const serviceNameElement = document.getElementById('editorServiceName');
        if (serviceNameElement) {
            // Find service in ServiceConfig
            const service = window.ServiceConfig.services.find(s => s.id === serviceId);
            if (service) {
                serviceNameElement.textContent = service.name;
            } else {
                serviceNameElement.textContent = serviceId;
            }
        }

        // Load index.js by default
        this.switchFile('index.js');
    }

    /**
     * Switch to a different file
     * @param {string} fileName File name
     */
    switchFile(fileName) {
        this.currentFile = fileName;

        // Update status
        const statusElement = document.getElementById('editorStatus');
        if (statusElement) {
            statusElement.textContent = `Editing ${fileName}`;
        }

        // Load file content
        this.loadFileContent(this.currentService, fileName);
    }

    /**
     * Load file content
     * @param {string} serviceId Service ID
     * @param {string} fileName File name
     */
    loadFileContent(serviceId, fileName) {
        const editor = document.getElementById('serviceCodeEditor');
        if (!editor) return;

        // In a real implementation, this would load the file content from the server
        // For now, we'll simulate loading the file content

        let content = '';

        if (fileName === 'index.js') {
            // Generate sample JavaScript code
            content = `/**
 * ${this.capitalizeFirstLetter(serviceId)} Service
 *
 * This file contains the implementation of the ${this.capitalizeFirstLetter(serviceId)} service.
 */

// Service class definition
window.${this.capitalizeFirstLetter(serviceId)}Service = class ${this.capitalizeFirstLetter(serviceId)}Service extends window.Service {
    constructor(config) {
        super(config);
        // Custom initialization
    }

    /**
     * Initialize the service
     * @returns {Promise<void>}
     */
    async init() {
        // Initialize service data
        console.log('Initializing ${this.capitalizeFirstLetter(serviceId)} service');
    }

    /**
     * Render the service card HTML
     * @returns {string} HTML string
     */
    render() {
        try {
            console.log(\`Rendering service: \${this.id}\`);
            const statusClass = this.status === 'offline' ? 'offline' : '';
            const statusDot = this.status === 'online' ? 'bg-green-500' : 'bg-red-500';
            const statusText = this.status === 'online' ? 'Online' : (this.status === 'offline' ? 'Offline' : 'Coming Soon');

            let actionButton = '';

            if (this.status === 'online' && this.url) {
                // External link for online services with URL
                actionButton = \`
                    <a href="\${this.url}" target="_blank"
                       class="w-full bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-md px-4 py-2 text-center transition-colors flex items-center justify-center group">
                        <span class="text-sm text-slate-300 group-hover:text-slate-100">Launch \${this.name}</span>
                        <i data-lucide="chevron-right" class="h-4 w-4 ml-1"></i>
                    </a>
                \`;
            } else {
                // Disabled button for offline or coming soon services
                actionButton = \`
                    <div class="w-full bg-slate-700/50 border border-slate-600/50 rounded-md px-4 py-2 text-center cursor-not-allowed">
                        <span class="text-sm text-slate-300 animate-pulse">\${this.status === 'offline' ? 'Offline' : 'Coming Soon'}</span>
                    </div>
                \`;
            }

            const html = \`
                <div class="service-card \${statusClass} bg-slate-800/50 border border-\${this.color}-500/30 p-6 rounded-lg hover:bg-slate-800/80 transition-colors" data-service-id="\${this.id}">
                    <div class="flex flex-col h-full">
                        <div class="flex items-start justify-between mb-4">
                            <div class="p-2 rounded-lg bg-\${this.color}-500/10">
                                <i data-lucide="\${this.icon}" class="h-6 w-6 text-\${this.color}-500"></i>
                            </div>
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full \${statusDot} mr-1.5"></div>
                                <span class="text-xs text-slate-400">\${statusText}</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-medium text-slate-100 mb-2">\${this.name}</h3>
                        <p class="text-sm text-slate-400 mb-4 flex-grow">\${this.description}</p>
                        \${actionButton}
                    </div>
                </div>
            \`;

            return html;
        } catch (error) {
            console.error(\`Error rendering service \${this.id}:\`, error);
            return \`<div class="service-card bg-red-900/50 border border-red-500/30 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-red-100 mb-2">Error: \${this.name}</h3>
                <p class="text-sm text-red-200">Failed to render service</p>
            </div>\`;
        }
    }

    /**
     * Get service metadata for the store
     * @returns {Object} Service metadata
     */
    static getMetadata() {
        return {
            id: '${serviceId}',
            name: '${this.capitalizeFirstLetter(serviceId)}',
            description: 'Custom service',
            icon: 'box',
            color: 'slate',
            category: 'Custom',
            version: '1.0.0',
            author: 'User',
            features: [
                'Custom feature 1',
                'Custom feature 2',
                'Custom feature 3'
            ]
        };
    }
};`;
        } else if (fileName === 'index.html') {
            // Generate sample HTML code
            content = `<!-- ${this.capitalizeFirstLetter(serviceId)} Service HTML -->
<div class="service-content">
    <h1>Welcome to ${this.capitalizeFirstLetter(serviceId)}</h1>
    <p>This is a custom service.</p>

    <div class="service-controls">
        <button class="primary-button">Action 1</button>
        <button class="secondary-button">Action 2</button>
    </div>
</div>`;
        } else if (fileName === 'index.css') {
            // Generate sample CSS code
            content = `/* ${this.capitalizeFirstLetter(serviceId)} Service CSS */
/* Note: Base styles are already defined in dashboard.css */

/* Add any service-specific overrides here */
.${serviceId}-specific-element {
    /* Custom styles for this service */
}`;
        }

        // Set editor content
        editor.value = content;
    }

    /**
     * Save the current file
     */
    saveCurrentFile() {
        if (!this.currentService || !this.currentFile) {
            console.error('No service or file selected');
            return;
        }

        const editor = document.getElementById('serviceCodeEditor');
        if (!editor) return;

        const content = editor.value;

        // Update status
        const statusElement = document.getElementById('editorStatus');
        if (statusElement) {
            statusElement.textContent = `Saving ${this.currentFile}...`;
        }

        // In a real implementation, this would save the file to the server
        // For now, we'll simulate saving the file

        setTimeout(() => {
            console.log(`Saved ${this.currentFile} for service ${this.currentService}`);
            console.log('Content:', content);

            // Update status
            if (statusElement) {
                statusElement.textContent = `Saved ${this.currentFile}`;

                // Reset status after a delay
                setTimeout(() => {
                    statusElement.textContent = `Editing ${this.currentFile}`;
                }, 2000);
            }
        }, 500);
    }

    /**
     * Activate a tab
     * @param {HTMLElement} tab Tab element
     */
    activateTab(tab) {
        tab.classList.add('active', 'border-b-2', 'border-cyan-500', 'text-slate-200', 'bg-slate-800');
        tab.classList.remove('text-slate-400', 'bg-slate-700');
    }

    /**
     * Deactivate a tab
     * @param {HTMLElement} tab Tab element
     */
    deactivateTab(tab) {
        tab.classList.remove('active', 'border-b-2', 'border-cyan-500', 'text-slate-200', 'bg-slate-800');
        tab.classList.add('text-slate-400', 'bg-slate-700');
    }

    /**
     * Capitalize the first letter of a string
     * @param {string} string String to capitalize
     * @returns {string} Capitalized string
     */
    capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
}

// Create global service editor instance
window.serviceEditor = new ServiceEditor();
