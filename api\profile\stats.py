from flask import jsonify
from flask_login import current_user
from models.user import User
from models.service_usage import ServiceUsage
from models.user_activity import UserActivity
from api.profile import profile_api
from datetime import datetime, timedelta

@profile_api.route('/public_stats/<username>', methods=['GET'])
def get_public_user_stats(username):
    """Get a user's statistics"""
    # Find the user
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    # Check if the profile is private and not the current user
    from models.profile_customization import ProfileCustomization
    profile = ProfileCustomization.get_or_create(user)
    
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({'success': False, 'error': 'Profile is private'}), 403
    
    # Calculate account age in days
    account_age = (datetime.now() - user.created_at).days
    
    # Get service usage stats
    service_usage = ServiceUsage.objects(user=user)
    total_services_used = len(set(usage.service_name for usage in service_usage)) if service_usage else 0
    
    # Get user activity for the last 30 days
    thirty_days_ago = datetime.now() - timedelta(days=30)
    activities = UserActivity.objects(user=user, last_active__gte=thirty_days_ago)
    
    # Calculate active days (days with activity)
    active_days = len(set(activity.last_active.date() for activity in activities)) if activities else 0
    
    # Get sections used
    sections_used = len(set(activity.section for activity in activities)) if activities else 0
    
    # Compile stats
    stats = {
        'Account Age': f"{account_age} days",
        'Services Used': total_services_used,
        'Active Days (30d)': active_days,
        'Sections Used': sections_used,
        'Last Login': user.last_login.strftime('%Y-%m-%d %H:%M')
    }
    
    # Add country if available
    if user.country_code:
        stats['Country'] = user.country_code
    
    return jsonify({
        'success': True,
        'stats': stats
    })