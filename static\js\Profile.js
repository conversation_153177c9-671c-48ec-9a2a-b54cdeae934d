/**
 * Profile.js
 * Handles user profile functionality
 */
class Profile {
    constructor() {
        this.currentUsername = '';
        this.isLoading = false;
        this.activeTab = 'account';
    }

    /**
     * Initialize the profile section
     */
    init() {
        console.log('Initializing Profile...');

        // Get current user data
        this.fetchCurrentUser();

        // Fetch usage statistics
        this.fetchUsageStats();

        // Fetch login history
        this.fetchLoginHistory();

        // Set up event listeners
        this.setupEventListeners();

        // Set up tab navigation
        this.setupTabNavigation();

        // Send user's timezone to the server
        this.sendUserTimezone();

        // Initialize user credits
        this.initializeUserCredits();
    }

    /**
     * Initialize user credits component
     */
    initializeUserCredits() {
        if (window.UserCredits) {
            console.log('Initializing User Credits component...');
            window.userCredits = new UserCredits();
            window.userCredits.init();
        }
    }

    /**
     * Format a date for display
     * @param {Date} date Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        if (!date) return '';

        // Format as relative time if recent, otherwise as date
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) {
            return 'Just now';
        } else if (diffMins < 60) {
            return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
        } else if (diffHours < 24) {
            return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        } else if (diffDays < 7) {
            return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * Send the user's timezone to the server
     */
    async sendUserTimezone() {
        try {
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            const response = await fetch('/api/profile/update-timezone', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ timezone })
            });

            if (!response.ok) {
                throw new Error('Failed to update timezone');
            }

            console.log('Timezone updated successfully:', timezone);
        } catch (error) {
            console.error('Error updating timezone:', error);
        }
    }

    /**
     * Fetch current user data
     */
    async fetchCurrentUser() {
        try {
            const response = await fetch('/auth/current-user');
            if (!response.ok) {
                throw new Error('Failed to fetch user data');
            }

            const userData = await response.json();
            this.currentUsername = userData.username;

            // Update UI with user data
            this.updateUI(userData);
        } catch (error) {
            console.error('Error fetching user data:', error);
        }
    }

    /**
     * Update UI with user data
     */
    updateUI(userData) {
        // Update username field
        const usernameInput = document.getElementById('profileUsername');
        if (usernameInput) {
            usernameInput.value = userData.username;
        }

        // Update display name header with username
        const displayNameHeader = document.getElementById('profileDisplayName');
        if (displayNameHeader) {
            displayNameHeader.textContent = userData.username;
        }

        // Update email display
        const emailDisplay = document.getElementById('profileEmail');
        if (emailDisplay) {
            emailDisplay.textContent = userData.email;
        }

        // Update profile avatar/initials
        this.updateProfileAvatar(userData);

        // Update join date
        const joinDateElement = document.getElementById('profileJoinDate');
        if (joinDateElement) {
            // First try to use created_at from userData if available
            if (userData.created_at) {
                const joinDate = new Date(userData.created_at);
                joinDateElement.textContent = `Member since: ${joinDate.toLocaleDateString()}`;
            } else {
                // If not available, fetch from the new endpoint
                this.fetchMemberSince();
            }
        }

        // Update last login time
        const lastLoginElement = document.getElementById('lastLoginTime');
        if (lastLoginElement && userData.last_login) {
            const lastLogin = new Date(userData.last_login);
            lastLoginElement.textContent = this.formatDateTime(lastLogin);
        }

        // Update account type tag
        const accountTypeTag = document.getElementById('accountTypeTag');
        if (accountTypeTag) {
            if (userData.is_admin) {
                accountTypeTag.innerHTML = `
                    <i data-lucide="shield" class="h-3 w-3 mr-1"></i>
                    <span>Admin Account</span>
                `;
                accountTypeTag.className = 'bg-red-900/30 text-red-400 text-xs px-2.5 py-1 rounded-full border border-red-800/50 flex items-center';
            } else if (userData.google_id) {
                accountTypeTag.innerHTML = `
                    <i data-lucide="globe" class="h-4 w-4 mr-2 text-cyan-400"></i>

                    <span>Google Account</span>
                `;
                accountTypeTag.className = 'bg-blue-900/30 text-blue-400 text-xs px-2.5 py-1 rounded-full border border-blue-800/50 flex items-center';
            }

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-3", "w-3"]
                    },
                    elements: [accountTypeTag]
                });
            }
        }

        // Handle Google user vs. regular user UI differences
        this.handleAuthTypeUI(userData);

        // Set 2FA toggle state
        const twoFactorToggle = document.getElementById('twoFactorToggle');
        if (twoFactorToggle) {
            twoFactorToggle.checked = userData.two_factor_enabled || false;
        }

        // Set email notifications toggle state (default to true if not set)
        const emailNotificationsToggle = document.getElementById('emailNotificationsToggle');
        if (emailNotificationsToggle) {
            emailNotificationsToggle.checked = userData.email_notifications !== false;
        }
    }

    /**
     * Update profile avatar or initials
     */
    updateProfileAvatar(userData) {
        const profileAvatar = document.getElementById('profileAvatar');
        const profileInitials = document.getElementById('profileInitials');

        if (!profileAvatar || !profileInitials) return;

        if (userData.profile_picture) {
            // User has a profile picture
            profileInitials.style.display = 'none';

            // Check if image already exists
            let profileImg = profileAvatar.querySelector('img');
            if (!profileImg) {
                profileImg = document.createElement('img');
                profileImg.className = 'w-full h-full object-cover';
                profileAvatar.appendChild(profileImg);
            }

            profileImg.src = userData.profile_picture;
            profileAvatar.classList.remove('bg-gradient-to-r', 'from-cyan-500', 'to-blue-500');
        } else {
            // User doesn't have a profile picture, show initials
            profileInitials.style.display = 'block';

            // Remove any existing image
            const profileImg = profileAvatar.querySelector('img');
            if (profileImg) {
                profileImg.remove();
            }

            // Set initials
            const name = userData.username;
            const initials = this.getInitials(name);
            profileInitials.textContent = initials;

            // Add gradient background
            profileAvatar.classList.add('bg-gradient-to-r', 'from-cyan-500', 'to-blue-500');
        }
    }

    /**
     * Get initials from a name
     */
    getInitials(name) {
        if (!name) return 'KS';

        // Handle email addresses
        if (name.includes('@')) {
            name = name.split('@')[0];
        }

        // Split by spaces, dots, underscores, or hyphens
        const parts = name.split(/[\s._-]+/);

        if (parts.length === 1) {
            // Single word, take first two characters
            return name.substring(0, 2).toUpperCase();
        } else {
            // Multiple words, take first character of first two words
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
        }
    }

    /**
     * Fetch member since date from the API
     */
    async fetchMemberSince() {
        try {
            const joinDateElement = document.getElementById('profileJoinDate');
            if (!joinDateElement) return;

            // Show loading state
            joinDateElement.textContent = 'Member since: Loading...';

            const response = await fetch('/api/user/member-since');
            if (!response.ok) {
                throw new Error('Failed to fetch member since date');
            }

            const data = await response.json();
            if (data.success && data.created_at) {
                const joinDate = new Date(data.created_at);
                joinDateElement.textContent = `Member since: ${joinDate.toLocaleDateString()}`;
            } else {
                joinDateElement.textContent = 'Member since: Unknown';
            }
        } catch (error) {
            console.error('Error fetching member since date:', error);
            const joinDateElement = document.getElementById('profileJoinDate');
            if (joinDateElement) {
                joinDateElement.textContent = 'Member since: Unknown';
            }
        }
    }

    /**
     * Format date and time
     */
    formatDateTime(date) {
        if (!date) return 'Unknown';

        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays < 1) {
            // Today, show time
            return `Today at ${date.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays === 1) {
            // Yesterday
            return `Yesterday at ${date.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })}`;
        } else if (diffDays < 7) {
            // Within a week
            return `${diffDays} days ago`;
        } else {
            // More than a week
            return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
        }
    }

    /**
     * Handle UI differences between Google users and regular users
     */
    handleAuthTypeUI(userData) {
        const isGoogleUser = !!userData.google_id;

        // Get UI elements
        const passwordSection = document.getElementById('passwordSection');
        const googleAccountSection = document.getElementById('googleAccountSection');
        const twoFactorSection = document.getElementById('twoFactorSection');

        if (isGoogleUser) {
            // For Google users: hide password and 2FA sections, show Google account notice
            if (passwordSection) passwordSection.classList.add('hidden');
            if (googleAccountSection) googleAccountSection.classList.remove('hidden');
            if (twoFactorSection) twoFactorSection.classList.add('hidden');
        } else {
            // For regular users: show password and 2FA sections, hide Google account notice
            if (passwordSection) passwordSection.classList.remove('hidden');
            if (googleAccountSection) googleAccountSection.classList.add('hidden');
            if (twoFactorSection) twoFactorSection.classList.remove('hidden');
        }
    }

    /**
     * Fetch login history
     */
    async fetchLoginHistory() {
        try {
            const loginHistoryContainer = document.querySelector('.login-history-list');
            if (!loginHistoryContainer) return;

            // Show loading state
            loginHistoryContainer.innerHTML = '<div class="text-center py-4 text-slate-400">Loading login history...</div>';

            const response = await fetch('/api/profile/login-history');
            if (!response.ok) {
                throw new Error('Failed to fetch login history');
            }

            const data = await response.json();

            if (!data.success || !data.login_history || data.login_history.length === 0) {
                loginHistoryContainer.innerHTML = '<div class="text-center py-4 text-slate-400">No login history available</div>';
                return;
            }

            // Clear container
            loginHistoryContainer.innerHTML = '';

            // Add login history entries
            data.login_history.forEach(entry => {
                const loginTime = new Date(entry.login_time);
                const deviceInfo = entry.device_info || {};
                const deviceType = deviceInfo.device_type || 'Unknown';
                const browser = deviceInfo.browser || 'Unknown';
                const os = deviceInfo.os || 'Unknown';

                // Create login history entry element
                const entryElement = document.createElement('div');
                entryElement.className = 'bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 mb-2';

                // Determine icon based on device type
                let deviceIcon = 'monitor';
                if (deviceType === 'Mobile') deviceIcon = 'smartphone';
                if (deviceType === 'Tablet') deviceIcon = 'tablet';

                // Create HTML content
                entryElement.innerHTML = `
                    <div class="flex justify-between items-center mb-1">
                        <div class="flex items-center">
                            <i data-lucide="${deviceIcon}" class="h-4 w-4 text-cyan-400 mr-2"></i>
                            <span class="text-sm text-slate-200">${browser} on ${os} (${deviceType})</span>
                        </div>
                        ${entry.is_current ? '<span class="text-xs bg-green-900/30 text-green-400 px-2 py-0.5 rounded-full">Active</span>' : ''}
                    </div>
                    <div class="text-xs text-slate-400">
                        <span>Login: ${this.formatDateTime(loginTime)}</span>
                        ${entry.ip_address ? `<span class="ml-2">• IP: ${entry.ip_address}</span>` : ''}
                    </div>
                `;

                // Add to container
                loginHistoryContainer.appendChild(entryElement);
            });

            // Initialize Lucide icons
            if (window.lucide) {
                lucide.createIcons({
                    attrs: {
                        class: ["h-4", "w-4"]
                    },
                    elements: [loginHistoryContainer]
                });
            }
        } catch (error) {
            console.error('Error fetching login history:', error);
            const loginHistoryContainer = document.querySelector('.login-history-list');
            if (loginHistoryContainer) {
                loginHistoryContainer.innerHTML = '<div class="text-center py-4 text-slate-400">Failed to load login history</div>';
            }
        }
    }

    /**
     * Set up tab navigation
     */
    setupTabNavigation() {
        const tabs = document.querySelectorAll('.profile-tab');
        const tabPanes = document.querySelectorAll('.profile-tab-pane');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Get tab ID
                const tabId = tab.id.replace('profileTab', '').toLowerCase();

                // Update active tab
                this.activeTab = tabId;

                // Update tab styles
                tabs.forEach(t => {
                    t.classList.remove('active', 'border-cyan-500', 'text-cyan-400');
                    t.classList.add('border-transparent', 'text-slate-400');
                });
                tab.classList.add('active', 'border-cyan-500', 'text-cyan-400');
                tab.classList.remove('border-transparent', 'text-slate-400');

                // Show active tab pane
                tabPanes.forEach(pane => {
                    pane.classList.add('hidden');
                });
                document.getElementById(`profileContent${tabId.charAt(0).toUpperCase() + tabId.slice(1)}`).classList.remove('hidden');

                // Refresh data for specific tabs
                if (tabId === 'usage') {
                    this.fetchUsageStats();
                } else if (tabId === 'security') {
                    this.fetchLoginHistory();
                }
            });
        });
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Update username button
        const updateUsernameBtn = document.getElementById('updateUsernameBtn');
        if (updateUsernameBtn) {
            updateUsernameBtn.addEventListener('click', () => this.updateUsername());
        }



        // Change avatar button
        const changeAvatarBtn = document.getElementById('changeAvatarBtn');
        if (changeAvatarBtn) {
            changeAvatarBtn.addEventListener('click', () => this.showChangeAvatarModal());
        }

        // Reset password button
        const resetPasswordBtn = document.getElementById('resetPasswordBtn');
        if (resetPasswordBtn) {
            resetPasswordBtn.addEventListener('click', () => this.showResetPasswordModal());
        }

        // Two-factor toggle
        const twoFactorToggle = document.getElementById('twoFactorToggle');
        if (twoFactorToggle) {
            twoFactorToggle.addEventListener('change', () => this.toggleTwoFactor(twoFactorToggle.checked));
        }

        // Email notifications toggle
        const emailNotificationsToggle = document.getElementById('emailNotificationsToggle');
        if (emailNotificationsToggle) {
            emailNotificationsToggle.addEventListener('change', () => this.toggleEmailNotifications(emailNotificationsToggle.checked));
        }

        // Clear threads button
        const clearThreadsBtn = document.getElementById('clearThreadsBtn');
        if (clearThreadsBtn) {
            clearThreadsBtn.addEventListener('click', () => this.clearThreads());
        }

        // Clear rooms button
        const clearRoomsBtn = document.getElementById('clearRoomsBtn');
        if (clearRoomsBtn) {
            clearRoomsBtn.addEventListener('click', () => this.clearRooms());
        }

        // Export data button
        const exportDataBtn = document.getElementById('exportDataBtn');
        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => this.requestDataExport());
        }

        // Request data deletion button
        const requestDataDeletionBtn = document.getElementById('requestDataDeletionBtn');
        if (requestDataDeletionBtn) {
            requestDataDeletionBtn.addEventListener('click', () => this.requestDataDeletion());
        }
    }

    /**
     * Update username
     */
    async updateUsername() {
        const usernameInput = document.getElementById('profileUsername');
        if (!usernameInput) return;

        const newUsername = usernameInput.value.trim();
        if (!newUsername) {
            this.showToast('Username cannot be empty', 'error');
            return;
        }

        if (newUsername === this.currentUsername) {
            this.showToast('No changes made to username', 'info');
            return;
        }

        try {
            this.setLoading(true, 'username');

            const response = await fetch('/api/profile/update-username', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username: newUsername })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to update username');
            }

            this.currentUsername = data.username;
            this.showToast('Username updated successfully', 'success');

            // Refresh user data in the UI
            this.fetchCurrentUser();
        } catch (error) {
            console.error('Error updating username:', error);
            this.showToast(error.message, 'error');
        } finally {
            this.setLoading(false, 'username');
        }
    }



    /**
     * Show change avatar modal
     */
    showChangeAvatarModal() {
        const modal = document.getElementById('avatarUploadModal');
        const closeBtn = document.getElementById('closeAvatarModal');
        const cancelBtn = document.getElementById('cancelAvatarUpload');
        const fileInput = document.getElementById('avatarFile');
        const selectedFileName = document.getElementById('selectedFileName');
        const uploadBtn = document.getElementById('uploadAvatarBtn');
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarPreviewInitials = document.getElementById('avatarPreviewInitials');

        if (!modal) return;

        // Reset form
        if (fileInput) fileInput.value = '';
        if (selectedFileName) selectedFileName.textContent = 'No file selected';
        if (uploadBtn) uploadBtn.disabled = true;

        // Reset preview to current user avatar or initials
        this.updateAvatarPreview();

        // Show modal
        modal.classList.remove('hidden');
        document.body.classList.add('modal-open');

        // Set up event listeners
        const closeModal = () => {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
        };

        if (closeBtn) closeBtn.onclick = closeModal;
        if (cancelBtn) cancelBtn.onclick = closeModal;

        // Handle file selection
        if (fileInput) {
            fileInput.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    // Update selected file name
                    selectedFileName.textContent = file.name;

                    // Enable upload button
                    uploadBtn.disabled = false;

                    // Show preview
                    this.previewAvatar(file);
                } else {
                    selectedFileName.textContent = 'No file selected';
                    uploadBtn.disabled = true;
                    this.updateAvatarPreview();
                }
            };
        }

        // Handle upload button click
        if (uploadBtn) {
            uploadBtn.onclick = () => this.uploadAvatar();
        }

        // Close modal when clicking outside
        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };
    }

    /**
     * Update avatar preview in modal to match current user avatar
     */
    updateAvatarPreview() {
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarPreviewInitials = document.getElementById('avatarPreviewInitials');

        if (!avatarPreview || !avatarPreviewInitials) return;

        // Get current user data
        fetch('/api/profile/info')
            .then(response => response.json())
            .then(userData => {
                if (userData.profile_picture) {
                    // User has a profile picture
                    avatarPreviewInitials.style.display = 'none';

                    // Check if image already exists
                    let previewImg = avatarPreview.querySelector('img');
                    if (!previewImg) {
                        previewImg = document.createElement('img');
                        previewImg.className = 'w-full h-full object-cover';
                        avatarPreview.appendChild(previewImg);
                    }

                    previewImg.src = userData.profile_picture;
                    avatarPreview.classList.remove('bg-gradient-to-r', 'from-cyan-500', 'to-blue-500');
                } else {
                    // User doesn't have a profile picture, show initials
                    avatarPreviewInitials.style.display = 'block';

                    // Remove any existing image
                    const previewImg = avatarPreview.querySelector('img');
                    if (previewImg) {
                        previewImg.remove();
                    }

                    // Set initials
                    const name = userData.username;
                    const initials = this.getInitials(name);
                    avatarPreviewInitials.textContent = initials;

                    // Add gradient background
                    avatarPreview.classList.add('bg-gradient-to-r', 'from-cyan-500', 'to-blue-500');
                }
            })
            .catch(error => {
                console.error('Error fetching user data for avatar preview:', error);
            });
    }

    /**
     * Preview selected avatar file
     */
    previewAvatar(file) {
        const avatarPreview = document.getElementById('avatarPreview');
        const avatarPreviewInitials = document.getElementById('avatarPreviewInitials');

        if (!avatarPreview || !avatarPreviewInitials || !file) return;

        // Hide initials
        avatarPreviewInitials.style.display = 'none';

        // Create or get image element
        let previewImg = avatarPreview.querySelector('img');
        if (!previewImg) {
            previewImg = document.createElement('img');
            previewImg.className = 'w-full h-full object-cover';
            avatarPreview.appendChild(previewImg);
        }

        // Create file reader to read the file
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            avatarPreview.classList.remove('bg-gradient-to-r', 'from-cyan-500', 'to-blue-500');
        };
        reader.readAsDataURL(file);
    }

    /**
     * Upload avatar to server
     */
    uploadAvatar() {
        const fileInput = document.getElementById('avatarFile');
        const uploadBtn = document.getElementById('uploadAvatarBtn');
        const uploadBtnText = document.getElementById('uploadBtnText');
        const uploadSpinner = document.getElementById('uploadSpinner');
        const modal = document.getElementById('avatarUploadModal');

        if (!fileInput || !fileInput.files[0]) {
            this.showToast('Please select a file first', 'error');
            return;
        }

        // Show loading state
        if (uploadBtn) uploadBtn.disabled = true;
        if (uploadBtnText) uploadBtnText.classList.add('hidden');
        if (uploadSpinner) uploadSpinner.classList.remove('hidden');

        // Create form data
        const formData = new FormData();
        formData.append('avatar', fileInput.files[0]);

        // Send request to server
        fetch('/api/profile/avatar/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading state
            if (uploadBtn) uploadBtn.disabled = false;
            if (uploadBtnText) uploadBtnText.classList.remove('hidden');
            if (uploadSpinner) uploadSpinner.classList.add('hidden');

            if (data.success) {
                // Close modal
                if (modal) {
                    modal.classList.add('hidden');
                    document.body.classList.remove('modal-open');
                }

                // Show success message
                this.showToast('Avatar uploaded successfully', 'success');

                // Refresh user data to update avatar
                this.fetchCurrentUser();

                // Force refresh the user menu avatar as well
                if (window.dashboard && typeof window.dashboard.fetchCurrentUser === 'function') {
                    window.dashboard.fetchCurrentUser();
                }

                // Check if we need to update avatar info
                this.checkAvatarInfo();
            } else {
                // Show error message
                this.showToast(data.error || 'Failed to upload avatar', 'error');
            }
        })
        .catch(error => {
            console.error('Error uploading avatar:', error);

            // Hide loading state
            if (uploadBtn) uploadBtn.disabled = false;
            if (uploadBtnText) uploadBtnText.classList.remove('hidden');
            if (uploadSpinner) uploadSpinner.classList.add('hidden');

            // Show error message
            this.showToast('Error uploading avatar', 'error');
        });
    }

    /**
     * Check avatar info for Google users
     */
    checkAvatarInfo() {
        fetch('/api/profile/avatar/info')
            .then(response => response.json())
            .then(data => {
                // If user is a Google user and has a custom avatar,
                // we need to update the UI to show the custom avatar
                if (data.is_google_user && data.has_custom_avatar) {
                    this.fetchCurrentUser();

                    // Force refresh the user menu avatar as well
                    if (window.dashboard && typeof window.dashboard.fetchCurrentUser === 'function') {
                        window.dashboard.fetchCurrentUser();
                    }
                }
            })
            .catch(error => {
                console.error('Error checking avatar info:', error);
            });
    }

    /**
     * Toggle email notifications
     */
    async toggleEmailNotifications(enabled) {
        try {
            this.setLoading(true, 'emailNotifications');

            const response = await fetch('/api/profile/toggle-email-notifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enabled })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to update email notification settings');
            }

            this.showToast(`Email notifications ${enabled ? 'enabled' : 'disabled'}`, 'success');
        } catch (error) {
            console.error('Error toggling email notifications:', error);
            this.showToast(error.message, 'error');

            // Reset toggle to previous state
            const emailNotificationsToggle = document.getElementById('emailNotificationsToggle');
            if (emailNotificationsToggle) {
                emailNotificationsToggle.checked = !enabled;
            }
        } finally {
            this.setLoading(false, 'emailNotifications');
        }
    }

    /**
     * Fetch usage statistics
     */
    async fetchUsageStats() {
        try {
            const response = await fetch('/api/profile/stats');
            if (!response.ok) {
                throw new Error('Failed to fetch usage statistics');
            }

            const stats = await response.json();
            this.updateUsageUI(stats);
        } catch (error) {
            console.error('Error fetching usage statistics:', error);
        }
    }

    /**
     * Update usage UI with statistics
     */
    updateUsageUI(stats) {
        // Update credit information if available
        if (window.userCredits) {
            const creditBalance = window.userCredits.balance || 0;
            const totalEarned = window.userCredits.totalEarned || 0;
            const lastUpdated = window.userCredits.lastUpdated;

            // Update credit balance value
            const creditBalanceValue = document.getElementById('creditBalanceValue');
            if (creditBalanceValue) {
                creditBalanceValue.textContent = creditBalance;
            }

            // Update credit balance progress bar (max 200 credits)
            const creditBalanceProgress = document.getElementById('creditBalanceProgress');
            if (creditBalanceProgress) {
                const progress = Math.min(creditBalance / 200 * 100, 100);
                creditBalanceProgress.style.width = `${progress}%`;

                // Update color based on balance
                creditBalanceProgress.classList.remove('from-amber-500', 'to-yellow-400', 'from-red-500', 'to-orange-400', 'from-green-500', 'to-emerald-400');

                if (creditBalance < 20) {
                    creditBalanceProgress.classList.add('from-red-500', 'to-orange-400');
                } else if (creditBalance < 50) {
                    creditBalanceProgress.classList.add('from-amber-500', 'to-yellow-400');
                } else {
                    creditBalanceProgress.classList.add('from-green-500', 'to-emerald-400');
                }
            }

            // Update total earned value
            const totalCreditsEarnedValue = document.getElementById('totalCreditsEarnedValue');
            if (totalCreditsEarnedValue) {
                totalCreditsEarnedValue.textContent = totalEarned;
            }

            // Update last updated value
            const lastCreditUpdateValue = document.getElementById('lastCreditUpdateValue');
            if (lastCreditUpdateValue && lastUpdated) {
                lastCreditUpdateValue.textContent = this.formatDate(lastUpdated);
            }
        }

        // Update Chat usage statistics
        const threadCount = stats.thread_count || 0;
        const totalThreadConversationSets = stats.total_thread_conversation_sets || 0;

        // Calculate maximum conversation sets based on thread count (15 per thread)
        const maxThreadConversationSets = threadCount > 0 ? threadCount * 15 : 15;

        // Update thread count
        document.getElementById('chatThreadCount').textContent = threadCount;
        document.getElementById('chatThreadCountValue').textContent = threadCount;

        // Update thread progress bar (max 10 threads)
        const threadProgress = Math.min(threadCount / 10 * 100, 100);
        document.getElementById('chatThreadCountProgress').style.width = `${threadProgress}%`;

        // Update conversation sets count and maximum
        document.getElementById('chatConversationSetsCount').textContent = totalThreadConversationSets;
        document.getElementById('chatConversationSetsValue').textContent = totalThreadConversationSets;
        document.getElementById('chatMaxConversationSets').textContent = maxThreadConversationSets;

        // Update conversation sets progress bar (based on max per thread count)
        const conversationSetsProgress = maxThreadConversationSets > 0 ?
            Math.min(totalThreadConversationSets / maxThreadConversationSets * 100, 100) : 0;
        document.getElementById('chatConversationSetsProgress').style.width = `${conversationSetsProgress}%`;

        // Update Live usage statistics
        const roomCount = stats.room_count || 0;
        const totalRoomConversationSets = stats.total_room_conversation_sets || 0;

        // Calculate maximum conversation sets based on room count (20 per room)
        const maxRoomConversationSets = roomCount > 0 ? roomCount * 20 : 20;

        // Update room count
        document.getElementById('liveRoomCount').textContent = roomCount;
        document.getElementById('liveRoomCountValue').textContent = roomCount;

        // Update room progress bar (max 5 rooms)
        const roomProgress = Math.min(roomCount / 5 * 100, 100);
        document.getElementById('liveRoomCountProgress').style.width = `${roomProgress}%`;

        // Update live conversation sets count and maximum
        document.getElementById('liveConversationSetsCount').textContent = totalRoomConversationSets;
        document.getElementById('liveConversationSetsValue').textContent = totalRoomConversationSets;
        document.getElementById('liveMaxConversationSets').textContent = maxRoomConversationSets;

        // Update live conversation sets progress bar (based on max per room count)
        const liveConversationSetsProgress = maxRoomConversationSets > 0 ?
            Math.min(totalRoomConversationSets / maxRoomConversationSets * 100, 100) : 0;
        document.getElementById('liveConversationSetsProgress').style.width = `${liveConversationSetsProgress}%`;

        // Update Friends usage statistics
        const friendCount = stats.friend_count || 0;

        // Update friend count
        document.getElementById('friendCount').textContent = friendCount;
        document.getElementById('friendCountValue').textContent = friendCount;

        // Update friend progress bar (max 7 friends)
        const friendProgress = Math.min(friendCount / 7 * 100, 100);
        document.getElementById('friendCountProgress').style.width = `${friendProgress}%`;
    }

    /**
     * Request data export
     */
    async requestDataExport() {
        try {
            this.setLoading(true, 'exportData');

            const response = await fetch('/api/profile/request-data-export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to request data export');
            }

            this.showToast('Data export request submitted. You will receive an email when your data is ready to download.', 'success');
        } catch (error) {
            console.error('Error requesting data export:', error);
            this.showToast(error.message, 'error');
        } finally {
            this.setLoading(false, 'exportData');
        }
    }

    /**
     * Format date and time
     */
    formatDateTime(date) {
        if (!date || !(date instanceof Date) || isNaN(date)) {
            return 'Unknown';
        }

        // Format date: Jan 1, 2023, 12:00 PM
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        };

        return date.toLocaleString('en-US', options);
    }

    /**
     * Request data deletion
     */
    async requestDataDeletion() {
        // Ask for confirmation
        if (!confirm('WARNING: This will permanently delete ALL your data from Kevko Systems. This action cannot be undone. Are you sure you want to proceed?')) {
            return;
        }

        try {
            this.setLoading(true, 'dataDeletion');

            const response = await fetch('/api/profile/request-data-deletion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to request data deletion');
            }

            // Check if verification is required
            if (data.requires_verification) {
                // Show verification code input dialog
                const verificationCode = prompt(data.message + '\n\nPlease enter the verification code:');

                if (!verificationCode) {
                    this.showToast('Data deletion cancelled.', 'info');
                    return;
                }

                // Submit verification code
                const verifyResponse = await fetch('/api/profile/request-data-deletion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ verification_code: verificationCode })
                });

                const verifyData = await verifyResponse.json();

                if (!verifyResponse.ok) {
                    throw new Error(verifyData.error || 'Failed to verify code');
                }

                this.showToast(verifyData.message || 'Data deletion request submitted. Your data will be deleted in 5 minutes.', 'success');

                // Redirect to logout after a delay
                setTimeout(() => {
                    window.location.href = '/auth/logout';
                }, 5000);
            } else {
                this.showToast(data.message || 'Data deletion request submitted. Your data will be deleted in 5 minutes.', 'success');

                // Redirect to logout after a delay
                setTimeout(() => {
                    window.location.href = '/auth/logout';
                }, 5000);
            }
        } catch (error) {
            console.error('Error requesting data deletion:', error);
            this.showToast(error.message, 'error');
        } finally {
            this.setLoading(false, 'dataDeletion');
        }
    }

    /**
     * Show reset password modal
     */
    showResetPasswordModal() {
        // Use the existing change password modal functionality
        if (window.dashboard && typeof window.dashboard.showChangePasswordModal === 'function') {
            window.dashboard.showChangePasswordModal(false);
        } else {
            this.showToast('Password reset functionality not available', 'error');
        }
    }

    /**
     * Toggle two-factor authentication
     */
    async toggleTwoFactor(enabled) {
        try {
            this.setLoading(true, 'twoFactor');

            const response = await fetch('/api/profile/toggle-two-factor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enabled })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to update two-factor authentication');
            }

            this.showToast(`Two-factor authentication ${enabled ? 'enabled' : 'disabled'}`, 'success');

            // Refresh user data in the UI
            this.fetchCurrentUser();
        } catch (error) {
            console.error('Error toggling two-factor authentication:', error);
            this.showToast(error.message, 'error');

            // Reset toggle to previous state
            const twoFactorToggle = document.getElementById('twoFactorToggle');
            if (twoFactorToggle) {
                twoFactorToggle.checked = !enabled;
            }
        } finally {
            this.setLoading(false, 'twoFactor');
        }
    }

    /**
     * Clear all threads
     */
    async clearThreads() {
        // Ask for confirmation
        if (!confirm('Are you sure you want to delete ALL your chat threads? This action cannot be undone.')) {
            return;
        }

        try {
            this.setLoading(true, 'threads');

            const response = await fetch('/api/profile/clear-threads', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to clear threads');
            }

            this.showToast(`Successfully deleted ${data.count} threads`, 'success');
        } catch (error) {
            console.error('Error clearing threads:', error);
            this.showToast(error.message, 'error');
        } finally {
            this.setLoading(false, 'threads');
        }
    }

    /**
     * Clear all rooms
     */
    async clearRooms() {
        // Ask for confirmation
        if (!confirm('Are you sure you want to delete ALL your live rooms? This action cannot be undone.')) {
            return;
        }

        try {
            this.setLoading(true, 'rooms');

            const response = await fetch('/api/profile/clear-rooms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to clear rooms');
            }

            this.showToast(`Successfully deleted ${data.count} rooms`, 'success');
        } catch (error) {
            console.error('Error clearing rooms:', error);
            this.showToast(error.message, 'error');
        } finally {
            this.setLoading(false, 'rooms');
        }
    }

    /**
     * Set loading state
     * @param {boolean} isLoading - Whether loading is active
     * @param {string} [type] - Type of loading action (username, displayName, etc.)
     */
    setLoading(isLoading, type = 'all') {
        this.isLoading = isLoading;

        // Define button configurations
        const buttonConfigs = {
            username: {
                id: 'updateUsernameBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="save" class="h-4 w-4"></i>',
                title: 'Save username'
            },

            threads: {
                id: 'clearThreadsBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="trash-2" class="h-4 w-4 mr-1"></i> Clear All Chat Threads'
            },
            rooms: {
                id: 'clearRoomsBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="trash-2" class="h-4 w-4 mr-1"></i> Clear All Live Rooms'
            },
            password: {
                id: 'resetPasswordBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="key" class="h-4 w-4 mr-2"></i> Change Password'
            },
            exportData: {
                id: 'exportDataBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="download" class="h-4 w-4 mr-2"></i> Request Data Export'
            },
            dataDeletion: {
                id: 'requestDataDeletionBtn',
                loadingIcon: '<i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>',
                defaultIcon: '<i data-lucide="trash-2" class="h-4 w-4 mr-2"></i> Request Data Deletion'
            },
            twoFactor: {
                id: 'twoFactorToggle',
                // For toggle switches, we don't change the HTML, just disable the element
                loadingIcon: null,
                defaultIcon: null
            },
            emailNotifications: {
                id: 'emailNotificationsToggle',
                loadingIcon: null,
                defaultIcon: null
            }
        };

        // Update specific button or all buttons
        if (type === 'all') {
            // Update all buttons
            Object.values(buttonConfigs).forEach(config => {
                this.updateButtonState(config, isLoading);
            });
        } else if (buttonConfigs[type]) {
            // Update specific button
            this.updateButtonState(buttonConfigs[type], isLoading);
        }

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * Update button state
     * @param {Object} config - Button configuration
     * @param {boolean} isLoading - Whether loading is active
     */
    updateButtonState(config, isLoading) {
        const element = document.getElementById(config.id);
        if (!element) return;

        // Disable the element
        element.disabled = isLoading;

        // For regular buttons, update the HTML content
        if (config.loadingIcon && config.defaultIcon) {
            element.innerHTML = isLoading ? config.loadingIcon : config.defaultIcon;
        }

        // Set title if provided
        if (config.title) {
            element.title = config.title;
        }

        // For toggle switches, add/remove a loading class to the parent label
        if (element.type === 'checkbox' && element.classList.contains('sr-only')) {
            const label = element.closest('label');
            if (label) {
                if (isLoading) {
                    label.classList.add('opacity-50', 'cursor-wait');
                } else {
                    label.classList.remove('opacity-50', 'cursor-wait');
                }
            }
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (window.dashboard && typeof window.dashboard.showToast === 'function') {
            window.dashboard.showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize profile when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global profile instance
    window.profile = new Profile();

    // Initialize profile
    window.profile.init();
});
