{% extends "base.html" %}

{% block title %}Private Profile{% endblock %}

{% block content %}
<div class="private-profile-container">
    <div class="private-profile-content">
        <h1 class="private-profile-title">Private Profile</h1>
        <p class="private-profile-message">This profile is set to private by the user.</p>
        <p class="private-profile-username">@{{ username }}</p>
        
        {% if current_user.is_authenticated %}
        <div class="private-profile-actions">
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary">Return to Dashboard</a>
        </div>
        {% else %}
        <div class="private-profile-actions">
            <a href="{{ url_for('auth.login') }}" class="btn btn-primary">Log In</a>
            <a href="{{ url_for('landing') }}" class="btn btn-secondary">Return to Home</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block head %}
<style>
    .private-profile-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 80vh;
        padding: 2rem;
    }
    
    .private-profile-content {
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        max-width: 500px;
        width: 100%;
    }
    
    .private-profile-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .private-profile-message {
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        opacity: 0.8;
    }
    
    .private-profile-username {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    
    .private-profile-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
        transition: opacity 0.2s;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn:hover {
        opacity: 0.9;
    }
    
    .btn-primary {
        background-color: var(--primary-color, #4a86e8);
        color: white;
    }
    
    .btn-secondary {
        background-color: rgba(255, 255, 255, 0.2);
        color: inherit;
    }
</style>
{% endblock %}