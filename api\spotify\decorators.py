from functools import wraps
from flask import jsonify, session
from flask_login import current_user
from .services.spotify_client import create_spotify_oauth
from models.restricted_user import RestrictedUser

def require_auth(f):
    """
    Decorator to check if a user is authenticated with Spotify.
    Verifies the Spotify token and refreshes it if needed.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check if user is restricted from Spotify service
        if current_user.is_authenticated and RestrictedUser.is_restricted(current_user.email, 'spotify'):
            return jsonify({
                'error': 'Access denied. Your account is restricted from using the Spotify service.',
                'restricted': True,
                'service': 'spotify'
            }), 403

        sp_oauth = create_spotify_oauth()
        token_info = session.get('token_info')

        if not token_info:
            return jsonify({'error': 'Not authenticated with Spotify'}), 401

        if sp_oauth.is_token_expired(token_info):
            try:
                token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
                session['token_info'] = token_info
            except:
                return jsonify({'error': 'Token refresh failed'}), 401

        return f(*args, **kwargs)
    return decorated_function
