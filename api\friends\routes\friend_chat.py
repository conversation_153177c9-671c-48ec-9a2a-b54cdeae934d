"""
Friend chat routes.
This module contains routes for managing direct chats between friends.
"""
from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from .. import friends_api
from models.friend_chat import FriendChat
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
from datetime import datetime, timezone
import logging


@friends_api.route('/chat/<chat_id>', methods=['GET'])
@login_required
@check_service_access('friends')
def get_chat(chat_id):
    """Get a chat by ID with pagination support"""
    chat = FriendChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this chat'}), 403

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    return jsonify(chat.to_dict(limit=limit, skip=skip))


@friends_api.route('/chat/<chat_id>/messages', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_message', 'friends')
def send_message(chat_id):
    """Send a message to a chat"""
    data = request.json
    message = data.get('message', '')
    images = data.get('images', [])
    is_system = data.get('is_system', False)
    message_type = data.get('message_type', 'info')

    # Require either a message or images
    if not message.strip() and not images:
        return jsonify({'error': 'Message or images are required'}), 400

    # Get the chat
    chat = FriendChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to send messages to this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to send messages to this chat'}), 403

    # Handle system messages
    if is_system:
        from datetime import datetime, timezone

        # System messages are not end-to-end encrypted
        # They are visible to all users and the server
        timestamp = datetime.now(timezone.utc)
        message_obj = {
            "user_id": "system",
            "username": "System",
            "display_name": "System",
            "content": message,  # Store plaintext for system messages
            "timestamp": timestamp.isoformat(),
            "is_system": True,
            "message_type": message_type,
            "encrypted": False  # System messages are not encrypted
        }

        # For invitation messages, include room_id and room_url
        if message_type == 'invite':
            room_id = data.get('room_id')
            room_url = data.get('room_url')

            if room_id:
                message_obj["room_id"] = room_id

            if room_url:
                message_obj["room_url"] = room_url
            elif room_id:
                # Construct room URL if not provided but room_id is available
                base_url = request.host_url.rstrip('/')
                message_obj["room_url"] = f"{base_url}/live/room/{room_id}"

            logging.info(f"Added room_id and room_url to invitation message: {room_id}, {message_obj.get('room_url')}")

        # Add to chat
        chat.messages.append(message_obj)
        chat.updated_at = timestamp
        chat.save()
    else:
        # Add regular message to chat
        # The message should already be encrypted by the client if E2E is enabled
        message_obj = chat.add_message(current_user, message, images)

    return jsonify({
        'success': True,
        'message': message_obj
    })


@friends_api.route('/chat/<chat_id>/messages', methods=['GET'])
@login_required
@check_service_access('friends')
def get_chat_messages(chat_id):
    """Get messages for a chat with pagination support"""
    chat = FriendChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this chat'}), 403

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    # Get the chat data with pagination
    chat_data = chat.to_dict(limit=limit, skip=skip)

    # Return only the messages part and pagination info
    return jsonify({
        'messages': chat_data['messages'],
        'total_messages': chat_data['total_messages'],
        'has_more': chat_data['has_more']
    })


@friends_api.route('/cleanup', methods=['POST'])
@login_required
def cleanup_chats():
    """Clean up stale chats (admin only)"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized'}), 403

    # Clean up stale chats
    count = FriendChat.cleanup_stale_chats()

    return jsonify({
        'success': True,
        'message': f'Cleaned up {count} stale chats'
    })