// Initialize Lucide icons when the DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initIcons);
} else {
    // If DOMContentLoaded has already fired, run immediately
    setTimeout(initIcons, 0);
}

function initIcons() {
    // Create all icons at once
    if (typeof lucide !== 'undefined') {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    } else {
        // If lucide isn't loaded yet, try again later
        setTimeout(initIcons, 100);
    }
}

class ChatInterface {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatContent = document.getElementById('chatContent');
        this.threadsList = document.getElementById('chatList');
        this.messages = [];
        this.currentThreadId = null;
        this.setupEventListeners();
        
        // Initialize audio player if not already initialized
        if (!window.audioPlayer) {
            window.audioPlayer = new AudioPlayer();
        }

        // Get initial thread ID from URL or window.initialThreadId
        const pathParts = window.location.pathname.split('/');
        const threadIdFromUrl = pathParts[pathParts.length - 1];
        const initialThreadId = threadIdFromUrl !== 'chat' ? threadIdFromUrl : window.initialThreadId;

        this.loadThreads().then(() => {
            if (initialThreadId) {
                this.loadThread(initialThreadId);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (event) => {
            const threadId = event.state?.threadId;
            if (threadId) {
                this.loadThread(threadId, false); // false means don't push state
            } else {
                this.resetChat();
            }
        });

        // Check if this is a shared thread
        const sharedThread = window.sharedThread;
        if (sharedThread) {
            this.loadSharedThread(sharedThread);
        }

        // Add share button handler
        this.shareButton = document.getElementById('shareChatBtn');
        if (this.shareButton) {
            this.shareButton.addEventListener('click', () => {
                if (this.currentThreadId) {
                    this.shareThread(this.currentThreadId);
                } else {
                    // Show error notification if no thread is selected
                    this.showNotification('Please select or create a thread first', 'error');
                }
            });
        }

        // Image handling
        this.imageBtn = document.getElementById('imageBtn');
        this.imageFileInput = document.getElementById('imageFileInput');
        this.imagePreviewContainer = document.getElementById('imagePreviewContainer');
        this.setupImageHandling();
    }

    setupEventListeners() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Add button handler
        const addButton = document.getElementById('addButton');
        if (addButton) {
            addButton.addEventListener('click', () => {
                // For now, just trigger the image button click
                document.getElementById('imageBtn').click();
            });
        }

        // Create new chat button
        document.getElementById('createChatBtn').addEventListener('click', () => this.createNewThread());
    }

    async loadThreads() {
        try {
            // Show loading state immediately
            if (!this.threadsList.querySelector('.thread-item:not(.placeholder)')) {
                const existingPlaceholders = this.threadsList.querySelectorAll('.placeholder');
                if (existingPlaceholders.length === 0) {
                    this.createThreadPlaceholders();
                }
            }

            // Start the fetch request immediately (don't await yet)
            let threadsPromise;
            if (window.threadsDataPromise) {
                threadsPromise = window.threadsDataPromise;
                window.threadsDataPromise = null;
            } else {
                threadsPromise = fetch('/api/chat/threads')
                    .then(response => response.json());
            }

            // Use Promise.race to implement a timeout
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Fetch timeout')), 5000)
            );

            // Wait for the data with timeout
            const threads = await Promise.race([threadsPromise, timeoutPromise])
                .catch(error => {
                    console.error('Thread fetch error:', error);
                    return []; // Return empty array on error
                });

            // Clear existing threads but keep placeholders until we render
            const fragment = document.createDocumentFragment();
            let activeThreadElement = null;

            // Sort threads by updated_at in descending order (most recent first)
            threads.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
            
            // Create all thread elements at once using a document fragment
            // This prevents multiple reflows
            threads.forEach(thread => {
                const threadElement = this.createThreadElement(thread);
                fragment.appendChild(threadElement);
                
                // Track the active thread element if needed
                if (this.currentThreadId && thread.id === this.currentThreadId) {
                    activeThreadElement = threadElement;
                }
            });

            // Remove existing threads and placeholders
            this.threadsList.querySelectorAll('.thread-item').forEach(t => t.remove());
            
            // Add all threads at once (single DOM operation)
            this.threadsList.appendChild(fragment);
            
            // Mark active thread if found
            if (activeThreadElement) {
                activeThreadElement.classList.add('active');
            }
        } catch (error) {
            console.error('Failed to load threads:', error);
        }
    }

    createThreadPlaceholders() {
        // Create placeholder elements to prevent layout shift
        const placeholders = Array(5).fill().map(() => {
            const div = document.createElement('div');
            div.className = 'thread-item placeholder';
            div.innerHTML = '<div class="thread-title">Loading...</div>';
            return div;
        });
        placeholders.forEach(p => this.threadsList.appendChild(p));
    }

    createThreadElement(thread) {
        // Use template literals for faster HTML creation
        const div = document.createElement('div');
        div.className = `thread-item${this.currentThreadId === thread.id ? ' active' : ''}`;
        div.setAttribute('data-thread-id', thread.id);
        
        // Format date with time for more precise sorting display
        const formattedDate = new Date(thread.updated_at).toLocaleDateString();
        const formattedTime = new Date(thread.updated_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        // Make sure we have a title, use a default if not present
        const threadTitle = thread.title || 'Untitled Chat';
        
        // Build HTML content in one operation - showing thread title and action buttons with direct SVG
        div.innerHTML = `
            <div class="thread-title">${threadTitle}</div>
            <div class="thread-actions">
                <button class="delete-thread" title="Delete Thread" aria-label="Delete thread">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                </button>
                <button class="edit-thread" title="Edit Thread Name" aria-label="Edit thread name">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
                </button>
            </div>
        `;
        
        // Add event listeners efficiently
        div.addEventListener('click', () => this.loadThread(thread.id));
        
        // Use event delegation for action buttons
        const actionsDiv = div.querySelector('.thread-actions');
        actionsDiv.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent thread loading
            
            if (e.target.closest('.edit-thread')) {
                this.editThreadTitle(thread.id, threadTitle);
            } else if (e.target.closest('.delete-thread')) {
                this.deleteThread(thread.id);
            }
        });
        
        return div;
    }

    async createNewThread() {
        try {
            const response = await fetch('/api/chat/thread', {
                method: 'POST'
            });

            if (!response.ok) {
                const data = await response.json();

                // Check if this is a feature restriction error
                if (data.feature_restricted) {
                    // Show feature restriction notification
                    if (window.showFeatureRestrictionNotification) {
                        window.showFeatureRestrictionNotification(data.error);
                    } else {
                        alert(data.error);
                    }
                    return;
                }

                throw new Error(data.error || 'Failed to create thread');
            }

            const thread = await response.json();

            // Update URL to include the thread ID
            const newUrl = `/chat/thread/${thread.id}`;
            window.history.pushState({ threadId: thread.id }, '', newUrl);

            this.loadThread(thread.id);
            this.loadThreads();
        } catch (error) {
            console.error('Failed to create thread:', error);
        }
    }

    async loadThread(threadId, pushState = true) {
        try {
            // Show loading indicator
            this.chatContent.innerHTML = '<div class="loading-indicator">Loading messages...</div>';
            
            // Update current thread ID immediately
            this.currentThreadId = threadId;
            
            // Update active thread highlighting immediately
            const allThreadItems = this.threadsList.querySelectorAll('.thread-item');
            allThreadItems.forEach(item => item.classList.remove('active'));

            const activeThreadItem = this.threadsList.querySelector(`.thread-item[data-thread-id="${threadId}"]`);
            if (activeThreadItem) {
                activeThreadItem.classList.add('active');
                
                // If we have the title in the thread item, use it immediately
                const titleElement = activeThreadItem.querySelector('.thread-title');
                if (titleElement) {
                    document.getElementById('activeChatName').textContent = titleElement.textContent;
                }
            }
            
            // Start fetch request
            let threadPromise;
            if (window.threadDataPromise && threadId === window.initialThreadId) {
                threadPromise = window.threadDataPromise;
                window.threadDataPromise = null;
            } else {
                threadPromise = fetch(`/api/chat/thread/${threadId}`)
                    .then(response => response.json());
            }
            
            // Wait for thread data
            const thread = await threadPromise;
            
            // Reload threads to update the order based on the updated timestamp
            this.loadThreads();
            
            // Update messages and title
            this.messages = thread.messages;
            document.getElementById('activeChatName').textContent = thread.title;
            
            // Create document fragment for better performance
            const fragment = document.createDocumentFragment();
            
            // Process messages in batches for better UI responsiveness
            const batchSize = 10;
            const processBatch = (startIndex) => {
                const endIndex = Math.min(startIndex + batchSize, this.messages.length);
                
                for (let i = startIndex; i < endIndex; i++) {
                    const msg = this.messages[i];
                    const { messageDiv, contentDiv } = this.createMessageElement(msg.content, msg.role === 'user');
                    contentDiv.innerHTML = this.formatMessage(msg.content);
                    
                    // Add images if present
                    if (msg.images && msg.images.length > 0 && msg.role === 'user') {
                        const imageContainer = document.createElement('div');
                        imageContainer.className = 'message-images';
                        msg.images.forEach(img => {
                            const imgElement = document.createElement('img');
                            imgElement.src = `data:${img.mime_type};base64,${img.data}`;
                            imgElement.className = 'message-image-preview';
                            imageContainer.appendChild(imgElement);
                        });
                        messageDiv.appendChild(imageContainer);
                    }
                    
                    // Add speech player if present
                    if (msg.speech && msg.role === 'assistant') {
                        const messageId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                        const audioPlayer = window.audioPlayer.createAudioPlayer(msg.speech, messageId);
                        messageDiv.appendChild(audioPlayer);
                    }
                    
                    fragment.appendChild(messageDiv);
                }
                
                // If we've processed all messages, append the fragment and apply highlighting
                if (endIndex >= this.messages.length) {
                    this.chatContent.innerHTML = '';
                    this.chatContent.appendChild(fragment);
                    
                    // Apply Prism highlighting
                    if (typeof Prism !== 'undefined') {
                        requestAnimationFrame(() => {
                            Prism.highlightAll();
                        });
                    }
                    
                    // Update URL if needed
                    if (pushState) {
                        const newUrl = `/chat/thread/${threadId}`;
                        window.history.pushState({ threadId }, '', newUrl);
                    }
                } else {
                    // Process next batch in next animation frame
                    requestAnimationFrame(() => {
                        processBatch(endIndex);
                    });
                }
            };
            
            // Start processing messages
            if (this.messages.length > 0) {
                processBatch(0);
            } else {
                this.chatContent.innerHTML = '<div class="empty-thread">No messages yet. Start a conversation!</div>';
            }
            
            // Reload the thread list to update the order
            setTimeout(() => this.loadThreads(), 500);
        } catch (error) {
            console.error('Failed to load thread:', error);
            this.chatContent.innerHTML = '<div class="error-message">Failed to load messages. Please try again.</div>';
        }
    }


    resetChat() {
        // Clear current thread ID and messages
        this.currentThreadId = null;
        this.messages = [];

        // Clear chat content
        this.chatContent.innerHTML = '';
        document.getElementById('activeChatName').textContent = 'New Chat';

        // Remove active class from all thread items
        const allThreadItems = this.threadsList.querySelectorAll('.thread-item');
        allThreadItems.forEach(item => item.classList.remove('active'));

        // Reload threads
        this.loadThreads();
    }

    async editThreadTitle(threadId, currentTitle) {
        // Prompt user for new title, pre-filling with current title
        const newTitle = prompt('Enter a new name for this chat:', currentTitle);
        
        // If user cancels or enters empty title, do nothing
        if (!newTitle || newTitle.trim() === '') return;
        
        try {
            const response = await fetch(`/api/chat/thread/${threadId}/title`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ title: newTitle.trim() })
            });
            
            if (!response.ok) {
                const data = await response.json();
                throw new Error(data.error || 'Failed to update thread title');
            }
            
            // Update UI immediately
            const threadElement = this.threadsList.querySelector(`.thread-item[data-thread-id="${threadId}"]`);
            if (threadElement) {
                const titleElement = threadElement.querySelector('.thread-title');
                if (titleElement) {
                    titleElement.textContent = newTitle.trim();
                }
            }
            
            // If this is the current thread, update the header title too
            if (this.currentThreadId === threadId) {
                document.getElementById('activeChatName').textContent = newTitle.trim();
            }
            
            // No need to reload all threads, we've updated the UI directly
        } catch (error) {
            console.error('Failed to update thread title:', error);
            this.showNotification('Failed to update thread title', 'error');
        }
    }
    
    async deleteThread(threadId) {
        if (!confirm('Are you sure you want to delete this thread?')) return;

        try {
            const response = await fetch(`/api/chat/thread/${threadId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                const data = await response.json();
                throw new Error(data.error || 'Failed to delete thread');
            }

            // Remove the thread element from the DOM immediately for better UX
            const threadElement = this.threadsList.querySelector(`.thread-item[data-thread-id="${threadId}"]`);
            if (threadElement) {
                threadElement.remove();
            }

            if (this.currentThreadId === threadId) {
                // Update URL to /chat when deleting current thread
                window.history.pushState({}, '', '/chat');
                this.resetChat();
            }

            this.showNotification('Thread deleted successfully', 'success');
            // No need to reload all threads, we've updated the UI directly
        } catch (error) {
            console.error('Failed to delete thread:', error);
            this.showNotification('Failed to delete thread', 'error');
            
            // If there was an error, reload threads to ensure UI is in sync
            this.loadThreads();
        }
    }
    
    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // Use SVG directly instead of relying on Lucide to render icons
        let iconSvg = '';
        switch (type) {
            case 'success':
                iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
                break;
            case 'error':
                iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
                break;
            case 'warning':
                iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
                break;
            case 'info':
                iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>';
                break;
        }
        
        // Set notification content
        notification.innerHTML = `
            <div class="notification-icon">${iconSvg}</div>
            <div class="notification-message">${message}</div>
        `;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300); // Wait for fade out animation
        }, 3000);
    }

    createMessageElement(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;

        // Create message header with options menu
        const messageHeader = document.createElement('div');
        messageHeader.className = 'message-header';

        // Create three-dots menu button
        const optionsButton = document.createElement('button');
        optionsButton.className = 'message-options-btn';
        optionsButton.setAttribute('aria-label', 'Message options');
        optionsButton.innerHTML = '<i data-lucide="more-vertical"></i>';
        
        // Create dropdown menu
        const dropdownMenu = document.createElement('div');
        dropdownMenu.className = 'message-dropdown-menu';
        
        // Add Chat Theme option to dropdown
        const themeOption = document.createElement('button');
        themeOption.className = 'dropdown-option';
        themeOption.innerHTML = '<i data-lucide="palette"></i><span>Chat Theme</span>';
        themeOption.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            this.openThemeModal();
            dropdownMenu.classList.remove('show');
        });
        
        // Add other options here if needed
        
        // Add options to dropdown menu
        dropdownMenu.appendChild(themeOption);
        
        // Add dropdown to message header
        messageHeader.appendChild(optionsButton);
        messageDiv.appendChild(messageHeader);
        messageDiv.appendChild(dropdownMenu);
        
        // Toggle dropdown menu on click
        optionsButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            dropdownMenu.classList.toggle('show');
            
            // Initialize Lucide icons in the dropdown
            if (dropdownMenu.classList.contains('show')) {
                if (window.lucide) {
                    window.lucide.createIcons({
                        attrs: {
                            'stroke-width': 2,
                            'class': 'dropdown-icon'
                        }
                    });
                }
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdownMenu.classList.remove('show');
        });

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // Set flag for message formatting
        this.isUserMessage = isUser;

        messageDiv.appendChild(contentDiv);
        
        // Initialize the Lucide icon in the options button
        setTimeout(() => {
            if (window.lucide) {
                window.lucide.createIcons({
                    attrs: {
                        'stroke-width': 2
                    }
                });
            }
        }, 0);
        
        return { messageDiv, contentDiv };
    }
    
    // Open theme selection modal
    openThemeModal() {
        // Create modal if it doesn't exist
        if (!document.getElementById('themeModal')) {
            this.createThemeModal();
        }
        
        // Show the modal
        const themeModal = document.getElementById('themeModal');
        themeModal.style.display = 'block';
        
        // Initialize Lucide icons in the modal
        setTimeout(() => {
            if (window.lucide) {
                window.lucide.createIcons({
                    attrs: {
                        'stroke-width': 2
                    }
                });
            }
        }, 0);
    }
    
    // Create theme selection modal
    createThemeModal() {
        const modal = document.createElement('div');
        modal.id = 'themeModal';
        modal.className = 'theme-modal';
        
        // Create modal structure
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Select Chat Theme</h3>
                    <button id="closeThemeModal" aria-label="Close theme modal">
                        <i data-lucide="x"></i>
                    </button>
                </div>
                <div class="theme-grid">
                    <div class="theme-option" data-theme="default">
                        <div class="theme-preview default-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">Default</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                    <div class="theme-option" data-theme="dark">
                        <div class="theme-preview dark-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">Dark</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                    <div class="theme-option" data-theme="light">
                        <div class="theme-preview light-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">Light</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                    <div class="theme-option" data-theme="contrast">
                        <div class="theme-preview contrast-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">High Contrast</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                    <div class="theme-option" data-theme="sunset">
                        <div class="theme-preview sunset-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">Sunset</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                    <div class="theme-option" data-theme="ocean">
                        <div class="theme-preview ocean-theme">
                            <div class="preview-user"></div>
                            <div class="preview-ai"></div>
                        </div>
                        <div class="theme-name">Ocean</div>
                        <div class="theme-selected"><i data-lucide="check"></i></div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to the body
        document.body.appendChild(modal);
        
        // Add event listeners
        const closeBtn = document.getElementById('closeThemeModal');
        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });
        
        // Close modal when clicking on backdrop
        const backdrop = modal.querySelector('.modal-backdrop');
        backdrop.addEventListener('click', () => {
            modal.style.display = 'none';
        });
        
        // Add event listeners to theme options
        const themeOptions = modal.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.addEventListener('click', () => {
                const theme = option.getAttribute('data-theme');
                this.setTheme(theme);
                
                // Update selected indicator
                themeOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });
        
        // Set the current theme as selected
        const currentTheme = localStorage.getItem('chatTheme') || 'default';
        const currentThemeOption = modal.querySelector(`.theme-option[data-theme="${currentTheme}"]`);
        if (currentThemeOption) {
            currentThemeOption.classList.add('selected');
        }
    }
    
    // Set the selected theme
    setTheme(theme) {
        // Remove any existing theme classes
        document.body.classList.remove(
            'theme-default', 
            'theme-dark', 
            'theme-light', 
            'theme-contrast', 
            'theme-sunset', 
            'theme-ocean'
        );
        
        // Add the selected theme class
        document.body.classList.add(`theme-${theme}`);
        
        // Save the theme preference
        localStorage.setItem('chatTheme', theme);
        
        // Show notification
        this.showNotification(`Theme changed to ${theme}`, 'success');
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message && !this.currentImages) return;

        // Get the display name and convert to actual model name
        const displayName = document.getElementById('currentModel').textContent;
        let selectedModel;

        if (displayName === 'GPT-4o Mini') {
            selectedModel = 'gpt-4o-mini';
        } else if (displayName === 'Gemini') {
            selectedModel = 'gemini-2.0-flash';
        } else if (displayName === 'Qwen') {
            selectedModel = 'qwen-qwq-32b';
        } else if (displayName === 'Gemma 2') {
            selectedModel = 'gemma2-9b-it';
        } else if (displayName === 'Llama 3.3 70B') {
            selectedModel = 'llama-3.3-70b-versatile';
        } else if (displayName === 'Llama 3.1 8B') {
            selectedModel = 'llama-3.1-8b-instant';
        } else if (displayName === 'Llama 3 70B') {
            selectedModel = 'llama3-70b-8192';
        } else if (displayName === 'Llama 3 8B') {
            selectedModel = 'llama3-8b-8192';
        } else if (displayName === 'PlayAI TTS') {
            selectedModel = 'playai-tts';
        }
        this.messageInput.value = '';

        // Immediately clear the image preview
        const hasImages = !!this.currentImages;
        const imageData = this.currentImages; // Store a reference to use in the message
        this.imagePreviewContainer.innerHTML = '';
        this.imagePreviewContainer.classList.add('hidden');

        // Create user message element
        const { messageDiv: userMessageDiv, contentDiv: userContentDiv } =
            this.createMessageElement(message, true);
        userContentDiv.innerHTML = message.replace(/\n/g, '<br>');

        // Add image previews to user message if present
        if (hasImages && imageData) {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'message-images';
            imageData.forEach(img => {
                const imgElement = document.createElement('img');
                imgElement.src = `data:${img.mime_type};base64,${img.data}`;
                imgElement.className = 'message-image-preview';
                imageContainer.appendChild(imgElement);
            });
            userMessageDiv.appendChild(imageContainer);
        }

        this.chatContent.appendChild(userMessageDiv);

        // Create AI response element
        const { messageDiv: aiMessageDiv, contentDiv: aiContentDiv } =
            this.createMessageElement();
        this.chatContent.appendChild(aiMessageDiv);

        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = '<span></span><span></span><span></span>';
        aiContentDiv.appendChild(typingIndicator);

        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message,
                    thread_id: this.currentThreadId,
                    messages: this.messages,
                    model: selectedModel,
                    images: this.currentImages || []
                }),
            });

            // Check for errors
            if (!response.ok) {
                const errorData = await response.json();

                // Remove the typing indicator
                aiContentDiv.innerHTML = '';

                // Handle insufficient credits with zero balance
                if (errorData.insufficient_credits && errorData.zero_balance) {
                    // Show insufficient credits notification
                    if (window.showInsufficientCreditsNotification) {
                        window.showInsufficientCreditsNotification(errorData.error);
                    } else {
                        alert(errorData.error);
                    }

                    // Remove the AI message element since we won't get a response
                    this.chatContent.removeChild(aiMessageDiv);
                    return;
                }
                // Handle feature restriction errors
                else if (errorData.feature_restricted) {
                    // Show feature restriction notification
                    if (window.showFeatureRestrictionNotification) {
                        window.showFeatureRestrictionNotification(errorData.error);
                    } else {
                        alert(errorData.error);
                    }

                    // Remove the AI message element since we won't get a response
                    this.chatContent.removeChild(aiMessageDiv);
                    return;
                }

                // Handle other errors
                aiContentDiv.innerHTML = `<div class="error-message">Error: ${errorData.error || 'Failed to send message'}</div>`;
                return;
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let accumulatedText = '';
            let partialLine = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = (partialLine + chunk).split('\n');

                // Save the last partial line for next iteration
                partialLine = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim() === '') continue;

                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.error) {
                                // Check if this is an insufficient credits error with zero balance
                                if (data.insufficient_credits && data.zero_balance) {
                                    // Show insufficient credits notification
                                    if (window.showInsufficientCreditsNotification) {
                                        window.showInsufficientCreditsNotification(data.error);
                                    } else {
                                        alert(data.error);
                                    }

                                    // Remove the AI message element since we won't get a response
                                    this.chatContent.removeChild(aiMessageDiv);
                                    return;
                                }
                                // Check if this is a feature restriction error
                                else if (data.feature_restricted) {
                                    // Show feature restriction notification
                                    if (window.showFeatureRestrictionNotification) {
                                        window.showFeatureRestrictionNotification(data.error);
                                    } else {
                                        alert(data.error);
                                    }

                                    // Remove the AI message element since we won't get a response
                                    this.chatContent.removeChild(aiMessageDiv);
                                    return;
                                }
                                // Handle regular insufficient credits error
                                else if (data.insufficient_credits) {
                                    // Show notification
                                    this.showNotification(data.error, 'warning');
                                    
                                    // Remove the AI message element since we won't get a response
                                    this.chatContent.removeChild(aiMessageDiv);
                                    return;
                                }

                                throw new Error(data.error);
                            }

                            if (data.text) {
                                if (!accumulatedText) {
                                    aiContentDiv.innerHTML = '';
                                }

                                accumulatedText += data.text;

                                // Check if we're starting a thinking block
                                const thinkStartRegex = /<think>/;
                                const thinkEndRegex = /<\/think>/;

                                // If we detect a new thinking block starting
                                if (thinkStartRegex.test(data.text) && !aiContentDiv.querySelector('.thinking-container')) {
                                    // Create a temporary thinking container that will be updated as more content arrives
                                    const tempThinkingContainer = document.createElement('div');
                                    tempThinkingContainer.className = 'thinking-container';

                                    tempThinkingContainer.innerHTML = `
                                        <div class="thinking-header" onclick="toggleThinking(this.parentElement)">
                                            <div class="thinking-header-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
                                                    <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-2.04Z"></path>
                                                    <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-2.04Z"></path>
                                                </svg>
                                            </div>
                                            <div class="thinking-header-title">Thinking</div>
                                            <div class="thinking-toggle">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                                                    <polyline points="6 9 12 15 18 9"></polyline>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="thinking-content"></div>
                                    `;

                                    // Insert at the beginning of the content
                                    if (aiContentDiv.firstChild) {
                                        aiContentDiv.insertBefore(tempThinkingContainer, aiContentDiv.firstChild);
                                    } else {
                                        aiContentDiv.appendChild(tempThinkingContainer);
                                    }

                                    // No timer needed
                                }

                                // If we have a thinking container, update its content
                                const thinkingContainer = aiContentDiv.querySelector('.thinking-container');
                                if (thinkingContainer) {
                                    const thinkingContent = thinkingContainer.querySelector('.thinking-content');
                                    const thinkingHeaderTitle = thinkingContainer.querySelector('.thinking-header-title');

                                    // Extract thinking content from accumulated text
                                    const fullThinkRegex = /<think>([\s\S]*?)(?:<\/think>|$)/;
                                    const fullThinkMatch = accumulatedText.match(fullThinkRegex);

                                    if (fullThinkMatch && fullThinkMatch[1]) {
                                        // Update thinking content
                                        const extractedThinking = fullThinkMatch[1].trim();
                                        thinkingContent.textContent = extractedThinking;

                                        // If thinking is complete, update title
                                        if (thinkEndRegex.test(accumulatedText)) {
                                            // Update title
                                            thinkingHeaderTitle.textContent = 'Thinking Done';
                                        }
                                    }
                                }

                                // Format the message for display
                                aiContentDiv.innerHTML = this.formatMessage(accumulatedText);
                                Prism.highlightAll();
                            }

                            if (data.full_message) {
                                // Create message object with content
                                const messageObj = { 
                                    role: "assistant", 
                                    content: data.full_message 
                                };
                                
                                // Add speech data if available
                                if (data.speech) {
                                    messageObj.speech = data.speech;
                                    
                                    // Add audio player to the message
                                    const messageId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                                    const audioPlayer = window.audioPlayer.createAudioPlayer(data.speech, messageId);
                                    
                                    // Find the current AI message div and append the audio player
                                    const currentAiMessage = this.chatContent.lastElementChild;
                                    if (currentAiMessage) {
                                        currentAiMessage.appendChild(audioPlayer);
                                    }
                                }
                                
                                this.messages.push(messageObj);
                                
                                if (data.thread_id) {
                                    this.currentThreadId = data.thread_id;
                                    // Update URL to reflect the thread ID
                                    const newUrl = `/chat/thread/${data.thread_id}`;
                                    window.history.pushState({ threadId: data.thread_id }, '', newUrl);
                                    this.loadThreads();
                                }
                            }
                        } catch (parseError) {
                            console.error('JSON parse error:', parseError, 'for line:', line);
                            continue;
                        }
                    }
                }
            }

            // Handle any remaining partial line
            if (partialLine && partialLine.startsWith('data: ')) {
                try {
                    const data = JSON.parse(partialLine.slice(6));
                    if (data.full_message) {
                        // Create message object with content
                        const messageObj = { 
                            role: "assistant", 
                            content: data.full_message 
                        };
                        
                        // Add speech data if available
                        if (data.speech) {
                            messageObj.speech = data.speech;
                            
                            // Add audio player to the message
                            const messageId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                            const audioPlayer = window.audioPlayer.createAudioPlayer(data.speech, messageId);
                            
                            // Find the current AI message div and append the audio player
                            const currentAiMessage = this.chatContent.lastElementChild;
                            if (currentAiMessage) {
                                currentAiMessage.appendChild(audioPlayer);
                            }
                        }
                        
                        this.messages.push(messageObj);
                        
                        if (data.thread_id) {
                            this.currentThreadId = data.thread_id;
                            // Update URL to reflect the thread ID
                            const newUrl = `/chat/thread/${data.thread_id}`;
                            window.history.pushState({ threadId: data.thread_id }, '', newUrl);
                            this.loadThreads();
                        }
                    }
                } catch (parseError) {
                    console.error('JSON parse error on final partial line:', parseError);
                }
            }

        } catch (error) {
            console.error('Chat error:', error);
            aiContentDiv.innerHTML = 'An error occurred while processing your message.';
        }

        this.chatContent.scrollTop = this.chatContent.scrollHeight;

        // We already cleared the image preview container earlier, but make sure currentImages is null
        this.currentImages = null;
        
        // Reload the thread list to update the order
        setTimeout(() => this.loadThreads(), 500);
    }


    formatMessage(text) {
        // First, extract and process thinking blocks
        let processedText = text;
        let thinkingContent = null;

        // Check for thinking blocks
        const thinkRegex = /<think>([\s\S]*?)<\/think>/;
        const thinkMatch = text.match(thinkRegex);

        if (thinkMatch && thinkMatch[1]) {
            // Extract thinking content
            thinkingContent = thinkMatch[1].trim();

            // Remove thinking block from the text
            processedText = text.replace(thinkRegex, '').trim();
        }

        // Configure marked options
        const markedOptions = {
            breaks: true,     // Enable line breaks
            gfm: true,       // Enable GitHub Flavored Markdown
            headerIds: false, // Disable header IDs to prevent conflicts
            mangle: false,    // Disable mangling to prevent conflicts
            sanitize: false,  // Allow HTML
            highlight: function(code, lang) {
                if (Prism.languages[lang]) {
                    return Prism.highlight(code, Prism.languages[lang], lang);
                }
                return code;
            }
        };

        // Convert markdown to HTML
        let html = marked.parse(processedText, markedOptions);

        // Only add CSS classes to elements in AI messages (not user messages)
        if (!this.isUserMessage) {
            html = html
                .replace(/<p>/g, '<p class="mb-4">')
                .replace(/<h([1-6])>/g, '<h$1 class="font-bold mb-2 mt-4">')
                .replace(/<ul>/g, '<ul class="list-disc ml-6 mb-4">')
                .replace(/<ol>/g, '<ol class="list-decimal ml-6 mb-4">')
                .replace(/<li>/g, '<li class="mb-1">');
        }

        // Handle code blocks
        html = html.replace(
            /<pre><code class="language-([^"]+)">([\s\S]+?)<\/code><\/pre>/g,
            '<div class="code-block-container">' +
                '<div class="code-block-header">' +
                    '<span class="code-block-title">$1</span>' +
                    '<button class="copy-code-btn" aria-label="Copy code" onclick="navigator.clipboard.writeText(this.parentElement.nextElementSibling.textContent)">' +
                        '<i data-lucide="copy"></i> Copy' +
                    '</button>' +
                '</div>' +
                '<pre class="code-block"><code class="language-$1">$2</code></pre>' +
            '</div>'
        );

        // Add thinking container if thinking content exists
        if (thinkingContent && !this.isUserMessage) {
            const thinkingContainer = `
                <div class="thinking-container">
                    <div class="thinking-header" onclick="toggleThinking(this.parentElement)">
                        <div class="thinking-header-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brain">
                                <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 4.44-2.04Z"></path>
                                <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-4.44-2.04Z"></path>
                            </svg>
                        </div>
                        <div class="thinking-header-title">Thinking Done</div>
                        <div class="thinking-toggle">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                    </div>
                    <div class="thinking-content">${thinkingContent}</div>
                </div>
            `;
            html = thinkingContainer + html;
        }

        return html;
    }

    async shareThread(threadId) {
        try {
            const response = await fetch(`/api/chat/thread/${threadId}/share`, {
                method: 'POST'
            });
            const data = await response.json();

            // Get the current domain
            const domain = window.location.origin;

            // Create full URL with domain
            const fullShareUrl = `${domain}${data.share_url}`;

            // Copy full share URL to clipboard
            await navigator.clipboard.writeText(fullShareUrl);

            this.showNotification('Share link copied to clipboard!', 'success');
        } catch (error) {
            console.error('Failed to share thread:', error);
            this.showNotification('Failed to share thread', 'error');
        }
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    async loadSharedThread(sharedThread) {
        this.chatContent.innerHTML = '';
        sharedThread.messages.forEach(msg => {
            const { messageDiv, contentDiv } = this.createMessageElement(msg.content, msg.role === 'user');
            // Replace textContent with innerHTML and use formatMessage
            contentDiv.innerHTML = this.formatMessage(msg.content);

            // Add images if present in the message
            if (msg.images && msg.images.length > 0 && msg.role === 'user') {
                const imageContainer = document.createElement('div');
                imageContainer.className = 'message-images';
                msg.images.forEach(img => {
                    const imgElement = document.createElement('img');
                    imgElement.src = `data:${img.mime_type};base64,${img.data}`;
                    imgElement.className = 'message-image-preview';
                    imageContainer.appendChild(imgElement);
                });
                messageDiv.appendChild(imageContainer);
            }

            this.chatContent.appendChild(messageDiv);
        });

        // Add "Copy Thread" button
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-thread-btn';
        copyButton.setAttribute('aria-label', 'Copy thread');
        copyButton.innerHTML = '<i data-lucide="copy"></i> Copy Thread';
        copyButton.addEventListener('click', () => this.copySharedThread(sharedThread.share_id));
        this.chatContent.appendChild(copyButton);

        // Update chat title
        document.getElementById('activeChatName').textContent = sharedThread.title;

        // Apply syntax highlighting after adding messages
        Prism.highlightAll();
    }

    async copySharedThread(shareId) {
        try {
            const response = await fetch(`/api/chat/shared/${shareId}/copy`, {
                method: 'POST'
            });
            const thread = await response.json();

            // Load the new thread
            this.loadThread(thread.id);
            this.loadThreads();

        } catch (error) {
            console.error('Failed to copy shared thread:', error);
        }
    }

    setupImageHandling() {
        this.imageBtn.addEventListener('click', () => {
            this.imageFileInput.click();
        });

        this.imageFileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            if (files.length > 2) {
                alert('Maximum 2 images allowed');
                return;
            }

            this.handleImageFiles(files);
        });
    }

    async handleImageFiles(files) {
        this.imagePreviewContainer.innerHTML = '';
        this.imagePreviewContainer.classList.remove('hidden');

        // Add a global remove button at the top right of the image preview container
        const globalRemoveBtn = document.createElement('button');
        globalRemoveBtn.className = 'global-remove-image-btn';
        globalRemoveBtn.setAttribute('aria-label', 'Remove all images');
        globalRemoveBtn.innerHTML = '<i data-lucide="x"></i>';
        globalRemoveBtn.onclick = () => {
            this.imagePreviewContainer.innerHTML = '';
            this.imagePreviewContainer.classList.add('hidden');
            this.currentImages = null;
        };

        // Initialize the X icon immediately
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                },
                elements: [globalRemoveBtn]
            });
        }

        const imagePromises = files.map(file => {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'relative';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'image-preview';

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-image-btn';
                    removeBtn.setAttribute('aria-label', 'Remove image');
                    removeBtn.innerHTML = '<i data-lucide="x"></i>';
                    removeBtn.onclick = () => {
                        previewDiv.remove();
                        if (this.imagePreviewContainer.children.length <= 1) { // Only the global remove button left
                            this.imagePreviewContainer.innerHTML = '';
                            this.imagePreviewContainer.classList.add('hidden');
                            this.currentImages = null;
                        }
                    };

                    previewDiv.appendChild(img);
                    previewDiv.appendChild(removeBtn);
                    this.imagePreviewContainer.appendChild(previewDiv);

                    // Initialize the X icon immediately for this specific button
                    if (window.lucide) {
                        window.lucide.createIcons({
                            attrs: {
                                'stroke-width': '2',
                                'class': 'icon'
                            },
                            elements: [removeBtn]
                        });
                    }

                    resolve({
                        data: e.target.result.split(',')[1],
                        mime_type: file.type
                    });
                };
                reader.readAsDataURL(file);
            });
        });

        // Store the image data for sending
        this.currentImages = await Promise.all(imagePromises);

        // Automatically switch to Gemini model
        document.getElementById('currentModel').textContent = 'Gemini';

        // Update the icon in the current model display
        const modelSelector = document.getElementById('modelSelector');
        const modelIcon = modelSelector.querySelector('.current-model-icon i');
        if (modelIcon) {
            modelIcon.setAttribute('data-lucide', 'zap');
            // Re-initialize the icon
            if (window.lucide) {
                window.lucide.createIcons({
                    attrs: {
                        'stroke-width': '2',
                        'class': 'icon'
                    },
                    elements: [modelIcon.parentElement]
                });
            }
        }

        // Update the selected card in the modal if it's visible
        const modelOptionCards = document.querySelectorAll('.model-option-card');
        modelOptionCards.forEach(card => {
            if (card.getAttribute('data-model') === 'gemini-2.0-flash') {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    }
}

// Initialize critical elements immediately
function initCriticalElements() {
    // Create a placeholder for thread titles to prevent layout shift
    const chatList = document.getElementById('chatList');
    if (chatList) {
        const placeholders = Array(5).fill().map(() => {
            const div = document.createElement('div');
            div.className = 'thread-item placeholder';
            div.innerHTML = '<div class="thread-title">Loading...</div>';
            return div;
        });
        placeholders.forEach(p => chatList.appendChild(p));
    }
}

// Run critical initialization immediately
if (document.getElementById('chatList')) {
    initCriticalElements();
}

// Initialize chat interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Lucide icons first to ensure they're available
    if (typeof lucide !== 'undefined') {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    }

    // Initialize chat interface
    window.chatInterface = new ChatInterface();

    // Setup sidebar toggle for mobile
    setupMobileSidebar();

    // Log for debugging
    console.log('DOM fully loaded, mobile sidebar setup complete');
});

// Function to handle mobile sidebar toggle
function setupMobileSidebar() {
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const closeSidebarBtn = document.getElementById('closeSidebarBtn');
    const sidebar = document.querySelector('.sidebar');
    const mainChat = document.querySelector('.main-chat');

    // Debug logging
    console.log('Setting up mobile sidebar:', {
        sidebarToggleBtn: sidebarToggleBtn,
        closeSidebarBtn: closeSidebarBtn,
        sidebar: sidebar
    });

    if (sidebar) {
        // Make sure sidebar starts in the correct state
        if (window.innerWidth <= 768) {
            sidebar.classList.remove('open');
        }

        // Toggle sidebar when button is clicked
        if (sidebarToggleBtn) {
            console.log('Adding click event to toggle button');
            sidebarToggleBtn.addEventListener('click', (e) => {
                console.log('Toggle button clicked');
                e.stopPropagation(); // Prevent event bubbling
                sidebar.classList.toggle('open');
            });
        } else {
            console.error('Sidebar toggle button not found!');
        }

        // Close sidebar with X button
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent event bubbling
                sidebar.classList.remove('open');
            });
        }

        // Close sidebar when clicking outside of it on mobile
        if (mainChat) {
            mainChat.addEventListener('click', () => {
                if (window.innerWidth <= 768 && sidebar.classList.contains('open')) {
                    sidebar.classList.remove('open');
                }
            });
        }

        // Close sidebar when window is resized to desktop size
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
            }
        });
    } else {
        console.error('Sidebar element not found!');
    }
}

// Model selector functionality
document.addEventListener('DOMContentLoaded', () => {
    const modelSelector = document.getElementById('modelSelector');
    const currentModel = document.getElementById('currentModel');
    const modelSelectorContainer = document.getElementById('modelSelectorContainer');
    const modelOptionCards = document.querySelectorAll('.model-option-card');
    const modelInfoIcons = document.querySelectorAll('.model-info-icon');

    // Set initial selected model
    updateSelectedModelCard(currentModel.textContent);

    // Toggle model selector when clicking on the current model
    modelSelector.addEventListener('click', (e) => {
        e.stopPropagation();
        modelSelectorContainer.classList.toggle('visible');
        // Initialize Lucide icons
        if (window.lucide) {
            window.lucide.createIcons({
                attrs: {
                    'stroke-width': '2',
                    'class': 'icon'
                }
            });
        }
    });

    // Get the tooltip element
    const tooltip = document.getElementById('modelDescriptionTooltip');

    // Handle info icon clicks
    modelInfoIcons.forEach(infoIcon => {
        infoIcon.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent card selection when clicking info icon

            // Get description from data attribute
            const description = infoIcon.getAttribute('data-description');

            // Set content (moved to the positioning section)

            // Position the tooltip above the info icon
            const card = infoIcon.closest('.model-option-card');

            // Move the tooltip to be a child of the model selector container for proper positioning
            const modelSelectorContainer = document.getElementById('modelSelectorContainer');
            modelSelectorContainer.appendChild(tooltip);

            // Get positions for accurate placement
            const cardRect = card.getBoundingClientRect();
            const containerRect = modelSelectorContainer.getBoundingClientRect();

            // Get the info icon position
            const infoIconRect = infoIcon.getBoundingClientRect();

            // Calculate position relative to the container
            const left = infoIconRect.left - containerRect.left + (infoIconRect.width / 2);
            const top = cardRect.top - containerRect.top - 50; // Position well above the card

            // Set tooltip content first
            tooltip.textContent = description;
            
            // Make tooltip visible to calculate its dimensions
            tooltip.style.visibility = 'visible';
            tooltip.style.display = 'block';
            
            // Get tooltip dimensions
            const tooltipHeight = tooltip.offsetHeight;
            
            // Set position relative to the card
            tooltip.style.position = 'absolute';
            tooltip.style.top = `${cardRect.top - containerRect.top - tooltipHeight - 10}px`;
            tooltip.style.left = `${cardRect.left - containerRect.left + (cardRect.width / 2)}px`;
            tooltip.style.transform = 'translateX(-50%)';
            tooltip.style.bottom = 'auto';
            // Adjust width based on content length
            const textLength = description.length;
            if (textLength > 35) {
                tooltip.style.width = '300px'; // Wider for longer descriptions
            } else if (textLength > 25) {
                tooltip.style.width = '250px'; // Medium width for medium descriptions
            } else {
                tooltip.style.width = '200px'; // Default width for short descriptions
            }

            // Show the tooltip
            tooltip.style.display = 'block';
        });
    });

    // Handle model selection
    modelOptionCards.forEach(card => {
        card.addEventListener('click', (e) => {
            // Don't select model if clicking on info icon
            if (e.target.closest('.model-info-icon')) {
                return;
            }

            const selectedModel = card.getAttribute('data-model');
            let displayName;

            if (selectedModel === 'gpt-4o-mini') {
                displayName = 'GPT-4o Mini';
            } else if (selectedModel === 'gemini-2.0-flash') {
                displayName = 'Gemini';
            } else if (selectedModel === 'qwen-qwq-32b') {
                displayName = 'Qwen';
            } else if (selectedModel === 'gemma2-9b-it') {
                displayName = 'Gemma 2';
            } else if (selectedModel === 'llama-3.3-70b-versatile') {
                displayName = 'Llama 3.3 70B';
            } else if (selectedModel === 'llama-3.1-8b-instant') {
                displayName = 'Llama 3.1 8B';
            } else if (selectedModel === 'llama3-70b-8192') {
                displayName = 'Llama 3 70B';
            } else if (selectedModel === 'llama3-8b-8192') {
                displayName = 'Llama 3 8B';
            } else if (selectedModel === 'playai-tts') {
                displayName = 'PlayAI TTS';
            }

            // Update the current model display
            currentModel.textContent = displayName;

            // Update the selected card in the modal
            updateSelectedModelCard(displayName);

            // Update the icon in the current model display
            const modelIcon = modelSelector.querySelector('.current-model-icon i');
            if (modelIcon) {
                let iconName;
                if (selectedModel === 'gpt-4o-mini') {
                    iconName = 'sparkles';
                } else if (selectedModel === 'gemini-2.0-flash') {
                    iconName = 'zap';
                } else if (selectedModel === 'qwen-qwq-32b') {
                    iconName = 'cpu';
                } else if (selectedModel === 'gemma2-9b-it') {
                    iconName = 'brain';
                } else if (selectedModel === 'llama-3.3-70b-versatile' || selectedModel === 'llama3-70b-8192') {
                    iconName = 'flame';
                } else if (selectedModel === 'llama-3.1-8b-instant' || selectedModel === 'llama3-8b-8192') {
                    iconName = 'zap';
                } else if (selectedModel === 'playai-tts') {
                    iconName = 'volume-2';
                }

                modelIcon.setAttribute('data-lucide', iconName);
                // Re-initialize the icon
                if (window.lucide) {
                    window.lucide.createIcons({
                        attrs: {
                            'stroke-width': '2',
                            'class': 'icon'
                        },
                        elements: [modelIcon.parentElement]
                    });
                }
            }

            // Close the modal
            modelSelectorContainer.classList.remove('visible');
        });
    });

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Hide model selector if visible
            if (modelSelectorContainer.classList.contains('visible')) {
                modelSelectorContainer.classList.remove('visible');
            }

            // Hide tooltip
            document.getElementById('modelDescriptionTooltip').style.display = 'none';
        }
    });

    // Close when clicking outside
    document.addEventListener('click', (e) => {
        // Close model selector when clicking outside
        if (modelSelectorContainer.classList.contains('visible') &&
            !modelSelectorContainer.contains(e.target) &&
            !modelSelector.contains(e.target)) {
            modelSelectorContainer.classList.remove('visible');
        }

        // Hide tooltip when clicking outside of info icons
        if (!e.target.closest('.model-info-icon')) {
            document.getElementById('modelDescriptionTooltip').style.display = 'none';
        }
    });

    // Function to update the selected model card
    function updateSelectedModelCard(displayName) {
        let modelName;

        if (displayName === 'GPT-4o Mini') {
            modelName = 'gpt-4o-mini';
        } else if (displayName === 'Gemini') {
            modelName = 'gemini-2.0-flash';
        } else if (displayName === 'Qwen') {
            modelName = 'qwen-qwq-32b';
        } else if (displayName === 'Gemma 2') {
            modelName = 'gemma2-9b-it';
        } else if (displayName === 'Llama 3.3 70B') {
            modelName = 'llama-3.3-70b-versatile';
        } else if (displayName === 'Llama 3.1 8B') {
            modelName = 'llama-3.1-8b-instant';
        } else if (displayName === 'Llama 3 70B') {
            modelName = 'llama3-70b-8192';
        } else if (displayName === 'Llama 3 8B') {
            modelName = 'llama3-8b-8192';
        } else if (displayName === 'PlayAI TTS') {
            modelName = 'playai-tts';
        }

        modelOptionCards.forEach(card => {
            if (card.getAttribute('data-model') === modelName) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    }
});

// Add this to your existing JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const joinLiveBtn = document.getElementById('joinLiveBtn');
    const liveWarningModal = document.getElementById('liveWarningModal');
    const cancelLiveBtn = document.getElementById('cancelLiveBtn');
    const confirmLiveBtn = document.getElementById('confirmLiveBtn');

    joinLiveBtn.addEventListener('click', () => {
        liveWarningModal.style.display = 'flex';
    });

    cancelLiveBtn.addEventListener('click', () => {
        liveWarningModal.style.display = 'none';
    });

    confirmLiveBtn.addEventListener('click', () => {
        window.location.href = '/live';
    });

    // Close on backdrop click
    liveWarningModal.addEventListener('click', (e) => {
        if (e.target === liveWarningModal) {
            liveWarningModal.style.display = 'none';
        }
    });

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && liveWarningModal.style.display === 'flex') {
            liveWarningModal.style.display = 'none';
        }
    });
});

// Function to toggle thinking container expansion
function toggleThinking(container) {
    const content = container.querySelector('.thinking-content');
    const toggle = container.querySelector('.thinking-toggle');

    // Toggle expanded class
    content.classList.toggle('expanded');
    toggle.classList.toggle('expanded');

    // Set appropriate height
    if (content.classList.contains('expanded')) {
        // Get the scroll height to determine the full height of the content
        const scrollHeight = content.scrollHeight;
        content.style.maxHeight = scrollHeight + 'px';

        // Add a small delay to ensure smooth animation
        setTimeout(() => {
            // Recalculate in case content changes (like code formatting)
            content.style.maxHeight = content.scrollHeight + 'px';
        }, 50);
    } else {
        content.style.maxHeight = '0';
    }

    // Initialize any Lucide icons that might be in the content
    if (typeof lucide !== 'undefined' && content.classList.contains('expanded')) {
        lucide.createIcons({
            attrs: {
                'stroke-width': '2',
                'class': 'icon'
            }
        });
    }

    // Apply syntax highlighting to code blocks if Prism is available
    if (typeof Prism !== 'undefined' && content.classList.contains('expanded')) {
        Prism.highlightAllUnder(content);
    }
}











