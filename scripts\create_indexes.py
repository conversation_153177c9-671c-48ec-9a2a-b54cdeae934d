from mongoengine import connect
from dotenv import load_dotenv
import os


def create_indexes():
    # Load environment variables
    load_dotenv()
    
    # Connect to MongoDB
    mongodb_uri = os.getenv('MONGO_URI')
    if not mongodb_uri:
        raise ValueError("No MONGO_URI environment variable set")
    
    connect(
        db='kevko_systems',
        host=mongodb_uri,
        alias='default'
    )

if __name__ == "__main__":
    create_indexes()
