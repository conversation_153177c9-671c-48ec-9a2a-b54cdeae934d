from flask import render_template, redirect, url_for, abort, flash
from flask_login import login_required, current_user
from . import live_views
from models.room import Room
from models.user_activity import UserActivity
from models.restricted_user import RestrictedUser

@live_views.route('/')
@login_required
def live_home():
    """Main live chat page"""
    # Check if user is restricted from live service
    if RestrictedUser.is_restricted(current_user.email, 'live'):
        return render_template('live.html', restricted=True, service_name='Live Chat')

    # Track user activity
    UserActivity.update_activity(current_user, 'live')
    return render_template('live.html')

@live_views.route('/room/<room_id>')
@login_required
def live_room(room_id):
    """Join a specific room by ID"""
    # Get the room first
    room = Room.objects(room_id=room_id).first()

    # Check if user is restricted from live service
    if RestrictedUser.is_restricted(current_user.email, 'live'):
        return render_template('live.html', room=room.to_dict() if room else None, restricted=True, service_name='Live Chat')

    # Track user activity
    UserActivity.update_activity(current_user, 'live')

    if not room:
        return redirect(url_for('live_views.live_home'))

    # Check if user is allowed to join this room
    if room.is_member(current_user):
        # User is already a member
        return render_template('live.html', room=room.to_dict())

    # Check if room is full
    if room.is_full():
        # Room is full and user is not a member
        return redirect(url_for('live_views.live_home'))

    # Add user as participant
    try:
        room.participant = current_user
        room.save()
    except Exception as e:
        print(f"Error adding participant: {str(e)}")
        return redirect(url_for('live_views.live_home'))

    return render_template('live.html', room=room.to_dict())
