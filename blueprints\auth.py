from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, session
from models.user import User
from models.feature_restriction import FeatureRestriction
from models.login_history import LoginHistory
from flask_login import Lo<PERSON><PERSON>anager, login_user, logout_user, login_required, current_user
import random
import string
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
import requests
import logging
from config.google_oauth import get_google_provider_cfg, create_google_oauth_client, get_callback_url

# Create blueprint with url_prefix
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')
login_manager = LoginManager()

def set_default_user_restrictions(user):
    """Set default feature restrictions for a new user"""
    # NOTE: Automatic feature restrictions have been removed.
    # This function now does nothing, but is kept for backward compatibility.
    # Feature restrictions are now only set by admins.
    print(f"Default feature restrictions are no longer automatically set for user: {user.username}")

@login_manager.user_loader
def load_user(user_id):
    return User.objects(id=user_id).first()

def generate_verification_code():
    return ''.join(random.choices(string.digits, k=6))

def send_verification_email(email, code):
    try:
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = os.getenv('EMAIL_ADDRESS')
        sender_password = os.getenv('EMAIL_PASSWORD')

        if not sender_email or not sender_password:
            print("Missing email credentials")
            return False

        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = email
        message["Subject"] = "Kevko Systems Verification Code"

        body = f"""
        Your verification code is: {code}

        This code will expire in 10 minutes.

        If you didn't request this code, please ignore this email.
        """
        message.attach(MIMEText(body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)
        return True
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False

@auth_bp.route('/get-email', methods=['POST'])
def get_email():
    try:
        data = request.get_json()
        login = data.get('login')  # Changed from username to login

        if not login:
            return jsonify({'error': 'Username or email is required'}), 400

        user = User.find_by_login(login)
        if not user:
            return jsonify({'error': 'No account found with these credentials'}), 404

        return jsonify({'email': user.email})
    except Exception as e:
        print(f"Error in get_email: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/send-verification', methods=['POST'])
def send_verification():
    try:
        data = request.get_json()
        email = data.get('email')
        request_type = data.get('type')

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        # For both login and registration, verify if user exists
        user = User.objects(email=email).first()

        # For login, verify if user exists
        if request_type == 'login':
            if not user:
                return jsonify({'error': 'No account found with this email'}), 404

        # For registration, check if email already exists
        elif request_type == 'register':
            if user:
                return jsonify({'error': 'Email already registered'}), 400

        # Generate and store verification code
        code = generate_verification_code()
        session[f'verification_code_{email}'] = code
        session[f'verification_code_timestamp_{email}'] = datetime.now().timestamp()

        # Send verification email
        if send_verification_email(email, code):
            return jsonify({'message': 'Verification code sent successfully'})
        return jsonify({'error': 'Failed to send verification code'}), 500

    except Exception as e:
        print(f"Error in send_verification: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/check-credentials', methods=['POST'])
def check_credentials():
    """Check user credentials and determine if 2FA is required"""
    try:
        data = request.get_json()
        login = data.get('login')
        password = data.get('password')

        if not login or not password:
            return jsonify({'error': 'Username/email and password are required'}), 400

        # Find user
        user = User.find_by_login(login)
        if not user:
            return jsonify({'error': 'Invalid credentials'}), 401

        # Check if user has a password set (not a Google user)
        if not user.password_hash:
            return jsonify({'error': 'Please log in with Google or reset your password'}), 401

        # Verify password
        if not user.check_password(password):
            return jsonify({'error': 'Invalid credentials'}), 401

        # Check if 2FA is enabled
        two_factor_required = hasattr(user, 'two_factor_enabled') and user.two_factor_enabled

        # If 2FA is required, send verification code
        if two_factor_required:
            # Generate and store verification code
            code = generate_verification_code()
            session[f'verification_code_{user.email}'] = code
            session[f'verification_code_timestamp_{user.email}'] = datetime.now().timestamp()

            # Send verification email
            send_verification_email(user.email, code)

            return jsonify({
                'message': 'Verification code sent',
                'two_factor_required': True
            })

        # If 2FA is not required, return success
        return jsonify({
            'message': 'Credentials verified',
            'two_factor_required': False
        })
    except Exception as e:
        print(f"Error in check_credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/resend-verification', methods=['POST'])
def resend_verification():
    """Resend verification code for 2FA"""
    try:
        data = request.get_json()
        login = data.get('login')

        if not login:
            return jsonify({'error': 'Username/email is required'}), 400

        # Find user
        user = User.find_by_login(login)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Generate and store verification code
        code = generate_verification_code()
        session[f'verification_code_{user.email}'] = code
        session[f'verification_code_timestamp_{user.email}'] = datetime.now().timestamp()

        # Send verification email
        if send_verification_email(user.email, code):
            return jsonify({'message': 'Verification code sent successfully'})
        return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        print(f"Error in resend_verification: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        login = request.form.get('login')
        password = request.form.get('password')
        verification_code = request.form.get('verification_code')

        # Store the next parameter in the session if it exists
        next_url = request.args.get('next')
        if next_url:
            session['next_url'] = next_url
            print(f"Storing next URL in session from login form: {next_url}")

        print(f"\n=== Login Attempt ===")
        print(f"Login input: {login}")
        print(f"Verification code provided: {verification_code if verification_code else 'None'}")

        user = User.find_by_login(login)
        if not user:
            print(f"Error: User not found for login: {login}")
            flash('Invalid credentials')
            return redirect(url_for('auth.login', next=request.args.get('next')))

        print(f"User found: {user.username} (Email: {user.email})")

        # Check if user has a password set
        if not user.password_hash:
            print("Error: User has no password set")
            flash('Please log in with Google or reset your password')
            return redirect(url_for('auth.login', next=request.args.get('next')))

        # Verify password
        if not user.check_password(password):
            print("Error: Invalid password")
            flash('Invalid credentials')
            return redirect(url_for('auth.login', next=request.args.get('next')))

        # Check if 2FA is enabled for this user
        two_factor_required = hasattr(user, 'two_factor_enabled') and user.two_factor_enabled

        # If 2FA is required, verify the code
        if two_factor_required:
            # Get stored verification code
            stored_code = session.get(f'verification_code_{user.email}')
            code_timestamp = session.get(f'verification_code_timestamp_{user.email}')

            # If no code is stored or provided, redirect back to login
            if not stored_code or not verification_code:
                flash('Verification code is required')
                return redirect(url_for('auth.login', next=request.args.get('next')))

            # Check if code is expired
            if (datetime.now().timestamp() - code_timestamp) > 600:
                flash('Verification code has expired')
                return redirect(url_for('auth.login', next=request.args.get('next')))

            # Check if code is correct
            if verification_code != stored_code:
                print("Error: Incorrect verification code")
                flash('Incorrect verification code')
                return redirect(url_for('auth.login', next=request.args.get('next')))

        # If we get here, either 2FA was not required or it passed
        print("Login successful")
        login_user(user)
        print(f"User logged in successfully: {user.username}")

        # Log login history
        try:
            LoginHistory.log_login(user, request)
            print("Login history logged successfully")
        except Exception as e:
            logging.warning(f"Failed to log login history: {str(e)}")
            print(f"Failed to log login history: {str(e)}")

        # Country detection is now handled by middleware

        # Ensure user has feature restrictions set
        if not FeatureRestriction.get_user_restrictions(user.id):
            set_default_user_restrictions(user)

        # Clear verification codes
        session.pop(f'verification_code_{user.email}', None)
        session.pop(f'verification_code_timestamp_{user.email}', None)

        print("=== Login Success ===\n")
        # Get the next parameter from the session or request args, or use the landing page as default
        next_page = session.get('next_url') or request.args.get('next') or url_for('landing')
        # Validate the next URL to prevent open redirect vulnerabilities
        if not next_page.startswith('/'):
            next_page = url_for('landing')
        # Clear the next_url from session
        session.pop('next_url', None)
        print(f"Redirecting to: {next_page}")
        return redirect(next_page)

    return render_template('login.html')

@auth_bp.route('/register', methods=['POST'])
def register():
    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')
    verification_code = request.form.get('verification_code')

    stored_code = session.get(f'verification_code_{email}')
    code_timestamp = session.get(f'verification_code_timestamp_{email}')

    if not stored_code or verification_code != stored_code:
        flash('Incorrect verification code')
        return redirect(url_for('auth.login'))

    if (datetime.now().timestamp() - code_timestamp) > 600:
        flash('Verification code has expired')
        return redirect(url_for('auth.login'))

    if User.objects(email=email).first():
        flash('Email already registered')
        return redirect(url_for('auth.login'))

    if User.objects(username=username).first():
        flash('Username already taken')
        return redirect(url_for('auth.login'))

    user = User(
        username=username,
        email=email,
        contact_settings=""
    )
    user.set_password(password)
    user.save()

    # Set default feature restrictions for the new user
    set_default_user_restrictions(user)

    session.pop(f'verification_code_{email}', None)
    session.pop(f'verification_code_timestamp_{email}', None)

    login_user(user)

    # Log login history
    try:
        LoginHistory.log_login(user, request)
        print("Login history logged successfully")
    except Exception as e:
        logging.warning(f"Failed to log login history: {str(e)}")
        print(f"Failed to log login history: {str(e)}")

    # Country detection is now handled by middleware

    return redirect(url_for('landing'))

@auth_bp.route('/logout')
@login_required
def logout():
    # Clear Flask-Login session (main application logout)
    logout_user()

    # Clear verification codes if any exist
    for key in list(session.keys()):
        if key.startswith('verification_code_'):
            session.pop(key, None)

    # Clear the session except Spotify tokens
    spotify_token = session.get('token_info')
    session.clear()

    # Restore Spotify token if it existed
    if spotify_token:
        session['token_info'] = spotify_token

    return redirect(url_for('auth.login'))

@auth_bp.route('/spotify/logout')
def spotify_logout():
    # Clear only Spotify-related session data
    if 'token_info' in session:
        session.pop('token_info', None)

    # Redirect to Spotify disconnect endpoint
    return redirect(url_for('spotify_api.logout', _external=True))

@auth_bp.route('/current-user')
@login_required
def get_current_user():
    """Get current user information"""
    try:
        if not current_user.is_authenticated:
            return jsonify({'error': 'Not authenticated'}), 401

        # Get user data
        user_data = {
            'username': current_user.username,
            'email': current_user.email,
            'initials': ''.join([name[0].upper() for name in current_user.username.split() if name]),
            'google_id': current_user.google_id if hasattr(current_user, 'google_id') else None,
            'profile_picture': current_user.profile_picture if hasattr(current_user, 'profile_picture') else None,
            'two_factor_enabled': current_user.two_factor_enabled if hasattr(current_user, 'two_factor_enabled') else False,
            'created_at': current_user.created_at.isoformat() if hasattr(current_user, 'created_at') else None
        }

        # Only use Google profile picture if user doesn't have a custom one
        if not user_data['profile_picture'] and 'google_user' in session and session['google_user'].get('picture'):
            user_data['profile_picture'] = session['google_user'].get('picture')

        return jsonify(user_data)
    except Exception as e:
        print(f"Error in get_current_user: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/verify-credentials', methods=['POST'])
def verify_credentials():
    try:
        data = request.get_json()
        login = data.get('login')  # Username or email
        password = data.get('password')

        if not login or not password:
            return jsonify({'error': 'Username/email and password are required'}), 400

        # Find user by username or email
        user = User.find_by_login(login)
        if not user:
            return jsonify({'error': 'No account found with these credentials'}), 404

        # Verify password
        if not user.check_password(password):
            return jsonify({'error': 'Incorrect password'}), 401

        # Generate and store verification code
        code = generate_verification_code()
        session[f'verification_code_{user.email}'] = code
        session[f'verification_code_timestamp_{user.email}'] = datetime.now().timestamp()

        # Send verification email
        if send_verification_email(user.email, code):
            return jsonify({
                'message': 'Verification code sent successfully',
                'email': user.email
            })
        return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        print(f"Error in verify_credentials: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/request-password-reset', methods=['POST'])
def request_password_reset():
    try:
        data = request.get_json()
        login = data.get('login')  # Username or email

        if not login:
            return jsonify({'error': 'Username or email is required'}), 400

        user = User.find_by_login(login)
        if not user:
            return jsonify({'error': 'No account found with these credentials'}), 404

        # Generate and store verification code
        code = generate_verification_code()
        session[f'verification_code_{user.email}'] = code
        session[f'verification_code_timestamp_{user.email}'] = datetime.now().timestamp()

        # Send verification email
        if send_verification_email(user.email, code):
            return jsonify({'message': 'Verification code sent successfully', 'email': user.email})
        return jsonify({'error': 'Failed to send verification code'}), 500
    except Exception as e:
        print(f"Error in request_password_reset: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/reset-password', methods=['POST'])
def reset_password():
    try:
        data = request.get_json()
        email = data.get('email')
        verification_code = data.get('verification_code')
        new_password = data.get('new_password')

        if not email or not verification_code or not new_password:
            return jsonify({'error': 'Email, verification code, and new password are required'}), 400

        user = User.objects(email=email).first()
        if not user:
            return jsonify({'error': 'No account found with this email'}), 404

        # Verify the stored verification code
        stored_code = session.get(f'verification_code_{email}')
        code_timestamp = session.get(f'verification_code_timestamp_{email}')

        if not stored_code or not code_timestamp:
            return jsonify({'error': 'Verification code has expired'}), 400

        if (datetime.now().timestamp() - code_timestamp) > 600:
            return jsonify({'error': 'Verification code has expired'}), 400

        if verification_code != stored_code:
            return jsonify({'error': 'Incorrect verification code'}), 400

        # Ensure required fields are properly initialized
        if user.contact_settings is None:
            user.contact_settings = ""

        # Set new password
        user.set_password(new_password)
        user.save()

        # Clear verification codes
        session.pop(f'verification_code_{email}', None)
        session.pop(f'verification_code_timestamp_{email}', None)

        return jsonify({'message': 'Password reset successfully'})
    except Exception as e:
        print(f"Error in reset_password: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route("/google/login")
def google_login():
    try:
        print("\n=== Starting Google Login Process ===")

        # Store the next parameter in the session
        next_url = request.args.get('next')
        if next_url:
            session['next_url'] = next_url
            print(f"Storing next URL in session: {next_url}")

        google_provider_cfg = get_google_provider_cfg()
        client = create_google_oauth_client()

        authorization_endpoint = google_provider_cfg["authorization_endpoint"]
        callback_url = get_callback_url()

        request_uri = client.prepare_request_uri(
            authorization_endpoint,
            redirect_uri=callback_url,
            scope=["openid", "email", "profile"],
        )

        print(f"Authorization URL: {request_uri}")
        print("===================================\n")

        return redirect(request_uri)
    except Exception as e:
        print(f"Google login error: {str(e)}")
        flash(f"Failed to initialize Google login: {str(e)}")
        # Redirect back to login with next parameter
        next_url = session.get('next_url')
        session.pop('next_url', None)  # Clear it from session
        return redirect(url_for('auth.login', next=next_url))

@auth_bp.route("/google/callback")
def google_callback():
    try:
        print("\n=== Processing Google Callback ===")
        print(f"Request URL: {request.url}")
        print(f"Request Args: {request.args}")

        # Set OAUTHLIB_INSECURE_TRANSPORT for development
        if os.getenv('FLASK_ENV') == 'development':
            os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

        google_provider_cfg = get_google_provider_cfg()
        client = create_google_oauth_client()

        code = request.args.get("code")
        if not code:
            print("No code received in callback")
            flash("Authentication failed - no code received")
            # Redirect back to login with next parameter
            next_url = session.get('next_url')
            session.pop('next_url', None)  # Clear it from session
            return redirect(url_for('auth.login', next=next_url))

        # Get tokens from Google
        token_endpoint = google_provider_cfg["token_endpoint"]
        callback_url = get_callback_url()

        print(f"Token Endpoint: {token_endpoint}")
        print(f"Callback URL: {callback_url}")
        print(f"Code: {code}")

        try:
            token_url, headers, body = client.prepare_token_request(
                token_endpoint,
                authorization_response=request.url,
                redirect_url=callback_url,
                code=code
            )

            print(f"Token URL: {token_url}")
            print(f"Headers: {headers}")
            print(f"Body: {body}")

            client_id = os.getenv("GOOGLE_CLIENT_ID")
            client_secret = os.getenv("GOOGLE_CLIENT_SECRET")

            token_response = requests.post(
                token_url,
                headers=headers,
                data=body,
                auth=(client_id, client_secret),
            )

            print(f"Token Response Status: {token_response.status_code}")
            print(f"Token Response: {token_response.text}")

            if not token_response.ok:
                raise Exception(f"Token request failed: {token_response.text}")

            token_data = token_response.json()

        except Exception as e:
            print(f"Token exchange error: {str(e)}")
            print(f"Full error details: ", e.__class__.__name__)
            raise

        # Get user info from Google
        try:
            userinfo_endpoint = google_provider_cfg["userinfo_endpoint"]
            access_token = token_data.get('access_token')

            if not access_token:
                raise Exception("No access token received")

            headers = {'Authorization': f'Bearer {access_token}'}
            userinfo_response = requests.get(userinfo_endpoint, headers=headers)

            print(f"Userinfo Response Status: {userinfo_response.status_code}")
            print(f"Userinfo Response: {userinfo_response.text}")

            if not userinfo_response.ok:
                raise Exception(f"Userinfo request failed: {userinfo_response.text}")

            userinfo = userinfo_response.json()

        except Exception as e:
            print(f"Userinfo error: {str(e)}")
            print(f"Full error details: ", e.__class__.__name__)
            raise

        if userinfo.get("email_verified"):
            google_id = userinfo["sub"]
            email = userinfo["email"]

            # Get the Google username (given_name) or fall back to email prefix
            google_username = (
                userinfo.get("given_name") or
                userinfo.get("name", "").split()[0] or
                email.split('@')[0]
            ).lower()

            # Store Google user info in session
            session['google_user'] = {
                'id': google_id,
                'email': email,
                'name': userinfo.get("name", ""),
                'picture': userinfo.get("picture")
            }

            print(f"\nGoogle User Info:")
            print(f"Email: {email}")
            print(f"Username from Google: {google_username}")
            print(f"Google ID: {google_id}")
            print(f"Profile Picture: {userinfo.get('picture')}")

            # Try to find existing user
            user = User.objects(google_id=google_id).first()
            if not user:
                user = User.objects(email=email).first()

            if user:
                print(f"Existing user found: {user.username}")
                if user.google_id and user.username != google_username:
                    if not User.objects(username=google_username).first():
                        user.username = google_username
                        user.save()
                        print(f"Updated username to: {google_username}")
                # Update user's Google ID and profile picture if needed
                update_needed = False

                if user.google_id != google_id:
                    user.google_id = google_id
                    update_needed = True

                # Only update profile picture if user doesn't have one yet
                if userinfo.get('picture') and not user.profile_picture:
                    user.profile_picture = userinfo.get('picture')
                    update_needed = True

                if update_needed:
                    user.save()
            else:
                print("Creating new user")
                username = google_username
                base_username = username
                counter = 1

                while User.objects(username=username).first():
                    username = f"{base_username}{counter}"
                    counter += 1

                user = User(
                    username=username,
                    email=email,
                    google_id=google_id,
                    profile_picture=userinfo.get('picture'),
                    contact_settings=""
                )
                user.save()
                print(f"New user created: {username}")

                # Set default feature restrictions for the new user
                set_default_user_restrictions(user)

            login_user(user)
            print("User logged in successfully")

            # Log login history
            try:
                LoginHistory.log_login(user, request)
                print("Login history logged successfully")
            except Exception as e:
                logging.warning(f"Failed to log login history: {str(e)}")
                print(f"Failed to log login history: {str(e)}")

            # Ensure user has feature restrictions set
            if not FeatureRestriction.get_user_restrictions(user.id):
                set_default_user_restrictions(user)

            print("===================================\n")

            # Get the next parameter from the session or use the landing page as default
            next_page = session.get('next_url') or url_for('landing')
            # Validate the next URL to prevent open redirect vulnerabilities
            if not next_page.startswith('/'):
                next_page = url_for('landing')
            # Clear the next_url from session
            session.pop('next_url', None)
            return redirect(next_page)
        else:
            print("Email not verified by Google")
            flash("Google account email not verified")
            # Redirect back to login with next parameter
            next_url = session.get('next_url')
            session.pop('next_url', None)  # Clear it from session
            return redirect(url_for('auth.login', next=next_url))

    except Exception as e:
        print(f"Error in google_callback: {str(e)}")
        print(f"Full error details: ", e.__class__.__name__)
        flash(f"Authentication failed: {str(e)}")
        # Redirect back to login with next parameter
        next_url = session.get('next_url')
        session.pop('next_url', None)  # Clear it from session
        return redirect(url_for('auth.login', next=next_url))
