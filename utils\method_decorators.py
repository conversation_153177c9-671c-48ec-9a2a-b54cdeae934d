"""
Method decorators for API endpoints
"""
from functools import wraps
from flask import jsonify, request, abort
import logging

def method_not_allowed(allowed_methods):
    """
    Decorator to restrict API endpoints to specific HTTP methods.
    Returns a 405 Method Not Allowed response for unsupported methods.
    
    Args:
        allowed_methods (list): List of allowed HTTP methods (e.g., ['GET', 'POST'])
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method not in allowed_methods:
                logging.warning(f"Method not allowed: {request.method} for {request.path}")
                # Check if the request is from a script by examining headers
                user_agent = request.headers.get('User-Agent', '').lower()
                # List of common script/bot user agents
                script_agents = ['python', 'curl', 'wget', 'postman', 'insomnia', 'axios', 'fetch', 'node']
                
                is_script = any(agent in user_agent for agent in script_agents)
                
                if is_script:
                    # For scripts, return a JSON response
                    return jsonify({
                        'success': False,
                        'error': 'Method Not Allowed',
                        'message': f'The method {request.method} is not allowed for this endpoint. Allowed methods: {", ".join(allowed_methods)}'
                    }), 405
                else:
                    # For browser requests, use Flask's abort
                    abort(405)
            return f(*args, **kwargs)
        return decorated_function
    return decorator