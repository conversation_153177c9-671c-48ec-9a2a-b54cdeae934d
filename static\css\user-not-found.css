/* User Not Found Page Styles */

:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --secondary-color: #06b6d4;
    --accent-color: #f43f5e;
    --dark-bg: #1e1e2e;
    --card-bg: rgba(30, 30, 46, 0.7);
    --text-primary: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --radius-lg: 12px;
    --transition-normal: 0.3s ease;
}

html, body {
    height: 100%; /* Ensure both html and body are full height */
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent scrolling */
}

body {
    background-color: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Inter', system-ui, sans-serif;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(79, 70, 229, 0.03) 2%, transparent 0%), 
        radial-gradient(circle at 75px 75px, rgba(6, 182, 212, 0.03) 2%, transparent 0%);
    background-size: 100px 100px;
    
    /* Perfect centering with flexbox */
    display: flex;
    align-items: center; /* Vertical centering */
    justify-content: center; /* Horizontal centering */
    min-height: 100vh; /* Use viewport height */
}

.not-found-container {
    max-width: 600px;
    width: 90%; /* Slightly reduced from 100% to prevent edge-to-edge on mobile */
    text-align: center;
    padding: 2.5rem; /* Slightly reduced padding */
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    position: fixed; /* Fixed positioning for better stability */
    overflow: hidden;
    
    /* Perfect centering */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Perfect centering trick */
    
    /* Add a subtle animation on load */
    animation: fadeInUp 0.6s ease-out forwards;
    z-index: 10; /* Ensure it's above decorations */
    
    /* Prevent any layout shifts */
    will-change: transform;
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate(-50%, -40%); /* Start from below center */
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%); /* End at perfect center */
    }
}

/* We'll handle hover effects with JavaScript for better reliability */

.not-found-decoration {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    z-index: 0;
}

.not-found-decoration-1 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
    top: -80px;
    right: -80px;
    opacity: 0.2;
    pointer-events: none; /* Ensure it doesn't interfere with clicks */
}

.not-found-decoration-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);
    bottom: -60px;
    left: -60px;
    opacity: 0.15;
    pointer-events: none; /* Ensure it doesn't interfere with clicks */
}

/* Add subtle animation to decorations */
@keyframes float {
    0% { transform: translateY(0) rotate(0); }
    50% { transform: translateY(-10px) rotate(1deg); }
    100% { transform: translateY(0) rotate(0); }
}

.not-found-decoration-1 {
    animation: float 8s ease-in-out infinite;
}

.not-found-decoration-2 {
    animation: float 10s ease-in-out infinite reverse;
}

.not-found-icon {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    position: relative;
    z-index: 1;
}

.not-found-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.not-found-message {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.not-found-username {
    font-weight: bold;
    color: var(--secondary-color);
}

.not-found-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    backdrop-filter: blur(5px);
}

@media (max-width: 576px) {
    .not-found-container {
        padding: 2rem 1.5rem;
        width: 85%; /* Slightly narrower on mobile */
        max-height: 90vh; /* Ensure it doesn't overflow vertically */
    }

    .not-found-title {
        font-size: 1.8rem; /* Slightly smaller on mobile */
    }

    .not-found-icon {
        font-size: 3.5rem; /* Slightly smaller on mobile */
        margin-bottom: 1rem; /* Reduced margin */
    }
    
    .not-found-message {
        font-size: 1rem; /* Slightly smaller text */
        margin-bottom: 1.5rem; /* Reduced margin */
    }

    .not-found-actions {
        flex-direction: column;
        gap: 0.75rem; /* Reduced gap */
    }

    .btn {
        width: 100%;
        padding: 0.75rem 1.5rem; /* Slightly smaller buttons */
    }
}