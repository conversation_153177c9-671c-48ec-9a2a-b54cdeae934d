/* 
 * Friends Loading Placeholders
 * Provides pulsating skeleton placeholders for the Friends area while content loads
 */

/* Friend List Placeholder Container */
.friend-list-placeholder {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px;
    overflow-y: auto;
    height: 100%;
}

/* Friend Item Placeholder */
.friend-item-placeholder {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    background-color: rgba(45, 45, 45, 0.5);
    position: relative;
    overflow: hidden;
}

/* Friend Avatar Placeholder */
.friend-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(60, 60, 60, 0.7);
    margin-right: 12px;
    flex-shrink: 0;
}

/* Friend Info Placeholder */
.friend-info-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Friend Name Placeholder */
.friend-name-placeholder {
    height: 16px;
    width: 70%;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
}

/* Friend Status Placeholder */
.friend-status-placeholder {
    height: 12px;
    width: 40%;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
}

/* Group Chat Placeholder */
.group-chat-placeholder {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    background-color: rgba(45, 45, 45, 0.5);
    position: relative;
    overflow: hidden;
}

.group-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background-color: rgba(60, 60, 60, 0.7);
    margin-right: 12px;
    flex-shrink: 0;
}

.group-info-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.group-name-placeholder {
    height: 16px;
    width: 80%;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
}

.group-members-placeholder {
    height: 12px;
    width: 60%;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
}

/* Pulsating Animation */
.friend-item-placeholder,
.group-chat-placeholder,
.friend-avatar-placeholder,
.friend-name-placeholder,
.friend-status-placeholder,
.group-avatar-placeholder,
.group-name-placeholder,
.group-members-placeholder {
    position: relative;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer effect overlay */
.friend-item-placeholder::after,
.group-chat-placeholder::after,
.friend-avatar-placeholder::after,
.friend-name-placeholder::after,
.friend-status-placeholder::after,
.group-avatar-placeholder::after,
.group-name-placeholder::after,
.group-members-placeholder::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(
        90deg,
        rgba(60, 60, 60, 0) 0%,
        rgba(99, 102, 241, 0.15) 50%,
        rgba(60, 60, 60, 0) 100%
    );
    animation: shimmer 1.8s infinite;
    transform: translateX(-100%);
    pointer-events: none;
}

/* Pulse animation for the base elements */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Shimmer animation for the overlay */
@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Empty State Placeholder */
.empty-state-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 24px;
}

.empty-icon-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(60, 60, 60, 0.7);
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
}

.empty-title-placeholder {
    height: 24px;
    width: 200px;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
}

.empty-text-placeholder {
    height: 16px;
    width: 300px;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

/* Chat Area Placeholders */
.chat-placeholder {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    height: 100%;
}

.message-placeholder {
    display: flex;
    align-items: flex-start;
    max-width: 80%;
}

.message-placeholder.incoming {
    align-self: flex-start;
}

.message-placeholder.outgoing {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar-placeholder {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(60, 60, 60, 0.7);
    margin: 0 12px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.message-content-placeholder {
    background-color: rgba(45, 45, 45, 0.7);
    padding: 12px 16px;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    width: 200px;
}

.message-placeholder.outgoing .message-content-placeholder {
    background-color: rgba(14, 165, 233, 0.3);
}

.message-text-placeholder {
    height: 14px;
    width: 100%;
    background-color: rgba(60, 60, 60, 0.7);
    border-radius: 4px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

.message-text-placeholder:last-child {
    width: 70%;
    margin-bottom: 0;
}

.message-placeholder.outgoing .message-text-placeholder {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .friend-list-placeholder {
        padding: 8px;
        gap: 8px;
    }
    
    .friend-item-placeholder,
    .group-chat-placeholder {
        padding: 10px;
    }
    
    .friend-avatar-placeholder,
    .group-avatar-placeholder {
        width: 36px;
        height: 36px;
    }
}
