from flask import request, jsonify, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import UploadedFile
from . import upload_api
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    """Check if the file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@upload_api.route('', methods=['POST'])
@login_required
def upload_file():
    """Upload a file and store it in the database"""
    try:
        # Check if the post request has the file part
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # If user does not select file, browser also
        # submit an empty part without filename
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        if file and allowed_file(file.filename):
            # Create a secure filename
            filename = secure_filename(file.filename)

            # Get the file extension
            ext = '.' + filename.rsplit('.', 1)[1].lower()

            # Read the file data
            file_data = file.read()

            # Determine content type based on file extension
            content_type = file.content_type
            if not content_type or content_type == 'application/octet-stream':
                if ext == '.jpg' or ext == '.jpeg':
                    content_type = 'image/jpeg'
                elif ext == '.png':
                    content_type = 'image/png'
                elif ext == '.gif':
                    content_type = 'image/gif'
                elif ext == '.webp':
                    content_type = 'image/webp'
                else:
                    content_type = 'image/jpeg'  # Default to JPEG

            # Store the file in the database
            uploaded_file = UploadedFile(
                filename=filename,
                content_type=content_type,
                data=file_data,
                uploaded_by=current_user
            )
            uploaded_file.save()

            # Generate the URL for the file
            file_url = f"/api/upload/{uploaded_file.file_id}"

            logger.info(f"File uploaded successfully: {file_url}")

            return jsonify({
                'success': True,
                'url': file_url,
                'file_id': uploaded_file.file_id,
                'filename': filename
            })

        return jsonify({'error': 'File type not allowed'}), 400

    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@upload_api.route('/<file_id>', methods=['GET'])
def get_file(file_id):
    """Get a file from the database by its ID"""
    try:
        # Find the file in the database
        uploaded_file = UploadedFile.objects(file_id=file_id).first()

        if not uploaded_file:
            return jsonify({'error': 'File not found'}), 404

        # Create a file-like object from the binary data
        file_data = io.BytesIO(uploaded_file.data)

        # Send the file to the client
        return send_file(
            file_data,
            mimetype=uploaded_file.content_type,
            as_attachment=False,
            download_name=uploaded_file.filename
        )

    except Exception as e:
        logger.error(f"Error retrieving file: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@upload_api.route('/<file_id>', methods=['DELETE'])
@login_required
def delete_file(file_id):
    """Delete a file from the database by its ID"""
    try:
        # Find the file in the database
        uploaded_file = UploadedFile.objects(file_id=file_id).first()

        if not uploaded_file:
            return jsonify({'error': 'File not found'}), 404

        # Check if the user is the owner of the file or an admin
        if str(uploaded_file.uploaded_by.id) != str(current_user.id) and not current_user.is_admin:
            return jsonify({'error': 'Not authorized to delete this file'}), 403

        # Delete the file
        uploaded_file.delete()
        logger.info(f"File {file_id} deleted by user {current_user.username}")

        return jsonify({
            'success': True,
            'message': 'File deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
