from flask import Blueprint, render_template, redirect, url_for, session, request, flash
from .services.spotify_client import create_spotify_oauth, save_spotify_credentials, get_spotify_client
import spotipy
from flask_login import current_user, login_required
from models.user_activity import UserActivity
from models.restricted_user import RestrictedUser

spotify_bp = Blueprint('spotify', __name__, url_prefix='/spotify')

@spotify_bp.route('/logout')
def logout():
    # Clear only Spotify-related session data
    if 'token_info' in session:
        session.pop('token_info', None)

    # Redirect back to the main Spotify page
    return redirect(url_for('spotify.index'))

@spotify_bp.route('/')
def index():
    # Check if user is restricted from spotify service
    if current_user.is_authenticated and RestrictedUser.is_restricted(current_user.email, 'spotify'):
        return render_template('spotify.html', restricted=True, service_name='Spotify')

    # Track user activity if logged in
    if current_user.is_authenticated:
        UserActivity.update_activity(current_user, 'spotify')

    sp_oauth = create_spotify_oauth()

    # Try to get token from session or database
    token_info = session.get('token_info')

    # If user is logged in and no token in session, try to get from database
    if current_user.is_authenticated and not token_info:
        try:
            # This will try to get credentials from database
            sp = get_spotify_client()
            token_info = session.get('token_info')  # Should be populated by get_spotify_client
        except Exception:
            # If no credentials found, redirect to connect
            return redirect(url_for('spotify.connect'))
    elif not token_info:
        # Not logged in and no token in session
        return redirect(url_for('spotify.connect'))

    # Check if token is expired and refresh if needed
    if sp_oauth.is_token_expired(token_info):
        try:
            token_info = sp_oauth.refresh_access_token(token_info['refresh_token'])
            session['token_info'] = token_info

            # Save refreshed token to database if user is logged in
            if current_user.is_authenticated:
                save_spotify_credentials(token_info)
        except Exception:
            # If refresh fails, redirect to connect
            return redirect(url_for('spotify.connect'))

    return render_template('spotify.html',
                         token=token_info['access_token'])

@spotify_bp.route('/connect')
def connect():
    sp_oauth = create_spotify_oauth()
    # Add state parameter to track the auth flow
    auth_url = sp_oauth.get_authorize_url(state='account_selection')
    return redirect(auth_url)

@spotify_bp.route('/callback')
def callback():
    sp_oauth = create_spotify_oauth()

    # Don't clear the entire session as it might contain user login info
    # Just clear Spotify-related data
    if 'token_info' in session:
        session.pop('token_info', None)
    if 'spotify_user' in session:
        session.pop('spotify_user', None)

    code = request.args.get('code')
    state = request.args.get('state')

    # Verify state to ensure it's from our auth flow
    if state != 'account_selection':
        return redirect(url_for('spotify.connect'))

    token_info = sp_oauth.get_access_token(code)
    session['token_info'] = token_info

    # Get user info to display account details
    sp = spotipy.Spotify(auth_manager=sp_oauth)
    user_info = sp.current_user()
    session['spotify_user'] = {
        'name': user_info['display_name'],
        'email': user_info['email'],
        'image': user_info['images'][0]['url'] if user_info.get('images') else None
    }

    # Save credentials to database if user is logged in
    if current_user.is_authenticated:
        save_spotify_credentials(token_info)
        flash('Your Spotify account has been connected successfully!', 'success')

    return redirect(url_for('spotify.index'))



