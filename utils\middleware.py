"""
Middleware functions for the Flask application
"""
import logging
from flask import request, g
from flask_login import current_user
from utils.geolocation import get_country_from_ip

# Configure logging
logger = logging.getLogger(__name__)

def country_detection_middleware():
    """
    Middleware to detect and store user country based on IP address
    This runs on each request but only updates the database if needed
    """
    try:
        # Skip for static files, API calls, and other non-HTML routes
        if request.path.startswith('/static/') or request.path.startswith('/api/'):
            return

        # Only process for authenticated users who don't have a country code
        if current_user.is_authenticated and (not current_user.country_code or current_user.country_code.strip() == ""):
            # Check if we've already processed this user in this request or session
            if not getattr(g, 'country_processed', False):
                # Get country from IP
                country_code = get_country_from_ip()

                if country_code:
                    # Update user's country code
                    if current_user.update_country_code(country_code):
                        logger.info(f"Updated country for user {current_user.username}: {country_code}")

                    # Mark as processed for this request
                    g.country_processed = True
    except Exception as e:
        logger.error(f"Error in country detection middleware: {str(e)}")
        # Don't raise the exception to avoid breaking the request
        pass
