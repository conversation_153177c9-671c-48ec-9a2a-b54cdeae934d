/* Statistics Modern - Enhanced, Aesthetic, Informative */

/* Base styles for statistics panel */
#statisticsView {
  --primary-gradient: linear-gradient(135deg, #06b6d4, #3b82f6);
  --secondary-gradient: linear-gradient(135deg, #8b5cf6, #ec4899);
  --accent-gradient: linear-gradient(135deg, #f59e0b, #ef4444);
  --success-gradient: linear-gradient(135deg, #10b981, #059669);
  --card-bg: rgba(15, 23, 42, 0.7);
  --card-border: rgba(51, 65, 85, 0.5);
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --chart-height: 220px;
  --transition-bezier: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Dark mode styles */
.stats-dark-mode {
  --card-bg: rgba(15, 23, 42, 0.85);
  --card-border: rgba(51, 65, 85, 0.6);
}

/* Card styling with glass morphism */
.stats-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: transform 0.4s var(--transition-bezier), 
              box-shadow 0.4s ease, 
              background-color 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Card header with improved styling */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  background-color: rgba(30, 41, 59, 0.5);
}

.stats-header h3 {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.stats-header h3 i {
  margin-right: 0.5rem;
  filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.3));
}

/* Grid layout with improved spacing */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.75rem;
  margin-top: 1.25rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.stats-grid-full {
  grid-column: span 2;
  width: 100%;
}

.stats-grid-half {
  grid-column: span 1;
  width: 100%;
}

/* Metrics grid with enhanced styling */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.25rem;
  padding: 1.5rem;
  width: 100%;
}

.metric-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 1rem;
  padding: 1.25rem 1rem;
  text-align: center;
  transition: transform 0.4s var(--transition-bezier), 
              box-shadow 0.3s ease, 
              background-color 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(51, 65, 85, 0.2);
  position: relative;
  overflow: hidden;
}

.metric-tile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-tile:hover {
  transform: translateY(-5px) scale(1.03);
  background: rgba(30, 41, 59, 0.6);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.metric-tile:hover::before {
  opacity: 1;
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(6, 182, 212, 0.2);
  transition: transform 0.3s ease;
  position: relative;
}

.metric-tile:hover .metric-value {
  transform: scale(1.1);
}

.metric-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.metric-tile:hover .metric-label {
  color: #e2e8f0;
}

/* Chart container with improved styling */
.chart-wrapper {
  padding: 1.5rem;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.chart-container {
  height: var(--chart-height);
  position: relative;
  width: 100%;
  max-width: 100%;
  transition: transform 0.4s var(--transition-bezier), 
              box-shadow 0.3s ease, 
              filter 0.3s ease;
  border-radius: 0.75rem;
  overflow: hidden;
  background: rgba(30, 41, 59, 0.2);
  border: 1px solid rgba(51, 65, 85, 0.1);
}

.chart-container:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  filter: brightness(1.05);
}

/* Legend styling with improved visuals */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: rgba(30, 41, 59, 0.4);
  border-radius: 0.5rem;
  padding: 0.35rem 0.75rem;
  transition: transform 0.3s ease, background-color 0.3s ease;
  border: 1px solid rgba(51, 65, 85, 0.2);
}

.legend-item:hover {
  transform: translateY(-2px);
  background: rgba(30, 41, 59, 0.6);
}

.legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.legend-label {
  margin-right: 0.5rem;
}

.legend-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Period selector buttons with improved styling */
.period-selector {
  display: flex;
  gap: 0.35rem;
}

.period-btn {
  background: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(51, 65, 85, 0.3);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.35rem 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-btn:hover {
  background: rgba(30, 41, 59, 0.6);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.period-btn.active {
  background: var(--primary-gradient);
  border-color: transparent;
  color: white;
  box-shadow: 0 5px 15px rgba(6, 182, 212, 0.2);
}

/* Chart clickable styles with improved interactions */
.chart-clickable {
  cursor: pointer;
  transition: transform 0.4s var(--transition-bezier), 
              box-shadow 0.3s ease;
  position: relative;
}

.chart-clickable::after {
  content: '';
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(148, 163, 184, 0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15 3 21 3 21 9'%3E%3C/polyline%3E%3Cpath d='M21 3L14 10'%3E%3C/path%3E%3Cpath d='M10 14L3 21'%3E%3C/path%3E%3Cpolyline points='3 15 3 21 9 21'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 5;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.2));
}

.chart-clickable:hover::after {
  opacity: 1;
  transform: scale(1.2);
}

/* Chart modal with improved styling */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease, visibility 0.4s ease;
}

.chart-modal.visible {
  opacity: 1;
  visibility: visible;
}

.chart-modal-content {
  background-color: rgba(15, 23, 42, 0.95);
  border: 1px solid var(--card-border);
  border-radius: 1rem;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(30px) scale(0.95);
  transition: transform 0.4s var(--transition-bezier);
}

.chart-modal.visible .chart-modal-content {
  transform: translateY(0) scale(1);
}

.chart-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  background-color: rgba(30, 41, 59, 0.5);
}

.chart-modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-modal-btn {
  background: rgba(51, 65, 85, 0.3);
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-modal-btn:hover {
  background: rgba(51, 65, 85, 0.5);
  color: var(--text-primary);
  transform: rotate(90deg);
}

.chart-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.chart-modal-container {
  height: 60vh;
  position: relative;
}

/* Users table with improved styling */
.top-users-container {
  height: 240px; /* Height for exactly 4 rows */
  overflow-y: auto;
  border-radius: 0.75rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) rgba(30, 41, 59, 0.5);
  background: rgba(30, 41, 59, 0.2);
  border: 1px solid rgba(51, 65, 85, 0.1);
}

.top-users-container::-webkit-scrollbar {
  width: 5px;
}

.top-users-container::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 5px;
}

.top-users-container::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.3);
  border-radius: 5px;
}

.top-users-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.5);
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  position: sticky;
  top: 0;
  background-color: rgba(30, 41, 59, 0.8);
  z-index: 1;
  backdrop-filter: blur(4px);
}

.users-table td {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(51, 65, 85, 0.1);
  transition: background-color 0.3s ease;
}

.users-table tr:hover td {
  background-color: rgba(51, 65, 85, 0.3);
}

/* Loading and error states with improved styling */
.stats-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(8px);
}

.stats-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.1);
}

.stats-error::before {
  content: '⚠️';
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

/* Tooltip with improved styling */
.stats-tooltip {
  position: absolute;
  background: rgba(15, 23, 42, 0.95);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  font-size: 0.8rem;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s var(--transition-bezier);
  z-index: 100;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(51, 65, 85, 0.5);
  max-width: 250px;
  backdrop-filter: blur(8px);
  transform: translateY(10px);
}

.stats-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Tooltip styling */
.tooltip-title {
  font-family: var(--font-heading, 'Space Grotesk', sans-serif);
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  color: #f1f5f9;
  border-bottom: 1px solid rgba(51, 65, 85, 0.3);
  padding-bottom: 0.5rem;
}

.tooltip-body {
  font-family: var(--font-body, 'Inter', sans-serif);
  font-size: 0.85rem;
}

.tooltip-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #e2e8f0;
}

.tooltip-color-box {
  width: 0.85rem;
  height: 0.85rem;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Loading animation */
.chart-container.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(4px);
}

.chart-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 3px solid rgba(6, 182, 212, 0.2);
  border-top-color: rgba(6, 182, 212, 1);
  animation: spin 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
  z-index: 11;
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.3);
}

@keyframes spin {
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced skeleton loader */
.skeleton-loader {
  background: linear-gradient(90deg, 
      rgba(30, 41, 59, 0.4) 25%, 
      rgba(51, 65, 85, 0.5) 50%, 
      rgba(30, 41, 59, 0.4) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Dark mode toggle */
#darkModeToggle {
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  background: rgba(30, 41, 59, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(51, 65, 85, 0.3);
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 5;
}

#darkModeToggle:hover {
  background: rgba(51, 65, 85, 0.6);
  transform: translateY(-2px) rotate(15deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* World Map Styles with improved visuals */
.world-map-container {
  height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  background-color: rgba(30, 41, 59, 0.4);
  animation: mapFadeIn 0.8s ease forwards;
  opacity: 1;
  border: 1px solid rgba(51, 65, 85, 0.2);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* D3 Map Styles */
.country {
  fill: rgba(30, 41, 59, 0.8);
  stroke: rgba(51, 65, 85, 0.5);
  stroke-width: 0.5px;
  transition: fill 0.3s ease, stroke 0.3s ease, transform 0.3s ease;
}

.country:hover {
  fill: rgba(6, 182, 212, 0.7);
  cursor: pointer;
  stroke: rgba(255, 255, 255, 0.8);
  stroke-width: 1px;
  filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.5));
}

.country-with-users {
  stroke: rgba(255, 255, 255, 0.5);
  stroke-width: 0.8px;
  filter: drop-shadow(0 0 3px rgba(6, 182, 212, 0.4));
}

/* Map tooltip with improved styling */
.map-tooltip {
  position: fixed;
  display: block;
  background: rgba(15, 23, 42, 0.95);
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(6, 182, 212, 0.5);
  pointer-events: none;
  z-index: 1000;
  max-width: 250px;
  min-width: 150px;
  text-align: center;
  backdrop-filter: blur(8px);
  transform: translateY(-5px);
  transition: opacity 0.3s ease, transform 0.3s var(--transition-bezier);
}

/* Tooltip country name */
.tooltip-country {
  color: #e2e8f0;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

/* Tooltip user count - highlighted */
.tooltip-users {
  color: #06b6d4;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-top: 0.5rem;
  padding: 0.25rem 0;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}

/* Map Legend with improved styling */
.map-legend {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 0.5rem;
  padding: 0.75rem;
  border: 1px solid rgba(51, 65, 85, 0.5);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  backdrop-filter: blur(4px);
  z-index: 100;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.map-legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.map-legend-color {
  width: 0.85rem;
  height: 0.85rem;
  border-radius: 0.25rem;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

/* Map animation */
@keyframes mapFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Map controls with improved styling */
.map-controls {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 100;
}

.map-control-btn {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  border: none;
  font-size: 1.25rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.map-control-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stats-grid-half {
    grid-column: span 1;
    width: 100%;
  }

  .stats-grid-full {
    grid-column: span 1;
    width: 100%;
  }

  .metrics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1.25rem;
  }

  .chart-container {
    height: 200px;
  }

  .world-map-container {
    height: 300px;
  }

  .stats-card {
    overflow: visible;
  }
  
  #darkModeToggle {
    top: 0.75rem;
    right: 0.75rem;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    padding: 1rem;
  }

  .metric-tile {
    padding: 1rem 0.75rem;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .metric-label {
    font-size: 0.75rem;
  }

  .chart-container {
    height: 180px;
  }

  .world-map-container {
    height: 250px;
  }

  /* Period selector responsive styles */
  .period-selector {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 0.75rem;
  }

  .stats-header {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }

  .chart-wrapper {
    padding: 1rem;
  }

  .stats-card {
    margin-bottom: 0;
  }
}